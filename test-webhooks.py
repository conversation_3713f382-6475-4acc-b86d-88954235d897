#!/usr/bin/env python3
"""
Test script to simulate webhook calls to the WordPress plugin
"""

import requests
import json
import hashlib
import hmac
import time
import uuid

# WordPress site URL
wordpress_url = 'http://localhost:8080'

# Webhook secret (should match the one configured in WordPress)
webhook_secret = 'test_webhook_secret_12345'  # This should be replaced with the actual secret

def generate_webhook_signature(payload, secret):
    """
    Generate webhook signature for GridSpoke
    """
    return hmac.new(
        secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

def send_webhook(event_type, payload_data):
    """
    Send webhook to WordPress plugin
    """
    # Add timestamp to payload
    payload_data['timestamp'] = int(time.time())
    
    # Convert payload to JSON
    payload_json = json.dumps(payload_data)
    
    # Generate signature
    signature = generate_webhook_signature(payload_json, webhook_secret)
    
    # Send webhook
    headers = {
        'Content-Type': 'application/json',
        'X-GridSpoke-Signature': signature
    }
    
    webhook_url = f'{wordpress_url}/wp-json/gridspoke-seo/v1/webhook'
    
    try:
        response = requests.post(webhook_url, headers=headers, data=payload_json, timeout=30)
        return {
            'status_code': response.status_code,
            'body': response.json() if response.content else None,
            'headers': dict(response.headers)
        }
    except requests.exceptions.RequestException as e:
        return {
            'status_code': 0,
            'body': {'error': str(e)},
            'headers': {}
        }

def test_optimization_completed():
    """
    Test optimization completed webhook
    """
    print("[INFO] Testing optimization completed webhook...")
    
    # Generate a unique optimization ID
    optimization_id = str(uuid.uuid4())
    
    # Create test payload
    payload = {
        'event': 'optimization.completed',
        'optimization_id': optimization_id,
        'ai_model': 'claude-3-haiku',
        'tokens_used': 1234,
        'products': [
            {
                'id': '123',
                'type': 'woocommerce',
                'optimized_content': {
                    'title': 'Optimized Product Title',
                    'description': 'This is an optimized product description that highlights the key features and benefits of the product.',
                    'meta_description': 'Optimized meta description for better SEO performance.',
                    'meta_title': 'Optimized Product Title - Best Product Ever',
                    'short_description': 'Short description of the optimized product.'
                }
            },
            {
                'id': '456',
                'type': 'woocommerce',
                'optimized_content': {
                    'title': 'Another Optimized Product Title',
                    'description': 'This is another optimized product description with improved keywords and structure.',
                    'meta_description': 'Another optimized meta description for better search visibility.',
                    'meta_title': 'Another Optimized Product Title - Premium Quality',
                    'short_description': 'Short description of another optimized product.'
                }
            }
        ]
    }
    
    response = send_webhook('optimization.completed', payload)
    
    print(f"  Status: {response['status_code']}")
    if response['body']:
        print(f"  Body: {response['body']}")
    
    return response

def test_optimization_failed():
    """
    Test optimization failed webhook
    """
    print("[INFO] Testing optimization failed webhook...")
    
    # Generate a unique optimization ID
    optimization_id = str(uuid.uuid4())
    
    # Create test payload
    payload = {
        'event': 'optimization.failed',
        'optimization_id': optimization_id,
        'error_message': 'Failed to generate content due to API timeout',
        'products': [
            {
                'id': '789',
                'type': 'woocommerce'
            }
        ]
    }
    
    response = send_webhook('optimization.failed', payload)
    
    print(f"  Status: {response['status_code']}")
    if response['body']:
        print(f"  Body: {response['body']}")
    
    return response

def test_sync_completed():
    """
    Test sync completed webhook
    """
    print("[INFO] Testing sync completed webhook...")
    
    # Generate a unique sync ID
    sync_id = str(uuid.uuid4())
    
    # Create test payload
    payload = {
        'event': 'sync.completed',
        'sync_id': sync_id,
        'synced_count': 15,
        'store_type': 'woocommerce'
    }
    
    response = send_webhook('sync.completed', payload)
    
    print(f"  Status: {response['status_code']}")
    if response['body']:
        print(f"  Body: {response['body']}")
    
    return response

def test_invalid_signature():
    """
    Test webhook with invalid signature
    """
    print("[INFO] Testing webhook with invalid signature...")
    
    # Create test payload
    payload_data = {
        'event': 'optimization.completed',
        'optimization_id': str(uuid.uuid4()),
        'products': []
    }
    
    # Convert payload to JSON
    payload_json = json.dumps(payload_data)
    
    # Generate invalid signature
    invalid_signature = 'invalid_signature_12345'
    
    # Send webhook
    headers = {
        'Content-Type': 'application/json',
        'X-GridSpoke-Signature': invalid_signature
    }
    
    webhook_url = f'{wordpress_url}/wp-json/gridspoke-seo/v1/webhook'
    
    try:
        response = requests.post(webhook_url, headers=headers, data=payload_json, timeout=30)
        result = {
            'status_code': response.status_code,
            'body': response.json() if response.content else None,
            'headers': dict(response.headers)
        }
    except requests.exceptions.RequestException as e:
        result = {
            'status_code': 0,
            'body': {'error': str(e)},
            'headers': {}
        }
    
    print(f"  Status: {result['status_code']}")
    if result['body']:
        print(f"  Body: {result['body']}")
    
    return result

if __name__ == "__main__":
    print(f"[INFO] Testing webhook handling in WordPress plugin at {wordpress_url}")
    
    # Test optimization completed webhook
    opt_completed_response = test_optimization_completed()
    
    # Test optimization failed webhook
    opt_failed_response = test_optimization_failed()
    
    # Test sync completed webhook
    sync_completed_response = test_sync_completed()
    
    # Test invalid signature
    invalid_sig_response = test_invalid_signature()
    
    print("\n[SUMMARY]")
    print(f"Optimization Completed: {opt_completed_response['status_code']}")
    print(f"Optimization Failed: {opt_failed_response['status_code']}")
    print(f"Sync Completed: {sync_completed_response['status_code']}")
    print(f"Invalid Signature: {invalid_sig_response['status_code']}")
    
    print("\n[INFO] Webhook testing completed!")