# Production Environment Variables for GridSpoke
# Copy this file to .env.prod and fill in the values

# =============================================================================
# Core Application Settings
# =============================================================================
ENV=production
SECRET_KEY=your-super-secret-key-here-change-this-in-production
DOMAIN_NAME=your-domain.com
SSL_EMAIL=<EMAIL>

# =============================================================================
# Database Configuration
# =============================================================================
POSTGRES_DB=gridspoke_prod
POSTGRES_USER=gridspoke_user
POSTGRES_PASSWORD=secure-postgres-password-here
DATABASE_URL=postgresql+asyncpg://gridspoke_user:secure-postgres-password-here@db:5432/gridspoke_prod

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_PASSWORD=secure-redis-password-here
REDIS_URL=redis://:secure-redis-password-here@redis:6379/0

# =============================================================================
# AI Service Configuration
# =============================================================================
OPENROUTER_API_KEY=your-openrouter-api-key-here

# =============================================================================
# Security & CORS
# =============================================================================
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# =============================================================================
# Monitoring & Error Tracking
# =============================================================================
SENTRY_DSN=your-sentry-dsn-here
GRAFANA_PASSWORD=secure-grafana-password-here

# =============================================================================
# Celery Flower Monitoring
# =============================================================================
FLOWER_USER=admin
FLOWER_PASSWORD=secure-flower-password-here

# =============================================================================
# Email Configuration (for notifications)
# =============================================================================
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password-here
FROM_EMAIL=GridSpoke <<EMAIL>>

# =============================================================================
# Rate Limiting & Performance
# =============================================================================
RATE_LIMIT_PER_MINUTE=60
MAX_REQUEST_SIZE=10485760  # 10MB
WORKER_TIMEOUT=30

# =============================================================================
# Backup Configuration
# =============================================================================
BACKUP_ENCRYPTION_KEY=backup-encryption-key-here
S3_BACKUP_BUCKET=gridspoke-backups
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key
S3_REGION=us-east-1

# =============================================================================
# WordPress Integration
# =============================================================================
WORDPRESS_WEBHOOK_SECRET=secure-webhook-secret-here
PLUGIN_DOWNLOAD_TOKEN=secure-plugin-token-here

# =============================================================================
# Feature Flags
# =============================================================================
ENABLE_REGISTRATION=false
ENABLE_RATE_LIMITING=true
ENABLE_ANALYTICS=true
ENABLE_CACHING=true
ENABLE_COMPRESSION=true

# =============================================================================
# Logging Configuration
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=/app/logs/gridspoke.log

# =============================================================================
# SSL Configuration
# =============================================================================
SSL_CERT_PATH=/etc/letsencrypt/live/your-domain.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/your-domain.com/privkey.pem
