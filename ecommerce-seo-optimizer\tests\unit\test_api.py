# GridSpoke API Endpoints Unit Tests
"""
Comprehensive unit tests for GridSpoke FastAPI endpoints.
Tests all optimization, authentication, and management APIs.
"""

import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import status
from fastapi.testclient import TestClient


class TestAuthenticationEndpoints:
    """Test authentication and authorization endpoints"""
    
    def test_login_success(self, test_client):
        """Test successful user login"""
        login_data = {
            "email": "<EMAIL>",
            "password": "secure_password_123"
        }
        
        with patch('api.core.security.authenticate_user') as mock_auth:
            with patch('api.core.security.create_access_token') as mock_token:
                mock_auth.return_value = {
                    "id": "user_123",
                    "email": "<EMAIL>",
                    "role": "admin"
                }
                mock_token.return_value = "jwt_token_123"
                
                response = test_client.post("/api/v1/auth/login", json=login_data)
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "access_token" in data
                assert data["token_type"] == "bearer"
                assert "user" in data
    
    def test_login_invalid_credentials(self, test_client):
        """Test login with invalid credentials"""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrong_password"
        }
        
        with patch('api.core.security.authenticate_user') as mock_auth:
            mock_auth.return_value = None
            
            response = test_client.post("/api/v1/auth/login", json=login_data)
            
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            assert "Invalid credentials" in response.json()["detail"]
    
    def test_token_refresh(self, test_client, auth_headers):
        """Test JWT token refresh"""
        with patch('api.core.security.verify_token') as mock_verify:
            with patch('api.core.security.create_access_token') as mock_create:
                mock_verify.return_value = {"user_id": "user_123"}
                mock_create.return_value = "new_jwt_token_456"
                
                response = test_client.post(
                    "/api/v1/auth/refresh",
                    headers=auth_headers
                )
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "access_token" in data
    
    def test_protected_endpoint_requires_auth(self, test_client):
        """Test that protected endpoints require authentication"""
        response = test_client.get("/api/v1/stores")
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestStoreManagementEndpoints:
    """Test store management API endpoints"""
    
    def test_create_store_success(self, test_client, auth_headers, sample_store):
        """Test successful store creation"""
        with patch('api.crud.store.create_store') as mock_create:
            mock_create.return_value = {**sample_store, "id": "store_new_123"}
            
            response = test_client.post(
                "/api/v1/stores",
                json=sample_store,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["name"] == sample_store["name"]
            assert data["platform"] == sample_store["platform"]
            assert "id" in data
    
    def test_create_store_validation_error(self, test_client, auth_headers):
        """Test store creation with validation errors"""
        invalid_store = {
            "name": "",  # Empty name should fail validation
            "url": "invalid-url",  # Invalid URL format
            "platform": "unknown_platform"  # Invalid platform
        }
        
        response = test_client.post(
            "/api/v1/stores",
            json=invalid_store,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        errors = response.json()["detail"]
        assert any("name" in str(error) for error in errors)
    
    def test_get_stores_list(self, test_client, auth_headers):
        """Test retrieving stores list"""
        mock_stores = [
            {"id": "store_1", "name": "Store 1", "platform": "woocommerce"},
            {"id": "store_2", "name": "Store 2", "platform": "surecart"}
        ]
        
        with patch('api.crud.store.get_stores') as mock_get:
            mock_get.return_value = mock_stores
            
            response = test_client.get("/api/v1/stores", headers=auth_headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data) == 2
            assert data[0]["name"] == "Store 1"
    
    def test_get_store_by_id(self, test_client, auth_headers, sample_store):
        """Test retrieving specific store by ID"""
        store_id = "store_test_456"
        
        with patch('api.crud.store.get_store') as mock_get:
            mock_get.return_value = {**sample_store, "id": store_id}
            
            response = test_client.get(
                f"/api/v1/stores/{store_id}",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == store_id
            assert data["name"] == sample_store["name"]
    
    def test_get_store_not_found(self, test_client, auth_headers):
        """Test retrieving non-existent store"""
        with patch('api.crud.store.get_store') as mock_get:
            mock_get.return_value = None
            
            response = test_client.get(
                "/api/v1/stores/nonexistent_id",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_update_store(self, test_client, auth_headers, sample_store):
        """Test updating store information"""
        store_id = "store_test_456"
        update_data = {
            "name": "Updated Store Name",
            "optimization_settings": {
                "auto_optimize": False,
                "schedule_time": "03:00"
            }
        }
        
        with patch('api.crud.store.update_store') as mock_update:
            updated_store = {**sample_store, **update_data, "id": store_id}
            mock_update.return_value = updated_store
            
            response = test_client.put(
                f"/api/v1/stores/{store_id}",
                json=update_data,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["name"] == update_data["name"]
    
    def test_delete_store(self, test_client, auth_headers):
        """Test deleting a store"""
        store_id = "store_test_456"
        
        with patch('api.crud.store.delete_store') as mock_delete:
            mock_delete.return_value = True
            
            response = test_client.delete(
                f"/api/v1/stores/{store_id}",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_204_NO_CONTENT


class TestProductOptimizationEndpoints:
    """Test product optimization API endpoints"""
    
    def test_optimize_single_product_success(self, test_client, auth_headers, sample_product, mock_openrouter_response):
        """Test successful single product optimization"""
        with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
            # Mock the AI service response based on OpenRouter format
            mock_optimize.return_value = {
                "title": "Premium Wireless Bluetooth Headphones - Superior Sound Quality",
                "description": "Experience crystal-clear audio with our premium wireless Bluetooth headphones...",
                "meta_title": "Premium Wireless Bluetooth Headphones | Superior Sound - TechBrand", 
                "meta_description": "Premium wireless Bluetooth headphones with superior sound quality...",
                "alt_text": "Premium wireless Bluetooth headphones with sleek black design",
                "keywords": ["premium wireless headphones", "bluetooth headphones noise cancellation"],
                "seo_score": 85.5,
                "optimization_id": "opt_123456"
            }
            
            response = test_client.post(
                "/api/v1/optimize/product",
                json=sample_product,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "optimization_id" in data
            assert "title" in data
            assert "seo_score" in data
            assert data["seo_score"] > 80  # Should be high quality optimization
            mock_optimize.assert_called_once()
    
    def test_optimize_product_auth_required(self, test_client, sample_product):
        """Test that product optimization requires authentication"""
        response = test_client.post("/api/v1/optimize/product", json=sample_product)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_optimize_product_invalid_data(self, test_client, auth_headers):
        """Test product optimization with invalid data"""
        invalid_product = {
            "name": "",  # Empty name
            "price": -10  # Negative price
        }
        
        response = test_client.post(
            "/api/v1/optimize/product",
            json=invalid_product,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_bulk_optimization_job_creation(self, test_client, auth_headers, sample_store):
        """Test bulk optimization job creation"""
        job_data = {
            "store_id": sample_store["id"],
            "product_ids": ["prod_1", "prod_2", "prod_3", "prod_4", "prod_5"],
            "optimization_type": "full",
            "priority": "normal"
        }
        
        with patch('workers.tasks.product_tasks.optimize_products_bulk.delay') as mock_task:
            mock_task.return_value = MagicMock(id="celery_task_123")
            
            with patch('api.crud.optimization_job.create_job') as mock_create_job:
                mock_create_job.return_value = {
                    "id": "job_bulk_123",
                    "status": "queued",
                    "total_items": 5,
                    "processed_items": 0
                }
                
                response = test_client.post(
                    "/api/v1/optimize/bulk",
                    json=job_data,
                    headers=auth_headers
                )
                
                assert response.status_code == status.HTTP_202_ACCEPTED
                data = response.json()
                assert "job_id" in data
                assert data["status"] == "queued"
                assert data["total_items"] == 5
                mock_task.assert_called_once()
    
    @pytest.mark.parametrize("invalid_data,expected_error", [
        ({}, "store_id"),
        ({"store_id": ""}, "store_id"),
        ({"store_id": "valid", "product_ids": []}, "product_ids"),
        ({"store_id": "valid", "product_ids": ["prod_1"], "optimization_type": "invalid"}, "optimization_type")
    ])
    def test_bulk_optimization_validation(self, test_client, auth_headers, invalid_data, expected_error):
        """Test bulk optimization input validation"""
        response = test_client.post(
            "/api/v1/optimize/bulk",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        error_details = response.json()["detail"]
        assert any(expected_error in str(error.get("loc", [])) for error in error_details)
    
    def test_get_optimization_job_status(self, test_client, auth_headers):
        """Test retrieving optimization job status"""
        job_id = "job_test_789"
        
        with patch('api.crud.optimization_job.get_job') as mock_get_job:
            mock_get_job.return_value = {
                "id": job_id,
                "status": "processing",
                "total_items": 10,
                "processed_items": 7,
                "progress": 70.0,
                "estimated_completion": "2025-08-12T15:30:00Z"
            }
            
            response = test_client.get(
                f"/api/v1/jobs/{job_id}",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == job_id
            assert data["status"] == "processing"
            assert data["progress"] == 70.0
    
    def test_get_optimization_job_not_found(self, test_client, auth_headers):
        """Test retrieving non-existent optimization job"""
        with patch('api.crud.optimization_job.get_job') as mock_get_job:
            mock_get_job.return_value = None
            
            response = test_client.get(
                "/api/v1/jobs/nonexistent_job",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_cancel_optimization_job(self, test_client, auth_headers):
        """Test canceling an optimization job"""
        job_id = "job_test_789"
        
        with patch('api.crud.optimization_job.cancel_job') as mock_cancel:
            with patch('workers.tasks.product_tasks.revoke_task') as mock_revoke:
                mock_cancel.return_value = {
                    "id": job_id,
                    "status": "cancelled",
                    "cancelled_at": "2025-08-12T14:30:00Z"
                }
                
                response = test_client.post(
                    f"/api/v1/jobs/{job_id}/cancel",
                    headers=auth_headers
                )
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "cancelled"


class TestProductManagementEndpoints:
    """Test product management API endpoints"""
    
    def test_get_products_list(self, test_client, auth_headers):
        """Test retrieving products list with pagination"""
        mock_products = {
            "items": [
                {"id": "prod_1", "name": "Product 1", "optimization_status": "optimized"},
                {"id": "prod_2", "name": "Product 2", "optimization_status": "pending"}
            ],
            "total": 25,
            "page": 1,
            "per_page": 10,
            "pages": 3
        }
        
        with patch('api.crud.product.get_products') as mock_get:
            mock_get.return_value = mock_products
            
            response = test_client.get(
                "/api/v1/products?page=1&per_page=10",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data["items"]) == 2
            assert data["total"] == 25
            assert data["page"] == 1
    
    def test_get_products_with_filters(self, test_client, auth_headers):
        """Test retrieving products with search and filter parameters"""
        with patch('api.crud.product.get_products') as mock_get:
            mock_get.return_value = {
                "items": [{"id": "prod_1", "name": "Bluetooth Headphones"}],
                "total": 1,
                "page": 1,
                "per_page": 10
            }
            
            response = test_client.get(
                "/api/v1/products?search=bluetooth&category=electronics&optimization_status=pending",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data["items"]) == 1
            # Verify that filters were applied
            mock_get.assert_called_once()
            call_args = mock_get.call_args
            assert "bluetooth" in str(call_args).lower()
    
    def test_get_product_by_id(self, test_client, auth_headers, sample_product):
        """Test retrieving specific product by ID"""
        product_id = "prod_test_123"
        
        with patch('api.crud.product.get_product') as mock_get:
            mock_get.return_value = {**sample_product, "id": product_id}
            
            response = test_client.get(
                f"/api/v1/products/{product_id}",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == product_id
            assert data["name"] == sample_product["name"]
    
    def test_update_product(self, test_client, auth_headers, sample_product):
        """Test updating product information"""
        product_id = "prod_test_123"
        update_data = {
            "name": "Updated Product Name",
            "description": "Updated product description with new features",
            "price": 129.99
        }
        
        with patch('api.crud.product.update_product') as mock_update:
            updated_product = {**sample_product, **update_data, "id": product_id}
            mock_update.return_value = updated_product
            
            response = test_client.put(
                f"/api/v1/products/{product_id}",
                json=update_data,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["name"] == update_data["name"]
            assert data["price"] == update_data["price"]


class TestAnalyticsEndpoints:
    """Test analytics and reporting API endpoints"""
    
    def test_get_optimization_stats(self, test_client, auth_headers):
        """Test retrieving optimization statistics"""
        mock_stats = {
            "total_products_optimized": 1500,
            "optimization_success_rate": 94.5,
            "average_seo_improvement": 35.2,
            "total_api_cost": 245.75,
            "optimizations_this_month": 320,
            "top_performing_categories": [
                {"category": "Electronics", "avg_improvement": 42.1},
                {"category": "Clothing", "avg_improvement": 38.7}
            ]
        }
        
        with patch('api.services.analytics.get_optimization_statistics') as mock_stats_service:
            mock_stats_service.return_value = mock_stats
            
            response = test_client.get(
                "/api/v1/analytics/optimization-stats",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total_products_optimized"] == 1500
            assert data["optimization_success_rate"] > 90
            assert "top_performing_categories" in data
    
    def test_get_cost_analytics(self, test_client, auth_headers):
        """Test retrieving cost analytics"""
        with patch('api.services.analytics.get_cost_analytics') as mock_cost:
            mock_cost.return_value = {
                "total_cost_usd": 245.75,
                "cost_per_optimization": 0.163,
                "monthly_costs": [
                    {"month": "2025-07", "cost": 98.50},
                    {"month": "2025-08", "cost": 147.25}
                ],
                "model_usage": {
                    "claude-3-opus": {"count": 800, "cost": 120.00},
                    "gpt-4": {"count": 700, "cost": 125.75}
                }
            }
            
            response = test_client.get(
                "/api/v1/analytics/costs",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "total_cost_usd" in data
            assert "model_usage" in data
    
    def test_get_performance_metrics(self, test_client, auth_headers):
        """Test retrieving performance metrics"""
        with patch('api.services.analytics.get_performance_metrics') as mock_perf:
            mock_perf.return_value = {
                "average_response_time": 1.25,
                "successful_requests": 9850,
                "failed_requests": 150,
                "success_rate": 98.5,
                "peak_requests_per_hour": 450,
                "database_query_time": 0.08,
                "ai_api_response_time": 0.95
            }
            
            response = test_client.get(
                "/api/v1/analytics/performance",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success_rate"] > 95
            assert data["average_response_time"] < 2.0


class TestHealthCheckEndpoints:
    """Test health check and system status endpoints"""
    
    def test_health_check_endpoint(self, test_client):
        """Test basic health check endpoint"""
        with patch('api.monitoring.health.check_system_health') as mock_health:
            mock_health.return_value = {
                "status": "healthy",
                "timestamp": "2025-08-12T14:30:00Z",
                "services": {
                    "database": {"status": "healthy", "response_time": 0.05},
                    "redis": {"status": "healthy", "response_time": 0.02},
                    "openrouter_api": {"status": "healthy", "response_time": 0.89}
                }
            }
            
            response = test_client.get("/health")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] == "healthy"
            assert "services" in data
    
    def test_detailed_health_check(self, test_client, auth_headers):
        """Test detailed health check with authentication"""
        with patch('api.monitoring.health.check_detailed_health') as mock_detailed:
            mock_detailed.return_value = {
                "status": "healthy",
                "uptime": 86400,  # 1 day in seconds
                "version": "1.0.0",
                "environment": "production",
                "services": {
                    "database": {
                        "status": "healthy",
                        "connection_pool": {"active": 5, "max": 20},
                        "query_performance": {"avg_time": 0.08}
                    },
                    "celery": {
                        "status": "healthy",
                        "active_workers": 4,
                        "queued_tasks": 12
                    }
                }
            }
            
            response = test_client.get("/health/detailed", headers=auth_headers)
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "uptime" in data
            assert "version" in data


class TestWebhookEndpoints:
    """Test webhook endpoints for WordPress integration"""
    
    def test_wordpress_webhook_product_update(self, test_client, mock_wordpress_product):
        """Test WordPress product update webhook"""
        webhook_payload = {
            "action": "product_updated",
            "product": mock_wordpress_product,
            "store_id": "store_wp_123",
            "timestamp": "2025-08-12T14:30:00Z"
        }
        
        webhook_headers = {
            "X-WP-Signature": "sha256=test_signature_hash",
            "Content-Type": "application/json",
            "User-Agent": "WordPress/6.3 GridSpoke-Connector/1.0"
        }
        
        with patch('api.services.webhook_service.verify_wordpress_signature') as mock_verify:
            with patch('api.tasks.product_tasks.sync_wordpress_product.delay') as mock_sync:
                mock_verify.return_value = True
                mock_sync.return_value = MagicMock(id="sync_task_123")
                
                response = test_client.post(
                    "/api/v1/webhooks/wordpress",
                    json=webhook_payload,
                    headers=webhook_headers
                )
                
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert data["status"] == "received"
                assert "task_id" in data
                mock_sync.assert_called_once()
    
    def test_wordpress_webhook_invalid_signature(self, test_client):
        """Test WordPress webhook with invalid signature"""
        webhook_payload = {"action": "test"}
        
        with patch('api.services.webhook_service.verify_wordpress_signature') as mock_verify:
            mock_verify.return_value = False
            
            response = test_client.post(
                "/api/v1/webhooks/wordpress",
                json=webhook_payload,
                headers={"Content-Type": "application/json"}
            )
            
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
            assert "Invalid signature" in response.json()["detail"]


# Security Testing
class TestSecurityEndpoints:
    """Test security-related functionality"""
    
    def test_rate_limiting(self, test_client):
        """Test API rate limiting"""
        # This test would require actual rate limiting implementation
        # For now, we'll test the structure
        
        login_data = {"email": "<EMAIL>", "password": "password"}
        
        # Simulate multiple rapid requests
        responses = []
        for _ in range(6):  # Assuming rate limit is 5 requests
            response = test_client.post("/api/v1/auth/login", json=login_data)
            responses.append(response.status_code)
        
        # The last request should be rate limited (if implemented)
        # assert responses[-1] == status.HTTP_429_TOO_MANY_REQUESTS
    
    def test_sql_injection_protection(self, test_client, auth_headers):
        """Test SQL injection protection"""
        malicious_input = "'; DROP TABLE products; --"
        
        response = test_client.get(
            f"/api/v1/products?search={malicious_input}",
            headers=auth_headers
        )
        
        # Should not cause a server error and should be handled gracefully
        assert response.status_code in [200, 400, 422]  # Not 500
    
    def test_xss_protection(self, test_client, auth_headers):
        """Test XSS protection in input handling"""
        xss_input = "<script>alert('xss')</script>"
        
        malicious_product = {
            "name": xss_input,
            "description": f"Product with {xss_input} in description",
            "price": 99.99
        }
        
        response = test_client.post(
            "/api/v1/optimize/product",
            json=malicious_product,
            headers=auth_headers
        )
        
        # Should either validate/sanitize input or return validation error
        assert response.status_code in [200, 400, 422]
