#!/usr/bin/env python3
"""
Test script to verify WordPress plugin functionality
"""

import requests
import json
import time
import uuid

# WordPress site URL (Local by Flywheel)
wordpress_url = 'http://localhost:10010'

# GridSpoke API endpoint
api_endpoint = 'http://localhost:8000/api/v1/'

def test_wordpress_connection():
    """
    Test basic connection to WordPress site
    """
    print("[INFO] Testing connection to WordPress site...")
    
    try:
        response = requests.get(wordpress_url, timeout=30)
        if response.status_code == 200:
            print("[OK] Successfully connected to WordPress site")
            return True
        else:
            print(f"[ERROR] Failed to connect to WordPress site. Status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Failed to connect to WordPress site: {str(e)}")
        return False

def test_wordpress_api_access():
    """
    Test access to WordPress REST API
    """
    print("[INFO] Testing access to WordPress REST API...")
    
    try:
        response = requests.get(f"{wordpress_url}/wp-json/wp/v2/posts", timeout=30)
        if response.status_code == 200:
            print("[OK] Successfully accessed WordPress REST API")
            return True
        else:
            print(f"[ERROR] Failed to access WordPress REST API. Status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Failed to access WordPress REST API: {str(e)}")
        return False

def test_gridspoke_api_connection():
    """
    Test connection to GridSpoke API
    """
    print("[INFO] Testing connection to GridSpoke API...")
    print(f"[DEBUG] API endpoint URL: {api_endpoint}health")
    
    try:
        response = requests.get(f"{api_endpoint}health", timeout=30)
        print(f"[DEBUG] Response status code: {response.status_code}")
        if response.status_code == 200:
            print("[OK] Successfully connected to GridSpoke API")
            return True
        else:
            print(f"[ERROR] Failed to connect to GridSpoke API. Status code: {response.status_code}")
            if response.content:
                try:
                    error_data = response.json()
                    print(f"  Error details: {error_data}")
                except:
                    print(f"  Error content: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Failed to connect to GridSpoke API: {str(e)}")
        return False
    except Exception as e:
        print(f"[ERROR] Unexpected error when connecting to GridSpoke API: {str(e)}")
        return False

def authenticate_with_gridspoke(email, password):
    """
    Authenticate with GridSpoke API
    """
    print("[INFO] Authenticating with GridSpoke API...")
    
    payload = {
        'email': email,
        'password': password
    }
    
    try:
        response = requests.post(f"{api_endpoint}auth/login", json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("[OK] Successfully authenticated with GridSpoke API")
            return data.get('access_token'), data.get('refresh_token')
        else:
            print(f"[ERROR] Failed to authenticate with GridSpoke API. Status code: {response.status_code}")
            if response.content:
                try:
                    error_data = response.json()
                    print(f"  Error details: {error_data}")
                except:
                    print(f"  Error content: {response.text}")
            return None, None
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Failed to authenticate with GridSpoke API: {str(e)}")
        return None, None

def create_store(access_token, store_name="Test Store", store_url="http://localhost:10010", platform="woocommerce"):
    """
    Create a store in GridSpoke API
    """
    print("[INFO] Creating store in GridSpoke API...")
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        'name': store_name,
        'store_url': store_url,
        'platform': platform,
        'api_credentials': {}
    }
    
    try:
        response = requests.post(
            f"{api_endpoint}stores/",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 201:
            data = response.json()
            store_id = data.get('id')
            print(f"[OK] Successfully created store in GridSpoke API with ID: {store_id}")
            return store_id
        elif response.status_code == 400:
            # Check if store already exists
            data = response.json()
            if 'already exists' in str(data):
                print("[INFO] Store already exists. Getting existing store...")
                # Get existing stores
                get_response = requests.get(
                    f"{api_endpoint}stores/",
                    headers=headers,
                    timeout=30
                )
                if get_response.status_code == 200:
                    stores = get_response.json()
                    for store in stores:
                        if store.get('domain') == 'localhost:10010':
                            store_id = store.get('id')
                            print(f"[OK] Using existing store with ID: {store_id}")
                            return store_id
            print(f"[ERROR] Failed to create store in GridSpoke API. Status code: {response.status_code}")
            if response.content:
                try:
                    error_data = response.json()
                    print(f"  Error details: {error_data}")
                except:
                    print(f"  Error content: {response.text}")
            return None
        else:
            print(f"[ERROR] Failed to create store in GridSpoke API. Status code: {response.status_code}")
            if response.content:
                try:
                    error_data = response.json()
                    print(f"  Error details: {error_data}")
                except:
                    print(f"  Error content: {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Failed to create store in GridSpoke API: {str(e)}")
        return None

def get_wordpress_products():
    """
    Get products from WordPress (both WooCommerce and SureCart)
    """
    print("[INFO] Getting products from WordPress...")
    
    products = []
    
    # Try to get WooCommerce products
    try:
        response = requests.get(f"{wordpress_url}/wp-json/wc/v3/products", timeout=30)
        if response.status_code == 200:
            woo_products = response.json()
            for product in woo_products:
                products.append({
                    'id': product['id'],
                    'name': product['name'],
                    'sku': product.get('sku', ''),
                    'description': product.get('description', ''),
                    'price': product.get('price'),
                    'categories': [cat['name'] for cat in product.get('categories', [])],
                    'tags': [tag['name'] for tag in product.get('tags', [])],
                    'images': [img['src'] for img in product.get('images', [])],
                    'type': 'woocommerce'
                })
            print(f"[OK] Found {len(woo_products)} WooCommerce products")
        else:
            print(f"[INFO] WooCommerce not available or no products found. Status code: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"[INFO] WooCommerce not available: {str(e)}")
    
    # Try to get SureCart products
    try:
        response = requests.get(f"{wordpress_url}/wp-json/surecart/v1/products", timeout=30)
        if response.status_code == 200:
            sure_products = response.json()
            for product in sure_products:
                products.append({
                    'id': product['id'],
                    'name': product['name'],
                    'sku': product.get('sku', ''),
                    'description': product.get('description', ''),
                    'price': product.get('price'),
                    'categories': product.get('categories', []),
                    'tags': product.get('tags', []),
                    'images': product.get('images', []),
                    'type': 'surecart'
                })
            print(f"[OK] Found {len(sure_products)} SureCart products")
        else:
            print(f"[INFO] SureCart not available or no products found. Status code: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"[INFO] SureCart not available: {str(e)}")
    
    return products

def sync_products_to_gridspoke(access_token, store_id, products):
    """
    Sync products to GridSpoke API
    """
    print("[INFO] Syncing products to GridSpoke API...")
    
    if not products:
        print("[INFO] No products to sync")
        return True
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    # Format products for GridSpoke API
    formatted_products = []
    for product in products:
        formatted_products.append({
            'store_id': store_id,
            'external_id': str(product['id']),
            'name': product['name'],
            'sku': product['sku'],
            'description': product['description'],
            'price': float(product['price']) if product['price'] else None,
            'categories': product['categories'],
            'tags': product['tags'],
            'images': product['images'],
            'platform': product['type'],
            'original_data': product
        })
    
    try:
        response = requests.post(
            f"{api_endpoint}products/bulk/sync",
            headers=headers,
            json=formatted_products,
            timeout=60
        )
        
        if response.status_code in [200, 201]:
            data = response.json()
            print(f"[OK] Successfully synced {len(products)} products to GridSpoke API")
            print(f"  Created: {data.get('created', 0)}, Updated: {data.get('updated', 0)}")
            return True
        else:
            print(f"[ERROR] Failed to sync products to GridSpoke API. Status code: {response.status_code}")
            if response.content:
                try:
                    error_data = response.json()
                    print(f"  Error details: {error_data}")
                except:
                    print(f"  Error content: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Failed to sync products to GridSpoke API: {str(e)}")
        return False

def request_product_optimization(access_token, store_id, product_ids, options=None):
    """
    Request optimization for products
    """
    print("[INFO] Requesting product optimization...")
    
    if not product_ids:
        print("[INFO] No products to optimize")
        return True
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    # Default optimization options
    if options is None:
        options = {
            'optimization_types': ['title', 'description', 'meta_description'],
            'ai_model': 'claude-3-haiku',
            'target_keywords': [],
            'custom_instructions': ''
        }
    
    # For multiple products, use bulk optimization
    if len(product_ids) > 1:
        payload = {
            'product_ids': product_ids,
            'optimization_request': options
        }
        
        endpoint = f"{api_endpoint}products/bulk/optimize"
    else:
        # For single product, use individual optimization
        payload = options
        # Get the actual product UUID from the API
        try:
            product_response = requests.get(
                f"{api_endpoint}products/?store_id={store_id}&external_id={product_ids[0]}",
                headers=headers,
                timeout=30
            )
            if product_response.status_code == 200:
                products = product_response.json()
                if products:
                    product_uuid = products[0]['id']
                    endpoint = f"{api_endpoint}products/{product_uuid}/optimize"
                else:
                    print(f"[ERROR] Product with external ID {product_ids[0]} not found")
                    return False
            else:
                print(f"[ERROR] Failed to get product with external ID {product_ids[0]}. Status code: {product_response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] Failed to get product with external ID {product_ids[0]}: {str(e)}")
            return False
    
    try:
        response = requests.post(
            endpoint,
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code in [200, 202]:
            data = response.json()
            print(f"[OK] Successfully requested optimization for {len(product_ids)} products")
            print(f"  Job ID: {data.get('job_id')}")
            return True
        else:
            print(f"[ERROR] Failed to request product optimization. Status code: {response.status_code}")
            if response.content:
                try:
                    error_data = response.json()
                    print(f"  Error details: {error_data}")
                except:
                    print(f"  Error content: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Failed to request product optimization: {str(e)}")
        return False

def test_webhook_handler():
    """
    Test webhook handler functionality
    """
    print("[INFO] Testing webhook handler...")
    
    # Create a test webhook payload
    payload = {
        'event': 'optimization.completed',
        'optimization_id': 'test-optimization-123',
        'products': [
            {
                'id': '123',
                'type': 'woocommerce',
                'optimized_content': {
                    'title': 'Optimized Product Title',
                    'description': 'This is an optimized product description.',
                    'meta_description': 'Optimized meta description for SEO.'
                }
            }
        ]
    }
    
    # Generate a simple signature (in a real implementation, this would be HMAC-SHA256)
    signature = 'test-signature-12345'
    
    headers = {
        'Content-Type': 'application/json',
        'X-GridSpoke-Signature': signature
    }
    
    try:
        response = requests.post(
            f"{wordpress_url}/wp-json/gridspoke-seo/v1/webhook",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"[INFO] Webhook handler responded with status {response.status_code}")
        if response.content:
            try:
                data = response.json()
                print(f"  Response: {data}")
            except:
                print(f"  Response content: {response.text}")
        
        if response.status_code in [200, 202]:
            print("[OK] Webhook handler responded successfully")
            return True
        else:
            print(f"[INFO] Webhook handler responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[INFO] Webhook handler not available: {str(e)}")
        return False

if __name__ == "__main__":
    print("[INFO] Starting WordPress plugin functionality test...")
    
    # Test basic connections
    if not test_wordpress_connection():
        print("[ERROR] Cannot connect to WordPress site. Exiting.")
        exit(1)
    
    if not test_wordpress_api_access():
        print("[ERROR] Cannot access WordPress REST API. Exiting.")
        exit(1)
    
    if not test_gridspoke_api_connection():
        print("[ERROR] Cannot connect to GridSpoke API. Exiting.")
        exit(1)
    
    # Authenticate with GridSpoke API
    access_token, refresh_token = authenticate_with_gridspoke(
        '<EMAIL>', 
        'test12345'
    )
    
    if not access_token:
        print("[ERROR] Failed to authenticate with GridSpoke API. Exiting.")
        exit(1)
    
    # Create or get store
    store_id = create_store(access_token)
    if not store_id:
        print("[ERROR] Failed to create or get store. Exiting.")
        exit(1)
    
    # Get products from WordPress
    products = get_wordpress_products()
    
    if not products:
        print("[INFO] No products found in WordPress. Creating test products...")
        # In a real implementation, we would create test products here
        # For now, we'll create some mock products
        products = [
            {
                'id': '123',
                'name': 'Test Product 1',
                'sku': 'TP001',
                'description': 'This is a test product for SEO optimization.',
                'price': '19.99',
                'categories': ['Test Category'],
                'tags': ['test', 'product'],
                'images': ['http://example.com/image1.jpg'],
                'type': 'woocommerce'
            },
            {
                'id': '456',
                'name': 'Test Product 2',
                'sku': 'TP002',
                'description': 'This is another test product for SEO optimization.',
                'price': '29.99',
                'categories': ['Test Category', 'Another Category'],
                'tags': ['test', 'product', 'second'],
                'images': ['http://example.com/image2.jpg', 'http://example.com/image3.jpg'],
                'type': 'woocommerce'
            }
        ]
    
    # Sync products to GridSpoke API
    if not sync_products_to_gridspoke(access_token, store_id, products):
        print("[ERROR] Failed to sync products to GridSpoke API.")
        # Continue anyway to test other functionality
    
    # Request optimization for products
    product_ids = [str(product['id']) for product in products[:2]]  # Limit to first 2 products
    if not request_product_optimization(access_token, store_id, product_ids):
        print("[ERROR] Failed to request product optimization.")
        # Continue anyway to test other functionality
    
    # Test webhook handler
    if not test_webhook_handler():
        print("[INFO] Webhook handler test completed (may not be implemented yet).")
    
    print("\n[SUCCESS] WordPress plugin functionality test completed!")
    print("The WordPress plugin should be able to connect to the GridSpoke API and perform basic operations.")