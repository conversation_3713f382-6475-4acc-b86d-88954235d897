"""
Verification script to check that all necessary modules exist and can be imported.
"""
import os
import sys

def verify_files_exist():
    """Verify that all necessary files exist."""
    base_path = os.path.dirname(os.path.abspath(__file__))
    
    # List of files that should exist
    required_files = [
        "api/workers/tasks/content_tasks.py",
        "api/workers/tasks/scheduled_tasks.py",
        "api/workers/tasks/validation_tasks.py",
        "api/workers/tasks/__init__.py",
        "api/workers/utils/progress_tracker.py",
        "api/workers/utils/redis_client.py",
        "api/workers/utils/__init__.py",
        "api/api/v1/endpoints/monitoring.py",
    ]
    
    missing_files = []
    
    for file_path in required_files:
        full_path = os.path.join(base_path, "..", file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("Missing files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("All required files exist!")
        return True

def verify_imports():
    """Verify that modules can be imported."""
    # Add the API directory to the Python path
    base_path = os.path.dirname(os.path.abspath(__file__))
    api_path = os.path.join(base_path, "..")
    sys.path.insert(0, api_path)
    
    try:
        # Try to import the modules
        import api.workers.tasks.content_tasks
        print("✓ Content tasks imported successfully")
        
        import api.workers.tasks.scheduled_tasks
        print("✓ Scheduled tasks imported successfully")
        
        import api.workers.tasks.validation_tasks
        print("✓ Validation tasks imported successfully")
        
        import api.api.v1.endpoints.monitoring
        print("✓ Monitoring endpoints imported successfully")
        
        print("\nAll imports successful!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("Verifying GridSpoke codebase fixes...")
    print("=" * 50)
    
    files_ok = verify_files_exist()
    print()
    
    if files_ok:
        imports_ok = verify_imports()
        print()
        
        if imports_ok:
            print("✓ All verifications passed! The fixes are complete.")
        else:
            print("✗ Import verification failed.")
    else:
        print("✗ File verification failed.")