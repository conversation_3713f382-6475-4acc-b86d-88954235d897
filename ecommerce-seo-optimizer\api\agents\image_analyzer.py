"""
Image Analyzer Agent - AI-powered image analysis and alt text generation
Analyzes product images and generates SEO-optimized alt text and captions.
"""

import os
import base64
import asyncio
from typing import List, Dict, Optional, Any
from urllib.parse import urlparse
from pathlib import Path

from pydantic import BaseModel, Field
from mirascope.core import openai
from mirascope.core.openai import OpenAICall

from ..core.config import settings


class ImageAnalysisResult(BaseModel):
    """Result of image analysis"""
    alt_text: str = Field(..., description="SEO-optimized alt text")
    caption: Optional[str] = Field(None, description="Descriptive caption")
    keywords: List[str] = Field(default_factory=list, description="Extracted keywords")
    objects_detected: List[str] = Field(default_factory=list, description="Objects found in image")
    colors: List[str] = Field(default_factory=list, description="Dominant colors")
    style_tags: List[str] = Field(default_factory=list, description="Style and aesthetic tags")
    seo_score: float = Field(..., description="SEO optimization score for alt text")


class BulkImageAnalysisResult(BaseModel):
    """Result of bulk image analysis"""
    product_id: str
    images: List[ImageAnalysisResult]
    overall_seo_score: float
    recommendations: List[str]


class ImageSEOOptimizer(OpenAICall):
    """AI agent for analyzing images and generating SEO-optimized alt text"""
    
    prompt_template = """
    Analyze this product image and generate SEO-optimized alt text and metadata.
    
    Product Context:
    - Product Name: {product_name}
    - Category: {product_category}
    - Brand: {brand_name}
    - Target Keywords: {target_keywords}
    
    Image Analysis Requirements:
    1. Generate descriptive, SEO-friendly alt text (125 characters max)
    2. Include primary product keywords naturally
    3. Describe key visual elements, colors, and features
    4. Focus on accessibility and user experience
    5. Identify objects, colors, and style elements
    
    Format response as JSON with:
    - alt_text: Optimized alt text
    - caption: Longer descriptive caption
    - keywords: Relevant keywords extracted
    - objects_detected: Objects visible in image
    - colors: Dominant colors
    - style_tags: Style and aesthetic descriptors
    """
    
    model = "gpt-4o"  # GPT-4 with vision capabilities
    api_key = os.getenv("OPENROUTER_API_KEY")
    base_url = "https://openrouter.ai/api/v1"
    
    product_name: str
    product_category: str = "product"
    brand_name: str = ""
    target_keywords: str = ""
    image_data: str  # Base64 encoded image


class ImageVariationGenerator(OpenAICall):
    """Generate variations of alt text for A/B testing"""
    
    prompt_template = """
    Generate {num_variations} different variations of alt text for this product image.
    
    Original Alt Text: {original_alt_text}
    Product: {product_name}
    Keywords: {keywords}
    
    Create variations that:
    1. Maintain SEO value and keyword inclusion
    2. Use different descriptive approaches
    3. Vary in tone (descriptive, benefit-focused, feature-focused)
    4. Stay under 125 characters each
    5. Remain accessible and descriptive
    
    Return as JSON array of alt text variations.
    """
    
    model = "anthropic/claude-3-opus"
    api_key = os.getenv("OPENROUTER_API_KEY") 
    base_url = "https://openrouter.ai/api/v1"
    
    original_alt_text: str
    product_name: str
    keywords: str
    num_variations: int = 3


class ImageAnalyzerAgent:
    """Main image analysis agent combining multiple AI capabilities"""
    
    def __init__(self):
        self.seo_optimizer = ImageSEOOptimizer()
        self.variation_generator = ImageVariationGenerator()
        
    async def analyze_single_image(
        self,
        image_url: str,
        product_name: str,
        product_category: str = "",
        brand_name: str = "",
        target_keywords: List[str] = None
    ) -> ImageAnalysisResult:
        """Analyze a single product image"""
        
        try:
            # Download and encode image
            image_data = await self._download_and_encode_image(image_url)
            
            # Prepare keywords string
            keywords_str = ", ".join(target_keywords or [])
            
            # Call AI agent for analysis
            result = await self.seo_optimizer.call_async(
                product_name=product_name,
                product_category=product_category,
                brand_name=brand_name,
                target_keywords=keywords_str,
                image_data=image_data
            )
            
            # Parse response and create structured result
            analysis_data = self._parse_ai_response(result)
            
            # Calculate SEO score
            seo_score = self._calculate_alt_text_seo_score(
                analysis_data["alt_text"],
                target_keywords or []
            )
            
            return ImageAnalysisResult(
                alt_text=analysis_data["alt_text"],
                caption=analysis_data.get("caption"),
                keywords=analysis_data.get("keywords", []),
                objects_detected=analysis_data.get("objects_detected", []),
                colors=analysis_data.get("colors", []),
                style_tags=analysis_data.get("style_tags", []),
                seo_score=seo_score
            )
            
        except Exception as e:
            # Fallback to basic alt text generation
            return await self._generate_fallback_alt_text(
                product_name, product_category, brand_name
            )
            
    async def analyze_product_images(
        self,
        product_id: str,
        image_urls: List[str],
        product_name: str,
        product_category: str = "",
        brand_name: str = "",
        target_keywords: List[str] = None
    ) -> BulkImageAnalysisResult:
        """Analyze all images for a product"""
        
        if not image_urls:
            return BulkImageAnalysisResult(
                product_id=product_id,
                images=[],
                overall_seo_score=0.0,
                recommendations=["Add product images for better SEO"]
            )
            
        # Analyze images in parallel (limit concurrent requests)
        semaphore = asyncio.Semaphore(3)  # Max 3 concurrent image analyses
        
        async def analyze_with_semaphore(url):
            async with semaphore:
                return await self.analyze_single_image(
                    url, product_name, product_category, brand_name, target_keywords
                )
                
        image_results = await asyncio.gather(
            *[analyze_with_semaphore(url) for url in image_urls],
            return_exceptions=True
        )
        
        # Filter out exceptions and create valid results
        valid_results = []
        for i, result in enumerate(image_results):
            if isinstance(result, Exception):
                # Create fallback result for failed analysis
                fallback = await self._generate_fallback_alt_text(
                    product_name, product_category, brand_name, i + 1
                )
                valid_results.append(fallback)
            else:
                valid_results.append(result)
                
        # Calculate overall SEO score
        overall_score = sum(result.seo_score for result in valid_results) / len(valid_results)
        
        # Generate recommendations
        recommendations = self._generate_image_seo_recommendations(valid_results)
        
        return BulkImageAnalysisResult(
            product_id=product_id,
            images=valid_results,
            overall_seo_score=round(overall_score, 1),
            recommendations=recommendations
        )
        
    async def generate_alt_text_variations(
        self,
        original_alt_text: str,
        product_name: str,
        keywords: List[str],
        num_variations: int = 3
    ) -> List[str]:
        """Generate variations of alt text for A/B testing"""
        
        try:
            keywords_str = ", ".join(keywords)
            
            result = await self.variation_generator.call_async(
                original_alt_text=original_alt_text,
                product_name=product_name,
                keywords=keywords_str,
                num_variations=num_variations
            )
            
            # Parse response
            variations = self._parse_variations_response(result)
            return variations
            
        except Exception as e:
            # Generate basic variations
            return self._generate_basic_variations(original_alt_text, num_variations)
            
    async def optimize_existing_alt_text(
        self,
        current_alt_text: str,
        product_name: str,
        target_keywords: List[str]
    ) -> Dict[str, Any]:
        """Optimize existing alt text for better SEO"""
        
        # Analyze current alt text
        current_score = self._calculate_alt_text_seo_score(current_alt_text, target_keywords)
        
        # Generate improved version
        improved_alt_text = await self._generate_improved_alt_text(
            current_alt_text, product_name, target_keywords
        )
        
        improved_score = self._calculate_alt_text_seo_score(improved_alt_text, target_keywords)
        
        return {
            "current_alt_text": current_alt_text,
            "current_score": current_score,
            "improved_alt_text": improved_alt_text,
            "improved_score": improved_score,
            "improvement": improved_score - current_score,
            "recommendations": self._generate_alt_text_recommendations(
                current_alt_text, target_keywords
            )
        }
        
    async def _download_and_encode_image(self, image_url: str) -> str:
        """Download image and encode as base64"""
        import aiohttp
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(image_url) as response:
                    if response.status == 200:
                        image_data = await response.read()
                        return base64.b64encode(image_data).decode('utf-8')
                    else:
                        raise Exception(f"Failed to download image: {response.status}")
        except Exception as e:
            raise Exception(f"Error downloading image: {str(e)}")
            
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response into structured data"""
        import json
        
        try:
            # Try to parse as JSON
            return json.loads(response)
        except json.JSONDecodeError:
            # Fallback parsing
            return {
                "alt_text": response[:125],  # Truncate to max length
                "caption": response,
                "keywords": [],
                "objects_detected": [],
                "colors": [],
                "style_tags": []
            }
            
    def _parse_variations_response(self, response: str) -> List[str]:
        """Parse variations response"""
        import json
        
        try:
            variations = json.loads(response)
            if isinstance(variations, list):
                return [var[:125] for var in variations]  # Ensure max length
            else:
                return [response[:125]]
        except json.JSONDecodeError:
            # Split by lines as fallback
            lines = response.split('\n')
            return [line.strip()[:125] for line in lines if line.strip()][:3]
            
    def _calculate_alt_text_seo_score(
        self,
        alt_text: str,
        target_keywords: List[str]
    ) -> float:
        """Calculate SEO score for alt text"""
        score = 100.0
        
        # Check length
        length = len(alt_text)
        if length == 0:
            return 0.0
        elif length < 10:
            score -= 30
        elif length > 125:
            score -= 20
            
        # Check keyword inclusion
        if target_keywords:
            keywords_found = 0
            for keyword in target_keywords:
                if keyword.lower() in alt_text.lower():
                    keywords_found += 1
                    
            if keywords_found == 0:
                score -= 40
            elif keywords_found < len(target_keywords) * 0.5:
                score -= 20
                
        # Check for descriptive words
        descriptive_words = [
            'color', 'size', 'material', 'style', 'design', 'feature',
            'texture', 'pattern', 'shape', 'quality'
        ]
        descriptive_count = sum(1 for word in descriptive_words if word in alt_text.lower())
        if descriptive_count == 0:
            score -= 15
            
        # Check for keyword stuffing
        words = alt_text.lower().split()
        if len(words) > 0:
            word_frequency = {}
            for word in words:
                word_frequency[word] = word_frequency.get(word, 0) + 1
            max_frequency = max(word_frequency.values())
            if max_frequency > 2:
                score -= 25
                
        return max(0.0, min(100.0, score))
        
    async def _generate_fallback_alt_text(
        self,
        product_name: str,
        category: str,
        brand: str,
        image_index: int = 1
    ) -> ImageAnalysisResult:
        """Generate basic alt text when AI analysis fails"""
        
        # Create basic alt text
        alt_parts = []
        
        if brand:
            alt_parts.append(brand)
            
        alt_parts.append(product_name)
        
        if category:
            alt_parts.append(category)
            
        if image_index > 1:
            alt_parts.append(f"image {image_index}")
            
        alt_text = " ".join(alt_parts)[:125]
        
        return ImageAnalysisResult(
            alt_text=alt_text,
            caption=f"Product image of {alt_text}",
            keywords=[],
            objects_detected=[],
            colors=[],
            style_tags=[],
            seo_score=50.0  # Basic score for fallback
        )
        
    def _generate_image_seo_recommendations(
        self,
        image_results: List[ImageAnalysisResult]
    ) -> List[str]:
        """Generate SEO recommendations based on image analysis"""
        recommendations = []
        
        if not image_results:
            return ["Add product images to improve SEO"]
            
        # Check overall quality
        avg_score = sum(result.seo_score for result in image_results) / len(image_results)
        
        if avg_score < 60:
            recommendations.append("Improve alt text quality for better SEO performance")
            
        # Check for missing alt text
        missing_alt = sum(1 for result in image_results if not result.alt_text)
        if missing_alt > 0:
            recommendations.append(f"Add alt text to {missing_alt} images")
            
        # Check image count
        if len(image_results) < 3:
            recommendations.append("Add more product images to showcase features")
        elif len(image_results) > 10:
            recommendations.append("Consider reducing number of images for better page load speed")
            
        # Check keyword usage
        keyword_usage = sum(1 for result in image_results if result.keywords)
        if keyword_usage < len(image_results) * 0.5:
            recommendations.append("Include more relevant keywords in image alt text")
            
        return recommendations
        
    def _generate_basic_variations(self, original_alt_text: str, num_variations: int) -> List[str]:
        """Generate basic variations when AI fails"""
        variations = []
        base_words = original_alt_text.split()
        
        if len(base_words) > 3:
            # Create variations by rearranging words
            variations.append(" ".join(base_words[::-1]))  # Reverse order
            variations.append(" ".join([base_words[0]] + base_words[2:] + [base_words[1]]))  # Move second word to end
            
        # Add descriptive variations
        variations.append(f"High-quality {original_alt_text}")
        variations.append(f"{original_alt_text} - detailed view")
        
        return variations[:num_variations]
        
    async def _generate_improved_alt_text(
        self,
        current_alt_text: str,
        product_name: str,
        target_keywords: List[str]
    ) -> str:
        """Generate improved version of existing alt text"""
        
        # Simple improvement logic
        improved_parts = []
        
        # Include primary keyword if missing
        if target_keywords and target_keywords[0].lower() not in current_alt_text.lower():
            improved_parts.append(target_keywords[0])
            
        # Add current alt text
        improved_parts.append(current_alt_text)
        
        # Ensure product name is included
        if product_name.lower() not in current_alt_text.lower():
            improved_parts.insert(0, product_name)
            
        improved_alt_text = " ".join(improved_parts)[:125]
        return improved_alt_text
        
    def _generate_alt_text_recommendations(
        self,
        alt_text: str,
        target_keywords: List[str]
    ) -> List[str]:
        """Generate recommendations for improving alt text"""
        recommendations = []
        
        # Check length
        if len(alt_text) < 10:
            recommendations.append("Make alt text more descriptive (10+ characters)")
        elif len(alt_text) > 125:
            recommendations.append("Shorten alt text to under 125 characters")
            
        # Check keywords
        if target_keywords:
            missing_keywords = [kw for kw in target_keywords if kw.lower() not in alt_text.lower()]
            if missing_keywords:
                recommendations.append(f"Include keywords: {', '.join(missing_keywords[:2])}")
                
        # General recommendations
        recommendations.append("Include specific visual details (color, material, style)")
        recommendations.append("Focus on product benefits and features")
        recommendations.append("Ensure accessibility for screen readers")
        
        return recommendations
