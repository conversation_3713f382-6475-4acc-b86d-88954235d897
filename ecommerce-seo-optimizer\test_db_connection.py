#!/usr/bin/env python3
"""Test database connection from API container"""
import asyncio
import asyncpg
import os

async def test_db():
    try:
        # Use the same DATABASE_URL format as the API
        db_url = os.environ.get('DATABASE_URL', 'postgresql+asyncpg://gridspoke_user:test_gridspoke_2024@db:5432/gridspoke_db')
        # Convert to asyncpg format
        db_url = db_url.replace('postgresql+asyncpg://', 'postgresql://')
        
        print(f"Connecting to: {db_url}")
        conn = await asyncpg.connect(db_url)
        
        # Test query
        result = await conn.fetchval("SELECT 1")
        print(f"Database connection successful! Result: {result}")
        
        await conn.close()
        return True
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_db())
