"""
Product schemas for request/response validation.
"""
import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, HttpUrl, ConfigDict
from enum import Enum


class ProductStatus(str, Enum):
    """Product status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"
    ARCHIVED = "archived"


class ProductBase(BaseModel):
    """Base product schema with common fields."""
    name: str = Field(..., min_length=1, max_length=255)
    sku: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    price: Optional[float] = Field(None, ge=0)
    status: ProductStatus = ProductStatus.ACTIVE
    categories: Optional[List[str]] = Field(default_factory=list)
    tags: Optional[List[str]] = Field(default_factory=list)
    images: Optional[List[HttpUrl]] = Field(default_factory=list)


class ProductCreate(ProductBase):
    """Schema for product creation."""
    store_id: uuid.UUID
    external_id: str = Field(..., max_length=255)
    original_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


class ProductUpdate(BaseModel):
    """Schema for product updates."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    sku: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    price: Optional[float] = Field(None, ge=0)
    status: Optional[ProductStatus] = None
    categories: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    images: Optional[List[HttpUrl]] = None
    original_data: Optional[Dict[str, Any]] = None


class ProductInDB(ProductBase):
    """Schema for product in database."""
    id: uuid.UUID
    store_id: uuid.UUID
    external_id: str
    created_at: datetime
    updated_at: datetime
    last_optimized_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class Product(ProductInDB):
    """Schema for product response."""
    pass


class ProductWithSEO(Product):
    """Product schema with SEO metrics."""
    seo_score: Optional[float] = None
    optimization_count: int = 0
    generated_content_count: int = 0
    has_vector_embedding: bool = False

    model_config = ConfigDict(from_attributes=True)


class ProductOptimizationRequest(BaseModel):
    """Schema for product optimization request."""
    optimization_types: List[str] = Field(
        default=["title", "description", "meta_title", "meta_description"],
        description="Types of content to optimize"
    )
    target_keywords: Optional[List[str]] = Field(None, max_length=10)
    ai_model: Optional[str] = Field("claude-3-sonnet", description="AI model to use")
    custom_instructions: Optional[str] = Field(None, max_length=1000)


class ProductOptimizationResponse(BaseModel):
    """Schema for product optimization response."""
    job_id: uuid.UUID
    message: str
    estimated_completion: Optional[datetime] = None


class ProductSearchRequest(BaseModel):
    """Schema for product search request."""
    query: str = Field(..., min_length=1, max_length=255)
    filters: Optional[Dict[str, Any]] = Field(default_factory=dict)
    sort_by: Optional[str] = Field("relevance", description="Sort field")
    sort_order: Optional[str] = Field("desc", pattern="^(asc|desc)$")


class ProductBulkAction(BaseModel):
    """Schema for bulk product actions."""
    product_ids: List[uuid.UUID] = Field(..., min_length=1, max_length=1000)
    action: str = Field(..., description="Action to perform")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict)
