"""
Generated content model for storing AI-generated SEO content.
"""
import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from enum import Enum
from sqlalchemy import String, Boolean, ForeignKey, Index, DateTime, Text, Float, Integer, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from models.base import BaseModel
import structlog

logger = structlog.get_logger(__name__)


class ContentType(str, Enum):
    """Types of generated content."""
    TITLE = "title"
    DESCRIPTION = "description"
    META_TITLE = "meta_title"
    META_DESCRIPTION = "meta_description"
    KEYWORDS = "keywords"
    ALT_TEXT = "alt_text"
    PRODUCT_FEATURES = "product_features"
    CATEGORY_DESCRIPTION = "category_description"
    FAQ = "faq"
    BLOG_POST = "blog_post"
    SOCIAL_MEDIA = "social_media"
    AD_COPY = "ad_copy"


class ContentStatus(str, Enum):
    """Status of generated content."""
    GENERATED = "generated"
    APPROVED = "approved"
    REJECTED = "rejected"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    NEEDS_REVIEW = "needs_review"


class AIModel(str, Enum):
    """AI models used for content generation."""
    GPT_4 = "gpt-4"
    GPT_4_TURBO = "gpt-4-turbo"
    CLAUDE_3_OPUS = "claude-3-opus"
    CLAUDE_3_SONNET = "claude-3-sonnet"
    CLAUDE_3_HAIKU = "claude-3-haiku"
    GEMINI_PRO = "gemini-pro"
    MISTRAL_LARGE = "mistral-large"


class GeneratedContent(BaseModel):
    """Model for storing AI-generated SEO content."""
    
    __tablename__ = "generated_content"
    
    # Product relationship
    product_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("products.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Job relationship (optional for manual generations)
    job_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("optimization_jobs.id", ondelete="SET NULL"),
        nullable=True,
        index=True
    )
    
    # Content identification
    content_type: Mapped[ContentType] = mapped_column(
        SQLEnum(ContentType),
        nullable=False,
        index=True
    )
    
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="Human-readable title for this content"
    )
    
    # Generated content
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="The actual generated content"
    )
    
    original_content: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Original content before optimization"
    )
    
    # AI generation metadata
    ai_model: Mapped[AIModel] = mapped_column(
        SQLEnum(AIModel),
        nullable=False,
        comment="AI model used for generation"
    )
    
    model_version: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="Specific model version used"
    )
    
    prompt_template: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="Template used for generation"
    )
    
    generation_parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB,
        nullable=True,
        default=dict,
        comment="AI model parameters used"
    )
    
    # Quality metrics
    confidence_score: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        comment="AI confidence in the generated content (0-1)"
    )
    
    seo_score: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        comment="SEO quality score (0-100)"
    )
    
    readability_score: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        comment="Content readability score"
    )
    
    keyword_density: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        comment="Target keyword density percentage"
    )
    
    # Content analysis
    word_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="Word count of generated content"
    )
    
    character_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="Character count including spaces"
    )
    
    keywords_used: Mapped[Optional[List[str]]] = mapped_column(
        JSONB,
        nullable=True,
        default=list,
        comment="Keywords incorporated in content"
    )
    
    # Status and approval
    status: Mapped[ContentStatus] = mapped_column(
        SQLEnum(ContentStatus),
        default=ContentStatus.GENERATED,
        nullable=False,
        index=True
    )
    
    is_published: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="Whether content is live on store"
    )
    
    published_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="When content was published"
    )
    
    # Review and feedback
    reviewer_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Human reviewer feedback"
    )
    
    approval_score: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="Human approval rating (1-10)"
    )
    
    feedback_tags: Mapped[Optional[List[str]]] = mapped_column(
        JSONB,
        nullable=True,
        default=list,
        comment="Categorized feedback tags"
    )
    
    # Performance tracking
    click_through_rate: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        comment="CTR for meta titles/descriptions"
    )
    
    conversion_rate: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        comment="Conversion rate attribution"
    )
    
    engagement_score: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        comment="Overall engagement metrics"
    )
    
    # A/B testing
    variant_id: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        index=True,
        comment="A/B test variant identifier"
    )
    
    is_control: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="Whether this is the control variant"
    )
    
    # Cost tracking
    tokens_used: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="AI tokens consumed for generation"
    )
    
    generation_cost: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        comment="Cost in USD for this generation"
    )
    
    # Language and localization
    language_code: Mapped[str] = mapped_column(
        String(10),
        default="en",
        nullable=False,
        index=True,
        comment="Content language (ISO 639-1)"
    )
    
    target_market: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="Target market/region"
    )
    
    # Relationships
    product: Mapped["Product"] = relationship(
        "Product",
        back_populates="generated_content"
    )
    
    job: Mapped[Optional["OptimizationJob"]] = relationship(
        "OptimizationJob",
        back_populates="generated_content"
    )
    
    # Database indexes
    __table_args__ = (
        Index("ix_content_product_type", "product_id", "content_type"),
        Index("ix_content_status_type", "status", "content_type"),
        Index("ix_content_published", "is_published", "published_at"),
        Index("ix_content_ab_test", "variant_id", "is_control"),
        Index("ix_content_performance", "click_through_rate", "conversion_rate"),
        Index("ix_content_language", "language_code", "target_market"),
        Index("ix_content_ai_model", "ai_model", "model_version"),
    )
    
    def __repr__(self) -> str:
        return f"<GeneratedContent(id={self.id}, type={self.content_type}, status={self.status})>"
    
    @property
    def is_approved(self) -> bool:
        """Check if content has been approved."""
        return self.status == ContentStatus.APPROVED
    
    @property
    def needs_review(self) -> bool:
        """Check if content needs human review."""
        return self.status == ContentStatus.NEEDS_REVIEW
    
    @property
    def performance_score(self) -> Optional[float]:
        """Calculate overall performance score."""
        scores = [
            self.click_through_rate,
            self.conversion_rate,
            self.engagement_score
        ]
        valid_scores = [s for s in scores if s is not None]
        
        if not valid_scores:
            return None
        
        return sum(valid_scores) / len(valid_scores)
    
    @property
    def quality_score(self) -> Optional[float]:
        """Calculate overall quality score."""
        scores = []
        
        if self.confidence_score is not None:
            scores.append(self.confidence_score * 100)
        
        if self.seo_score is not None:
            scores.append(self.seo_score)
        
        if self.readability_score is not None:
            scores.append(self.readability_score)
        
        if self.approval_score is not None:
            scores.append(self.approval_score * 10)  # Convert 1-10 to 10-100
        
        if not scores:
            return None
        
        return sum(scores) / len(scores)
    
    def approve(self, reviewer_notes: Optional[str] = None, approval_score: Optional[int] = None) -> None:
        """Approve the generated content."""
        self.status = ContentStatus.APPROVED
        if reviewer_notes:
            self.reviewer_notes = reviewer_notes
        if approval_score:
            self.approval_score = approval_score
        
        logger.info(
            "Content approved",
            content_id=str(self.id),
            type=self.content_type,
            approval_score=approval_score
        )
    
    def reject(self, reviewer_notes: str, feedback_tags: Optional[List[str]] = None) -> None:
        """Reject the generated content."""
        self.status = ContentStatus.REJECTED
        self.reviewer_notes = reviewer_notes
        if feedback_tags:
            self.feedback_tags = feedback_tags
        
        logger.info(
            "Content rejected",
            content_id=str(self.id),
            type=self.content_type,
            reason=reviewer_notes
        )
    
    def publish(self) -> None:
        """Mark content as published."""
        if self.status != ContentStatus.APPROVED:
            raise ValueError("Content must be approved before publishing")
        
        self.status = ContentStatus.PUBLISHED
        self.is_published = True
        self.published_at = datetime.now(timezone.utc)
        
        logger.info(
            "Content published",
            content_id=str(self.id),
            type=self.content_type,
            product_id=str(self.product_id)
        )
    
    def calculate_seo_score(self) -> float:
        """Calculate SEO score based on content analysis."""
        score = 0.0
        
        # Length scoring
        if self.content_type == ContentType.TITLE:
            if 30 <= self.character_count <= 60:
                score += 25
            elif 20 <= self.character_count <= 70:
                score += 15
        elif self.content_type == ContentType.META_DESCRIPTION:
            if 120 <= self.character_count <= 160:
                score += 25
            elif 100 <= self.character_count <= 180:
                score += 15
        
        # Keyword density scoring
        if self.keyword_density:
            if 1.0 <= self.keyword_density <= 3.0:
                score += 25
            elif 0.5 <= self.keyword_density <= 5.0:
                score += 15
        
        # Readability scoring
        if self.readability_score:
            score += min(25, self.readability_score * 0.25)
        
        # AI confidence scoring
        if self.confidence_score:
            score += self.confidence_score * 25
        
        self.seo_score = min(100.0, score)
        return self.seo_score
    
    def update_analytics(self, ctr: Optional[float] = None, conversion_rate: Optional[float] = None, 
                        engagement: Optional[float] = None) -> None:
        """Update performance analytics."""
        if ctr is not None:
            self.click_through_rate = ctr
        if conversion_rate is not None:
            self.conversion_rate = conversion_rate
        if engagement is not None:
            self.engagement_score = engagement
        
        logger.debug(
            "Content analytics updated",
            content_id=str(self.id),
            ctr=ctr,
            conversion_rate=conversion_rate,
            engagement=engagement
        )
