"""
Database configuration and session management for GridSpoke API.
"""
import uuid
from typing import AsyncGenerator, Optional
from sqlalchemy import MetaD<PERSON>, event, text
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    AsyncEngine,
    create_async_engine,
    async_sessionmaker
)
from sqlalchemy.orm import declarative_base, DeclarativeBase, Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.pool import NullPool
from datetime import datetime, timezone
import structlog

from core.config import settings

logger = structlog.get_logger(__name__)

# Backward-compat aliased names expected by older modules
# These are defined at the bottom to avoid forward-reference issues

# Naming convention for constraints
convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

metadata = MetaData(naming_convention=convention)


class Base(DeclarativeBase):
    """Base class for all database models."""
    metadata = metadata
    
    # Common fields for all models
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True
    )
    created_at: Mapped[datetime] = mapped_column(
        default=lambda: datetime.now(timezone.utc),
        index=True
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        default=None,
        onupdate=lambda: datetime.now(timezone.utc)
    )


# Async Engine Configuration
def create_database_engine() -> AsyncEngine:
    """Create async database engine with optimal configuration."""
    engine_kwargs = {
        "echo": settings.DATABASE_ECHO,
        "pool_size": settings.DATABASE_POOL_SIZE,
        "max_overflow": settings.DATABASE_MAX_OVERFLOW,
        "pool_pre_ping": True,
        "pool_recycle": 3600,  # Recycle connections every hour
    }
    
    # Use NullPool for testing to avoid connection issues
    if settings.DATABASE_URL.endswith("_test"):
        engine_kwargs["poolclass"] = NullPool
    
    engine = create_async_engine(settings.DATABASE_URL, **engine_kwargs)
    
    # Add event listeners for connection management
    @event.listens_for(engine.sync_engine, "connect")
    def set_postgres_search_path(dbapi_connection, connection_record):
        """Set search path and enable required extensions."""
        try:
            # Enable required PostgreSQL extensions
            dbapi_connection.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")
            dbapi_connection.execute("CREATE EXTENSION IF NOT EXISTS vector")
            dbapi_connection.execute("SET search_path TO public")
        except Exception as e:
            logger.warning("Failed to set up database extensions", error=str(e))
    
    return engine


# Global engine instance
engine: AsyncEngine = create_database_engine()

# Session factory
AsyncSessionLocal = async_sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=True,
    autocommit=False
)


async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.
    
    Yields:
        AsyncSession: Database session instance.
    """
    async with AsyncSessionLocal() as session:
        try:
            logger.debug("Database session created")
            yield session
        except Exception as e:
            logger.error("Database session error", error=str(e))
            await session.rollback()
            raise
        finally:
            await session.close()
            logger.debug("Database session closed")


async def create_tables() -> None:
    """Create all database tables."""
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created successfully")
    except Exception as e:
        # Tables might already exist, which is fine
        error_msg = str(e).lower()
        if "already exists" in error_msg or "duplicate" in error_msg:
            logger.info("Database tables already exist, continuing...")
        else:
            logger.error("Failed to create database tables", error=str(e))
            raise
        logger.error("Failed to create database tables", error=str(e))
        raise


async def drop_tables() -> None:
    """Drop all database tables (for testing)."""
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error("Failed to drop database tables", error=str(e))
        raise


async def check_database_connection() -> bool:
    """
    Check if database connection is working.
    
    Returns:
        bool: True if connection is successful, False otherwise.
    """
    try:
        async with AsyncSessionLocal() as session:
            await session.execute(text("SELECT 1"))
        logger.info("Database connection check successful")
        return True
    except Exception as e:
        logger.error("Database connection check failed", error=str(e))
        return False


async def init_database() -> None:
    """Initialize database with required extensions and tables."""
    try:
        logger.info("Initializing database...")

        # Skip table creation since they already exist
        logger.info("Skipping table creation - tables already exist")

        logger.info("Database initialization completed successfully")
    except Exception as e:
        logger.error("Database initialization failed", error=str(e))
        raise


async def close_database() -> None:
    """Close database connections."""
    try:
        await engine.dispose()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error("Error closing database connections", error=str(e))


# Backward-compatibility adapter functions expected by older imports
# These provide the old function names used throughout the codebase
async def init_db() -> None:  # pragma: no cover - simple alias
    await init_database()


async def close_db() -> None:  # pragma: no cover - simple alias
    await close_database()


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:  # pragma: no cover - simple alias
    async for s in get_session():
        yield s


# Database health check
async def health_check() -> dict:
    """
    Database health check for monitoring.

    Returns:
        dict: Health status information.
    """
    try:
        start_time = datetime.now(timezone.utc)
        is_healthy = await check_database_connection()
        end_time = datetime.now(timezone.utc)
        response_time = (end_time - start_time).total_seconds()

        return {
            "database": {
                "status": "healthy" if is_healthy else "unhealthy",
                "response_time": response_time,
                "timestamp": end_time.isoformat()
            }
        }
    except Exception as e:
        return {
            "database": {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }
