"""
Base model classes and common functionality.
"""
import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from sqlalchemy import String, Boolean, DateTime, Text, event
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID, JSONB
from core.database import Base
import structlog

logger = structlog.get_logger(__name__)


class TimestampMixin:
    """Mixin for timestamp fields."""
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=False),
        default=lambda: datetime.now(timezone.utc).replace(tzinfo=None),
        nullable=False,
        index=True
    )
    
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=False),
        default=None,
        onupdate=lambda: datetime.now(timezone.utc).replace(tzinfo=None),
        nullable=True
    )


class SoftDeleteMixin:
    """Mixin for soft delete functionality."""
    
    is_deleted: Mapped[bool] = mapped_column(
        <PERSON>olean,
        default=False,
        nullable=False,
        index=True
    )
    
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        default=None,
        nullable=True
    )
    
    def soft_delete(self) -> None:
        """Mark record as deleted."""
        self.is_deleted = True
        self.deleted_at = datetime.now(timezone.utc)
    
    def restore(self) -> None:
        """Restore soft-deleted record."""
        self.is_deleted = False
        self.deleted_at = None


class AuditMixin:
    """Mixin for audit trail functionality."""
    
    created_by: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        nullable=True,
        index=True
    )
    
    updated_by: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        nullable=True
    )


class BaseModel(Base, TimestampMixin):
    """
    Base model class with common functionality.
    
    Includes:
    - UUID primary key
    - Timestamp fields
    - Common methods
    """
    __abstract__ = True
    
    def to_dict(self, exclude: Optional[set] = None) -> Dict[str, Any]:
        """
        Convert model instance to dictionary.
        
        Args:
            exclude: Fields to exclude from dict
            
        Returns:
            dict: Model data as dictionary
        """
        exclude = exclude or set()
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude:
                value = getattr(self, column.name)
                
                # Handle special types
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                elif isinstance(value, uuid.UUID):
                    result[column.name] = str(value)
                else:
                    result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude: Optional[set] = None) -> None:
        """
        Update model instance from dictionary.
        
        Args:
            data: Data to update
            exclude: Fields to exclude from update
        """
        exclude = exclude or {"id", "created_at"}
        
        for key, value in data.items():
            if key not in exclude and hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """String representation of model."""
        return f"<{self.__class__.__name__}(id={self.id})>"


# Event listeners for automatic timestamping
@event.listens_for(BaseModel, "before_update", propagate=True)
def update_timestamp(mapper, connection, target):
    """Update timestamp before record update."""
    target.updated_at = datetime.now(timezone.utc).replace(tzinfo=None)


@event.listens_for(BaseModel, "before_insert", propagate=True)
def set_creation_timestamp(mapper, connection, target):
    """Set creation timestamp before record insert."""
    if target.created_at is None:
        target.created_at = datetime.now(timezone.utc).replace(tzinfo=None)
    # Ensure updated_at is initialized so response schemas expecting a datetime pass validation
    if getattr(target, "updated_at", None) is None:
        target.updated_at = target.created_at
