# GridSpoke Frontend JavaScript Testing
"""
JavaScript testing for GridSpoke dashboard frontend using <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>.
Tests user interactions, real-time updates, and UI components.
"""

# Jest Configuration for GridSpoke
jest_config = {
    "testEnvironment": "jsdom",
    "setupFilesAfterEnv": ["<rootDir>/tests/frontend/setup.js"],
    "moduleNameMapping": {
        "\\.(css|less|scss|sass)$": "identity-obj-proxy",
        "\\.(gif|ttf|eot|svg|png)$": "<rootDir>/tests/frontend/__mocks__/fileMock.js"
    },
    "testMatch": [
        "<rootDir>/tests/frontend/**/*.test.js",
        "<rootDir>/frontend/static/js/**/*.test.js"
    ],
    "collectCoverageFrom": [
        "frontend/static/js/**/*.js",
        "!frontend/static/js/vendor/**",
        "!frontend/static/js/**/*.min.js"
    ],
    "coverageDirectory": "coverage/frontend",
    "coverageReporters": ["text", "lcov", "html"]
}

# Frontend Test Setup
setup_js = """
// GridSpoke Frontend Test Setup
import 'jest-dom/extend-expect';

// Mock WebSocket for testing
global.WebSocket = class MockWebSocket {
    constructor(url) {
        this.url = url;
        this.readyState = 1; // OPEN
        this.onopen = null;
        this.onmessage = null;
        this.onclose = null;
        this.onerror = null;
    }
    
    send(data) {
        // Mock send functionality
        console.log('WebSocket send:', data);
    }
    
    close() {
        this.readyState = 3; // CLOSED
        if (this.onclose) {
            this.onclose();
        }
    }
    
    // Simulate receiving message
    simulateMessage(data) {
        if (this.onmessage) {
            this.onmessage({ data: JSON.stringify(data) });
        }
    }
};

// Mock fetch for API calls
global.fetch = jest.fn(() =>
    Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        text: () => Promise.resolve('')
    })
);

// Mock localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock DOM methods
document.createRange = () => ({
    setStart: () => {},
    setEnd: () => {},
    commonAncestorContainer: {
        nodeName: "BODY",
        ownerDocument: document,
    },
});

// Global test utilities
global.testUtils = {
    // Helper to create DOM elements for testing
    createElement: (tag, attributes = {}, textContent = '') => {
        const element = document.createElement(tag);
        Object.entries(attributes).forEach(([key, value]) => {
            element.setAttribute(key, value);
        });
        if (textContent) {
            element.textContent = textContent;
        }
        return element;
    },
    
    // Helper to simulate user events
    simulateEvent: (element, eventType, eventInit = {}) => {
        const event = new Event(eventType, eventInit);
        element.dispatchEvent(event);
        return event;
    },
    
    // Helper to wait for async operations
    waitFor: (callback, timeout = 1000) => {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const check = () => {
                try {
                    const result = callback();
                    if (result) {
                        resolve(result);
                    } else if (Date.now() - startTime >= timeout) {
                        reject(new Error('Timeout waiting for condition'));
                    } else {
                        setTimeout(check, 10);
                    }
                } catch (error) {
                    if (Date.now() - startTime >= timeout) {
                        reject(error);
                    } else {
                        setTimeout(check, 10);
                    }
                }
            };
            check();
        });
    }
};
"""

# Dashboard Component Tests
dashboard_test_js = """
// GridSpoke Dashboard Component Tests
import { Dashboard } from '../../../frontend/static/js/components/dashboard.js';
import { ApiClient } from '../../../frontend/static/js/services/api-client.js';

describe('Dashboard Component', () => {
    let dashboard;
    let mockContainer;
    
    beforeEach(() => {
        // Create mock container
        mockContainer = testUtils.createElement('div', { id: 'dashboard-container' });
        document.body.appendChild(mockContainer);
        
        // Reset fetch mock
        fetch.mockClear();
        
        // Create dashboard instance
        dashboard = new Dashboard(mockContainer);
    });
    
    afterEach(() => {
        // Cleanup
        document.body.removeChild(mockContainer);
        if (dashboard.websocket) {
            dashboard.websocket.close();
        }
    });
    
    test('should initialize dashboard with default state', () => {
        expect(dashboard.container).toBe(mockContainer);
        expect(dashboard.isLoading).toBe(false);
        expect(dashboard.currentView).toBe('overview');
    });
    
    test('should render overview statistics', async () => {
        const mockStats = {
            total_products: 150,
            optimized_products: 120,
            pending_jobs: 3,
            avg_seo_score: 87.5,
            total_cost: 45.67
        };
        
        fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve(mockStats)
        });
        
        await dashboard.loadOverviewStats();
        
        expect(fetch).toHaveBeenCalledWith('/api/v1/analytics/overview', {
            method: 'GET',
            headers: {
                'Authorization': expect.stringContaining('Bearer'),
                'Content-Type': 'application/json'
            }
        });
        
        // Check that stats are rendered
        const statsElements = mockContainer.querySelectorAll('.stat-value');
        expect(statsElements.length).toBeGreaterThan(0);
        
        // Check specific values
        expect(mockContainer.textContent).toContain('150');
        expect(mockContainer.textContent).toContain('120');
        expect(mockContainer.textContent).toContain('87.5');
    });
    
    test('should handle API errors gracefully', async () => {
        fetch.mockRejectedValueOnce(new Error('API Error'));
        
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
        
        await dashboard.loadOverviewStats();
        
        expect(consoleSpy).toHaveBeenCalledWith(
            expect.stringContaining('Error loading overview stats'),
            expect.any(Error)
        );
        
        consoleSpy.mockRestore();
    });
    
    test('should show loading state during API calls', async () => {
        let resolvePromise;
        const pendingPromise = new Promise(resolve => {
            resolvePromise = resolve;
        });
        
        fetch.mockReturnValueOnce(pendingPromise);
        
        const loadPromise = dashboard.loadOverviewStats();
        
        // Should show loading state
        expect(dashboard.isLoading).toBe(true);
        expect(mockContainer.querySelector('.loading-spinner')).toBeTruthy();
        
        // Resolve the promise
        resolvePromise({
            ok: true,
            json: () => Promise.resolve({})
        });
        
        await loadPromise;
        
        // Should hide loading state
        expect(dashboard.isLoading).toBe(false);
        expect(mockContainer.querySelector('.loading-spinner')).toBeFalsy();
    });
    
    test('should switch between different views', () => {
        dashboard.switchView('products');
        expect(dashboard.currentView).toBe('products');
        
        dashboard.switchView('analytics');
        expect(dashboard.currentView).toBe('analytics');
        
        dashboard.switchView('settings');
        expect(dashboard.currentView).toBe('settings');
    });
});
"""

# Product Management Tests
product_management_test_js = """
// GridSpoke Product Management Tests
import { ProductManager } from '../../../frontend/static/js/components/product-manager.js';

describe('Product Manager', () => {
    let productManager;
    let mockContainer;
    
    beforeEach(() => {
        mockContainer = testUtils.createElement('div', { id: 'product-manager' });
        document.body.appendChild(mockContainer);
        
        fetch.mockClear();
        productManager = new ProductManager(mockContainer);
    });
    
    afterEach(() => {
        document.body.removeChild(mockContainer);
    });
    
    test('should load and display products list', async () => {
        const mockProducts = [
            {
                id: 'prod_1',
                name: 'Test Product 1',
                price: 29.99,
                seo_score: 75.5,
                optimization_status: 'optimized',
                last_optimized: '2024-01-15T10:30:00Z'
            },
            {
                id: 'prod_2',
                name: 'Test Product 2',
                price: 49.99,
                seo_score: null,
                optimization_status: 'pending',
                last_optimized: null
            }
        ];
        
        fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve({
                products: mockProducts,
                total: 2,
                page: 1,
                per_page: 20
            })
        });
        
        await productManager.loadProducts();
        
        // Check products are rendered
        const productRows = mockContainer.querySelectorAll('.product-row');
        expect(productRows.length).toBe(2);
        
        // Check product details
        expect(mockContainer.textContent).toContain('Test Product 1');
        expect(mockContainer.textContent).toContain('Test Product 2');
        expect(mockContainer.textContent).toContain('75.5');
        expect(mockContainer.textContent).toContain('$29.99');
    });
    
    test('should handle product search', async () => {
        const searchInput = testUtils.createElement('input', { 
            type: 'text',
            id: 'product-search'
        });
        mockContainer.appendChild(searchInput);
        
        fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve({ products: [], total: 0 })
        });
        
        // Simulate search input
        searchInput.value = 'wireless';
        testUtils.simulateEvent(searchInput, 'input');
        
        // Wait for debounced search
        await testUtils.waitFor(() => fetch.mock.calls.length > 0, 1000);
        
        expect(fetch).toHaveBeenCalledWith(
            expect.stringContaining('search=wireless'),
            expect.any(Object)
        );
    });
    
    test('should handle product optimization', async () => {
        const mockProduct = {
            id: 'prod_optimize_test',
            name: 'Product to Optimize'
        };
        
        // Mock optimization response
        fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve({
                job_id: 'job_123',
                status: 'queued'
            })
        });
        
        await productManager.optimizeProduct(mockProduct.id, 'full');
        
        expect(fetch).toHaveBeenCalledWith('/api/v1/optimize/product', {
            method: 'POST',
            headers: {
                'Authorization': expect.stringContaining('Bearer'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                product_id: mockProduct.id,
                optimization_type: 'full'
            })
        });
    });
    
    test('should handle bulk product selection', () => {
        // Create product checkboxes
        const product1 = testUtils.createElement('input', {
            type: 'checkbox',
            'data-product-id': 'prod_1',
            class: 'product-checkbox'
        });
        const product2 = testUtils.createElement('input', {
            type: 'checkbox',
            'data-product-id': 'prod_2',
            class: 'product-checkbox'
        });
        
        mockContainer.appendChild(product1);
        mockContainer.appendChild(product2);
        
        // Select products
        product1.checked = true;
        product2.checked = true;
        
        testUtils.simulateEvent(product1, 'change');
        testUtils.simulateEvent(product2, 'change');
        
        const selectedProducts = productManager.getSelectedProducts();
        expect(selectedProducts).toEqual(['prod_1', 'prod_2']);
    });
    
    test('should handle bulk optimization', async () => {
        // Setup selected products
        const selectedProducts = ['prod_1', 'prod_2', 'prod_3'];
        productManager.selectedProducts = selectedProducts;
        
        fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve({
                job_id: 'bulk_job_456',
                status: 'queued',
                total_items: 3
            })
        });
        
        await productManager.optimizeBulkProducts('full');
        
        expect(fetch).toHaveBeenCalledWith('/api/v1/optimize/bulk', {
            method: 'POST',
            headers: {
                'Authorization': expect.stringContaining('Bearer'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                product_ids: selectedProducts,
                optimization_type: 'full'
            })
        });
    });
});
"""

# WebSocket Real-time Updates Tests
websocket_test_js = """
// GridSpoke WebSocket Real-time Updates Tests
import { WebSocketManager } from '../../../frontend/static/js/services/websocket-manager.js';

describe('WebSocket Manager', () => {
    let wsManager;
    let mockWebSocket;
    
    beforeEach(() => {
        mockWebSocket = new WebSocket('ws://localhost:8000/ws');
        wsManager = new WebSocketManager();
        wsManager.socket = mockWebSocket;
    });
    
    afterEach(() => {
        if (wsManager) {
            wsManager.disconnect();
        }
    });
    
    test('should connect to WebSocket server', () => {
        const onConnectSpy = jest.fn();
        wsManager.onConnect = onConnectSpy;
        
        // Simulate connection
        mockWebSocket.onopen();
        
        expect(onConnectSpy).toHaveBeenCalled();
        expect(wsManager.isConnected).toBe(true);
    });
    
    test('should handle optimization progress updates', () => {
        const onProgressSpy = jest.fn();
        wsManager.onOptimizationProgress = onProgressSpy;
        
        const progressData = {
            type: 'job_progress',
            data: {
                job_id: 'job_123',
                progress: 75,
                processed_items: 75,
                total_items: 100,
                current_item: 'Product ABC'
            }
        };
        
        // Simulate receiving progress update
        mockWebSocket.simulateMessage(progressData);
        
        expect(onProgressSpy).toHaveBeenCalledWith(progressData.data);
    });
    
    test('should handle optimization completion', () => {
        const onCompletionSpy = jest.fn();
        wsManager.onOptimizationComplete = onCompletionSpy;
        
        const completionData = {
            type: 'job_completed',
            data: {
                job_id: 'job_456',
                status: 'completed',
                total_items: 50,
                successful_items: 48,
                failed_items: 2,
                summary: {
                    avg_seo_score: 87.5,
                    total_cost: 12.34
                }
            }
        };
        
        mockWebSocket.simulateMessage(completionData);
        
        expect(onCompletionSpy).toHaveBeenCalledWith(completionData.data);
    });
    
    test('should handle connection errors', () => {
        const onErrorSpy = jest.fn();
        wsManager.onError = onErrorSpy;
        
        const error = new Error('WebSocket connection failed');
        mockWebSocket.onerror(error);
        
        expect(onErrorSpy).toHaveBeenCalledWith(error);
    });
    
    test('should reconnect after connection loss', async () => {
        wsManager.autoReconnect = true;
        wsManager.reconnectInterval = 100; // Fast reconnect for testing
        
        const connectSpy = jest.spyOn(wsManager, 'connect');
        
        // Simulate connection loss
        mockWebSocket.onclose();
        
        // Wait for reconnect attempt
        await testUtils.waitFor(() => connectSpy.mock.calls.length > 0, 500);
        
        expect(connectSpy).toHaveBeenCalled();
    });
    
    test('should subscribe to job updates', () => {
        const sendSpy = jest.spyOn(mockWebSocket, 'send');
        
        wsManager.subscribeToJob('job_789');
        
        expect(sendSpy).toHaveBeenCalledWith(JSON.stringify({
            type: 'subscribe',
            channel: 'job_updates',
            job_id: 'job_789'
        }));
    });
});
"""

# UI Components Tests
ui_components_test_js = """
// GridSpoke UI Components Tests
import { ProgressBar } from '../../../frontend/static/js/components/progress-bar.js';
import { Modal } from '../../../frontend/static/js/components/modal.js';
import { Toast } from '../../../frontend/static/js/components/toast.js';

describe('UI Components', () => {
    describe('Progress Bar', () => {
        let progressBar;
        let container;
        
        beforeEach(() => {
            container = testUtils.createElement('div');
            document.body.appendChild(container);
            progressBar = new ProgressBar(container);
        });
        
        afterEach(() => {
            document.body.removeChild(container);
        });
        
        test('should render progress bar', () => {
            progressBar.render();
            
            expect(container.querySelector('.progress-bar')).toBeTruthy();
            expect(container.querySelector('.progress-fill')).toBeTruthy();
            expect(container.querySelector('.progress-text')).toBeTruthy();
        });
        
        test('should update progress value', () => {
            progressBar.render();
            progressBar.setProgress(75);
            
            const fillElement = container.querySelector('.progress-fill');
            expect(fillElement.style.width).toBe('75%');
            
            const textElement = container.querySelector('.progress-text');
            expect(textElement.textContent).toContain('75%');
        });
        
        test('should show custom status text', () => {
            progressBar.render();
            progressBar.setProgress(50, 'Processing products...');
            
            const textElement = container.querySelector('.progress-text');
            expect(textElement.textContent).toContain('Processing products...');
        });
    });
    
    describe('Modal', () => {
        let modal;
        
        beforeEach(() => {
            modal = new Modal({
                title: 'Test Modal',
                content: 'Test content'
            });
        });
        
        afterEach(() => {
            modal.close();
        });
        
        test('should create and show modal', () => {
            modal.show();
            
            const modalElement = document.querySelector('.modal');
            expect(modalElement).toBeTruthy();
            expect(modalElement.style.display).not.toBe('none');
            
            expect(modalElement.textContent).toContain('Test Modal');
            expect(modalElement.textContent).toContain('Test content');
        });
        
        test('should close modal when clicking backdrop', () => {
            modal.show();
            
            const backdrop = document.querySelector('.modal-backdrop');
            testUtils.simulateEvent(backdrop, 'click');
            
            const modalElement = document.querySelector('.modal');
            expect(modalElement).toBeFalsy();
        });
        
        test('should close modal when pressing escape', () => {
            modal.show();
            
            const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
            document.dispatchEvent(escapeEvent);
            
            const modalElement = document.querySelector('.modal');
            expect(modalElement).toBeFalsy();
        });
    });
    
    describe('Toast Notifications', () => {
        beforeEach(() => {
            // Clear any existing toasts
            document.querySelectorAll('.toast').forEach(toast => toast.remove());
        });
        
        test('should show success toast', () => {
            Toast.success('Operation completed successfully');
            
            const toast = document.querySelector('.toast.toast-success');
            expect(toast).toBeTruthy();
            expect(toast.textContent).toContain('Operation completed successfully');
        });
        
        test('should show error toast', () => {
            Toast.error('Something went wrong');
            
            const toast = document.querySelector('.toast.toast-error');
            expect(toast).toBeTruthy();
            expect(toast.textContent).toContain('Something went wrong');
        });
        
        test('should auto-dismiss toast after timeout', async () => {
            Toast.info('This will disappear', { duration: 100 });
            
            let toast = document.querySelector('.toast.toast-info');
            expect(toast).toBeTruthy();
            
            // Wait for auto-dismiss
            await testUtils.waitFor(() => !document.querySelector('.toast.toast-info'), 500);
            
            toast = document.querySelector('.toast.toast-info');
            expect(toast).toBeFalsy();
        });
        
        test('should allow manual dismissal', () => {
            Toast.warning('Manual dismiss test');
            
            const toast = document.querySelector('.toast.toast-warning');
            const closeButton = toast.querySelector('.toast-close');
            
            testUtils.simulateEvent(closeButton, 'click');
            
            expect(document.querySelector('.toast.toast-warning')).toBeFalsy();
        });
    });
});
"""

# API Client Tests
api_client_test_js = """
// GridSpoke API Client Tests
import { ApiClient } from '../../../frontend/static/js/services/api-client.js';

describe('API Client', () => {
    let apiClient;
    
    beforeEach(() => {
        fetch.mockClear();
        apiClient = new ApiClient();
        
        // Mock localStorage for auth token
        localStorage.getItem.mockReturnValue('mock_jwt_token');
    });
    
    test('should include auth headers in requests', async () => {
        fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve({})
        });
        
        await apiClient.get('/test-endpoint');
        
        expect(fetch).toHaveBeenCalledWith('/api/v1/test-endpoint', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer mock_jwt_token',
                'Content-Type': 'application/json'
            }
        });
    });
    
    test('should handle POST requests with data', async () => {
        const testData = { name: 'Test', value: 123 };
        
        fetch.mockResolvedValueOnce({
            ok: true,
            json: () => Promise.resolve({ id: 'created_123' })
        });
        
        const result = await apiClient.post('/test-endpoint', testData);
        
        expect(fetch).toHaveBeenCalledWith('/api/v1/test-endpoint', {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer mock_jwt_token',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testData)
        });
        
        expect(result).toEqual({ id: 'created_123' });
    });
    
    test('should handle API errors', async () => {
        fetch.mockResolvedValueOnce({
            ok: false,
            status: 400,
            json: () => Promise.resolve({
                error: 'Bad Request',
                message: 'Invalid input data'
            })
        });
        
        await expect(apiClient.get('/error-endpoint')).rejects.toThrow('Bad Request: Invalid input data');
    });
    
    test('should handle network errors', async () => {
        fetch.mockRejectedValueOnce(new Error('Network error'));
        
        await expect(apiClient.get('/network-error')).rejects.toThrow('Network error');
    });
    
    test('should handle 401 unauthorized responses', async () => {
        fetch.mockResolvedValueOnce({
            ok: false,
            status: 401,
            json: () => Promise.resolve({ error: 'Unauthorized' })
        });
        
        const logoutSpy = jest.spyOn(apiClient, 'handleUnauthorized');
        
        await expect(apiClient.get('/protected-endpoint')).rejects.toThrow();
        expect(logoutSpy).toHaveBeenCalled();
    });
    
    test('should retry failed requests', async () => {
        // First two calls fail, third succeeds
        fetch
            .mockRejectedValueOnce(new Error('Network error'))
            .mockRejectedValueOnce(new Error('Network error'))
            .mockResolvedValueOnce({
                ok: true,
                json: () => Promise.resolve({ success: true })
            });
        
        const result = await apiClient.get('/retry-endpoint', { retries: 2 });
        
        expect(fetch).toHaveBeenCalledTimes(3);
        expect(result).toEqual({ success: true });
    });
});
"""

# End-to-End Tests with Puppeteer
e2e_test_js = """
// GridSpoke End-to-End Tests with Puppeteer
const puppeteer = require('puppeteer');

describe('GridSpoke E2E Tests', () => {
    let browser;
    let page;
    
    beforeAll(async () => {
        browser = await puppeteer.launch({
            headless: process.env.CI === 'true',
            slowMo: 50,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
    });
    
    afterAll(async () => {
        await browser.close();
    });
    
    beforeEach(async () => {
        page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 720 });
        
        // Mock API responses
        await page.setRequestInterception(true);
        page.on('request', request => {
            if (request.url().includes('/api/v1/auth/login')) {
                request.respond({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        access_token: 'mock_token',
                        user: { id: 'user_123', email: '<EMAIL>' }
                    })
                });
            } else if (request.url().includes('/api/v1/analytics/overview')) {
                request.respond({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({
                        total_products: 150,
                        optimized_products: 120,
                        avg_seo_score: 87.5
                    })
                });
            } else {
                request.continue();
            }
        });
    });
    
    afterEach(async () => {
        await page.close();
    });
    
    test('should login and display dashboard', async () => {
        await page.goto('http://localhost:8000/login');
        
        // Fill login form
        await page.type('#email', '<EMAIL>');
        await page.type('#password', 'password123');
        await page.click('#login-button');
        
        // Wait for redirect to dashboard
        await page.waitForNavigation();
        
        // Check dashboard elements
        await page.waitForSelector('.dashboard-overview');
        
        const totalProducts = await page.$eval('[data-stat="total-products"]', el => el.textContent);
        expect(totalProducts).toContain('150');
        
        const optimizedProducts = await page.$eval('[data-stat="optimized-products"]', el => el.textContent);
        expect(optimizedProducts).toContain('120');
    });
    
    test('should navigate to products page and load products', async () => {
        // Login first
        await page.goto('http://localhost:8000/dashboard');
        
        // Mock products API
        await page.route('/api/v1/products*', route => {
            route.fulfill({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify({
                    products: [
                        { id: 'prod_1', name: 'Test Product 1', seo_score: 85.5 },
                        { id: 'prod_2', name: 'Test Product 2', seo_score: null }
                    ],
                    total: 2
                })
            });
        });
        
        // Navigate to products
        await page.click('[data-nav="products"]');
        await page.waitForSelector('.products-table');
        
        // Check products are displayed
        const productRows = await page.$$('.product-row');
        expect(productRows.length).toBe(2);
        
        const firstProductName = await page.$eval('.product-row:first-child .product-name', el => el.textContent);
        expect(firstProductName).toBe('Test Product 1');
    });
    
    test('should optimize a product', async () => {
        await page.goto('http://localhost:8000/dashboard/products');
        
        // Mock optimization API
        await page.route('/api/v1/optimize/product', route => {
            route.fulfill({
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify({
                    job_id: 'job_123',
                    status: 'queued'
                })
            });
        });
        
        // Click optimize button for first product
        await page.click('.product-row:first-child .optimize-button');
        
        // Wait for optimization modal
        await page.waitForSelector('.optimization-modal');
        
        // Select optimization type
        await page.select('#optimization-type', 'full');
        
        // Start optimization
        await page.click('#start-optimization');
        
        // Check for success message
        await page.waitForSelector('.toast-success');
        const successMessage = await page.$eval('.toast-success', el => el.textContent);
        expect(successMessage).toContain('Optimization started');
    });
    
    test('should display real-time optimization progress', async () => {
        await page.goto('http://localhost:8000/dashboard');
        
        // Mock WebSocket connection
        await page.evaluateOnNewDocument(() => {
            window.mockWebSocketData = {
                type: 'job_progress',
                data: {
                    job_id: 'job_123',
                    progress: 75,
                    current_item: 'Product ABC'
                }
            };
        });
        
        // Wait for WebSocket connection
        await page.waitForFunction(() => window.wsManager && window.wsManager.isConnected);
        
        // Simulate progress update
        await page.evaluate(() => {
            window.wsManager.socket.simulateMessage(window.mockWebSocketData);
        });
        
        // Check progress is displayed
        await page.waitForSelector('.progress-notification');
        const progressText = await page.$eval('.progress-notification', el => el.textContent);
        expect(progressText).toContain('75%');
        expect(progressText).toContain('Product ABC');
    });
    
    test('should handle bulk product optimization', async () => {
        await page.goto('http://localhost:8000/dashboard/products');
        
        // Select multiple products
        await page.click('.product-row:nth-child(1) .product-checkbox');
        await page.click('.product-row:nth-child(2) .product-checkbox');
        
        // Check bulk actions are enabled
        await page.waitForSelector('.bulk-actions:not(.disabled)');
        
        // Start bulk optimization
        await page.click('.bulk-optimize-button');
        
        // Confirm in modal
        await page.waitForSelector('.bulk-optimization-modal');
        await page.click('#confirm-bulk-optimization');
        
        // Check success notification
        await page.waitForSelector('.toast-success');
        const successMessage = await page.$eval('.toast-success', el => el.textContent);
        expect(successMessage).toContain('Bulk optimization started');
    });
});
"""

# Package.json for Frontend Testing
package_json = {
    "name": "gridspoke-frontend-tests",
    "version": "1.0.0",
    "description": "Frontend testing for GridSpoke dashboard",
    "scripts": {
        "test": "jest",
        "test:watch": "jest --watch",
        "test:coverage": "jest --coverage",
        "test:e2e": "jest --config=jest.e2e.config.js",
        "lint": "eslint frontend/static/js/**/*.js tests/frontend/**/*.js",
        "lint:fix": "eslint --fix frontend/static/js/**/*.js tests/frontend/**/*.js"
    },
    "devDependencies": {
        "jest": "^29.0.0",
        "jest-environment-jsdom": "^29.0.0",
        "@testing-library/jest-dom": "^5.16.0",
        "puppeteer": "^19.0.0",
        "eslint": "^8.0.0",
        "identity-obj-proxy": "^3.0.0"
    },
    "jest": jest_config
}

print("GridSpoke Frontend JavaScript Testing Suite Created!")
print("=" * 60)
print("Components included:")
print("- Jest configuration for testing JavaScript components")
print("- Dashboard component tests")
print("- Product management tests") 
print("- WebSocket real-time update tests")
print("- UI component tests (Progress bar, Modal, Toast)")
print("- API client tests")
print("- End-to-end tests with Puppeteer")
print("- Test setup and utilities")
print("\nTo run tests:")
print("npm test                  # Run all tests")
print("npm run test:watch        # Watch mode")
print("npm run test:coverage     # Coverage report")
print("npm run test:e2e          # End-to-end tests")
print("npm run lint              # Code linting")
