# GridSpoke Analytics Schemas
# Pydantic models for analytics API responses

from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from enum import Enum

# =============================================================================
# BASE ANALYTICS MODELS
# =============================================================================

class AnalyticsDateRange(BaseModel):
    """Date range for analytics queries."""
    start_date: datetime
    end_date: datetime
    period: str = Field(..., description="Period: daily, weekly, monthly")

class MetricValue(BaseModel):
    """Individual metric value with timestamp."""
    timestamp: datetime
    value: float
    label: Optional[str] = None

class TrendData(BaseModel):
    """Trend data over time."""
    metric_name: str
    data_points: List[MetricValue]
    total_change: float
    percentage_change: float

# =============================================================================
# OVERVIEW ANALYTICS
# =============================================================================

class AnalyticsOverview(BaseModel):
    """High-level analytics overview."""
    total_products: int
    optimized_products: int
    optimization_rate: float = Field(..., description="Percentage of products optimized")
    
    average_seo_score_before: float
    average_seo_score_after: float
    seo_improvement: float
    
    total_optimizations: int
    successful_optimizations: int
    success_rate: float
    
    total_ai_cost: float
    cost_per_optimization: float
    
    active_stores: int
    last_optimization: Optional[datetime]
    
    recent_activity: List[Dict[str, Any]]

class PerformanceMetrics(BaseModel):
    """Detailed performance metrics."""
    metric_type: str
    time_period: str
    
    current_value: float
    previous_value: float
    change_amount: float
    change_percentage: float
    
    trend_data: List[MetricValue]
    
    top_performers: List[Dict[str, Any]]
    improvement_opportunities: List[Dict[str, Any]]
    
    benchmarks: Dict[str, float]

# =============================================================================
# SEO ANALYTICS
# =============================================================================

class SEOScoreDistribution(BaseModel):
    """SEO score distribution data."""
    score_range: str
    product_count: int
    percentage: float

class SEOScoreAnalysis(BaseModel):
    """Comprehensive SEO score analysis."""
    overall_average: float
    median_score: float
    
    score_distribution: List[SEOScoreDistribution]
    
    top_scoring_products: List[Dict[str, Any]]
    lowest_scoring_products: List[Dict[str, Any]]
    
    category_performance: Dict[str, float]
    
    improvement_recommendations: List[Dict[str, str]]
    
    score_trends: TrendData

# =============================================================================
# OPTIMIZATION ANALYTICS
# =============================================================================

class OptimizationJobStats(BaseModel):
    """Optimization job statistics."""
    period: str
    total_jobs: int
    successful_jobs: int
    failed_jobs: int
    pending_jobs: int
    average_processing_time: float

class OptimizationTrends(BaseModel):
    """Optimization trends and patterns."""
    time_period: str
    date_range: AnalyticsDateRange
    
    job_statistics: List[OptimizationJobStats]
    
    peak_usage_hours: List[int]
    success_rate_trend: TrendData
    processing_time_trend: TrendData
    
    optimization_types: Dict[str, int]
    
    seasonal_patterns: Dict[str, Any]

# =============================================================================
# COST ANALYTICS
# =============================================================================

class CostBreakdown(BaseModel):
    """Cost breakdown by category."""
    category: str
    amount: float
    percentage: float
    item_count: int

class ModelUsage(BaseModel):
    """AI model usage statistics."""
    model_name: str
    total_requests: int
    total_tokens: int
    total_cost: float
    average_cost_per_request: float

class CostAnalysis(BaseModel):
    """Comprehensive cost analysis."""
    total_cost: float
    date_range: AnalyticsDateRange
    
    cost_breakdown: List[CostBreakdown]
    model_usage: List[ModelUsage]
    
    daily_costs: List[MetricValue]
    cost_trend: TrendData
    
    projected_monthly_cost: float
    cost_per_optimization: float
    cost_efficiency_score: float
    
    cost_saving_recommendations: List[Dict[str, str]]

# =============================================================================
# CONTENT ANALYTICS
# =============================================================================

class ContentTypeStats(BaseModel):
    """Statistics for a content type."""
    content_type: str
    total_generated: int
    success_rate: float
    average_quality_score: float
    total_tokens_used: int
    total_cost: float

class ContentGenerationStats(BaseModel):
    """Content generation statistics."""
    date_range: AnalyticsDateRange
    
    content_types: List[ContentTypeStats]
    
    total_content_pieces: int
    average_generation_time: float
    
    quality_distribution: List[Dict[str, Any]]
    
    top_performing_content: List[Dict[str, Any]]
    content_performance_trends: TrendData

# =============================================================================
# STORE ANALYTICS
# =============================================================================

class StoreAnalytics(BaseModel):
    """Comprehensive store analytics."""
    store_id: str
    store_name: str
    
    total_products: int
    optimized_products: int
    optimization_coverage: float
    
    average_seo_score: float
    seo_improvement: float
    
    revenue_impact_estimate: Optional[float]
    traffic_improvement_estimate: Optional[float]
    
    top_categories: List[Dict[str, Any]]
    optimization_timeline: List[Dict[str, Any]]
    
    competitive_position: Dict[str, Any]
    
    recommendations: List[Dict[str, str]]

# =============================================================================
# PRODUCT ANALYTICS
# =============================================================================

class OptimizationHistory(BaseModel):
    """Product optimization history entry."""
    timestamp: datetime
    optimization_type: str
    seo_score_before: float
    seo_score_after: float
    improvement: float
    ai_model_used: str
    cost: float

class ProductAnalytics(BaseModel):
    """Detailed product analytics."""
    product_id: str
    product_name: str
    
    current_seo_score: float
    initial_seo_score: float
    total_improvement: float
    
    optimization_count: int
    last_optimized: Optional[datetime]
    
    optimization_history: List[OptimizationHistory]
    
    performance_metrics: Dict[str, Any]
    
    similar_products_comparison: List[Dict[str, Any]]
    
    recommendations: List[Dict[str, str]]

# =============================================================================
# COMPETITOR ANALYTICS
# =============================================================================

class CompetitorProduct(BaseModel):
    """Competitor product data."""
    url: str
    title: str
    seo_score: float
    key_features: List[str]
    price_range: Optional[str]

class CompetitorAnalysis(BaseModel):
    """Competitor analysis results."""
    store_id: str
    analysis_date: datetime
    
    competitor_count: int
    average_competitor_seo_score: float
    
    your_position: Dict[str, Any]
    
    top_competitors: List[CompetitorProduct]
    
    gap_analysis: Dict[str, Any]
    opportunity_areas: List[Dict[str, str]]
    
    recommendations: List[Dict[str, str]]

# =============================================================================
# EXPORT AND REPORTING
# =============================================================================

class ExportFormat(str, Enum):
    PDF = "pdf"
    CSV = "csv"
    EXCEL = "excel"
    JSON = "json"

class AnalyticsExportRequest(BaseModel):
    """Request for analytics data export."""
    export_type: ExportFormat
    store_id: Optional[str] = None
    date_range: AnalyticsDateRange
    
    include_sections: List[str] = Field(
        default=["overview", "performance", "seo_analysis"],
        description="Sections to include in export"
    )
    
    email_delivery: bool = Field(default=True, description="Email the report when ready")
    
    custom_filters: Optional[Dict[str, Any]] = None

class ReportStatus(BaseModel):
    """Status of a report generation."""
    report_id: str
    status: str  # pending, processing, completed, failed
    progress: float
    estimated_completion: Optional[datetime]
    download_url: Optional[str]
    error_message: Optional[str]

# =============================================================================
# REAL-TIME ANALYTICS
# =============================================================================

class QueueStatus(BaseModel):
    """Real-time queue status."""
    queue_name: str
    pending_jobs: int
    active_jobs: int
    failed_jobs: int
    average_processing_time: float

class SystemHealthMetrics(BaseModel):
    """System health metrics."""
    status: str
    uptime_seconds: float
    healthy_components: int
    total_components: int
    
    queue_status: List[QueueStatus]
    
    api_response_time: float
    error_rate: float
