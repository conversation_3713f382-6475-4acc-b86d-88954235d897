"""
Product management endpoints.
"""
import uuid
from decimal import Decimal
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.database import get_db_session
from core.security import get_current_user
from models.user import User as UserModel
from models.product import Product as ProductModel
from models.store import Store as StoreModel
from schemas.product import ProductCreate, Product, ProductUpdate, ProductBulkAction, ProductOptimizationRequest, ProductOptimizationResponse
from schemas.job import OptimizationJobCreate, JobType
from crud import crud_job
from workers.tasks.optimization_tasks import optimize_single_product, optimize_product_batch
import structlog

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=list[Product])
async def get_products(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    store_id: uuid.UUID = Query(...),
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get products for a store owned by current user."""
    # Validate store ownership
    store_res = await db.execute(select(StoreModel).where(StoreModel.id == store_id))
    store = store_res.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Store not found")
    stmt = (
        select(ProductModel)
        .where(ProductModel.store_id == store_id)
        .offset(skip)
        .limit(limit)
    )
    result = await db.execute(stmt)
    products = result.scalars().all()
    return products


@router.get("/{product_id}", response_model=Product)
async def get_product(
    product_id: uuid.UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    stmt = select(ProductModel).where(ProductModel.id == product_id)
    result = await db.execute(stmt)
    product = result.scalar_one_or_none()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    # Verify ownership through store
    store_res = await db.execute(select(StoreModel).where(StoreModel.id == product.store_id))
    store = store_res.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Product not found")
    return product

@router.post("/", response_model=Product, status_code=status.HTTP_201_CREATED)
async def create_product(
    product_in: ProductCreate,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    # Ownership check
    store_res = await db.execute(select(StoreModel).where(StoreModel.id == product_in.store_id))
    store = store_res.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Store not found")
    # Prevent duplicate external_id per store
    dup_res = await db.execute(
        select(ProductModel).where(
            ProductModel.store_id == product_in.store_id,
            ProductModel.external_id == product_in.external_id
        )
    )
    if dup_res.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="Product external_id already exists in store")
    db_obj = ProductModel(
        store_id=product_in.store_id,
        external_id=product_in.external_id,
        title=product_in.name,
        sku=product_in.sku,
        description=product_in.description,
        price=product_in.price,
        categories=product_in.categories or [],
        tags=product_in.tags or [],
        image_urls=[str(u) for u in (product_in.images or [])],
        attributes=product_in.original_data or {},
    )
    db.add(db_obj)
    await db.commit()
    await db.refresh(db_obj)
    logger.info("Product created", product_id=str(db_obj.id), store_id=str(store.id))
    # Update store product count (simple increment)
    store.total_products += 1
    await db.commit()
    return db_obj


@router.post("/{product_id}/optimize", response_model=ProductOptimizationResponse)
async def optimize_product(
    product_id: uuid.UUID,
    optimization_request: ProductOptimizationRequest,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    # Confirm ownership and respond. Actual optimization logic will be queued via Celery in later phase.
    stmt = select(ProductModel).where(ProductModel.id == product_id)
    result = await db.execute(stmt)
    product = result.scalar_one_or_none()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    store_res = await db.execute(select(StoreModel).where(StoreModel.id == product.store_id))
    store = store_res.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Product not found")

    # Create a job to track the optimization
    job_create = OptimizationJobCreate(
        store_id=store.id,
        job_type=JobType.PRODUCT_OPTIMIZATION,
        title=f"Optimize product: {product.title}",
        total_items=1,
        parameters=optimization_request.dict()
    )
    job = await crud_job.create_job(db, job=job_create)

    # Queue optimization job with Celery
    task = optimize_single_product.delay(
        product_id=str(product.id),
        store_id=str(store.id),
        optimization_options=optimization_request.dict()
    )

    logger.info("Product optimization queued",
                product_id=str(product_id),
                job_id=str(job.id),
                task_id=task.id,
                optimization_types=optimization_request.optimization_types)

    return ProductOptimizationResponse(
        job_id=job.id,
        message=f"Optimization queued for product {product_id}",
        estimated_completion=None
    )


@router.post("/bulk/sync")
async def bulk_sync_products(
    products_data: List[ProductCreate],
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Bulk sync products from external store."""
    if not products_data:
        raise HTTPException(status_code=400, detail="No products provided")

    if len(products_data) > 1000:
        raise HTTPException(status_code=400, detail="Too many products (max 1000)")

    # Verify store ownership for all products
    store_ids = list(set(product.store_id for product in products_data))
    for store_id in store_ids:
        store_res = await db.execute(select(StoreModel).where(StoreModel.id == store_id))
        store = store_res.scalar_one_or_none()
        if not store or store.owner_id != current_user.id:
            raise HTTPException(status_code=404, detail=f"Store {store_id} not found")

    created_products = []
    updated_products = []
    errors = []

    for product_data in products_data:
        try:
            # Check if product already exists
            existing_res = await db.execute(
                select(ProductModel).where(
                    ProductModel.store_id == product_data.store_id,
                    ProductModel.external_id == product_data.external_id
                )
            )
            existing_product = existing_res.scalar_one_or_none()

            if existing_product:
                # Update existing product
                existing_product.title = product_data.name
                existing_product.sku = product_data.sku
                existing_product.description = product_data.description
                existing_product.price = Decimal(str(product_data.price)) if product_data.price is not None else None
                existing_product.categories = product_data.categories or []
                existing_product.tags = product_data.tags or []
                existing_product.image_urls = [str(u) for u in (product_data.images or [])]
                existing_product.attributes = product_data.original_data or {}
                updated_products.append(existing_product)
            else:
                # Create new product
                db_obj = ProductModel(
                    store_id=product_data.store_id,
                    external_id=product_data.external_id,
                    title=product_data.name,
                    sku=product_data.sku,
                    description=product_data.description,
                    price=Decimal(str(product_data.price)) if product_data.price is not None else None,
                    categories=product_data.categories or [],
                    tags=product_data.tags or [],
                    image_urls=[str(u) for u in (product_data.images or [])],
                    attributes=product_data.original_data or {},
                )
                db.add(db_obj)
                created_products.append(db_obj)

        except Exception as e:
            errors.append({
                "external_id": product_data.external_id,
                "error": str(e)
            })

    await db.commit()

    # Update store product counts
    for store_id in store_ids:
        store_res = await db.execute(select(StoreModel).where(StoreModel.id == store_id))
        store = store_res.scalar_one_or_none()
        if store:
            # Count products for this store
            count_res = await db.execute(
                select(ProductModel).where(ProductModel.store_id == store_id)
            )
            store.total_products = len(count_res.scalars().all())
            await db.commit()

    logger.info("Bulk product sync completed",
                created=len(created_products),
                updated=len(updated_products),
                errors=len(errors))

    return {
        "message": "Bulk sync completed",
        "created": len(created_products),
        "updated": len(updated_products),
        "errors": errors,
        "total_processed": len(products_data)
    }


@router.post("/bulk/optimize")
async def bulk_optimize_products(
    bulk_action: ProductBulkAction,
    optimization_request: ProductOptimizationRequest,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Bulk optimize multiple products."""
    if not bulk_action.product_ids:
        raise HTTPException(status_code=400, detail="No product IDs provided")

    if len(bulk_action.product_ids) > 100:
        raise HTTPException(status_code=400, detail="Too many products (max 100 per batch)")

    # Verify ownership of all products
    valid_products = []
    store_id = None
    for product_id in bulk_action.product_ids:
        stmt = select(ProductModel).where(ProductModel.id == product_id)
        result = await db.execute(stmt)
        product = result.scalar_one_or_none()

        if product:
            # Check store ownership
            store_res = await db.execute(select(StoreModel).where(StoreModel.id == product.store_id))
            store = store_res.scalar_one_or_none()
            if store and store.owner_id == current_user.id:
                valid_products.append(product)
                if not store_id:
                    store_id = store.id

    if not valid_products:
        raise HTTPException(status_code=404, detail="No valid products found")

    # Create a job to track the optimization
    job_create = OptimizationJobCreate(
        store_id=store_id,
        job_type=JobType.BULK_OPTIMIZATION,
        title=f"Bulk optimize {len(valid_products)} products",
        total_items=len(valid_products),
        parameters=optimization_request.dict()
    )
    job = await crud_job.create_job(db, job=job_create)

    # Queue bulk optimization job with Celery
    task = optimize_product_batch.delay(
        store_id=str(store_id),
        product_ids=[str(p.id) for p in valid_products]
    )

    logger.info("Bulk product optimization queued",
                job_id=str(job.id),
                task_id=task.id,
                product_count=len(valid_products),
                optimization_types=optimization_request.optimization_types)

    return ProductOptimizationResponse(
        job_id=job.id,
        message=f"Bulk optimization queued for {len(valid_products)} products",
        estimated_completion=None
    )
