"""
Competitor Analysis Service - Advanced competitor SEO analysis
Analyzes competitor websites, keywords, and content strategies.
"""

import asyncio
import re
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta
from urllib.parse import urlparse, urljoin
from dataclasses import dataclass

from pydantic import BaseModel, Field, HttpUrl
from sqlalchemy.ext.asyncio import AsyncSession

from ..agents.seo_optimizer import SEOOptimizer
from ..core.config import settings


class CompetitorMetrics(BaseModel):
    """Competitor website metrics"""
    domain: str
    domain_authority: Optional[float] = None
    page_authority: Optional[float] = None
    total_backlinks: Optional[int] = None
    referring_domains: Optional[int] = None
    organic_keywords: Optional[int] = None
    organic_traffic: Optional[int] = None
    paid_keywords: Optional[int] = None
    content_pages: Optional[int] = None


class CompetitorContent(BaseModel):
    """Competitor content analysis"""
    url: str
    title: str
    meta_description: Optional[str] = None
    word_count: int
    headings: Dict[str, List[str]] = {}  # h1, h2, h3, etc.
    keywords: List[str] = []
    images_count: int = 0
    internal_links: int = 0
    external_links: int = 0
    schema_markup: List[str] = []
    social_signals: Dict[str, int] = {}


class KeywordGap(BaseModel):
    """Keyword gap analysis result"""
    keyword: str
    search_volume: Optional[int] = None
    difficulty: Optional[float] = None
    our_position: Optional[int] = None
    competitor_position: int
    competitor_url: str
    opportunity_score: float  # 0-100
    recommended_action: str


class ContentGap(BaseModel):
    """Content gap analysis result"""
    topic: str
    competitor_urls: List[str]
    content_type: str  # "blog_post", "product_page", "category_page"
    estimated_traffic: Optional[int] = None
    competition_level: str  # "low", "medium", "high"
    opportunity_score: float
    recommended_content: str


class CompetitorProduct(BaseModel):
    """Competitor product analysis"""
    url: str
    title: str
    price: Optional[str] = None
    description_length: int
    images_count: int
    reviews_count: Optional[int] = None
    rating: Optional[float] = None
    key_features: List[str] = []
    seo_score: float
    structured_data: bool = False


class CompetitorAnalysisReport(BaseModel):
    """Complete competitor analysis report"""
    competitor_domain: str
    analysis_date: datetime = Field(default_factory=datetime.utcnow)
    metrics: CompetitorMetrics
    top_pages: List[CompetitorContent]
    keyword_gaps: List[KeywordGap]
    content_gaps: List[ContentGap]
    competitor_products: List[CompetitorProduct]
    strengths: List[str]
    weaknesses: List[str]
    opportunities: List[str]
    recommendations: List[str]
    overall_threat_level: str  # "low", "medium", "high", "critical"


class CompetitorAnalysisService:
    """Main competitor analysis service"""
    
    def __init__(self):
        self.seo_optimizer = SEOOptimizer()
        
    async def analyze_competitor(
        self,
        competitor_domain: str,
        our_domain: str,
        focus_keywords: List[str] = None,
        analyze_products: bool = True,
        max_pages: int = 50
    ) -> CompetitorAnalysisReport:
        """Perform comprehensive competitor analysis"""
        
        # 1. Get competitor metrics
        metrics = await self._get_competitor_metrics(competitor_domain)
        
        # 2. Crawl and analyze top pages
        top_pages = await self._analyze_competitor_content(
            competitor_domain, max_pages
        )
        
        # 3. Perform keyword gap analysis
        keyword_gaps = await self._analyze_keyword_gaps(
            competitor_domain, our_domain, focus_keywords or []
        )
        
        # 4. Perform content gap analysis
        content_gaps = await self._analyze_content_gaps(
            competitor_domain, our_domain, top_pages
        )
        
        # 5. Analyze competitor products (if enabled)
        competitor_products = []
        if analyze_products:
            competitor_products = await self._analyze_competitor_products(
                competitor_domain, max_pages=20
            )
            
        # 6. Generate SWOT analysis
        strengths, weaknesses, opportunities = await self._generate_swot_analysis(
            metrics, top_pages, keyword_gaps, content_gaps
        )
        
        # 7. Generate recommendations
        recommendations = await self._generate_recommendations(
            competitor_domain, metrics, keyword_gaps, content_gaps, competitor_products
        )
        
        # 8. Assess threat level
        threat_level = await self._assess_threat_level(metrics, keyword_gaps)
        
        return CompetitorAnalysisReport(
            competitor_domain=competitor_domain,
            metrics=metrics,
            top_pages=top_pages,
            keyword_gaps=keyword_gaps,
            content_gaps=content_gaps,
            competitor_products=competitor_products,
            strengths=strengths,
            weaknesses=weaknesses,
            opportunities=opportunities,
            recommendations=recommendations,
            overall_threat_level=threat_level
        )
        
    async def bulk_competitor_analysis(
        self,
        competitor_domains: List[str],
        our_domain: str,
        focus_keywords: List[str] = None
    ) -> List[CompetitorAnalysisReport]:
        """Analyze multiple competitors"""
        
        # Analyze competitors in parallel (with rate limiting)
        semaphore = asyncio.Semaphore(2)  # Limit concurrent analyses
        
        async def analyze_with_semaphore(domain):
            async with semaphore:
                return await self.analyze_competitor(
                    domain, our_domain, focus_keywords, analyze_products=False, max_pages=25
                )
                
        results = await asyncio.gather(
            *[analyze_with_semaphore(domain) for domain in competitor_domains],
            return_exceptions=True
        )
        
        # Filter out failed analyses
        valid_results = [r for r in results if isinstance(r, CompetitorAnalysisReport)]
        return valid_results
        
    async def monitor_competitor_changes(
        self,
        db: AsyncSession,
        competitor_domain: str,
        pages_to_monitor: List[str]
    ) -> Dict[str, Any]:
        """Monitor competitor content changes"""
        
        changes_detected = []
        
        for page_url in pages_to_monitor:
            try:
                # Get current content
                current_content = await self._scrape_page_content(page_url)
                
                # Get previous content from database (if exists)
                previous_content = await self._get_previous_content(db, page_url)
                
                if previous_content:
                    # Compare content
                    changes = await self._detect_content_changes(
                        previous_content, current_content
                    )
                    
                    if changes:
                        changes_detected.append({
                            "url": page_url,
                            "changes": changes,
                            "analysis_date": datetime.utcnow()
                        })
                        
                # Store current content for future comparisons
                await self._store_content_snapshot(db, page_url, current_content)
                
            except Exception as e:
                continue  # Skip failed pages
                
        return {
            "competitor_domain": competitor_domain,
            "pages_monitored": len(pages_to_monitor),
            "changes_detected": len(changes_detected),
            "changes": changes_detected
        }
        
    async def find_competitor_keywords(
        self,
        competitor_domain: str,
        min_search_volume: int = 100,
        max_difficulty: float = 60.0
    ) -> List[KeywordGap]:
        """Find high-opportunity keywords from competitors"""
        
        # Simulate keyword research API call
        keywords = await self._research_competitor_keywords(competitor_domain)
        
        opportunities = []
        for keyword_data in keywords:
            # Calculate opportunity score
            opportunity_score = await self._calculate_keyword_opportunity(
                keyword_data, min_search_volume, max_difficulty
            )
            
            if opportunity_score > 50:  # Only high-opportunity keywords
                gap = KeywordGap(
                    keyword=keyword_data["keyword"],
                    search_volume=keyword_data.get("search_volume"),
                    difficulty=keyword_data.get("difficulty"),
                    our_position=None,  # We don't rank for this
                    competitor_position=keyword_data["position"],
                    competitor_url=keyword_data["url"],
                    opportunity_score=opportunity_score,
                    recommended_action=await self._get_keyword_recommendation(keyword_data)
                )
                opportunities.append(gap)
                
        # Sort by opportunity score
        opportunities.sort(key=lambda x: x.opportunity_score, reverse=True)
        return opportunities[:50]  # Return top 50 opportunities
        
    async def analyze_competitor_backlinks(
        self,
        competitor_domain: str,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Analyze competitor backlink profile"""
        
        # Simulate backlink analysis
        backlinks = await self._get_competitor_backlinks(competitor_domain, limit)
        
        # Analyze backlink quality
        high_quality_domains = []
        medium_quality_domains = []
        low_quality_domains = []
        
        for backlink in backlinks:
            domain_authority = backlink.get("domain_authority", 0)
            
            if domain_authority >= 70:
                high_quality_domains.append(backlink)
            elif domain_authority >= 40:
                medium_quality_domains.append(backlink)
            else:
                low_quality_domains.append(backlink)
                
        # Find link building opportunities
        opportunities = await self._find_link_opportunities(high_quality_domains + medium_quality_domains)
        
        return {
            "total_backlinks": len(backlinks),
            "high_quality_domains": len(high_quality_domains),
            "medium_quality_domains": len(medium_quality_domains),
            "low_quality_domains": len(low_quality_domains),
            "top_referring_domains": [bl["domain"] for bl in high_quality_domains[:10]],
            "link_opportunities": opportunities
        }
        
    async def _get_competitor_metrics(self, domain: str) -> CompetitorMetrics:
        """Get competitor SEO metrics"""
        
        # Simulate API calls to SEO tools
        # In real implementation, this would call APIs like Ahrefs, SEMrush, etc.
        
        return CompetitorMetrics(
            domain=domain,
            domain_authority=65.0,
            page_authority=45.0,
            total_backlinks=15420,
            referring_domains=856,
            organic_keywords=2340,
            organic_traffic=45600,
            paid_keywords=156,
            content_pages=234
        )
        
    async def _analyze_competitor_content(
        self,
        domain: str,
        max_pages: int
    ) -> List[CompetitorContent]:
        """Analyze competitor content pages"""
        
        # Get list of top pages (simulated)
        top_pages = await self._get_top_competitor_pages(domain, max_pages)
        
        content_analyses = []
        semaphore = asyncio.Semaphore(5)  # Limit concurrent requests
        
        async def analyze_page_with_semaphore(url):
            async with semaphore:
                return await self._analyze_single_page(url)
                
        results = await asyncio.gather(
            *[analyze_page_with_semaphore(url) for url in top_pages],
            return_exceptions=True
        )
        
        # Filter successful analyses
        content_analyses = [r for r in results if isinstance(r, CompetitorContent)]
        return content_analyses
        
    async def _analyze_keyword_gaps(
        self,
        competitor_domain: str,
        our_domain: str,
        focus_keywords: List[str]
    ) -> List[KeywordGap]:
        """Analyze keyword gaps between us and competitor"""
        
        gaps = []
        
        # Analyze focus keywords first
        for keyword in focus_keywords:
            gap = await self._analyze_single_keyword_gap(
                keyword, competitor_domain, our_domain
            )
            if gap:
                gaps.append(gap)
                
        # Find additional keyword opportunities
        additional_gaps = await self.find_competitor_keywords(competitor_domain)
        gaps.extend(additional_gaps[:20])  # Add top 20 additional opportunities
        
        return gaps
        
    async def _analyze_content_gaps(
        self,
        competitor_domain: str,
        our_domain: str,
        competitor_pages: List[CompetitorContent]
    ) -> List[ContentGap]:
        """Analyze content gaps"""
        
        gaps = []
        
        # Analyze competitor content topics
        topics = await self._extract_content_topics(competitor_pages)
        
        # Check which topics we're missing
        our_topics = await self._get_our_content_topics(our_domain)
        
        for topic, pages in topics.items():
            if topic not in our_topics:
                # Calculate opportunity
                opportunity_score = await self._calculate_content_opportunity(topic, pages)
                
                gap = ContentGap(
                    topic=topic,
                    competitor_urls=[page.url for page in pages],
                    content_type=await self._classify_content_type(pages[0]),
                    estimated_traffic=sum(page.word_count for page in pages) * 10,  # Rough estimate
                    competition_level="medium",
                    opportunity_score=opportunity_score,
                    recommended_content=await self._generate_content_recommendation(topic, pages)
                )
                gaps.append(gap)
                
        # Sort by opportunity score
        gaps.sort(key=lambda x: x.opportunity_score, reverse=True)
        return gaps[:20]  # Return top 20 opportunities
        
    async def _analyze_competitor_products(
        self,
        domain: str,
        max_pages: int
    ) -> List[CompetitorProduct]:
        """Analyze competitor product pages"""
        
        # Find product pages
        product_urls = await self._find_product_pages(domain, max_pages)
        
        products = []
        for url in product_urls[:20]:  # Limit to 20 products
            try:
                product = await self._analyze_product_page(url)
                products.append(product)
            except Exception:
                continue
                
        return products
        
    async def _generate_swot_analysis(
        self,
        metrics: CompetitorMetrics,
        content: List[CompetitorContent],
        keyword_gaps: List[KeywordGap],
        content_gaps: List[ContentGap]
    ) -> Tuple[List[str], List[str], List[str]]:
        """Generate SWOT analysis"""
        
        strengths = []
        weaknesses = []
        opportunities = []
        
        # Analyze strengths
        if metrics.domain_authority and metrics.domain_authority > 60:
            strengths.append(f"High domain authority ({metrics.domain_authority})")
            
        if metrics.organic_keywords and metrics.organic_keywords > 1000:
            strengths.append(f"Large keyword portfolio ({metrics.organic_keywords} keywords)")
            
        if content and len(content) > 30:
            strengths.append(f"Extensive content library ({len(content)} pages)")
            
        # Analyze weaknesses
        avg_word_count = sum(c.word_count for c in content) / len(content) if content else 0
        if avg_word_count < 500:
            weaknesses.append("Low average content length")
            
        schema_coverage = sum(1 for c in content if c.schema_markup) / len(content) if content else 0
        if schema_coverage < 0.5:
            weaknesses.append("Poor structured data implementation")
            
        # Analyze opportunities
        high_opportunity_keywords = [g for g in keyword_gaps if g.opportunity_score > 70]
        if high_opportunity_keywords:
            opportunities.append(f"{len(high_opportunity_keywords)} high-value keyword opportunities")
            
        high_opportunity_content = [g for g in content_gaps if g.opportunity_score > 70]
        if high_opportunity_content:
            opportunities.append(f"{len(high_opportunity_content)} content gap opportunities")
            
        return strengths, weaknesses, opportunities
        
    async def _generate_recommendations(
        self,
        competitor_domain: str,
        metrics: CompetitorMetrics,
        keyword_gaps: List[KeywordGap],
        content_gaps: List[ContentGap],
        products: List[CompetitorProduct]
    ) -> List[str]:
        """Generate actionable recommendations"""
        
        recommendations = []
        
        # Keyword recommendations
        top_keywords = sorted(keyword_gaps, key=lambda x: x.opportunity_score, reverse=True)[:5]
        if top_keywords:
            recommendations.append(
                f"Target these high-opportunity keywords: {', '.join([k.keyword for k in top_keywords])}"
            )
            
        # Content recommendations
        top_content = sorted(content_gaps, key=lambda x: x.opportunity_score, reverse=True)[:3]
        if top_content:
            recommendations.append(
                f"Create content around these topics: {', '.join([c.topic for c in top_content])}"
            )
            
        # Product recommendations
        if products:
            avg_product_score = sum(p.seo_score for p in products) / len(products)
            if avg_product_score > 80:
                recommendations.append("Competitor has strong product SEO - improve product descriptions and structure")
            else:
                recommendations.append("Competitor has weak product SEO - opportunity to outrank with better optimization")
                
        # Technical recommendations
        if metrics.domain_authority and metrics.domain_authority > 70:
            recommendations.append("Focus on building high-quality backlinks to compete with strong domain authority")
            
        return recommendations
        
    async def _assess_threat_level(
        self,
        metrics: CompetitorMetrics,
        keyword_gaps: List[KeywordGap]
    ) -> str:
        """Assess overall competitive threat level"""
        
        threat_score = 0
        
        # Domain authority factor
        if metrics.domain_authority:
            if metrics.domain_authority > 80:
                threat_score += 40
            elif metrics.domain_authority > 60:
                threat_score += 25
            elif metrics.domain_authority > 40:
                threat_score += 15
                
        # Keyword overlap factor
        high_overlap_keywords = [g for g in keyword_gaps if g.competitor_position <= 3]
        if len(high_overlap_keywords) > 20:
            threat_score += 30
        elif len(high_overlap_keywords) > 10:
            threat_score += 20
        elif len(high_overlap_keywords) > 5:
            threat_score += 10
            
        # Traffic factor
        if metrics.organic_traffic:
            if metrics.organic_traffic > 100000:
                threat_score += 30
            elif metrics.organic_traffic > 50000:
                threat_score += 20
            elif metrics.organic_traffic > 10000:
                threat_score += 10
                
        # Determine threat level
        if threat_score >= 80:
            return "critical"
        elif threat_score >= 60:
            return "high"
        elif threat_score >= 40:
            return "medium"
        else:
            return "low"
            
    # Placeholder methods for external API calls and data processing
    async def _get_top_competitor_pages(self, domain: str, limit: int) -> List[str]:
        """Get top competitor pages"""
        # Mock implementation
        return [f"https://{domain}/page-{i}" for i in range(1, min(limit + 1, 21))]
        
    async def _analyze_single_page(self, url: str) -> CompetitorContent:
        """Analyze single competitor page"""
        # Mock implementation
        return CompetitorContent(
            url=url,
            title="Sample Competitor Page",
            meta_description="Sample meta description",
            word_count=850,
            headings={"h1": ["Main Heading"], "h2": ["Subheading 1", "Subheading 2"]},
            keywords=["keyword1", "keyword2", "keyword3"],
            images_count=5,
            internal_links=12,
            external_links=3,
            schema_markup=["Product", "BreadcrumbList"],
            social_signals={"facebook": 25, "twitter": 12}
        )
        
    async def _scrape_page_content(self, url: str) -> Dict[str, Any]:
        """Scrape page content"""
        # Mock implementation
        return {
            "title": "Sample Title",
            "content": "Sample content",
            "last_modified": datetime.utcnow()
        }
        
    async def _get_previous_content(self, db: AsyncSession, url: str) -> Optional[Dict[str, Any]]:
        """Get previous content snapshot"""
        # Implementation would query database
        return None
        
    async def _detect_content_changes(self, old_content: Dict[str, Any], new_content: Dict[str, Any]) -> List[str]:
        """Detect changes between content versions"""
        changes = []
        
        if old_content.get("title") != new_content.get("title"):
            changes.append("Title changed")
            
        # Additional change detection logic
        return changes
        
    async def _store_content_snapshot(self, db: AsyncSession, url: str, content: Dict[str, Any]):
        """Store content snapshot for monitoring"""
        # Implementation would save to database
        pass
        
    async def _research_competitor_keywords(self, domain: str) -> List[Dict[str, Any]]:
        """Research competitor keywords"""
        # Mock implementation - would call actual SEO APIs
        return [
            {
                "keyword": "example keyword",
                "search_volume": 1000,
                "difficulty": 45.0,
                "position": 3,
                "url": f"https://{domain}/example-page"
            }
        ]
        
    async def _calculate_keyword_opportunity(
        self,
        keyword_data: Dict[str, Any],
        min_volume: int,
        max_difficulty: float
    ) -> float:
        """Calculate keyword opportunity score"""
        score = 50.0  # Base score
        
        # Adjust for search volume
        volume = keyword_data.get("search_volume", 0)
        if volume >= min_volume:
            score += min(30, volume / 1000 * 10)
            
        # Adjust for difficulty
        difficulty = keyword_data.get("difficulty", 100)
        if difficulty <= max_difficulty:
            score += (max_difficulty - difficulty) / max_difficulty * 20
            
        return min(100, score)
        
    async def _get_keyword_recommendation(self, keyword_data: Dict[str, Any]) -> str:
        """Get recommendation for keyword"""
        position = keyword_data.get("position", 100)
        
        if position <= 3:
            return "Create superior content to outrank"
        elif position <= 10:
            return "Optimize existing content or create new page"
        else:
            return "Create comprehensive content targeting this keyword"
