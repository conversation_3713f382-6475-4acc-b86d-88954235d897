# GridSpoke CI/CD Pipeline
name: GridSpoke Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  PYTHON_VERSION: "3.11"
  NODE_VERSION: "18"
  DOCKER_COMPOSE_VERSION: "2.21.0"

jobs:
  lint:
    name: Code Linting
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install black isort flake8 mypy
          pip install -r api/requirements.txt

      - name: Run Black formatter check
        run: black --check api/ workers/ tests/

      - name: Run isort import sorter check  
        run: isort --check-only api/ workers/ tests/

      - name: Run Flake8 linter
        run: flake8 api/ workers/ tests/

      - name: Run MyPy type checker
        run: mypy api/ workers/

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Node.js dependencies
        run: |
          cd frontend
          npm ci

      - name: Run ESLint
        run: |
          cd frontend
          npm run lint

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: lint

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: gridspoke_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r api/requirements.txt
          pip install -r workers/requirements.txt
          pip install pytest pytest-cov pytest-asyncio pytest-mock httpx

      - name: Set up test environment
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/gridspoke_test
          REDIS_URL: redis://localhost:6379/0
          SECRET_KEY: test_secret_key_for_testing
          OPENROUTER_API_KEY: test_openrouter_key
        run: |
          # Create test database tables
          cd api
          python -c "
          import asyncio
          from core.database import init_db
          asyncio.run(init_db())
          "

      - name: Run API unit tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/gridspoke_test
          REDIS_URL: redis://localhost:6379/0
          SECRET_KEY: test_secret_key_for_testing
          OPENROUTER_API_KEY: test_openrouter_key
          TESTING: true
        run: |
          pytest tests/unit/test_api.py -v --cov=api --cov-report=xml

      - name: Run AI Agents unit tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/gridspoke_test
          REDIS_URL: redis://localhost:6379/0
          SECRET_KEY: test_secret_key_for_testing
          OPENROUTER_API_KEY: test_openrouter_key
          TESTING: true
        run: |
          pytest tests/unit/test_agents.py -v --cov=api/agents --cov-report=xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage.xml
          flags: unit-tests
          name: codecov-umbrella

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: lint

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: gridspoke_integration_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r api/requirements.txt
          pip install -r workers/requirements.txt
          pip install pytest pytest-asyncio pytest-mock celery[redis]

      - name: Start Celery worker for integration tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/gridspoke_integration_test
          REDIS_URL: redis://localhost:6379/1
          SECRET_KEY: test_secret_key_for_integration
          OPENROUTER_API_KEY: test_openrouter_key
          TESTING: true
        run: |
          cd workers
          celery -A celery_app worker --loglevel=info --detach

      - name: Run integration tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/gridspoke_integration_test
          REDIS_URL: redis://localhost:6379/1
          SECRET_KEY: test_secret_key_for_integration
          OPENROUTER_API_KEY: test_openrouter_key
          TESTING: true
        run: |
          pytest tests/integration/test_workflows.py -v --cov=api --cov=workers

  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: lint

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: gridspoke_security_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r api/requirements.txt
          pip install pytest pytest-asyncio safety bandit

      - name: Run security vulnerability scan
        run: |
          safety check --json --output safety-report.json || true
          bandit -r api/ workers/ -f json -o bandit-report.json || true

      - name: Run security tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/gridspoke_security_test
          REDIS_URL: redis://localhost:6379/2
          SECRET_KEY: test_secret_key_for_security
          OPENROUTER_API_KEY: test_openrouter_key
          TESTING: true
        run: |
          pytest tests/security/test_security.py -v

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            safety-report.json
            bandit-report.json

  websocket-tests:
    name: WebSocket Tests
    runs-on: ubuntu-latest
    needs: lint

    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r api/requirements.txt
          pip install pytest pytest-asyncio websockets

      - name: Run WebSocket tests
        env:
          REDIS_URL: redis://localhost:6379/3
          SECRET_KEY: test_secret_key_for_websocket
          TESTING: true
        run: |
          pytest tests/websocket/test_websocket.py -v

  database-tests:
    name: Database Migration Tests
    runs-on: ubuntu-latest
    needs: lint

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: gridspoke_migration_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r api/requirements.txt
          pip install pytest alembic

      - name: Run database migration tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/gridspoke_migration_test
          SECRET_KEY: test_secret_key_for_migrations
          TESTING: true
        run: |
          pytest tests/database/test_migrations.py -v

  frontend-tests:
    name: Frontend JavaScript Tests
    runs-on: ubuntu-latest
    needs: lint

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Node.js dependencies
        run: |
          cd frontend
          npm ci

      - name: Run Jest tests
        run: |
          cd frontend
          npm test -- --coverage --watchAll=false

      - name: Upload frontend coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./frontend/coverage/lcov.info
          flags: frontend-tests
          name: codecov-frontend

  wordpress-plugin-tests:
    name: WordPress Plugin Tests
    runs-on: ubuntu-latest
    needs: lint

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root_password
          MYSQL_DATABASE: wordpress_test
          MYSQL_USER: wp_user
          MYSQL_PASSWORD: wp_password
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
        ports:
          - 3306:3306

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'
          extensions: mysqli, zip
          tools: composer, phpunit

      - name: Install WordPress test environment
        run: |
          # Install WordPress test suite
          bash wordpress-plugin/bin/install-wp-tests.sh wordpress_test wp_user wp_password localhost latest

      - name: Run WordPress plugin tests
        run: |
          cd wordpress-plugin
          phpunit --configuration phpunit.xml.dist

  load-tests:
    name: Load Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install load testing dependencies
        run: |
          python -m pip install --upgrade pip
          pip install locust

      - name: Start GridSpoke services
        run: |
          # Start services with Docker Compose
          docker-compose -f docker-compose.test.yml up -d
          
          # Wait for services to be ready
          sleep 30
          
          # Health check
          curl -f http://localhost:8000/health || exit 1

      - name: Run load tests
        run: |
          cd tests/load
          locust -f locustfile.py --headless --users 50 --spawn-rate 5 --run-time 5m --host http://localhost:8000

      - name: Collect load test results
        if: always()
        run: |
          docker-compose -f docker-compose.test.yml logs > load-test-logs.txt

      - name: Upload load test artifacts
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: load-test-results
          path: |
            load-test-logs.txt
            tests/load/*.html

  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: github.event_name == 'push'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install E2E dependencies
        run: |
          cd frontend
          npm ci
          npx playwright install

      - name: Start GridSpoke services
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30

      - name: Run E2E tests
        run: |
          cd frontend
          npm run test:e2e

      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: |
            frontend/test-results/
            frontend/playwright-report/

  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, security-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push API image
        uses: docker/build-push-action@v5
        with:
          context: ./api
          push: true
          tags: |
            gridspoke/api:latest
            gridspoke/api:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Worker image
        uses: docker/build-push-action@v5
        with:
          context: ./workers
          push: true
          tags: |
            gridspoke/workers:latest
            gridspoke/workers:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          push: true
          tags: |
            gridspoke/frontend:latest
            gridspoke/frontend:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /opt/gridspoke
            docker-compose pull
            docker-compose up -d
            docker-compose exec api alembic upgrade head

  notify-deployment:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: always()

    steps:
      - name: Notify Slack on success
        if: needs.deploy-staging.result == 'success'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: "✅ GridSpoke deployed successfully to staging!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on failure
        if: needs.deploy-staging.result == 'failure'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: "❌ GridSpoke deployment to staging failed!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

# Cleanup job for test artifacts
  cleanup:
    name: Cleanup Test Artifacts
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, security-tests, websocket-tests, database-tests, frontend-tests, wordpress-plugin-tests, load-tests, e2e-tests]
    if: always()

    steps:
      - name: Clean up test databases
        run: |
          # Cleanup commands would go here
          echo "Cleaning up test artifacts..."

      - name: Generate test summary
        run: |
          echo "## GridSpoke Test Summary" >> $GITHUB_STEP_SUMMARY
          echo "| Test Suite | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|------------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Unit Tests | ${{ needs.unit-tests.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Integration Tests | ${{ needs.integration-tests.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security Tests | ${{ needs.security-tests.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| WebSocket Tests | ${{ needs.websocket-tests.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Database Tests | ${{ needs.database-tests.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Frontend Tests | ${{ needs.frontend-tests.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| WordPress Plugin Tests | ${{ needs.wordpress-plugin-tests.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Load Tests | ${{ needs.load-tests.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| E2E Tests | ${{ needs.e2e-tests.result }} |" >> $GITHUB_STEP_SUMMARY
