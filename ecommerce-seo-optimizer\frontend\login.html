<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GridSpoke SEO Optimizer - Login</title>
    
    <!-- Tailwind CSS v3 CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js v3 CDN -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="css/styles.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gridspoke-primary': '#3B82F6',
                        'gridspoke-secondary': '#8B5CF6',
                        'gridspoke-success': '#10B981',
                        'gridspoke-warning': '#F59E0B',
                        'gridspoke-error': '#EF4444',
                        'gridspoke-dark': '#1F2937',
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen bg-gray-50 flex items-center justify-center">
    <div x-data="loginForm()" class="w-full max-w-md">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
            <div class="mx-auto h-12 w-12 flex items-center justify-center bg-gridspoke-primary rounded-lg mb-4">
                <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-gray-900">GridSpoke</h1>
            <p class="text-gray-600 mt-2">AI-Powered Ecommerce SEO Optimizer</p>
        </div>

        <!-- Login Form -->
        <div                         class="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Sign in to your account</h2>
            
            <!-- Error Message -->
            <div x-show="error" x-text="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4" x-transition></div>
            
            <!-- Success Message -->
            <div x-show="success" x-text="success" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md mb-4" x-transition></div>
            
            <form @submit.prevent="handleLogin" class="space-y-4">
                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email address</label>
                    <input 
                        type="email" 
                        id="email" 
                        x-model="email"
                        required
                        autocomplete="email"
                        placeholder="Enter your email"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-gridspoke-primary focus:border-gridspoke-primary"
                        :disabled="loading"
                    >
                </div>
                
                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <div class="relative">
                        <input 
                            :type="showPassword ? 'text' : 'password'" 
                            id="password" 
                            x-model="password"
                            required
                            autocomplete="current-password"
                            placeholder="Enter your password"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-gridspoke-primary focus:border-gridspoke-primary pr-10"
                            :disabled="loading"
                        >
                        <button 
                            type="button"
                            @click="showPassword = !showPassword"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center"
                            :disabled="loading"
                        >
                            <svg x-show="!showPassword" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            <svg x-show="showPassword" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L12 12m-3-3l6.364 6.364M21 21l-6.364-6.364m0 0L12 12m-3-3l3 3"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="remember" 
                            x-model="remember"
                            class="h-4 w-4 text-gridspoke-primary focus:ring-gridspoke-primary border-gray-300 rounded"
                            :disabled="loading"
                        >
                        <label for="remember" class="ml-2 block text-sm text-gray-700">Remember me</label>
                    </div>
                    
                    <a href="#" class="text-sm text-gridspoke-primary hover:text-blue-600">Forgot password?</a>
                </div>
                
                <!-- Submit Button -->
                <button 
                    type="submit" 
                    class="w-full px-4 py-2 bg-gridspoke-primary text-white rounded-md font-medium transition-colors duration-200 hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    :disabled="loading || !email || !password"
                    :class="{'opacity-50 cursor-not-allowed': loading || !email || !password}"
                >
                    <span x-show="!loading">Sign in</span>
                    <span x-show="loading" class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Signing in...
                    </span>
                </button>
            </form>
            
            <!-- Demo Credentials -->
            <div class="mt-6 p-4 bg-gray-50 rounded-md">
                <h3 class="text-sm font-medium text-gray-700 mb-2">Demo Credentials</h3>
                <p class="text-xs text-gray-600 mb-1">Email: <EMAIL></p>
                <p class="text-xs text-gray-600">Password: test12345</p>
            </div>
        </div>
        
        <!-- Sign Up Link -->
        <div class="text-center mt-6">
            <p class="text-sm text-gray-600">
                Don't have an account? 
                <a href="#" class="text-gridspoke-primary hover:text-blue-600 font-medium">Contact sales</a>
            </p>
        </div>
    </div>

    <!-- Scripts with aggressive cache busting -->
    <script>
        const CB = Date.now() + Math.random().toString(36).substr(2, 9);
        document.write(`<script src="js/utils.js?v=${CB}&cb=${Date.now()}"><\/script>`);
        document.write(`<script src="js/auth.js?v=${CB}&cb=${Date.now()}"><\/script>`);
    </script>
    
    <script>
        function loginForm() {
            return {
                email: '',
                password: '',
                remember: false,
                showPassword: false,
                loading: false,
                error: '',
                success: '',
                
                async handleLogin() {
                    this.error = '';
                    this.success = '';
                    this.loading = true;
                    
                    try {
                        await Auth.login(this.email, this.password, this.remember);
                        this.success = 'Login successful! Redirecting...';
                        
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1000);
                        
                    } catch (error) {
                        this.error = error.message || 'Login failed. Please try again.';
                    } finally {
                        this.loading = false;
                    }
                }
            }
        }
        
        // Check if already logged in
        (async () => {
            await Auth.init();
            if (Auth.isAuthenticated()) {
                window.location.href = 'dashboard.html';
            }
        })();
    </script>
</body>
</html>
