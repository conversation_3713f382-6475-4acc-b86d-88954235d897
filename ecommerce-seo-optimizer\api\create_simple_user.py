#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create a simple test user for dashboard testing.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database import get_db_session
from crud.user import user_crud
from schemas.user import UserCreate


async def create_simple_user():
    """Create a simple test user with known credentials."""
    
    email = "<EMAIL>"
    password = "test12345"  # Must be at least 8 characters
    
    try:
        # Get database session
        async for db in get_db_session():
            # Delete existing user if exists
            existing_user = await user_crud.get_by_email(db, email=email)
            if existing_user:
                await user_crud.remove(db, id=existing_user.id)
                print(f"Deleted existing user: {email}")
            
            # Create new user
            user_in = UserCreate(
                email=email,
                password=password,
                full_name="Test User",
                is_active=True,
                is_superuser=False,
                is_verified=True
            )
            
            user = await user_crud.create(db, obj_in=user_in)
            print(f"✅ Created user successfully!")
            print(f"📧 Email: {user.email}")
            print(f"🔐 Password: {password}")
            print(f"🆔 ID: {user.id}")
            print(f"✨ Active: {user.is_active}")
            break
            
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(create_simple_user())
