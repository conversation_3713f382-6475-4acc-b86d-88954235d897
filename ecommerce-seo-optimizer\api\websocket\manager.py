"""
WebSocket Manager for GridSpoke Ecommerce SEO Optimizer
Handles real-time communication for task progress updates and system notifications.
"""

import json
import asyncio
import logging
from typing import Dict, Set, Any, Optional, List
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
import redis.asyncio as redis

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        # Store active connections by various categories
        self.active_connections: Dict[str, WebSocket] = {}
        self.task_connections: Dict[str, Set[str]] = {}  # task_id -> set of connection_ids
        self.store_connections: Dict[str, Set[str]] = {}  # store_id -> set of connection_ids
        self.user_connections: Dict[str, Set[str]] = {}  # user_id -> set of connection_ids
        
        # Connection metadata
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Redis client for pub/sub
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub_task: Optional[asyncio.Task] = None
    
    async def initialize_redis(self):
        """Initialize Redis connection for pub/sub."""
        try:
            from core.config import get_settings
            settings = get_settings()
            redis_url = settings.REDIS_URL
            
            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            
            # Start pub/sub listener
            self.pubsub_task = asyncio.create_task(self._listen_for_updates())
            
            logger.info(f"WebSocket manager Redis connection initialized using URL: {redis_url}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis for WebSocket manager: {str(e)}")
    
    async def _listen_for_updates(self):
        """Listen for Redis pub/sub updates and broadcast to connected clients."""
        try:
            pubsub = self.redis_client.pubsub()
            await pubsub.psubscribe("task_updates:*")
            
            async for message in pubsub.listen():
                if message['type'] == 'pmessage':
                    try:
                        # Extract task_id from channel
                        channel = message['channel']
                        task_id = channel.split(':')[1]
                        
                        # Parse message data
                        data = json.loads(message['data'])
                        
                        # Broadcast to all connections listening to this task
                        await self._broadcast_task_update(task_id, data)
                        
                    except Exception as e:
                        logger.error(f"Error processing pub/sub message: {str(e)}")
                        
        except Exception as e:
            logger.error(f"Error in pub/sub listener: {str(e)}")
    
    async def connect(
        self,
        websocket: WebSocket,
        connection_id: str,
        user_id: str,
        store_id: Optional[str] = None,
        task_id: Optional[str] = None
    ):
        """
        Accept a new WebSocket connection and register it.
        
        Args:
            websocket: WebSocket connection
            connection_id: Unique connection identifier
            user_id: User ID
            store_id: Optional store ID for store-specific updates
            task_id: Optional task ID for task-specific updates
        """
        await websocket.accept()
        
        # Store the connection
        self.active_connections[connection_id] = websocket
        
        # Store metadata
        self.connection_metadata[connection_id] = {
            'user_id': user_id,
            'store_id': store_id,
            'task_id': task_id,
            'connected_at': datetime.utcnow().isoformat(),
            'last_ping': datetime.utcnow().isoformat()
        }
        
        # Register for user updates
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(connection_id)
        
        # Register for store updates
        if store_id:
            if store_id not in self.store_connections:
                self.store_connections[store_id] = set()
            self.store_connections[store_id].add(connection_id)
        
        # Register for task updates
        if task_id:
            if task_id not in self.task_connections:
                self.task_connections[task_id] = set()
            self.task_connections[task_id].add(connection_id)
        
        logger.info(f"WebSocket connected: {connection_id} (user: {user_id})")
        
        # Send welcome message
        await self.send_personal_message(connection_id, {
            'type': 'connection_established',
            'connection_id': connection_id,
            'timestamp': datetime.utcnow().isoformat()
        })
    
    def disconnect(self, connection_id: str):
        """
        Remove a WebSocket connection.
        
        Args:
            connection_id: Connection identifier to remove
        """
        if connection_id not in self.active_connections:
            return
        
        # Get metadata before removing
        metadata = self.connection_metadata.get(connection_id, {})
        user_id = metadata.get('user_id')
        store_id = metadata.get('store_id')
        task_id = metadata.get('task_id')
        
        # Remove from active connections
        del self.active_connections[connection_id]
        del self.connection_metadata[connection_id]
        
        # Remove from user connections
        if user_id and user_id in self.user_connections:
            self.user_connections[user_id].discard(connection_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        # Remove from store connections
        if store_id and store_id in self.store_connections:
            self.store_connections[store_id].discard(connection_id)
            if not self.store_connections[store_id]:
                del self.store_connections[store_id]
        
        # Remove from task connections
        if task_id and task_id in self.task_connections:
            self.task_connections[task_id].discard(connection_id)
            if not self.task_connections[task_id]:
                del self.task_connections[task_id]
        
        logger.info(f"WebSocket disconnected: {connection_id}")
    
    async def send_personal_message(self, connection_id: str, message: Dict[str, Any]):
        """
        Send a message to a specific connection.
        
        Args:
            connection_id: Target connection ID
            message: Message to send
        """
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {connection_id}: {str(e)}")
                # Remove dead connection
                self.disconnect(connection_id)
    
    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """
        Send a message to all connections for a specific user.
        
        Args:
            user_id: Target user ID
            message: Message to send
        """
        if user_id in self.user_connections:
            connection_ids = self.user_connections[user_id].copy()
            
            for connection_id in connection_ids:
                await self.send_personal_message(connection_id, message)
    
    async def send_to_store(self, store_id: str, message: Dict[str, Any]):
        """
        Send a message to all connections for a specific store.
        
        Args:
            store_id: Target store ID
            message: Message to send
        """
        if store_id in self.store_connections:
            connection_ids = self.store_connections[store_id].copy()
            
            for connection_id in connection_ids:
                await self.send_personal_message(connection_id, message)
    
    async def send_task_update(self, task_id: str, update_data: Dict[str, Any]):
        """
        Send a task update to all connections listening to that task.
        
        Args:
            task_id: Task ID
            update_data: Update data to send
        """
        if task_id in self.task_connections:
            connection_ids = self.task_connections[task_id].copy()
            
            message = {
                'type': 'task_update',
                'task_id': task_id,
                'data': update_data,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            for connection_id in connection_ids:
                await self.send_personal_message(connection_id, message)
    
    async def _broadcast_task_update(self, task_id: str, update_data: Dict[str, Any]):
        """
        Internal method to broadcast task updates from Redis pub/sub.
        
        Args:
            task_id: Task ID
            update_data: Update data from Redis
        """
        await self.send_task_update(task_id, update_data)
    
    async def broadcast_system_message(self, message: Dict[str, Any], user_ids: Optional[List[str]] = None):
        """
        Broadcast a system message to all or specific users.
        
        Args:
            message: System message to broadcast
            user_ids: Optional list of specific user IDs to send to
        """
        system_message = {
            'type': 'system_message',
            'data': message,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        if user_ids:
            # Send to specific users
            for user_id in user_ids:
                await self.send_to_user(user_id, system_message)
        else:
            # Send to all connected users
            for connection_id in list(self.active_connections.keys()):
                await self.send_personal_message(connection_id, system_message)
    
    async def send_progress_update(self, task_id: str, progress_data: Dict[str, Any]):
        """
        Send a progress update for a specific task.
        
        Args:
            task_id: Task ID
            progress_data: Progress information
        """
        update_message = {
            'type': 'progress_update',
            'task_id': task_id,
            'progress': progress_data,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        await self.send_task_update(task_id, update_message)
    
    async def send_optimization_complete(self, task_id: str, result_data: Dict[str, Any]):
        """
        Send optimization completion notification.
        
        Args:
            task_id: Task ID
            result_data: Optimization results
        """
        completion_message = {
            'type': 'optimization_complete',
            'task_id': task_id,
            'result': result_data,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        await self.send_task_update(task_id, completion_message)
    
    async def send_error_notification(self, task_id: str, error_data: Dict[str, Any]):
        """
        Send error notification for a task.
        
        Args:
            task_id: Task ID
            error_data: Error information
        """
        error_message = {
            'type': 'task_error',
            'task_id': task_id,
            'error': error_data,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        await self.send_task_update(task_id, error_message)
    
    async def handle_ping(self, connection_id: str):
        """
        Handle ping message from client.
        
        Args:
            connection_id: Connection ID that sent ping
        """
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]['last_ping'] = datetime.utcnow().isoformat()
        
        await self.send_personal_message(connection_id, {
            'type': 'pong',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    async def handle_subscribe_task(self, connection_id: str, task_id: str):
        """
        Subscribe a connection to task updates.
        
        Args:
            connection_id: Connection ID
            task_id: Task ID to subscribe to
        """
        if task_id not in self.task_connections:
            self.task_connections[task_id] = set()
        
        self.task_connections[task_id].add(connection_id)
        
        # Update connection metadata
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]['task_id'] = task_id
        
        await self.send_personal_message(connection_id, {
            'type': 'subscription_confirmed',
            'task_id': task_id,
            'timestamp': datetime.utcnow().isoformat()
        })
    
    async def handle_unsubscribe_task(self, connection_id: str, task_id: str):
        """
        Unsubscribe a connection from task updates.
        
        Args:
            connection_id: Connection ID
            task_id: Task ID to unsubscribe from
        """
        if task_id in self.task_connections:
            self.task_connections[task_id].discard(connection_id)
            
            if not self.task_connections[task_id]:
                del self.task_connections[task_id]
        
        # Update connection metadata
        if connection_id in self.connection_metadata:
            if self.connection_metadata[connection_id].get('task_id') == task_id:
                self.connection_metadata[connection_id]['task_id'] = None
        
        await self.send_personal_message(connection_id, {
            'type': 'unsubscription_confirmed',
            'task_id': task_id,
            'timestamp': datetime.utcnow().isoformat()
        })
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about current connections.
        
        Returns:
            Dict containing connection statistics
        """
        return {
            'total_connections': len(self.active_connections),
            'unique_users': len(self.user_connections),
            'stores_with_connections': len(self.store_connections),
            'tasks_being_monitored': len(self.task_connections),
            'connections_by_user': {
                user_id: len(connections) 
                for user_id, connections in self.user_connections.items()
            }
        }
    
    async def cleanup_dead_connections(self):
        """
        Clean up dead connections by sending ping and removing unresponsive ones.
        """
        dead_connections = []
        
        for connection_id, websocket in self.active_connections.items():
            try:
                await websocket.ping()
            except Exception:
                dead_connections.append(connection_id)
        
        for connection_id in dead_connections:
            logger.info(f"Removing dead connection: {connection_id}")
            self.disconnect(connection_id)
        
        if dead_connections:
            logger.info(f"Cleaned up {len(dead_connections)} dead connections")
    
    async def shutdown(self):
        """
        Shutdown the WebSocket manager and close all connections.
        """
        # Cancel pub/sub task
        if self.pubsub_task:
            self.pubsub_task.cancel()
            try:
                await self.pubsub_task
            except asyncio.CancelledError:
                pass
        
        # Close Redis connection
        if self.redis_client:
            await self.redis_client.close()
        
        # Close all WebSocket connections
        for connection_id in list(self.active_connections.keys()):
            try:
                websocket = self.active_connections[connection_id]
                await websocket.close()
            except Exception as e:
                logger.error(f"Error closing connection {connection_id}: {str(e)}")
        
        # Clear all data structures
        self.active_connections.clear()
        self.task_connections.clear()
        self.store_connections.clear()
        self.user_connections.clear()
        self.connection_metadata.clear()
        
        logger.info("WebSocket manager shut down")

# Global connection manager instance
manager = ConnectionManager()
