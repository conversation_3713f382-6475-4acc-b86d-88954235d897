import asyncio
from core.database import get_session
from crud.user import user_crud

async def check_user():
    async for db in get_session():
        user = await user_crud.get_by_email(db, email="<EMAIL>")
        if user:
            print(f"User found: {user.email}")
            print(f"User active: {user.is_active}")
            print(f"User verified: {user.is_verified}")
            print(f"User ID: {user.id}")
        else:
            print("User not found")
        break

if __name__ == "__main__":
    asyncio.run(check_user())
