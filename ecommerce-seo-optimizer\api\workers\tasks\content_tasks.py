"""
Content generation tasks for GridSpoke.
Provides Celery tasks for generating various types of content.
"""
import logging
from typing import Dict, Any, List, Optional
from celery import Task
from datetime import datetime

from workers.tasks.ai_service import optimize_product_content

logger = logging.getLogger(__name__)

# Import the Celery app
from workers.celery_app import celery_app

class ContentTask(Task):
    """Base class for content generation tasks."""
    pass

@celery_app.task(base=ContentTask, bind=True)
def generate_blog_post(self, topic: str, store_id: str, target_keywords: List[str] = None, word_count: int = 500) -> Dict[str, Any]:
    """
    Generate an AI blog post.
    
    Args:
        topic: Blog post topic
        store_id: Store ID
        target_keywords: Target keywords for SEO
        word_count: Desired word count
        
    Returns:
        Dict with blog post content
    """
    try:
        logger.info(f"Generating blog post for topic: {topic}")
        
        # Generate blog post content using AI
        content_data = {
            "topic": topic,
            "keywords": target_keywords or [],
            "word_count": word_count
        }
        
        optimized_content = optimize_product_content(
            product_data=content_data,
            optimization_options={
                "content_type": "blog_post",
                "tone": "informative",
                "audience": "ecommerce_shoppers"
            }
        )
        
        result = {
            "status": "success",
            "topic": topic,
            "content": optimized_content.get("content", ""),
            "title": optimized_content.get("title", f"Guide to {topic}"),
            "meta_description": optimized_content.get("meta_description", ""),
            "keywords": optimized_content.get("keywords", target_keywords or []),
            "word_count": len(optimized_content.get("content", "").split()),
            "generated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Blog post generated successfully for topic: {topic}")
        return result
        
    except Exception as e:
        logger.error(f"Failed to generate blog post for topic {topic}: {str(e)}")
        return {
            "status": "failed",
            "topic": topic,
            "error": str(e),
            "generated_at": datetime.utcnow().isoformat()
        }

@celery_app.task(base=ContentTask, bind=True)
def generate_product_faqs(self, product_ids: List[str], store_id: str) -> Dict[str, Any]:
    """
    Generate FAQ content for products.
    
    Args:
        product_ids: List of product IDs
        store_id: Store ID
        
    Returns:
        Dict with FAQ content
    """
    try:
        logger.info(f"Generating FAQs for {len(product_ids)} products")
        
        # Generate FAQ content using AI
        faq_content = []
        
        for product_id in product_ids:
            content_data = {
                "product_id": product_id,
                "content_type": "faqs"
            }
            
            optimized_content = optimize_product_content(
                product_data=content_data,
                optimization_options={
                    "content_type": "product_faqs",
                    "question_count": 5
                }
            )
            
            faq_content.append({
                "product_id": product_id,
                "questions": optimized_content.get("faqs", [])
            })
        
        result = {
            "status": "success",
            "product_count": len(product_ids),
            "faqs": faq_content,
            "generated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"FAQs generated successfully for {len(product_ids)} products")
        return result
        
    except Exception as e:
        logger.error(f"Failed to generate FAQs for products: {str(e)}")
        return {
            "status": "failed",
            "product_count": len(product_ids),
            "error": str(e),
            "generated_at": datetime.utcnow().isoformat()
        }

@celery_app.task(base=ContentTask, bind=True)
def generate_buyers_guide(self, category: str, store_id: str, products: List[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Generate a buyer's guide for a product category.
    
    Args:
        category: Product category
        store_id: Store ID
        products: List of products in the category
        
    Returns:
        Dict with buyer's guide content
    """
    try:
        logger.info(f"Generating buyer's guide for category: {category}")
        
        # Generate buyer's guide content using AI
        content_data = {
            "category": category,
            "products": products or [],
            "content_type": "buyers_guide"
        }
        
        optimized_content = optimize_product_content(
            product_data=content_data,
            optimization_options={
                "content_type": "buyers_guide",
                "sections": ["introduction", "key_features", "comparison", "recommendations"]
            }
        )
        
        result = {
            "status": "success",
            "category": category,
            "content": optimized_content.get("content", ""),
            "title": optimized_content.get("title", f"The Ultimate Buyer's Guide to {category}"),
            "meta_description": optimized_content.get("meta_description", ""),
            "keywords": optimized_content.get("keywords", []),
            "sections": optimized_content.get("sections", []),
            "generated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Buyer's guide generated successfully for category: {category}")
        return result
        
    except Exception as e:
        logger.error(f"Failed to generate buyer's guide for category {category}: {str(e)}")
        return {
            "status": "failed",
            "category": category,
            "error": str(e),
            "generated_at": datetime.utcnow().isoformat()
        }

@celery_app.task(base=ContentTask, bind=True)
def generate_meta_descriptions(self, product_ids: List[str], store_id: str) -> Dict[str, Any]:
    """
    Generate meta descriptions for products.
    
    Args:
        product_ids: List of product IDs
        store_id: Store ID
        
    Returns:
        Dict with meta descriptions
    """
    try:
        logger.info(f"Generating meta descriptions for {len(product_ids)} products")
        
        # Generate meta descriptions using AI
        meta_descriptions = []
        
        for product_id in product_ids:
            content_data = {
                "product_id": product_id,
                "content_type": "meta_description"
            }
            
            optimized_content = optimize_product_content(
                product_data=content_data,
                optimization_options={
                    "content_type": "meta_description",
                    "max_length": 160
                }
            )
            
            meta_descriptions.append({
                "product_id": product_id,
                "meta_description": optimized_content.get("meta_description", "")
            })
        
        result = {
            "status": "success",
            "product_count": len(product_ids),
            "meta_descriptions": meta_descriptions,
            "generated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Meta descriptions generated successfully for {len(product_ids)} products")
        return result
        
    except Exception as e:
        logger.error(f"Failed to generate meta descriptions for products: {str(e)}")
        return {
            "status": "failed",
            "product_count": len(product_ids),
            "error": str(e),
            "generated_at": datetime.utcnow().isoformat()
        }