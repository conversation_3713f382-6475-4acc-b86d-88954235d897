# Phase 9: Testing Suite Research for GridSpoke Ecommerce SEO Optimizer

## Overview
Comprehensive testing strategy for GridSpoke's AI-powered ecommerce SEO optimization platform. This research covers unit testing, integration testing, and load testing best practices for a FastAPI + Celery + AI microservices architecture.

## Testing Architecture Strategy

### 1. Testing Pyramid for GridSpoke
```
    E2E/Load Tests (5%)
    ├── WordPress Plugin Integration
    ├── Full Optimization Workflow
    └── Performance Under Load
    
  Integration Tests (25%)
  ├── FastAPI + Database Integration
  ├── Celery Task Processing
  ├── AI Model Integration (Mirascope)
  ├── Redis Cache Testing
  └── External API Testing (OpenRouter)
  
Unit Tests (70%)
├── Business Logic (SEO optimization)
├── Database Models & Services
├── AI Agent Functions
├── Utility Functions
└── Configuration & Validators
```

## 1. Pytest Framework Foundation

### Core Testing Configuration
```python
# conftest.py - GridSpoke specific test configuration
import pytest
import asyncio
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from api.main import app
from api.database import get_db, Base
from api.config import settings

# Test Database Setup
SQLALCHEMY_TEST_DATABASE_URL = "sqlite:///./test_gridspoke.db"
test_engine = create_engine(
    SQLALCHEMY_TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine"""
    Base.metadata.create_all(bind=test_engine)
    yield test_engine
    Base.metadata.drop_all(bind=test_engine)

@pytest.fixture
def db_session(db_engine):
    """Create test database session"""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    yield session
    session.close()
    transaction.rollback()
    connection.close()

@pytest.fixture
def test_client(db_session):
    """FastAPI test client with database override"""
    def override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as client:
        yield client
    app.dependency_overrides.clear()
```

### GridSpoke-Specific Pytest Fixtures
```python
# fixtures/gridspoke_fixtures.py
@pytest.fixture
def sample_product():
    """Sample product data for testing"""
    return {
        "id": "prod_123",
        "name": "Wireless Bluetooth Headphones",
        "description": "Basic wireless headphones",
        "price": 99.99,
        "category": "Electronics",
        "brand": "TechBrand",
        "keywords": ["wireless", "bluetooth", "headphones"]
    }

@pytest.fixture
def sample_store():
    """Sample WordPress store for testing"""
    return {
        "id": "store_456",
        "name": "Tech Store",
        "url": "https://techstore.example.com",
        "platform": "woocommerce",
        "api_key": "test_api_key",
        "webhook_secret": "test_webhook_secret"
    }

@pytest.fixture
def mock_openrouter_response():
    """Mock OpenRouter API response"""
    return {
        "choices": [{
            "message": {
                "content": json.dumps({
                    "title": "Premium Wireless Bluetooth Headphones - Superior Sound Quality",
                    "description": "Experience crystal-clear audio with our premium wireless Bluetooth headphones...",
                    "meta_title": "Premium Wireless Bluetooth Headphones | TechBrand",
                    "meta_description": "Premium wireless Bluetooth headphones with superior sound quality...",
                    "alt_text": "Premium wireless Bluetooth headphones with sleek black design",
                    "keywords": ["premium wireless headphones", "bluetooth headphones", "superior sound quality"]
                })
            }
        }],
        "usage": {
            "prompt_tokens": 150,
            "completion_tokens": 200,
            "total_tokens": 350
        }
    }
```

## 2. FastAPI Testing Best Practices

### API Endpoint Testing Strategy
```python
# tests/test_api/test_optimization_endpoints.py
import pytest
from fastapi import status
from unittest.mock import patch, MagicMock

class TestOptimizationEndpoints:
    """Test GridSpoke optimization API endpoints"""
    
    def test_optimize_single_product_success(self, test_client, sample_product, mock_openrouter_response):
        """Test successful single product optimization"""
        with patch('api.services.ai_service.OpenRouterClient.generate') as mock_generate:
            mock_generate.return_value = mock_openrouter_response
            
            response = test_client.post(
                "/api/v1/optimize/product",
                json=sample_product,
                headers={"Authorization": "Bearer test_token"}
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "optimization_id" in data
            assert "status" in data
            assert data["status"] == "completed"
    
    def test_optimize_single_product_auth_required(self, test_client, sample_product):
        """Test authentication required for optimization"""
        response = test_client.post("/api/v1/optimize/product", json=sample_product)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_bulk_optimization_job_creation(self, test_client, sample_store):
        """Test bulk optimization job creation"""
        job_data = {
            "store_id": sample_store["id"],
            "product_ids": ["prod_1", "prod_2", "prod_3"],
            "optimization_type": "full"
        }
        
        with patch('api.tasks.optimization_tasks.optimize_products_bulk.delay') as mock_task:
            mock_task.return_value = MagicMock(id="task_123")
            
            response = test_client.post(
                "/api/v1/optimize/bulk",
                json=job_data,
                headers={"Authorization": "Bearer test_token"}
            )
            
            assert response.status_code == status.HTTP_202_ACCEPTED
            data = response.json()
            assert "job_id" in data
            assert data["status"] == "queued"

    @pytest.mark.parametrize("invalid_data,expected_error", [
        ({}, "store_id"),
        ({"store_id": ""}, "store_id"),
        ({"store_id": "valid", "product_ids": []}, "product_ids"),
    ])
    def test_bulk_optimization_validation(self, test_client, invalid_data, expected_error):
        """Test bulk optimization input validation"""
        response = test_client.post(
            "/api/v1/optimize/bulk",
            json=invalid_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert expected_error in response.json()["detail"][0]["loc"]
```

### Database Integration Testing
```python
# tests/test_database/test_models.py
import pytest
from api.models import Store, Product, OptimizationJob, OptimizationResult

class TestDatabaseModels:
    """Test GridSpoke database models and relationships"""
    
    def test_store_creation(self, db_session):
        """Test store model creation and validation"""
        store = Store(
            name="Test Store",
            url="https://teststore.com",
            platform="woocommerce",
            api_key="test_key"
        )
        db_session.add(store)
        db_session.commit()
        
        assert store.id is not None
        assert store.name == "Test Store"
        assert store.platform == "woocommerce"
        assert store.created_at is not None
    
    def test_optimization_job_lifecycle(self, db_session, sample_store):
        """Test optimization job creation and status updates"""
        store = Store(**sample_store)
        db_session.add(store)
        db_session.commit()
        
        job = OptimizationJob(
            store_id=store.id,
            product_count=5,
            optimization_type="full",
            status="queued"
        )
        db_session.add(job)
        db_session.commit()
        
        # Test status progression
        job.status = "processing"
        job.started_at = datetime.utcnow()
        db_session.commit()
        
        job.status = "completed"
        job.completed_at = datetime.utcnow()
        db_session.commit()
        
        assert job.id is not None
        assert job.status == "completed"
        assert job.started_at is not None
        assert job.completed_at is not None
    
    def test_optimization_results_relationship(self, db_session):
        """Test optimization results and job relationship"""
        # Create job
        job = OptimizationJob(
            store_id="store_123",
            product_count=1,
            optimization_type="full",
            status="completed"
        )
        db_session.add(job)
        db_session.commit()
        
        # Create result
        result = OptimizationResult(
            job_id=job.id,
            product_id="prod_123",
            original_title="Original Title",
            optimized_title="Optimized Title",
            seo_score_before=45.0,
            seo_score_after=85.0,
            ai_model_used="claude-3-opus",
            tokens_used=350,
            cost_usd=0.0105
        )
        db_session.add(result)
        db_session.commit()
        
        # Test relationship
        assert len(job.results) == 1
        assert job.results[0].product_id == "prod_123"
        assert job.results[0].seo_score_improvement == 40.0
```

## 3. Celery Task Testing

### Celery Test Configuration
```python
# conftest.py - Celery specific configuration
@pytest.fixture(scope='session')
def celery_config():
    """Celery configuration for testing"""
    return {
        'broker_url': 'redis://localhost:6379/15',  # Test Redis DB
        'result_backend': 'redis://localhost:6379/15',
        'task_always_eager': False,  # Test real async behavior
        'task_eager_propagates': True,
        'task_store_eager_result': True,
        'result_expires': 3600,
    }

@pytest.fixture(scope='session')
def celery_includes():
    """Include GridSpoke task modules"""
    return [
        'api.tasks.optimization_tasks',
        'api.tasks.sync_tasks',
        'api.tasks.notification_tasks',
    ]

@pytest.fixture(scope='session')
def celery_worker_parameters():
    """Celery worker parameters for testing"""
    return {
        'queues': ['optimization', 'sync', 'notifications'],
        'shutdown_timeout': 30,
    }
```

### Task Testing Strategies
```python
# tests/test_tasks/test_optimization_tasks.py
import pytest
from unittest.mock import patch, MagicMock
from celery.exceptions import Retry

from api.tasks.optimization_tasks import (
    optimize_single_product,
    optimize_products_bulk,
    update_product_content
)

class TestOptimizationTasks:
    """Test GridSpoke Celery optimization tasks"""
    
    def test_optimize_single_product_success(self, celery_app, celery_worker):
        """Test successful single product optimization task"""
        with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
            mock_optimize.return_value = {
                "title": "Optimized Title",
                "description": "Optimized Description",
                "meta_title": "Optimized Meta Title",
                "meta_description": "Optimized Meta Description",
                "alt_text": "Optimized Alt Text",
                "keywords": ["keyword1", "keyword2"]
            }
            
            result = optimize_single_product.delay(
                product_id="prod_123",
                store_id="store_456",
                optimization_type="full"
            )
            
            assert result.get(timeout=10) is not None
            mock_optimize.assert_called_once()
    
    def test_optimize_single_product_retry_on_failure(self, celery_app):
        """Test task retry on API failure"""
        with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
            with patch('api.tasks.optimization_tasks.optimize_single_product.retry') as mock_retry:
                mock_optimize.side_effect = Exception("API Error")
                mock_retry.side_effect = Retry()
                
                with pytest.raises(Retry):
                    optimize_single_product.apply(
                        args=["prod_123", "store_456", "full"]
                    )
                
                mock_retry.assert_called_once()
    
    def test_bulk_optimization_progress_tracking(self, celery_app, celery_worker):
        """Test bulk optimization with progress tracking"""
        product_ids = ["prod_1", "prod_2", "prod_3"]
        
        with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
            with patch('api.services.redis_service.update_job_progress') as mock_progress:
                mock_optimize.return_value = {"title": "Optimized"}
                
                result = optimize_products_bulk.delay(
                    store_id="store_456",
                    product_ids=product_ids,
                    optimization_type="full"
                )
                
                task_result = result.get(timeout=30)
                assert task_result["total_products"] == 3
                assert task_result["status"] == "completed"
                
                # Check progress was tracked
                assert mock_progress.call_count >= 3
    
    def test_task_with_database_session(self, celery_app, db_session):
        """Test task that requires database access"""
        with patch('api.database.get_db') as mock_get_db:
            mock_get_db.return_value = db_session
            
            # Test task that creates/updates database records
            result = update_product_content.apply(
                args=["prod_123", {"title": "New Title"}]
            )
            
            assert result.successful()
```

### Mock Strategy for External Dependencies
```python
# tests/test_tasks/test_external_integrations.py
import pytest
from unittest.mock import patch, MagicMock
import requests

class TestExternalIntegrations:
    """Test tasks that integrate with external services"""
    
    @patch('requests.post')
    def test_openrouter_api_integration(self, mock_post, celery_app):
        """Test OpenRouter API integration with mocking"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "choices": [{"message": {"content": "Optimized content"}}],
            "usage": {"total_tokens": 100}
        }
        mock_post.return_value = mock_response
        
        from api.services.ai_service import OpenRouterClient
        client = OpenRouterClient()
        result = client.generate("Test prompt")
        
        assert result is not None
        mock_post.assert_called_once()
    
    @patch('requests.get')
    @patch('requests.post')
    def test_wordpress_api_integration(self, mock_post, mock_get, celery_app):
        """Test WordPress API integration"""
        # Mock product fetch
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {
            "id": 123,
            "name": "Test Product",
            "description": "Test Description"
        }
        
        # Mock product update
        mock_post.return_value.status_code = 200
        mock_post.return_value.json.return_value = {"id": 123}
        
        from api.tasks.sync_tasks import sync_product_to_wordpress
        result = sync_product_to_wordpress.apply(
            args=["store_456", "prod_123", {"title": "Updated Title"}]
        )
        
        assert result.successful()
        mock_get.assert_called_once()
        mock_post.assert_called_once()
```

## 4. Load Testing Strategy

### Locust Performance Testing
```python
# tests/load_testing/locustfile.py
from locust import HttpUser, task, between
import json
import random

class GridSpokeLoadTest(HttpUser):
    """Load testing for GridSpoke optimization API"""
    
    wait_time = between(1, 3)
    
    def on_start(self):
        """Setup authentication"""
        self.auth_headers = {"Authorization": "Bearer test_token"}
        self.sample_products = [
            {"id": f"prod_{i}", "name": f"Product {i}", "price": random.uniform(10, 1000)}
            for i in range(100)
        ]
    
    @task(3)
    def optimize_single_product(self):
        """Test single product optimization endpoint"""
        product = random.choice(self.sample_products)
        
        with self.client.post(
            "/api/v1/optimize/product",
            json=product,
            headers=self.auth_headers,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Status: {response.status_code}")
    
    @task(1)
    def create_bulk_optimization(self):
        """Test bulk optimization job creation"""
        product_ids = [f"prod_{random.randint(1, 100)}" for _ in range(random.randint(5, 20))]
        
        job_data = {
            "store_id": "store_load_test",
            "product_ids": product_ids,
            "optimization_type": "full"
        }
        
        with self.client.post(
            "/api/v1/optimize/bulk",
            json=job_data,
            headers=self.auth_headers,
            catch_response=True
        ) as response:
            if response.status_code == 202:
                response.success()
            else:
                response.failure(f"Status: {response.status_code}")
    
    @task(2)
    def check_job_status(self):
        """Test job status endpoint"""
        job_id = f"job_{random.randint(1, 100)}"
        
        with self.client.get(
            f"/api/v1/jobs/{job_id}",
            headers=self.auth_headers,
            catch_response=True
        ) as response:
            if response.status_code in [200, 404]:  # 404 is acceptable for non-existent jobs
                response.success()
            else:
                response.failure(f"Status: {response.status_code}")

# Load testing scenarios
class OptimizationUser(GridSpokeLoadTest):
    """Heavy optimization user"""
    weight = 3
    
class MonitoringUser(HttpUser):
    """Light monitoring user"""
    weight = 1
    wait_time = between(5, 10)
    
    @task
    def health_check(self):
        self.client.get("/health")
    
    @task
    def metrics_check(self):
        self.client.get("/metrics")
```

### Performance Benchmarking
```python
# tests/performance/test_performance_benchmarks.py
import pytest
import time
import asyncio
from unittest.mock import patch

class TestPerformanceBenchmarks:
    """Performance benchmarks for GridSpoke components"""
    
    def test_single_optimization_performance(self, test_client, sample_product):
        """Test single optimization response time"""
        with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
            mock_optimize.return_value = {"title": "Optimized"}
            
            start_time = time.time()
            response = test_client.post(
                "/api/v1/optimize/product",
                json=sample_product,
                headers={"Authorization": "Bearer test_token"}
            )
            end_time = time.time()
            
            assert response.status_code == 200
            assert (end_time - start_time) < 2.0  # Should complete in under 2 seconds
    
    @pytest.mark.asyncio
    async def test_concurrent_optimizations(self, test_client, sample_product):
        """Test concurrent optimization handling"""
        with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
            mock_optimize.return_value = {"title": "Optimized"}
            
            # Create multiple concurrent requests
            tasks = []
            for i in range(10):
                product = {**sample_product, "id": f"prod_{i}"}
                task = asyncio.create_task(
                    self._async_post(test_client, "/api/v1/optimize/product", product)
                )
                tasks.append(task)
            
            start_time = time.time()
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            # All requests should succeed
            assert all(r.status_code == 200 for r in results)
            # Should handle concurrency efficiently
            assert (end_time - start_time) < 5.0
    
    async def _async_post(self, client, url, data):
        """Helper for async HTTP requests"""
        return client.post(
            url,
            json=data,
            headers={"Authorization": "Bearer test_token"}
        )
```

## 5. AI Model Testing Strategy

### Mirascope Integration Testing
```python
# tests/test_ai/test_mirascope_integration.py
import pytest
from unittest.mock import patch, MagicMock

from api.services.ai_service import (
    ProductOptimizer,
    SEOAnalyzer,
    ContentGenerator
)

class TestMirascapeIntegration:
    """Test Mirascope AI framework integration"""
    
    @patch('api.services.ai_service.OpenAICall.call')
    def test_product_optimizer_structured_output(self, mock_call):
        """Test ProductOptimizer returns structured output"""
        mock_call.return_value = MagicMock(
            content=json.dumps({
                "title": "Premium Wireless Bluetooth Headphones",
                "description": "Experience superior sound quality...",
                "meta_title": "Premium Wireless Bluetooth Headphones | TechStore",
                "meta_description": "Shop premium wireless headphones...",
                "alt_text": "Premium black wireless headphones",
                "keywords": ["premium headphones", "wireless audio", "bluetooth"]
            })
        )
        
        optimizer = ProductOptimizer()
        result = optimizer.optimize({
            "name": "Bluetooth Headphones",
            "description": "Basic headphones",
            "price": 99.99
        })
        
        assert "title" in result
        assert "description" in result
        assert "keywords" in result
        assert len(result["keywords"]) > 0
    
    @patch('api.services.ai_service.OpenAICall.call')
    def test_seo_analyzer_scoring(self, mock_call):
        """Test SEO analyzer scoring functionality"""
        mock_call.return_value = MagicMock(
            content=json.dumps({
                "overall_score": 85.5,
                "title_score": 90.0,
                "description_score": 80.0,
                "keyword_score": 85.0,
                "suggestions": [
                    "Add more specific keywords",
                    "Improve meta description length"
                ]
            })
        )
        
        analyzer = SEOAnalyzer()
        result = analyzer.analyze({
            "title": "Premium Headphones",
            "description": "Great sound quality headphones",
            "keywords": ["headphones", "audio"]
        })
        
        assert "overall_score" in result
        assert result["overall_score"] > 0
        assert "suggestions" in result
    
    def test_ai_service_error_handling(self):
        """Test AI service error handling and fallbacks"""
        with patch('api.services.ai_service.OpenAICall.call') as mock_call:
            mock_call.side_effect = Exception("API Error")
            
            optimizer = ProductOptimizer()
            
            # Should handle errors gracefully
            with pytest.raises(Exception):
                optimizer.optimize({"name": "Test Product"})
    
    @pytest.mark.parametrize("model_name,expected_provider", [
        ("anthropic/claude-3-opus", "anthropic"),
        ("openai/gpt-4", "openai"),
        ("meta-llama/llama-2-70b", "meta-llama"),
    ])
    def test_multi_model_support(self, model_name, expected_provider):
        """Test support for multiple AI models"""
        with patch('api.services.ai_service.OpenRouterClient') as mock_client:
            mock_response = MagicMock()
            mock_response.json.return_value = {
                "choices": [{"message": {"content": "Test response"}}]
            }
            mock_client.return_value.generate.return_value = mock_response
            
            optimizer = ProductOptimizer(model=model_name)
            result = optimizer.optimize({"name": "Test Product"})
            
            # Verify correct model was used
            mock_client.assert_called_once()
```

## 6. WordPress Plugin Testing

### Plugin Integration Tests
```python
# tests/test_wordpress/test_plugin_integration.py
import pytest
from unittest.mock import patch, MagicMock
import requests

class TestWordPressPluginIntegration:
    """Test GridSpoke WordPress plugin integration"""
    
    @patch('requests.post')
    def test_webhook_product_sync(self, mock_post, test_client):
        """Test product sync webhook from WordPress"""
        webhook_payload = {
            "action": "product_updated",
            "product": {
                "id": 123,
                "name": "Updated Product",
                "description": "Updated description",
                "price": 149.99
            },
            "store_id": "store_wp_123",
            "timestamp": "2025-01-01T12:00:00Z"
        }
        
        # Mock WordPress verification
        mock_post.return_value.status_code = 200
        
        response = test_client.post(
            "/api/v1/webhooks/wordpress",
            json=webhook_payload,
            headers={
                "X-WP-Signature": "test_signature",
                "Content-Type": "application/json"
            }
        )
        
        assert response.status_code == 200
        assert response.json()["status"] == "received"
    
    def test_webhook_authentication(self, test_client):
        """Test webhook signature verification"""
        invalid_payload = {"action": "test"}
        
        response = test_client.post(
            "/api/v1/webhooks/wordpress",
            json=invalid_payload,
            headers={"Content-Type": "application/json"}  # Missing signature
        )
        
        assert response.status_code == 401
    
    @patch('requests.get')
    def test_wordpress_api_connectivity(self, mock_get):
        """Test WordPress API connectivity check"""
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {
            "name": "Test Store",
            "url": "https://teststore.com"
        }
        
        from api.services.wordpress_service import WordPressConnector
        connector = WordPressConnector("https://teststore.com", "test_key")
        
        result = connector.test_connection()
        assert result["status"] == "connected"
        mock_get.assert_called_once()
```

## 7. Test Data Management

### Test Database Management
```python
# tests/fixtures/database_fixtures.py
import pytest
from faker import Faker
from api.models import Store, Product, OptimizationJob

fake = Faker()

@pytest.fixture
def test_data_factory():
    """Factory for creating test data"""
    class TestDataFactory:
        @staticmethod
        def create_store(**kwargs):
            defaults = {
                "name": fake.company(),
                "url": fake.url(),
                "platform": "woocommerce",
                "api_key": fake.uuid4(),
                "webhook_secret": fake.uuid4()
            }
            defaults.update(kwargs)
            return Store(**defaults)
        
        @staticmethod
        def create_product(**kwargs):
            defaults = {
                "external_id": fake.uuid4(),
                "name": fake.catch_phrase(),
                "description": fake.text(max_nb_chars=200),
                "price": float(fake.pydecimal(left_digits=3, right_digits=2, positive=True)),
                "category": fake.word(),
                "brand": fake.company()
            }
            defaults.update(kwargs)
            return Product(**defaults)
        
        @staticmethod
        def create_optimization_job(**kwargs):
            defaults = {
                "store_id": fake.uuid4(),
                "product_count": fake.random_int(min=1, max=100),
                "optimization_type": fake.random_element(["title", "description", "full"]),
                "status": "queued"
            }
            defaults.update(kwargs)
            return OptimizationJob(**defaults)
    
    return TestDataFactory()

@pytest.fixture(scope="session")
def load_test_data(db_session, test_data_factory):
    """Load test data for complex scenarios"""
    # Create test stores
    stores = [test_data_factory.create_store() for _ in range(5)]
    for store in stores:
        db_session.add(store)
    
    # Create test products
    products = [test_data_factory.create_product() for _ in range(50)]
    for product in products:
        db_session.add(product)
    
    db_session.commit()
    return {"stores": stores, "products": products}
```

## 8. Continuous Integration Testing

### GitHub Actions Configuration
```yaml
# .github/workflows/test.yml
name: GridSpoke Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: gridspoke_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    strategy:
      matrix:
        python-version: [3.11, 3.12]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Run unit tests
      run: |
        pytest tests/unit -v --cov=api --cov-report=xml
    
    - name: Run integration tests
      run: |
        pytest tests/integration -v
      env:
        DATABASE_URL: postgresql://postgres:test@localhost:5432/gridspoke_test
        REDIS_URL: redis://localhost:6379/0
    
    - name: Run load tests (smoke)
      run: |
        locust --headless --users 10 --spawn-rate 2 --run-time 30s --host http://localhost:8000 -f tests/load_testing/locustfile.py
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

## 9. Test Organization Structure

### Recommended Test Directory Structure
```
tests/
├── conftest.py                    # Global pytest configuration
├── fixtures/                     # Reusable test fixtures
│   ├── __init__.py
│   ├── database_fixtures.py      # Database test data
│   ├── gridspoke_fixtures.py     # GridSpoke-specific fixtures
│   └── external_fixtures.py      # External service mocks
├── unit/                          # Unit tests (70%)
│   ├── test_models/               # Database model tests
│   ├── test_services/             # Business logic tests
│   ├── test_ai/                   # AI service tests
│   └── test_utils/                # Utility function tests
├── integration/                   # Integration tests (25%)
│   ├── test_api/                  # API endpoint tests
│   ├── test_tasks/                # Celery task tests
│   ├── test_database/             # Database integration tests
│   └── test_wordpress/            # WordPress integration tests
├── e2e/                          # End-to-end tests (5%)
│   ├── test_optimization_flow.py  # Full optimization workflow
│   └── test_plugin_integration.py # WordPress plugin E2E
├── load_testing/                  # Performance tests
│   ├── locustfile.py             # Locust load testing
│   └── performance_benchmarks.py # Performance benchmarks
└── test_config.py                # Test configuration
```

## 10. Quality Metrics & Coverage

### Coverage Configuration
```ini
# .coveragerc
[run]
source = api
omit = 
    api/tests/*
    api/migrations/*
    api/config.py
    api/main.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
precision = 2
fail_under = 85
```

### Test Metrics Goals
- **Unit Test Coverage**: 90%+ for business logic
- **Integration Test Coverage**: 80%+ for API endpoints
- **Performance Benchmarks**: < 2s response time for single optimization
- **Load Test Targets**: 100 concurrent users, 500 req/sec
- **Error Rate**: < 1% under normal load

## Implementation Priorities

1. **Phase 9.1**: Core unit tests for business logic and models
2. **Phase 9.2**: FastAPI integration tests and API endpoint testing
3. **Phase 9.3**: Celery task testing and async workflow testing
4. **Phase 9.4**: AI service testing and Mirascope integration
5. **Phase 9.5**: WordPress plugin integration testing
6. **Phase 9.6**: Load testing and performance benchmarking
7. **Phase 9.7**: E2E testing and production readiness validation

This comprehensive testing strategy ensures GridSpoke's reliability, performance, and maintainability while supporting continuous integration and deployment practices.
