"""
User model for authentication and authorization.
"""
import uuid
from typing import Optional, List
from sqlalchemy import String, Boolean, DateTime, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from models.base import BaseModel
import structlog

logger = structlog.get_logger(__name__)


class User(BaseModel):
    """User model for API authentication."""
    
    __tablename__ = "users"
    
    # Personal Information
    email: Mapped[str] = mapped_column(
        String(255),
        unique=True,
        nullable=False,
        index=True
    )
    
    full_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True
    )
    
    # Authentication
    hashed_password: Mapped[str] = mapped_column(
        String(255),
        nullable=False
    )
    
    # Status flags
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True
    )
    
    is_superuser: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )
    
    is_verified: Mapped[bool] = mapped_column(
        <PERSON>olean,
        default=False,
        nullable=False
    )
    
    # Profile information
    profile_settings: Mapped[Optional[dict]] = mapped_column(
        JSONB,
        nullable=True,
        default=dict
    )
    
    # Last activity tracking
    last_login: Mapped[Optional[DateTime]] = mapped_column(
        DateTime(timezone=False),
        nullable=True
    )
    
    # Relationships
    stores: Mapped[List["Store"]] = relationship(
        "Store",
        back_populates="owner",
        cascade="all, delete-orphan"
    )
    
    # Database indexes
    __table_args__ = (
        Index("ix_users_email_active", "email", "is_active"),
        Index("ix_users_superuser", "is_superuser"),
    )
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, email={self.email})>"
    
    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.is_superuser and self.is_active
    
    def can_access_store(self, store_id: uuid.UUID) -> bool:
        """Check if user can access a specific store."""
        if self.is_superuser:
            return True
        
        return any(store.id == store_id for store in self.stores)
    
    def get_store_ids(self) -> List[uuid.UUID]:
        """Get list of store IDs this user owns."""
        return [store.id for store in self.stores]
