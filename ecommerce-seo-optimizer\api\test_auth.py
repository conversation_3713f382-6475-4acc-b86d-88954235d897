import asyncio
from core.database import get_session
from crud.user import user_crud

async def test_auth():
    async for db in get_session():
        user = await user_crud.authenticate(db, email="<EMAIL>", password="demo12345")
        if user:
            print(f"Authentication successful: {user.email}")
        else:
            print("Authentication failed")
        break

if __name__ == "__main__":
    asyncio.run(test_auth())
