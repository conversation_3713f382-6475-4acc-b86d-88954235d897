# GridSpoke Monitoring - Prometheus Metrics Collection
# Phase 8: Comprehensive monitoring and analytics implementation

import time
from typing import Dict, Any, Optional
from prometheus_client import (
    Counter, Histogram, Gauge, Info, CollectorRegistry,
    generate_latest, CONTENT_TYPE_LATEST, start_http_server
)
from functools import wraps
import psutil
import asyncio
from sqlalchemy import text
from api.core.database import get_db_session
from api.core.config import settings
import logging

logger = logging.getLogger(__name__)

# Create custom registry for GridSpoke metrics
gridspoke_registry = CollectorRegistry()

# =============================================================================
# BUSINESS METRICS - GridSpoke Specific SEO Performance
# =============================================================================

# Product optimization metrics
optimization_requests_total = Counter(
    'gridspoke_optimization_requests_total',
    'Total number of product optimization requests',
    ['store_id', 'optimization_type', 'status'],
    registry=gridspoke_registry
)

optimization_duration_seconds = Histogram(
    'gridspoke_optimization_duration_seconds',
    'Time spent optimizing products',
    ['optimization_type', 'ai_model'],
    buckets=[0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0],
    registry=gridspoke_registry
)

# AI API usage and cost tracking
ai_api_calls_total = Counter(
    'gridspoke_ai_api_calls_total',
    'Total AI API calls made',
    ['provider', 'model', 'status'],
    registry=gridspoke_registry
)

ai_tokens_used_total = Counter(
    'gridspoke_ai_tokens_used_total',
    'Total tokens consumed by AI calls',
    ['provider', 'model', 'token_type'],
    registry=gridspoke_registry
)

ai_cost_usd_total = Counter(
    'gridspoke_ai_cost_usd_total',
    'Total cost in USD for AI API usage',
    ['provider', 'model'],
    registry=gridspoke_registry
)

# SEO performance metrics
seo_improvement_score = Gauge(
    'gridspoke_seo_improvement_score',
    'SEO improvement score for optimized products',
    ['store_id', 'metric_type'],
    registry=gridspoke_registry
)

content_quality_score = Histogram(
    'gridspoke_content_quality_score',
    'AI-generated content quality scores',
    ['content_type', 'ai_model'],
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
    registry=gridspoke_registry
)

# =============================================================================
# TECHNICAL METRICS - Infrastructure Performance
# =============================================================================

# Database performance
db_query_duration_seconds = Histogram(
    'gridspoke_db_query_duration_seconds',
    'Database query execution time',
    ['query_type', 'table'],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5],
    registry=gridspoke_registry
)

db_connections_active = Gauge(
    'gridspoke_db_connections_active',
    'Number of active database connections',
    registry=gridspoke_registry
)

# Celery task metrics
celery_tasks_total = Counter(
    'gridspoke_celery_tasks_total',
    'Total Celery tasks processed',
    ['task_name', 'status'],
    registry=gridspoke_registry
)

celery_task_duration_seconds = Histogram(
    'gridspoke_celery_task_duration_seconds',
    'Celery task execution time',
    ['task_name'],
    buckets=[1.0, 5.0, 10.0, 30.0, 60.0, 300.0, 600.0, 1800.0, 3600.0],
    registry=gridspoke_registry
)

celery_queue_size = Gauge(
    'gridspoke_celery_queue_size',
    'Number of tasks in Celery queue',
    ['queue_name'],
    registry=gridspoke_registry
)

# WordPress integration metrics
wordpress_sync_requests_total = Counter(
    'gridspoke_wordpress_sync_requests_total',
    'WordPress synchronization requests',
    ['store_id', 'sync_type', 'status'],
    registry=gridspoke_registry
)

wordpress_webhook_duration_seconds = Histogram(
    'gridspoke_wordpress_webhook_duration_seconds',
    'WordPress webhook processing time',
    ['webhook_type'],
    buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
    registry=gridspoke_registry
)

# =============================================================================
# SYSTEM METRICS - Resource Utilization
# =============================================================================

system_cpu_usage_percent = Gauge(
    'gridspoke_system_cpu_usage_percent',
    'System CPU usage percentage',
    registry=gridspoke_registry
)

system_memory_usage_bytes = Gauge(
    'gridspoke_system_memory_usage_bytes',
    'System memory usage in bytes',
    ['memory_type'],
    registry=gridspoke_registry
)

system_disk_usage_bytes = Gauge(
    'gridspoke_system_disk_usage_bytes',
    'System disk usage in bytes',
    ['disk_path', 'usage_type'],
    registry=gridspoke_registry
)

# =============================================================================
# APPLICATION METRICS - API Performance
# =============================================================================

http_requests_total = Counter(
    'gridspoke_http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status'],
    registry=gridspoke_registry
)

http_request_duration_seconds = Histogram(
    'gridspoke_http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint'],
    buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0, 25.0],
    registry=gridspoke_registry
)

active_users = Gauge(
    'gridspoke_active_users',
    'Number of active users',
    ['time_window'],
    registry=gridspoke_registry
)

# =============================================================================
# DECORATORS FOR AUTOMATIC METRICS COLLECTION
# =============================================================================

def track_optimization_metrics(optimization_type: str, ai_model: str = "unknown"):
    """Decorator to track product optimization metrics."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            store_id = kwargs.get('store_id', 'unknown')
            status = 'success'
            
            try:
                result = await func(*args, **kwargs)
                
                # Track token usage if available in result
                if hasattr(result, 'token_usage'):
                    ai_tokens_used_total.labels(
                        provider='openrouter',
                        model=ai_model,
                        token_type='input'
                    ).inc(result.token_usage.get('input_tokens', 0))
                    
                    ai_tokens_used_total.labels(
                        provider='openrouter',
                        model=ai_model,
                        token_type='output'
                    ).inc(result.token_usage.get('output_tokens', 0))
                
                # Track cost if available
                if hasattr(result, 'cost_usd'):
                    ai_cost_usd_total.labels(
                        provider='openrouter',
                        model=ai_model
                    ).inc(result.cost_usd)
                
                return result
                
            except Exception as e:
                status = 'error'
                logger.error(f"Optimization error: {e}")
                raise
            
            finally:
                duration = time.time() - start_time
                
                optimization_requests_total.labels(
                    store_id=store_id,
                    optimization_type=optimization_type,
                    status=status
                ).inc()
                
                optimization_duration_seconds.labels(
                    optimization_type=optimization_type,
                    ai_model=ai_model
                ).observe(duration)
        
        return wrapper
    return decorator

def track_db_metrics(query_type: str, table: str = "unknown"):
    """Decorator to track database query metrics."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                db_query_duration_seconds.labels(
                    query_type=query_type,
                    table=table
                ).observe(duration)
        
        return wrapper
    return decorator

def track_celery_metrics(task_name: str):
    """Decorator to track Celery task metrics."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            status = 'success'
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                status = 'error'
                logger.error(f"Celery task error in {task_name}: {e}")
                raise
            finally:
                duration = time.time() - start_time
                
                celery_tasks_total.labels(
                    task_name=task_name,
                    status=status
                ).inc()
                
                celery_task_duration_seconds.labels(
                    task_name=task_name
                ).observe(duration)
        
        return wrapper
    return decorator

# =============================================================================
# METRICS COLLECTION FUNCTIONS
# =============================================================================

async def collect_system_metrics():
    """Collect system resource metrics."""
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        system_cpu_usage_percent.set(cpu_percent)
        
        # Memory usage
        memory = psutil.virtual_memory()
        system_memory_usage_bytes.labels(memory_type='used').set(memory.used)
        system_memory_usage_bytes.labels(memory_type='available').set(memory.available)
        system_memory_usage_bytes.labels(memory_type='total').set(memory.total)
        
        # Disk usage
        disk = psutil.disk_usage('/')
        system_disk_usage_bytes.labels(disk_path='/', usage_type='used').set(disk.used)
        system_disk_usage_bytes.labels(disk_path='/', usage_type='free').set(disk.free)
        system_disk_usage_bytes.labels(disk_path='/', usage_type='total').set(disk.total)
        
    except Exception as e:
        logger.error(f"Error collecting system metrics: {e}")

async def collect_database_metrics():
    """Collect database connection and performance metrics."""
    try:
        async with get_db_session() as session:
            # Count active connections
            result = await session.execute(
                text("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
            )
            active_connections = result.scalar()
            db_connections_active.set(active_connections)
            
    except Exception as e:
        logger.error(f"Error collecting database metrics: {e}")

async def collect_business_metrics():
    """Collect GridSpoke-specific business metrics."""
    try:
        async with get_db_session() as session:
            # Get optimization statistics
            result = await session.execute(text("""
                SELECT 
                    COUNT(*) as total_optimizations,
                    AVG(CASE WHEN optimization_status = 'completed' THEN 1 ELSE 0 END) as success_rate
                FROM products 
                WHERE last_optimized > NOW() - INTERVAL '24 hours'
            """))
            
            stats = result.fetchone()
            if stats:
                # Update SEO improvement metrics
                seo_improvement_score.labels(
                    store_id='aggregate',
                    metric_type='success_rate'
                ).set(stats.success_rate or 0)
            
    except Exception as e:
        logger.error(f"Error collecting business metrics: {e}")

# =============================================================================
# METRICS SERVER AND UTILITIES
# =============================================================================

class MetricsManager:
    """Centralized metrics management for GridSpoke."""
    
    def __init__(self):
        self.registry = gridspoke_registry
        self._collection_interval = 30  # seconds
        self._collection_task = None
    
    async def start_collection(self):
        """Start automatic metrics collection."""
        logger.info("Starting GridSpoke metrics collection")
        self._collection_task = asyncio.create_task(self._collection_loop())
    
    async def stop_collection(self):
        """Stop automatic metrics collection."""
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped GridSpoke metrics collection")
    
    async def _collection_loop(self):
        """Main metrics collection loop."""
        while True:
            try:
                await collect_system_metrics()
                await collect_database_metrics()
                await collect_business_metrics()
                
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
            
            await asyncio.sleep(self._collection_interval)
    
    def get_metrics(self) -> str:
        """Get all metrics in Prometheus format."""
        return generate_latest(self.registry)
    
    def start_http_server(self, port: int = 8090):
        """Start Prometheus metrics HTTP server."""
        start_http_server(port, registry=self.registry)
        logger.info(f"GridSpoke metrics server started on port {port}")

# Global metrics manager instance
metrics_manager = MetricsManager()

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def record_ai_api_call(provider: str, model: str, status: str, 
                      input_tokens: int = 0, output_tokens: int = 0, 
                      cost_usd: float = 0.0):
    """Record AI API call metrics."""
    ai_api_calls_total.labels(
        provider=provider,
        model=model,
        status=status
    ).inc()
    
    if input_tokens > 0:
        ai_tokens_used_total.labels(
            provider=provider,
            model=model,
            token_type='input'
        ).inc(input_tokens)
    
    if output_tokens > 0:
        ai_tokens_used_total.labels(
            provider=provider,
            model=model,
            token_type='output'
        ).inc(output_tokens)
    
    if cost_usd > 0:
        ai_cost_usd_total.labels(
            provider=provider,
            model=model
        ).inc(cost_usd)

def record_seo_improvement(store_id: str, metric_type: str, score: float):
    """Record SEO improvement metrics."""
    seo_improvement_score.labels(
        store_id=store_id,
        metric_type=metric_type
    ).set(score)

def record_content_quality(content_type: str, ai_model: str, score: float):
    """Record content quality metrics."""
    content_quality_score.labels(
        content_type=content_type,
        ai_model=ai_model
    ).observe(score)

def record_wordpress_sync(store_id: str, sync_type: str, status: str, duration: float):
    """Record WordPress synchronization metrics."""
    wordpress_sync_requests_total.labels(
        store_id=store_id,
        sync_type=sync_type,
        status=status
    ).inc()
    
    wordpress_webhook_duration_seconds.labels(
        webhook_type=sync_type
    ).observe(duration)

# Export main components
__all__ = [
    'metrics_manager',
    'track_optimization_metrics',
    'track_db_metrics', 
    'track_celery_metrics',
    'record_ai_api_call',
    'record_seo_improvement',
    'record_content_quality',
    'record_wordpress_sync',
    'gridspoke_registry'
]
