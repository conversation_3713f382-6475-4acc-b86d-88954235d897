"""
Product Optimization Tasks for GridSpoke Ecommerce SEO Optimizer
Handles individual and batch product optimization using AI agents.
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from celery import current_task
from celery.exceptions import Retry, Ignore

# Add the API directory to the Python path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '../../api'))

from workers.celery_app import celery_app
from workers.utils.progress_tracker import track_progress, update_task_status
from workers.utils.redis_client import get_redis_client
from workers.utils.error_handler import handle_task_error, retry_with_backoff

logger = logging.getLogger(__name__)

@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    retry_backoff=True,
    retry_jitter=True,
    acks_late=True
)
def optimize_single_product(self, product_id: str, store_id: str, optimization_options: Dict[str, Any] = None):
    """
    Optimize a single product using AI agents.
    
    Args:
        product_id: Unique identifier for the product
        store_id: Store identifier
        optimization_options: Configuration options for optimization
    
    Returns:
        Dict containing optimization results
    """
    try:
        # Import AI services (done inside function to avoid import issues)
        from api.services.ai_service import AIService
        from api.crud.product import get_product_by_id, update_product
        from api.core.database import get_db
        
        logger.info(f"Starting optimization for product {product_id} in store {store_id}")
        
        # Initialize progress tracking
        track_progress(self.request.id, 0, 5, "Initializing product optimization")
        
        # Get database session
        db = next(get_db())
        
        # Fetch product data
        track_progress(self.request.id, 1, 5, "Fetching product data")
        product = get_product_by_id(db, product_id)
        if not product:
            raise ValueError(f"Product {product_id} not found")
        
        # Initialize AI service
        track_progress(self.request.id, 2, 5, "Initializing AI service")
        ai_service = AIService()
        
        # Prepare optimization payload
        optimization_payload = {
            'product_id': product_id,
            'title': product.title,
            'description': product.description,
            'category': product.category,
            'price': product.price,
            'keywords': product.keywords or [],
            'images': product.images or [],
            'options': optimization_options or {}
        }
        
        # Perform AI optimization
        track_progress(self.request.id, 3, 5, "Running AI optimization")
        optimization_result = ai_service.optimize_product(optimization_payload)
        
        # Update product in database
        track_progress(self.request.id, 4, 5, "Updating product data")
        update_data = {
            'title': optimization_result.get('title', product.title),
            'description': optimization_result.get('description', product.description),
            'meta_description': optimization_result.get('meta_description'),
            'keywords': optimization_result.get('keywords', product.keywords),
            'seo_score': optimization_result.get('seo_score'),
            'optimization_status': 'completed',
            'last_optimized': datetime.utcnow()
        }
        
        updated_product = update_product(db, product_id, update_data)
        
        # Store optimization history
        optimization_record = {
            'product_id': product_id,
            'store_id': store_id,
            'optimization_type': 'single_product',
            'ai_model': optimization_result.get('model_used'),
            'tokens_used': optimization_result.get('tokens_used', 0),
            'cost_usd': optimization_result.get('cost_usd', 0),
            'improvements': optimization_result.get('improvements', {}),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Cache result in Redis for 1 hour
        redis_client = get_redis_client()
        redis_client.setex(
            f"optimization_result:{product_id}",
            3600,
            json.dumps(optimization_record)
        )
        
        # Complete progress tracking
        track_progress(self.request.id, 5, 5, "Optimization completed successfully")
        
        logger.info(f"Successfully optimized product {product_id}")
        
        return {
            'status': 'success',
            'product_id': product_id,
            'store_id': store_id,
            'optimization_result': optimization_result,
            'updated_fields': list(update_data.keys()),
            'tokens_used': optimization_result.get('tokens_used', 0),
            'cost_usd': optimization_result.get('cost_usd', 0),
            'task_id': self.request.id,
            'completed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error optimizing product {product_id}: {str(exc)}")
        
        # Handle error with retry logic
        error_info = handle_task_error(exc, self.request.retries)
        
        if self.request.retries < 3:
            # Retry with exponential backoff
            countdown = retry_with_backoff(self.request.retries)
            logger.info(f"Retrying product optimization in {countdown} seconds")
            raise self.retry(countdown=countdown, exc=exc)
        else:
            # Max retries reached, mark as failed
            update_task_status(self.request.id, 'failed', str(exc))
            raise Ignore()

@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 120},
    acks_late=True
)
def optimize_product_batch(self, store_id: str, product_ids: List[str], batch_size: int = 10):
    """
    Optimize a batch of products in parallel with progress tracking.
    
    Args:
        store_id: Store identifier
        product_ids: List of product IDs to optimize
        batch_size: Number of products to process in parallel
    
    Returns:
        Dict containing batch optimization results
    """
    try:
        from celery import group
        
        logger.info(f"Starting batch optimization for {len(product_ids)} products in store {store_id}")
        
        # Initialize progress tracking
        total_products = len(product_ids)
        track_progress(self.request.id, 0, total_products, f"Starting batch optimization of {total_products} products")
        
        # Split products into batches
        product_batches = [
            product_ids[i:i + batch_size] 
            for i in range(0, len(product_ids), batch_size)
        ]
        
        results = []
        processed_count = 0
        
        for batch_index, batch in enumerate(product_batches):
            logger.info(f"Processing batch {batch_index + 1}/{len(product_batches)}")
            
            # Create parallel optimization tasks for this batch
            job = group(
                optimize_single_product.s(product_id, store_id)
                for product_id in batch
            )
            
            # Execute batch and wait for completion
            batch_result = job.apply_async()
            batch_results = batch_result.get(timeout=600)  # 10 minute timeout per batch
            
            # Process results
            for result in batch_results:
                if result and result.get('status') == 'success':
                    results.append(result)
                    processed_count += 1
                    
                    # Update progress
                    track_progress(
                        self.request.id, 
                        processed_count, 
                        total_products, 
                        f"Optimized {processed_count}/{total_products} products"
                    )
        
        # Calculate batch statistics
        successful_optimizations = len(results)
        failed_optimizations = total_products - successful_optimizations
        total_tokens = sum(r.get('tokens_used', 0) for r in results)
        total_cost = sum(r.get('cost_usd', 0) for r in results)
        
        batch_summary = {
            'status': 'completed',
            'store_id': store_id,
            'total_products': total_products,
            'successful_optimizations': successful_optimizations,
            'failed_optimizations': failed_optimizations,
            'success_rate': (successful_optimizations / total_products) * 100,
            'total_tokens_used': total_tokens,
            'total_cost_usd': total_cost,
            'average_cost_per_product': total_cost / successful_optimizations if successful_optimizations > 0 else 0,
            'task_id': self.request.id,
            'started_at': datetime.utcnow().isoformat(),
            'completed_at': datetime.utcnow().isoformat(),
            'results': results
        }
        
        # Store batch summary in Redis
        redis_client = get_redis_client()
        redis_client.setex(
            f"batch_optimization:{store_id}:{self.request.id}",
            7200,  # 2 hours
            json.dumps(batch_summary)
        )
        
        track_progress(self.request.id, total_products, total_products, "Batch optimization completed")
        
        logger.info(f"Completed batch optimization: {successful_optimizations}/{total_products} successful")
        
        return batch_summary
        
    except Exception as exc:
        logger.error(f"Error in batch optimization: {str(exc)}")
        
        if self.request.retries < 2:
            countdown = retry_with_backoff(self.request.retries)
            raise self.retry(countdown=countdown, exc=exc)
        else:
            update_task_status(self.request.id, 'failed', str(exc))
            raise Ignore()

@celery_app.task(bind=True, acks_late=True)
def validate_products(self, store_id: str, product_ids: List[str]):
    """
    Validate product data before optimization.
    
    Args:
        store_id: Store identifier
        product_ids: List of product IDs to validate
    
    Returns:
        Dict containing validation results
    """
    try:
        from api.crud.product import get_products_by_ids
        from api.core.database import get_db
        
        logger.info(f"Validating {len(product_ids)} products for store {store_id}")
        
        db = next(get_db())
        products = get_products_by_ids(db, product_ids)
        
        valid_products = []
        invalid_products = []
        
        for product_id in product_ids:
            product = next((p for p in products if p.id == product_id), None)
            
            if not product:
                invalid_products.append({
                    'product_id': product_id,
                    'reason': 'Product not found'
                })
                continue
            
            # Validation rules
            validation_errors = []
            
            if not product.title or len(product.title.strip()) < 3:
                validation_errors.append('Title too short or missing')
            
            if not product.description or len(product.description.strip()) < 10:
                validation_errors.append('Description too short or missing')
            
            if not product.category:
                validation_errors.append('Category missing')
            
            if validation_errors:
                invalid_products.append({
                    'product_id': product_id,
                    'reason': '; '.join(validation_errors)
                })
            else:
                valid_products.append(product_id)
        
        validation_result = {
            'store_id': store_id,
            'total_products': len(product_ids),
            'valid_products': valid_products,
            'invalid_products': invalid_products,
            'valid_count': len(valid_products),
            'invalid_count': len(invalid_products),
            'validation_passed': len(invalid_products) == 0,
            'task_id': self.request.id,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        logger.info(f"Validation completed: {len(valid_products)} valid, {len(invalid_products)} invalid")
        
        return validation_result
        
    except Exception as exc:
        logger.error(f"Error validating products: {str(exc)}")
        raise

@celery_app.task(bind=True, acks_late=True)
def calculate_seo_score(self, product_id: str):
    """
    Calculate SEO score for a product.
    
    Args:
        product_id: Product identifier
    
    Returns:
        Dict containing SEO score and recommendations
    """
    try:
        from api.services.seo_analyzer import SEOAnalyzer
        from api.crud.product import get_product_by_id
        from api.core.database import get_db
        
        logger.info(f"Calculating SEO score for product {product_id}")
        
        db = next(get_db())
        product = get_product_by_id(db, product_id)
        
        if not product:
            raise ValueError(f"Product {product_id} not found")
        
        seo_analyzer = SEOAnalyzer()
        score_result = seo_analyzer.calculate_score(product)
        
        return {
            'product_id': product_id,
            'seo_score': score_result['score'],
            'recommendations': score_result['recommendations'],
            'score_breakdown': score_result['breakdown'],
            'task_id': self.request.id,
            'calculated_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error calculating SEO score: {str(exc)}")
        raise
