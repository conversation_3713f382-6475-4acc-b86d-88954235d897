"""
WebSocket Endpoints for GridSpoke Ecommerce SEO Optimizer
Handles WebSocket connections for real-time task updates and system notifications.
"""

import json
import logging
from typing import Optional
from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.routing import APIRouter
from datetime import datetime
import uuid

from .manager import manager
from core.security import get_current_user_ws
from models.user import User

logger = logging.getLogger(__name__)

# Create WebSocket router
ws_router = APIRouter()

@ws_router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None),
    store_id: Optional[str] = Query(None),
    task_id: Optional[str] = Query(None)
):
    """
    Main WebSocket endpoint for real-time updates.
    
    Args:
        websocket: WebSocket connection
        token: JWT token for authentication
        store_id: Optional store ID for store-specific updates
        task_id: Optional task ID for task-specific updates
    """
    
    # Generate unique connection ID
    connection_id = str(uuid.uuid4())
    
    try:
        # Authenticate user
        if not token:
            await websocket.close(code=4001, reason="Authentication token required")
            return
        
        # Verify token and get user (simplified for WebSocket)
        try:
            # In a real implementation, you'd validate the JWT token here
            # For now, we'll extract user_id from token (simplified)
            import jwt
            from ..core.config import settings
            
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            user_id = payload.get("sub")
            
            if not user_id:
                await websocket.close(code=4001, reason="Invalid token")
                return
                
        except jwt.InvalidTokenError:
            await websocket.close(code=4001, reason="Invalid token")
            return
        
        # Connect to manager
        await manager.connect(
            websocket=websocket,
            connection_id=connection_id,
            user_id=user_id,
            store_id=store_id,
            task_id=task_id
        )
        
        logger.info(f"WebSocket connected: {connection_id} for user {user_id}")
        
        # Listen for client messages
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                await handle_client_message(connection_id, message)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected: {connection_id}")
                break
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received from {connection_id}")
                await manager.send_personal_message(connection_id, {
                    'type': 'error',
                    'message': 'Invalid JSON format'
                })
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {str(e)}")
                await manager.send_personal_message(connection_id, {
                    'type': 'error',
                    'message': f'Server error: {str(e)}'
                })
    
    except Exception as e:
        logger.error(f"WebSocket connection error: {str(e)}")
    
    finally:
        # Ensure cleanup
        manager.disconnect(connection_id)

async def handle_client_message(connection_id: str, message: dict):
    """
    Handle messages received from WebSocket clients.
    
    Args:
        connection_id: Connection identifier
        message: Parsed message from client
    """
    
    message_type = message.get('type')
    
    if message_type == 'ping':
        # Handle ping for connection health check
        await manager.handle_ping(connection_id)
    
    elif message_type == 'subscribe_task':
        # Subscribe to task updates
        task_id = message.get('task_id')
        if task_id:
            await manager.handle_subscribe_task(connection_id, task_id)
        else:
            await manager.send_personal_message(connection_id, {
                'type': 'error',
                'message': 'task_id required for subscription'
            })
    
    elif message_type == 'unsubscribe_task':
        # Unsubscribe from task updates
        task_id = message.get('task_id')
        if task_id:
            await manager.handle_unsubscribe_task(connection_id, task_id)
        else:
            await manager.send_personal_message(connection_id, {
                'type': 'error',
                'message': 'task_id required for unsubscription'
            })
    
    elif message_type == 'get_connection_stats':
        # Send connection statistics
        stats = manager.get_connection_stats()
        await manager.send_personal_message(connection_id, {
            'type': 'connection_stats',
            'data': stats,
            'timestamp': datetime.utcnow().isoformat()
        })
    
    elif message_type == 'heartbeat':
        # Update last activity time
        await manager.send_personal_message(connection_id, {
            'type': 'heartbeat_ack',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    else:
        # Unknown message type
        await manager.send_personal_message(connection_id, {
            'type': 'error',
            'message': f'Unknown message type: {message_type}'
        })

@ws_router.websocket("/ws/admin")
async def admin_websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None)
):
    """
    Admin WebSocket endpoint for system-wide monitoring.
    
    Args:
        websocket: WebSocket connection
        token: JWT token for authentication (must be admin)
    """
    
    connection_id = f"admin_{str(uuid.uuid4())}"
    
    try:
        # Authenticate admin user
        if not token:
            await websocket.close(code=4001, reason="Authentication token required")
            return
        
        try:
            import jwt
            from ..core.config import settings
            
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            user_id = payload.get("sub")
            is_admin = payload.get("is_admin", False)
            
            if not user_id or not is_admin:
                await websocket.close(code=4003, reason="Admin access required")
                return
                
        except jwt.InvalidTokenError:
            await websocket.close(code=4001, reason="Invalid token")
            return
        
        # Connect admin to manager
        await manager.connect(
            websocket=websocket,
            connection_id=connection_id,
            user_id=user_id
        )
        
        logger.info(f"Admin WebSocket connected: {connection_id}")
        
        # Send initial system stats
        stats = manager.get_connection_stats()
        await manager.send_personal_message(connection_id, {
            'type': 'admin_connected',
            'system_stats': stats,
            'timestamp': datetime.utcnow().isoformat()
        })
        
        # Listen for admin commands
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                await handle_admin_message(connection_id, message)
                
            except WebSocketDisconnect:
                logger.info(f"Admin WebSocket disconnected: {connection_id}")
                break
            except Exception as e:
                logger.error(f"Error handling admin WebSocket message: {str(e)}")
                await manager.send_personal_message(connection_id, {
                    'type': 'error',
                    'message': f'Admin command error: {str(e)}'
                })
    
    except Exception as e:
        logger.error(f"Admin WebSocket connection error: {str(e)}")
    
    finally:
        manager.disconnect(connection_id)

async def handle_admin_message(connection_id: str, message: dict):
    """
    Handle messages from admin WebSocket clients.
    
    Args:
        connection_id: Admin connection identifier
        message: Parsed message from admin client
    """
    
    message_type = message.get('type')
    
    if message_type == 'broadcast_message':
        # Broadcast system message to all users
        broadcast_data = message.get('data', {})
        target_users = message.get('target_users')  # Optional list of user IDs
        
        await manager.broadcast_system_message(broadcast_data, target_users)
        
        await manager.send_personal_message(connection_id, {
            'type': 'broadcast_sent',
            'message': 'System message broadcasted successfully',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    elif message_type == 'get_system_stats':
        # Send detailed system statistics
        stats = manager.get_connection_stats()
        
        await manager.send_personal_message(connection_id, {
            'type': 'system_stats',
            'data': stats,
            'timestamp': datetime.utcnow().isoformat()
        })
    
    elif message_type == 'cleanup_connections':
        # Clean up dead connections
        await manager.cleanup_dead_connections()
        
        await manager.send_personal_message(connection_id, {
            'type': 'cleanup_complete',
            'message': 'Dead connections cleaned up',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    elif message_type == 'force_disconnect':
        # Force disconnect a specific connection
        target_connection_id = message.get('connection_id')
        if target_connection_id and target_connection_id in manager.active_connections:
            try:
                websocket = manager.active_connections[target_connection_id]
                await websocket.close(code=4000, reason="Forced disconnect by admin")
                manager.disconnect(target_connection_id)
                
                await manager.send_personal_message(connection_id, {
                    'type': 'disconnect_success',
                    'message': f'Connection {target_connection_id} disconnected',
                    'timestamp': datetime.utcnow().isoformat()
                })
            except Exception as e:
                await manager.send_personal_message(connection_id, {
                    'type': 'error',
                    'message': f'Failed to disconnect {target_connection_id}: {str(e)}'
                })
        else:
            await manager.send_personal_message(connection_id, {
                'type': 'error',
                'message': f'Connection {target_connection_id} not found'
            })
    
    else:
        await manager.send_personal_message(connection_id, {
            'type': 'error',
            'message': f'Unknown admin command: {message_type}'
        })

# Startup event to initialize Redis connection
async def startup_websocket_manager():
    """Initialize WebSocket manager with Redis connection."""
    await manager.initialize_redis()
    logger.info("WebSocket manager initialized")

# Shutdown event to cleanup resources
async def shutdown_websocket_manager():
    """Shutdown WebSocket manager and cleanup resources."""
    await manager.shutdown()
    logger.info("WebSocket manager shut down")
