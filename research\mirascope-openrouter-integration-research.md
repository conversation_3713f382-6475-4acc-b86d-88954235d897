# Mirascope & OpenRouter Integration Research for GridSpoke Ecommerce SEO Service

## Executive Summary

This research document provides comprehensive technical details for implementing AI agents using the Mirascope framework integrated with OpenRouter API for the GridSpoke ecommerce SEO optimization service. The combination offers a powerful, cost-effective solution for generating optimized product content at scale.

## 1. Mirascope Framework Overview

### Core Architecture
Mirascope is a modern AI framework that provides a clean, decorator-based approach to building AI applications with strong type safety and structured outputs.

**Key Benefits for GridSpoke:**
- **Type Safety**: Full Pydantic integration ensures reliable data structures
- **Provider Agnostic**: Easy switching between AI providers (critical for cost optimization)
- **Streaming Support**: Real-time content generation for user feedback
- **Tool Integration**: Built-in function calling for complex SEO workflows
- **Agent Patterns**: State management for multi-step optimization processes

### Basic Call Pattern
```python
from mirascope import llm

@llm.call(provider="openai", model="gpt-4o-mini")
def generate_seo_title(product_name: str, category: str) -> str:
    return f"Generate an SEO-optimized title for {product_name} in {category}"

# Usage
optimized_title = generate_seo_title("Wireless Headphones", "Electronics")
```

## 2. Mirascope Agent Patterns for SEO Optimization

### State Management Architecture
```python
from mirascope import llm
from pydantic import BaseModel
from typing import List, Optional

class SEOOptimizationState(BaseModel):
    product_id: str
    current_title: Optional[str] = None
    generated_title: Optional[str] = None
    meta_description: Optional[str] = None
    keywords: List[str] = []
    optimization_status: str = "pending"
    ai_model_used: Optional[str] = None

class ProductOptimizer:
    def __init__(self):
        self.state = SEOOptimizationState()
    
    @llm.call(provider="openai", model="gpt-4o")
    def generate_title(self, product_data: dict) -> str:
        return f"""Generate an SEO-optimized title for this product:
        Name: {product_data['name']}
        Category: {product_data['category']}
        Features: {product_data['features']}
        
        Requirements:
        - 60 characters or less
        - Include primary keyword
        - Compelling and clickable
        """
    
    @llm.call(provider="openai", model="gpt-3.5-turbo")
    def generate_meta_description(self, title: str, product_data: dict) -> str:
        return f"""Create a meta description for:
        Title: {title}
        Product: {product_data['name']}
        
        Requirements:
        - 155 characters or less
        - Include call-to-action
        - Relevant keywords
        """
```

### Tool Integration for Complex Workflows
```python
from mirascope import llm, BaseDynamicConfig
from typing import Literal

def analyze_competitor_keywords(product_category: str) -> dict:
    """Simulate competitor keyword analysis"""
    return {
        "primary_keywords": ["wireless headphones", "bluetooth audio"],
        "competitor_titles": ["Premium Wireless Headphones", "HD Bluetooth Headphones"]
    }

def check_seo_score(title: str, description: str) -> dict:
    """Simulate SEO scoring"""
    return {
        "title_score": 85,
        "description_score": 90,
        "overall_score": 87
    }

class SEOOptimizerConfig(BaseDynamicConfig):
    tools = [analyze_competitor_keywords, check_seo_score]

@llm.call(config=SEOOptimizerConfig)
def optimize_product_seo(product_data: dict) -> str:
    return f"""Optimize SEO for this product using available tools:
    Product: {product_data}
    
    1. First analyze competitor keywords for the category
    2. Generate optimized title and meta description
    3. Check SEO scores and refine if needed
    """
```

### Human-in-the-Loop Pattern
```python
class SEOReviewAgent:
    def __init__(self):
        self.requires_human_review = False
    
    @llm.call(provider="openai", model="gpt-4")
    def generate_content(self, product_data: dict) -> str:
        # Generate content
        pass
    
    def quality_check(self, generated_content: dict) -> bool:
        """Determine if human review is needed"""
        quality_indicators = [
            len(generated_content.get('title', '')) < 60,
            len(generated_content.get('meta_description', '')) < 155,
            'keyword_density' in generated_content
        ]
        
        if not all(quality_indicators):
            self.requires_human_review = True
            return False
        return True
    
    async def process_with_review(self, product_data: dict):
        content = self.generate_content(product_data)
        
        if not self.quality_check(content):
            # Send to human review queue
            await self.queue_for_human_review(content)
            return None
        
        return content
```

## 3. Streaming Implementation for Real-time Feedback

### Basic Streaming Setup
```python
@llm.call(provider="openai", model="gpt-4o-mini", stream=True)
def generate_product_description(product_name: str, features: List[str]) -> str:
    return f"""Generate a compelling product description for {product_name} 
    with these features: {', '.join(features)}"""

# Real-time streaming usage
def stream_product_optimization(product_data: dict):
    stream = generate_product_description(
        product_data['name'], 
        product_data['features']
    )
    
    generated_content = ""
    for chunk, _ in stream:
        generated_content += chunk.content
        # Send real-time updates to frontend via WebSocket
        yield {
            "type": "content_chunk",
            "content": chunk.content,
            "timestamp": time.time()
        }
    
    # After streaming complete, access full response
    print(f"Final content: {stream.content}")
    print(f"Token usage: {stream.construct_call_response().usage}")
```

### Error Handling in Streams
```python
from openai import OpenAIError

@llm.call(provider="openai", model="gpt-4", stream=True)
def safe_generate_content(product_data: dict) -> str:
    return f"Generate SEO content for: {product_data}"

def robust_streaming_generation(product_data: dict):
    try:
        stream = safe_generate_content(product_data)
        
        for chunk, tool in stream:
            try:
                # Process each chunk
                yield chunk.content
            except Exception as chunk_error:
                print(f"Error processing chunk: {chunk_error}")
                continue
                
    except OpenAIError as api_error:
        print(f"OpenAI API error: {api_error}")
        # Fallback to different model or provider
        return fallback_content_generation(product_data)
    except Exception as general_error:
        print(f"General error: {general_error}")
        # Log error and return default content
        return default_seo_content(product_data)
```

## 4. OpenRouter Integration Strategy

### Multi-Provider Configuration
```python
import os
from mirascope import llm

# OpenRouter configuration for multiple models
OPENROUTER_CONFIG = {
    "api_key": os.getenv("OPENROUTER_API_KEY"),
    "base_url": "https://openrouter.ai/api/v1",
    "extra_headers": {
        "HTTP-Referer": "https://gridspoke.com",
        "X-Title": "GridSpoke SEO Optimizer"
    }
}

@llm.call(
    provider="openai", 
    model="anthropic/claude-3-opus",
    api_key=OPENROUTER_CONFIG["api_key"],
    base_url=OPENROUTER_CONFIG["base_url"]
)
def premium_seo_optimization(product_data: dict) -> str:
    return f"Generate premium SEO optimization for: {product_data}"

@llm.call(
    provider="openai", 
    model="openai/gpt-3.5-turbo",
    api_key=OPENROUTER_CONFIG["api_key"],
    base_url=OPENROUTER_CONFIG["base_url"]
)
def cost_effective_optimization(product_data: dict) -> str:
    return f"Generate cost-effective SEO optimization for: {product_data}"
```

### Smart Model Selection Based on Content Type
```python
class SmartModelSelector:
    """Selects optimal model based on content type and cost requirements"""
    
    MODEL_TIERS = {
        "premium": {
            "model": "anthropic/claude-3-opus",
            "cost_per_token": 0.000015,  # Example pricing
            "use_cases": ["complex_descriptions", "brand_voice_matching"]
        },
        "standard": {
            "model": "openai/gpt-4o",
            "cost_per_token": 0.00001,
            "use_cases": ["titles", "meta_descriptions", "alt_text"]
        },
        "economy": {
            "model": "openai/gpt-3.5-turbo",
            "cost_per_token": 0.000002,
            "use_cases": ["bulk_processing", "simple_tags"]
        }
    }
    
    def select_model(self, content_type: str, batch_size: int = 1) -> str:
        if content_type in ["product_description", "brand_content"] and batch_size < 10:
            return self.MODEL_TIERS["premium"]["model"]
        elif content_type in ["title", "meta_description"] and batch_size < 100:
            return self.MODEL_TIERS["standard"]["model"]
        else:
            return self.MODEL_TIERS["economy"]["model"]

# Usage in optimization workflow
selector = SmartModelSelector()

@llm.call(provider="openai", api_key=OPENROUTER_CONFIG["api_key"], base_url=OPENROUTER_CONFIG["base_url"])
def dynamic_optimize_content(content_type: str, product_data: dict, batch_size: int = 1) -> str:
    # Model selection happens at runtime
    selected_model = selector.select_model(content_type, batch_size)
    # Update the call configuration dynamically
    return f"Optimize {content_type} for: {product_data}"
```

### Fallback Strategy Implementation
```python
class OpenRouterFallbackHandler:
    """Handles provider failures and implements intelligent fallbacks"""
    
    FALLBACK_CHAIN = [
        "anthropic/claude-3-opus",
        "openai/gpt-4o",
        "openai/gpt-3.5-turbo",
        "google/gemini-pro"
    ]
    
    async def optimize_with_fallback(self, product_data: dict, content_type: str):
        for model in self.FALLBACK_CHAIN:
            try:
                result = await self.try_optimization(model, product_data, content_type)
                if result:
                    return {
                        "content": result,
                        "model_used": model,
                        "success": True
                    }
            except Exception as e:
                print(f"Model {model} failed: {e}")
                continue
        
        # All models failed - return error
        return {
            "content": None,
            "model_used": None,
            "success": False,
            "error": "All fallback models failed"
        }
    
    @llm.call(provider="openai", api_key=OPENROUTER_CONFIG["api_key"], base_url=OPENROUTER_CONFIG["base_url"])
    async def try_optimization(self, model: str, product_data: dict, content_type: str) -> str:
        return f"Generate {content_type} for: {product_data}"
```

## 5. Cost Optimization Strategies

### Request Batching and Caching
```python
import hashlib
import redis
from typing import List, Dict

class SEOCacheManager:
    """Manages caching for similar product optimizations"""
    
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.cache_ttl = 86400  # 24 hours
    
    def generate_cache_key(self, product_data: dict, content_type: str) -> str:
        """Generate consistent cache key for similar products"""
        cache_string = f"{product_data.get('category', '')}-{content_type}-{product_data.get('price_range', '')}"
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def get_cached_optimization(self, cache_key: str) -> Optional[dict]:
        """Retrieve cached optimization if available"""
        cached = self.redis_client.get(cache_key)
        if cached:
            return json.loads(cached)
        return None
    
    def cache_optimization(self, cache_key: str, optimization_result: dict):
        """Cache optimization result for similar products"""
        self.redis_client.setex(
            cache_key, 
            self.cache_ttl, 
            json.dumps(optimization_result)
        )

# Batch processing with caching
class BatchSEOOptimizer:
    def __init__(self):
        self.cache_manager = SEOCacheManager()
        self.batch_size = 10
    
    async def optimize_product_batch(self, products: List[dict]) -> List[dict]:
        """Process products in batches with intelligent caching"""
        results = []
        
        for i in range(0, len(products), self.batch_size):
            batch = products[i:i + self.batch_size]
            batch_results = await self.process_batch_with_cache(batch)
            results.extend(batch_results)
        
        return results
    
    async def process_batch_with_cache(self, batch: List[dict]) -> List[dict]:
        """Process batch with cache checking"""
        cached_results = []
        uncached_products = []
        
        # Check cache first
        for product in batch:
            cache_key = self.cache_manager.generate_cache_key(product, "seo_optimization")
            cached = self.cache_manager.get_cached_optimization(cache_key)
            
            if cached:
                cached_results.append(cached)
            else:
                uncached_products.append(product)
        
        # Process uncached products
        if uncached_products:
            new_results = await self.ai_optimize_batch(uncached_products)
            
            # Cache new results
            for product, result in zip(uncached_products, new_results):
                cache_key = self.cache_manager.generate_cache_key(product, "seo_optimization")
                self.cache_manager.cache_optimization(cache_key, result)
            
            cached_results.extend(new_results)
        
        return cached_results
```

### Off-Peak Scheduling Strategy
```python
from datetime import datetime, time
import pytz

class CostOptimizedScheduler:
    """Schedules AI operations during low-cost periods"""
    
    # OpenRouter typically has lower rates during off-peak hours
    OFF_PEAK_HOURS = {
        "start": time(2, 0),  # 2 AM
        "end": time(6, 0)     # 6 AM
    }
    
    def is_off_peak_time(self, timezone: str = "UTC") -> bool:
        """Check if current time is in off-peak hours"""
        tz = pytz.timezone(timezone)
        current_time = datetime.now(tz).time()
        
        return (self.OFF_PEAK_HOURS["start"] <= current_time <= self.OFF_PEAK_HOURS["end"])
    
    def calculate_cost_multiplier(self) -> float:
        """Calculate cost multiplier based on time"""
        if self.is_off_peak_time():
            return 0.7  # 30% discount during off-peak
        else:
            return 1.0  # Regular pricing
    
    async def schedule_bulk_optimization(self, products: List[dict], priority: str = "normal"):
        """Schedule optimization based on cost and priority"""
        if priority == "urgent":
            # Process immediately regardless of cost
            return await self.immediate_optimization(products)
        
        elif self.is_off_peak_time():
            # Process during off-peak hours
            return await self.batch_optimization(products)
        
        else:
            # Queue for next off-peak period
            return await self.queue_for_off_peak(products)
```

## 6. Integration Architecture for GridSpoke

### Celery Task Integration
```python
from celery import Celery
from typing import List, Dict
import asyncio

celery_app = Celery('gridspoke_seo')

@celery_app.task(bind=True)
def optimize_products_task(self, store_id: str, product_ids: List[str], optimization_config: Dict):
    """Celery task for bulk product optimization"""
    
    # Initialize Mirascope optimizer
    optimizer = ProductSEOOptimizer(
        model_selector=SmartModelSelector(),
        cache_manager=SEOCacheManager(),
        openrouter_config=OPENROUTER_CONFIG
    )
    
    results = []
    total_products = len(product_ids)
    
    for i, product_id in enumerate(product_ids):
        try:
            # Update task progress
            self.update_state(
                state='PROGRESS',
                meta={'current': i, 'total': total_products, 'status': f'Processing product {product_id}'}
            )
            
            # Optimize single product
            result = asyncio.run(optimizer.optimize_product(product_id, optimization_config))
            results.append(result)
            
            # Optional: Real-time WebSocket update
            send_websocket_update(store_id, {
                'type': 'product_optimized',
                'product_id': product_id,
                'result': result,
                'progress': (i + 1) / total_products * 100
            })
            
        except Exception as e:
            results.append({
                'product_id': product_id,
                'success': False,
                'error': str(e)
            })
    
    return {
        'store_id': store_id,
        'total_processed': len(results),
        'successful': sum(1 for r in results if r.get('success', False)),
        'failed': sum(1 for r in results if not r.get('success', False)),
        'results': results
    }
```

### WebSocket Real-time Updates
```python
import asyncio
import websockets
import json

class SEOProgressWebSocket:
    """WebSocket handler for real-time optimization progress"""
    
    def __init__(self):
        self.active_connections = {}
    
    async def connect(self, websocket, store_id: str):
        """Register new WebSocket connection"""
        if store_id not in self.active_connections:
            self.active_connections[store_id] = set()
        self.active_connections[store_id].add(websocket)
    
    async def disconnect(self, websocket, store_id: str):
        """Remove WebSocket connection"""
        if store_id in self.active_connections:
            self.active_connections[store_id].discard(websocket)
    
    async def send_optimization_update(self, store_id: str, update_data: dict):
        """Send real-time update to all connected clients"""
        if store_id in self.active_connections:
            message = json.dumps({
                'type': 'seo_optimization_update',
                'timestamp': datetime.utcnow().isoformat(),
                'data': update_data
            })
            
            # Send to all connected websockets for this store
            disconnected = set()
            for websocket in self.active_connections[store_id]:
                try:
                    await websocket.send(message)
                except websockets.exceptions.ConnectionClosed:
                    disconnected.add(websocket)
            
            # Clean up disconnected websockets
            self.active_connections[store_id] -= disconnected

# Global WebSocket manager
websocket_manager = SEOProgressWebSocket()

async def send_websocket_update(store_id: str, update_data: dict):
    """Helper function to send WebSocket updates"""
    await websocket_manager.send_optimization_update(store_id, update_data)
```

## 7. Production Implementation Checklist

### Environment Configuration
```python
# environment.py
import os
from typing import Dict, Any

class GridSpokeConfig:
    """Centralized configuration management"""
    
    # OpenRouter Configuration
    OPENROUTER_API_KEY: str = os.getenv("OPENROUTER_API_KEY")
    OPENROUTER_BASE_URL: str = "https://openrouter.ai/api/v1"
    OPENROUTER_SITE_URL: str = "https://gridspoke.com"
    OPENROUTER_SITE_NAME: str = "GridSpoke SEO Optimizer"
    
    # Model Selection
    DEFAULT_PREMIUM_MODEL: str = "anthropic/claude-3-opus"
    DEFAULT_STANDARD_MODEL: str = "openai/gpt-4o"
    DEFAULT_ECONOMY_MODEL: str = "openai/gpt-3.5-turbo"
    
    # Caching
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    CACHE_TTL: int = 86400  # 24 hours
    
    # Rate Limiting
    MAX_REQUESTS_PER_MINUTE: int = 60
    MAX_BATCH_SIZE: int = 100
    
    # Cost Controls
    MAX_MONTHLY_SPEND: float = float(os.getenv("MAX_MONTHLY_SPEND", "1000.0"))
    COST_ALERT_THRESHOLD: float = 0.8  # Alert at 80% of budget
    
    @classmethod
    def get_openrouter_headers(cls) -> Dict[str, str]:
        """Get OpenRouter-specific headers"""
        return {
            "HTTP-Referer": cls.OPENROUTER_SITE_URL,
            "X-Title": cls.OPENROUTER_SITE_NAME
        }
```

### Error Handling and Monitoring
```python
import logging
from typing import Optional
from dataclasses import dataclass

@dataclass
class OptimizationError:
    """Structured error reporting for optimization failures"""
    product_id: str
    error_type: str
    error_message: str
    model_used: Optional[str]
    timestamp: datetime
    retry_count: int = 0

class SEOOptimizationMonitor:
    """Monitoring and error handling for SEO optimization"""
    
    def __init__(self):
        self.logger = logging.getLogger("gridspoke.seo.optimizer")
        self.error_history = []
        self.success_metrics = {
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "failed_optimizations": 0,
            "average_response_time": 0.0
        }
    
    def log_optimization_attempt(self, product_id: str, model: str):
        """Log optimization attempt"""
        self.logger.info(f"Starting optimization for product {product_id} using model {model}")
    
    def log_optimization_success(self, product_id: str, model: str, response_time: float):
        """Log successful optimization"""
        self.success_metrics["total_optimizations"] += 1
        self.success_metrics["successful_optimizations"] += 1
        self.success_metrics["average_response_time"] = (
            (self.success_metrics["average_response_time"] * (self.success_metrics["total_optimizations"] - 1) + response_time) 
            / self.success_metrics["total_optimizations"]
        )
        self.logger.info(f"Successfully optimized product {product_id} in {response_time:.2f}s")
    
    def log_optimization_failure(self, error: OptimizationError):
        """Log failed optimization"""
        self.success_metrics["total_optimizations"] += 1
        self.success_metrics["failed_optimizations"] += 1
        self.error_history.append(error)
        self.logger.error(f"Optimization failed for product {error.product_id}: {error.error_message}")
    
    def get_health_metrics(self) -> Dict[str, Any]:
        """Get system health metrics"""
        total = self.success_metrics["total_optimizations"]
        if total == 0:
            return {"status": "no_data"}
        
        success_rate = self.success_metrics["successful_optimizations"] / total
        recent_errors = [e for e in self.error_history if (datetime.utcnow() - e.timestamp).seconds < 3600]
        
        return {
            "status": "healthy" if success_rate > 0.95 else "degraded" if success_rate > 0.8 else "unhealthy",
            "success_rate": success_rate,
            "total_optimizations": total,
            "average_response_time": self.success_metrics["average_response_time"],
            "recent_errors": len(recent_errors),
            "error_types": list(set(e.error_type for e in recent_errors))
        }
```

## 8. Security and Compliance Considerations

### API Key Management
```python
import os
from cryptography.fernet import Fernet

class SecureConfigManager:
    """Secure handling of API keys and sensitive configuration"""
    
    def __init__(self):
        self.encryption_key = os.getenv("ENCRYPTION_KEY")
        if self.encryption_key:
            self.cipher = Fernet(self.encryption_key.encode())
    
    def encrypt_api_key(self, api_key: str) -> str:
        """Encrypt API key for storage"""
        if self.cipher:
            return self.cipher.encrypt(api_key.encode()).decode()
        return api_key
    
    def decrypt_api_key(self, encrypted_key: str) -> str:
        """Decrypt API key for use"""
        if self.cipher:
            return self.cipher.decrypt(encrypted_key.encode()).decode()
        return encrypted_key
    
    @property
    def openrouter_api_key(self) -> str:
        """Get decrypted OpenRouter API key"""
        encrypted_key = os.getenv("OPENROUTER_API_KEY_ENCRYPTED")
        if encrypted_key:
            return self.decrypt_api_key(encrypted_key)
        return os.getenv("OPENROUTER_API_KEY", "")
```

### Data Privacy Controls
```python
class PrivacyComplianceManager:
    """Ensure compliance with data privacy regulations"""
    
    def __init__(self):
        self.data_retention_days = 30
        self.anonymize_user_data = True
    
    def sanitize_product_data(self, product_data: dict) -> dict:
        """Remove or anonymize sensitive product information"""
        sanitized = product_data.copy()
        
        # Remove customer-specific information
        sensitive_fields = ["customer_email", "internal_notes", "supplier_info"]
        for field in sensitive_fields:
            sanitized.pop(field, None)
        
        # Anonymize if required
        if self.anonymize_user_data:
            sanitized["store_id"] = self.hash_identifier(sanitized.get("store_id", ""))
        
        return sanitized
    
    def hash_identifier(self, identifier: str) -> str:
        """Create anonymized hash of identifier"""
        import hashlib
        return hashlib.sha256(identifier.encode()).hexdigest()[:16]
    
    def should_use_no_training_models(self, store_settings: dict) -> bool:
        """Check if store requires no-training models"""
        return store_settings.get("privacy_mode", False) or store_settings.get("gdpr_compliance", False)
```

## 9. Testing Strategy

### Unit Tests for Mirascope Integration
```python
import pytest
from unittest.mock import Mock, patch
from gridspoke.seo.optimizer import ProductSEOOptimizer

class TestProductSEOOptimizer:
    """Test suite for SEO optimization functionality"""
    
    @pytest.fixture
    def mock_product_data(self):
        return {
            "id": "test-product-123",
            "name": "Wireless Bluetooth Headphones",
            "category": "Electronics",
            "features": ["Noise Cancelling", "30-hour Battery", "Quick Charge"],
            "price": 199.99,
            "description": "High-quality wireless headphones"
        }
    
    @pytest.fixture
    def optimizer(self):
        return ProductSEOOptimizer(
            model_selector=Mock(),
            cache_manager=Mock(),
            openrouter_config={"api_key": "test-key", "base_url": "https://test.com"}
        )
    
    @patch('mirascope.llm.call')
    async def test_title_generation(self, mock_llm_call, optimizer, mock_product_data):
        """Test SEO title generation"""
        mock_llm_call.return_value = "Premium Wireless Bluetooth Headphones - 30Hr Battery & Noise Cancelling"
        
        result = await optimizer.generate_title(mock_product_data)
        
        assert len(result) <= 60  # SEO title length requirement
        assert "Wireless Bluetooth Headphones" in result
        mock_llm_call.assert_called_once()
    
    @patch('mirascope.llm.call')
    async def test_meta_description_generation(self, mock_llm_call, optimizer, mock_product_data):
        """Test meta description generation"""
        mock_llm_call.return_value = "Shop premium wireless headphones with 30-hour battery and noise cancelling. Free shipping and 2-year warranty. Order now!"
        
        result = await optimizer.generate_meta_description(mock_product_data)
        
        assert len(result) <= 155  # Meta description length requirement
        assert "wireless headphones" in result.lower()
        mock_llm_call.assert_called_once()
    
    async def test_cost_calculation(self, optimizer, mock_product_data):
        """Test cost calculation for optimization"""
        estimated_cost = optimizer.estimate_optimization_cost(mock_product_data, "standard")
        
        assert isinstance(estimated_cost, float)
        assert estimated_cost > 0
        assert estimated_cost < 1.0  # Should be reasonable cost per product
```

### Integration Tests
```python
import pytest
import asyncio
from gridspoke.seo.batch_optimizer import BatchSEOOptimizer

class TestOpenRouterIntegration:
    """Integration tests for OpenRouter API"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_openrouter_connectivity(self):
        """Test actual OpenRouter API connectivity"""
        # This test requires valid API key
        optimizer = BatchSEOOptimizer()
        
        test_product = {
            "name": "Test Product",
            "category": "Test Category",
            "features": ["Feature 1", "Feature 2"]
        }
        
        try:
            result = await optimizer.optimize_single_product(test_product)
            assert result is not None
            assert "title" in result
            assert "meta_description" in result
        except Exception as e:
            pytest.skip(f"OpenRouter API not available: {e}")
    
    @pytest.mark.integration
    async def test_fallback_mechanism(self):
        """Test fallback when primary model fails"""
        optimizer = BatchSEOOptimizer()
        
        # Force primary model to fail
        with patch('gridspoke.seo.optimizer.ProductSEOOptimizer.optimize_with_model') as mock_optimize:
            mock_optimize.side_effect = [Exception("Model unavailable"), {"title": "Fallback Title"}]
            
            result = await optimizer.optimize_with_fallback({"name": "Test Product"})
            
            assert result is not None
            assert result["model_used"] != "anthropic/claude-3-opus"  # Should use fallback
```

## 10. Deployment and Monitoring

### Docker Configuration
```dockerfile
# Dockerfile for GridSpoke AI Service
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Environment variables
ENV PYTHONPATH=/app
ENV OPENROUTER_API_KEY=""
ENV REDIS_URL="redis://redis:6379"

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')"

# Run application
CMD ["uvicorn", "gridspoke.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose Integration
```yaml
# docker-compose.yml addition for AI services
version: '3.8'
services:
  ai-optimizer:
    build: .
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=************************************/gridspoke
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./logs:/app/logs
    networks:
      - gridspoke-network

  celery-ai-worker:
    build: .
    command: celery -A gridspoke.celery worker --loglevel=info --concurrency=4
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=************************************/gridspoke
    depends_on:
      - redis
      - postgres
      - ai-optimizer
    volumes:
      - ./logs:/app/logs
    networks:
      - gridspoke-network
```

## 11. Next Steps for Implementation

### Phase 1: Core Integration (Week 1-2)
1. **Set up Mirascope environment** with OpenRouter configuration
2. **Implement basic product optimization** with title and meta description generation
3. **Create simple streaming interface** for real-time feedback
4. **Basic error handling and logging**

### Phase 2: Advanced Features (Week 3-4)
1. **Implement agent patterns** with state management
2. **Add tool integration** for SEO scoring and competitor analysis
3. **Create smart model selection** based on content type and cost
4. **Implement caching strategy** for similar products

### Phase 3: Production Readiness (Week 5-6)
1. **Comprehensive error handling** and fallback mechanisms
2. **Monitoring and alerting** system
3. **Security and compliance** features
4. **Performance optimization** and load testing

### Phase 4: Integration (Week 7-8)
1. **Celery task integration** for bulk processing
2. **WebSocket real-time updates** to frontend
3. **WordPress plugin compatibility** testing
4. **End-to-end testing** and optimization

## Conclusion

The combination of Mirascope's elegant AI framework with OpenRouter's multi-provider API offers GridSpoke a powerful, cost-effective, and scalable solution for AI-powered ecommerce SEO optimization. The structured approach to agent patterns, streaming responses, and intelligent model selection provides the foundation for a production-ready service that can handle thousands of products efficiently while maintaining high quality output and cost control.

The key advantages of this architecture:

1. **Cost Efficiency**: Smart model selection and OpenRouter's competitive pricing
2. **Reliability**: Multi-provider fallbacks and robust error handling  
3. **Scalability**: Async processing with Celery and Redis caching
4. **User Experience**: Real-time streaming and WebSocket updates
5. **Maintainability**: Clean code patterns with strong typing and structured outputs

This research provides the technical foundation needed to implement Phase 3 of the GridSpoke project with confidence and best practices.
