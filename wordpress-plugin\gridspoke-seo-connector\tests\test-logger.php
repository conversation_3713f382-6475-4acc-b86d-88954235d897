<?php
/**
 * Tests for GridSpoke_Logger class
 */

class Test_GridSpoke_Logger extends GridSpoke_Test_Case {
    
    /**
     * Logger instance
     */
    private $logger;
    
    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();
        $this->logger = GridSpoke_Logger::get_instance();
    }
    
    /**
     * Test singleton instance
     */
    public function test_singleton_instance() {
        $instance1 = GridSpoke_Logger::get_instance();
        $instance2 = GridSpoke_Logger::get_instance();
        
        $this->assertSame($instance1, $instance2);
        $this->assertInstanceOf('GridSpoke_Logger', $instance1);
    }
    
    /**
     * Test basic logging functionality
     */
    public function test_basic_logging() {
        $this->logger->log('Test info message', 'info');
        $this->logger->log('Test error message', 'error');
        $this->logger->log('Test warning message', 'warning');
        
        $logs = $this->logger->get_logs();
        
        $this->assertCount(3, $logs);
        
        // Check that logs are ordered by timestamp (newest first)
        $this->assertEquals('Test warning message', $logs[0]['message']);
        $this->assertEquals('warning', $logs[0]['level']);
    }
    
    /**
     * Test logging with context
     */
    public function test_logging_with_context() {
        $context = array(
            'product_id' => 123,
            'action' => 'sync',
            'user_id' => 1
        );
        
        $this->logger->log('Test message with context', 'info', $context);
        
        $logs = $this->logger->get_logs();
        $this->assertCount(1, $logs);
        
        $log = $logs[0];
        $this->assertEquals('Test message with context', $log['message']);
        $this->assertEquals('info', $log['level']);
        $this->assertEquals(json_encode($context), $log['context']);
    }
    
    /**
     * Test log level filtering
     */
    public function test_log_level_filtering() {
        // Set log level to warning (should exclude debug and info)
        $this->update_plugin_settings(array('log_level' => 'warning'));
        
        $this->logger->log('Debug message', 'debug');
        $this->logger->log('Info message', 'info');
        $this->logger->log('Warning message', 'warning');
        $this->logger->log('Error message', 'error');
        
        $logs = $this->logger->get_logs();
        
        // Should only have warning and error logs
        $this->assertCount(2, $logs);
        
        $levels = array_column($logs, 'level');
        $this->assertContains('warning', $levels);
        $this->assertContains('error', $levels);
        $this->assertNotContains('debug', $levels);
        $this->assertNotContains('info', $levels);
    }
    
    /**
     * Test log retrieval with filters
     */
    public function test_log_retrieval_with_filters() {
        // Create test logs
        $this->logger->log('Error message 1', 'error');
        $this->logger->log('Info message 1', 'info');
        $this->logger->log('Error message 2', 'error');
        $this->logger->log('Warning message 1', 'warning');
        
        // Test level filter
        $error_logs = $this->logger->get_logs(array('level' => 'error'));
        $this->assertCount(2, $error_logs);
        
        foreach ($error_logs as $log) {
            $this->assertEquals('error', $log['level']);
        }
        
        // Test search filter
        $search_logs = $this->logger->get_logs(array('search' => 'Error message 1'));
        $this->assertCount(1, $search_logs);
        $this->assertEquals('Error message 1', $search_logs[0]['message']);
        
        // Test limit and offset
        $limited_logs = $this->logger->get_logs(array('limit' => 2, 'offset' => 1));
        $this->assertCount(2, $limited_logs);
    }
    
    /**
     * Test log clearing
     */
    public function test_log_clearing() {
        // Create test logs
        $this->logger->log('Error message', 'error');
        $this->logger->log('Info message', 'info');
        $this->logger->log('Warning message', 'warning');
        
        // Clear error logs only
        $result = $this->logger->clear_logs('error');
        $this->assertTrue($result);
        
        $remaining_logs = $this->logger->get_logs();
        $this->assertCount(2, $remaining_logs);
        
        $levels = array_column($remaining_logs, 'level');
        $this->assertNotContains('error', $levels);
        
        // Clear all logs
        $result = $this->logger->clear_logs();
        $this->assertTrue($result);
        
        $all_logs = $this->logger->get_logs();
        $this->assertCount(0, $all_logs);
    }
    
    /**
     * Test log statistics
     */
    public function test_log_statistics() {
        // Create test logs with different levels
        $this->logger->log('Error 1', 'error');
        $this->logger->log('Error 2', 'error');
        $this->logger->log('Warning 1', 'warning');
        $this->logger->log('Info 1', 'info');
        $this->logger->log('Info 2', 'info');
        $this->logger->log('Info 3', 'info');
        
        $stats = $this->logger->get_log_stats();
        
        $this->assertEquals(6, $stats['total']);
        $this->assertEquals(2, $stats['by_level']['error']);
        $this->assertEquals(1, $stats['by_level']['warning']);
        $this->assertEquals(3, $stats['by_level']['info']);
        
        // Check recent errors
        $this->assertCount(2, $stats['recent_errors']);
        $this->assertEquals('Error 2', $stats['recent_errors'][0]['message']);
    }
    
    /**
     * Test log export functionality
     */
    public function test_log_export() {
        // Create test logs
        $this->logger->log('Export test error', 'error', array('test' => 'context'));
        $this->logger->log('Export test info', 'info');
        
        $export_result = $this->logger->export_logs();
        
        $this->assertNotFalse($export_result);
        $this->assertArrayHasKey('download_url', $export_result);
        $this->assertArrayHasKey('filename', $export_result);
        
        // Check that file was created
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/gridspoke-logs/' . $export_result['filename'];
        $this->assertFileExists($file_path);
        
        // Check file content
        $content = file_get_contents($file_path);
        $this->assertStringContainsString('Export test error', $content);
        $this->assertStringContainsString('Export test info', $content);
        $this->assertStringContainsString('Timestamp,Level,Message,Context', $content); // CSV header
        
        // Clean up
        unlink($file_path);
    }
    
    /**
     * Test log export with filters
     */
    public function test_log_export_with_filters() {
        // Create test logs
        $this->logger->log('Filtered error', 'error');
        $this->logger->log('Filtered info', 'info');
        $this->logger->log('Another error', 'error');
        
        // Export only error logs
        $export_result = $this->logger->export_logs('error');
        
        $this->assertNotFalse($export_result);
        
        // Check file content
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/gridspoke-logs/' . $export_result['filename'];
        $content = file_get_contents($file_path);
        
        $this->assertStringContainsString('Filtered error', $content);
        $this->assertStringContainsString('Another error', $content);
        $this->assertStringNotContainsString('Filtered info', $content);
        
        // Clean up
        unlink($file_path);
    }
    
    /**
     * Test automatic log cleanup
     */
    public function test_automatic_log_cleanup() {
        global $wpdb;
        
        // Set cleanup to 7 days
        $this->update_plugin_settings(array('cleanup_logs_days' => 7));
        
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        
        // Create old log (10 days ago)
        $wpdb->insert($table_name, array(
            'level' => 'info',
            'message' => 'Old log message',
            'context' => '',
            'user_id' => 1,
            'ip_address' => '127.0.0.1',
            'timestamp' => date('Y-m-d H:i:s', time() - (10 * DAY_IN_SECONDS))
        ));
        
        // Create recent log (2 days ago)
        $wpdb->insert($table_name, array(
            'level' => 'info',
            'message' => 'Recent log message',
            'context' => '',
            'user_id' => 1,
            'ip_address' => '127.0.0.1',
            'timestamp' => date('Y-m-d H:i:s', time() - (2 * DAY_IN_SECONDS))
        ));
        
        // Run cleanup
        $this->logger->cleanup_old_logs();
        
        $logs = $this->logger->get_logs();
        
        // Should only have the recent log
        $this->assertCount(1, $logs);
        $this->assertEquals('Recent log message', $logs[0]['message']);
    }
    
    /**
     * Test static log method
     */
    public function test_static_log_method() {
        GridSpoke_Logger::log('Static log test', 'info');
        
        $logs = $this->logger->get_logs();
        $this->assertCount(1, $logs);
        $this->assertEquals('Static log test', $logs[0]['message']);
        $this->assertEquals('info', $logs[0]['level']);
    }
}
