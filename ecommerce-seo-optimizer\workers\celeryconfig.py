"""
Celery Configuration for GridSpoke Ecommerce SEO Optimizer
Contains all Celery settings including beat schedule, task routing, and performance settings.
"""

from celery.schedules import crontab
from datetime import timedelta
import os

# Broker and backend configuration
broker_url = os.environ.get('CELERY_BROKER_URL', 'redis://redis:6379/0')
result_backend = os.environ.get('CELERY_RESULT_BACKEND', 'redis://redis:6379/0')

# Timezone configuration
timezone = 'UTC'
enable_utc = True

# Task execution settings
task_serializer = 'json'
accept_content = ['json']
result_serializer = 'json'
task_compression = 'gzip'
result_compression = 'gzip'

# Task timing settings
task_time_limit = 30 * 60  # 30 minutes hard limit
task_soft_time_limit = 25 * 60  # 25 minutes soft limit
task_acks_late = True
task_reject_on_worker_lost = True
task_track_started = True

# Result settings
result_expires = 3600  # 1 hour
result_compression = 'gzip'

# Worker settings
worker_prefetch_multiplier = 1
worker_max_tasks_per_child = 1000
worker_disable_rate_limits = False
worker_hijack_root_logger = False
worker_log_format = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
worker_task_log_format = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'

# Beat scheduler settings
beat_scheduler = 'celery.beat:PersistentScheduler'
beat_schedule_filename = 'celerybeat-schedule'

# Celery Beat schedule for GridSpoke automation
beat_schedule = {
    # Daily optimization run at 2 AM (off-peak hours for cost optimization)
    'daily-product-optimization': {
        'task': 'workers.tasks.scheduled_tasks.daily_optimization_run',
        'schedule': crontab(hour=2, minute=0),
        'options': {'queue': 'scheduled_jobs', 'priority': 5}
    },
    
    # Weekly SEO performance analysis every Monday at 3 AM
    'weekly-seo-analysis': {
        'task': 'workers.tasks.scheduled_tasks.weekly_seo_analysis',
        'schedule': crontab(day_of_week=1, hour=3, minute=0),
        'options': {'queue': 'scheduled_jobs', 'priority': 3}
    },
    
    # Hourly store data sync check
    'hourly-store-sync': {
        'task': 'workers.tasks.scheduled_tasks.check_store_updates',
        'schedule': crontab(minute=0),  # Every hour
        'options': {'queue': 'scheduled_jobs', 'priority': 2}
    },
    
    # Monthly analytics aggregation on the 1st of each month at 4 AM
    'monthly-analytics': {
        'task': 'workers.tasks.scheduled_tasks.monthly_analytics_report',
        'schedule': crontab(day_of_month=1, hour=4, minute=0),
        'options': {'queue': 'scheduled_jobs', 'priority': 3}
    },
    
    # Clean up old task results daily at 1:30 AM
    'cleanup-old-results': {
        'task': 'workers.tasks.scheduled_tasks.cleanup_old_task_results',
        'schedule': crontab(hour=1, minute=30),
        'options': {'queue': 'scheduled_jobs', 'priority': 1}
    },
    
    # Health check every 5 minutes
    'health-check': {
        'task': 'workers.celery_app.health_check',
        'schedule': timedelta(minutes=5),
        'options': {'queue': 'scheduled_jobs', 'priority': 1}
    },
    
    # Cost optimization analysis weekly on Sunday at 5 AM
    'weekly-cost-analysis': {
        'task': 'workers.tasks.scheduled_tasks.analyze_api_costs',
        'schedule': crontab(day_of_week=0, hour=5, minute=0),
        'options': {'queue': 'scheduled_jobs', 'priority': 3}
    }
}

# Task routing configuration
task_routes = {
    'workers.tasks.product_tasks.*': {'queue': 'product_optimization'},
    'workers.tasks.content_tasks.*': {'queue': 'content_generation'},
    'workers.tasks.scheduled_tasks.*': {'queue': 'scheduled_jobs'},
    'workers.tasks.bulk_tasks.*': {'queue': 'bulk_processing'},
    'workers.celery_app.health_check': {'queue': 'scheduled_jobs'},
    'workers.celery_app.debug_task': {'queue': 'product_optimization'},
}

# Error handling
task_annotations = {
    '*': {
        'rate_limit': '100/m',  # Global rate limit
        'time_limit': 1800,     # 30 minutes
        'soft_time_limit': 1500, # 25 minutes
    },
    'workers.tasks.product_tasks.optimize_single_product': {
        'rate_limit': '50/m',   # More restrictive for AI calls
        'time_limit': 300,      # 5 minutes for single product
        'soft_time_limit': 240, # 4 minutes soft limit
    },
    'workers.tasks.bulk_tasks.optimize_bulk_products': {
        'rate_limit': '10/m',   # Very restrictive for bulk operations
        'time_limit': 3600,     # 1 hour for bulk processing
        'soft_time_limit': 3300, # 55 minutes soft limit
    }
}

# Monitoring and logging
worker_send_task_events = True
task_send_sent_event = True

# Security settings
worker_disable_rate_limits = False
task_always_eager = False  # Set to True only for testing

# Custom settings for GridSpoke
GRIDSPOKE_SETTINGS = {
    'OPTIMIZATION_BATCH_SIZE': 50,
    'MAX_CONCURRENT_AI_CALLS': 10,
    'OPENROUTER_RATE_LIMIT': 60,  # requests per minute
    'COST_TRACKING_ENABLED': True,
    'WEBHOOK_TIMEOUT': 30,
    'RETRY_EXPONENTIAL_BASE': 2,
    'RETRY_EXPONENTIAL_MAX': 300,  # 5 minutes max retry delay
}
