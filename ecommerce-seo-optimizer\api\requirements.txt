# FastAPI Dependencies
fastapi[standard]==0.115.0
uvicorn[standard]==0.30.6

# Logging
structlog==25.4.0

# Database
sqlalchemy[asyncio]==2.0.34
alembic==1.13.2
asyncpg==0.29.0
# psycopg2-binary removed; async engine uses asyncpg

# Vector Database
pgvector==0.3.2

# AI Framework
mirascope[openai]==1.0.0
openai==1.44.1

# Async Processing
celery==5.4.0
redis==5.0.8

# Rate Limiting
slowapi==0.1.9
limits==3.13.0

# Caching & Serialization
pickle-mixin==1.0.2

# Timing utilities
pytz==2024.1

# Data Validation
pydantic==2.8.2
pydantic-settings==2.4.0

# HTTP Client
httpx==0.27.2
aiohttp==3.10.5

# Security
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.9

# Utilities
python-dotenv==1.0.1
email-validator==2.2.0
jinja2==3.1.4

# Monitoring
prometheus-client==0.20.0

# Development
pytest==8.3.2
pytest-asyncio==0.24.0
black==24.8.0
isort==5.13.2
mypy==1.11.2
