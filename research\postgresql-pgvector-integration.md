# PostgreSQL + pgvector Integration Research (2025)

## Overview
PostgreSQL with pgvector extension provides vector similarity search capabilities essential for AI-powered ecommerce SEO, enabling semantic product matching and intelligent content recommendations.

## Key Components

### 1. PostgreSQL 16 + pgvector
- **Version**: PostgreSQL 16 for latest performance improvements
- **Extension**: pgvector for vector operations and similarity search
- **Docker Image**: `pgvector/pgvector:pg16`
- **Vector Dimensions**: 1536 (OpenAI embeddings standard)

### 2. SQLAlchemy 2.0 Async Patterns
```python
# Modern async database configuration
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import declarative_base, sessionmaker

DATABASE_URL = "postgresql+asyncpg://user:pass@localhost/gridspoke"

engine = create_async_engine(DATABASE_URL, echo=True)
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

Base = declarative_base()
```

### 3. Database Schema Design

#### Core Tables
```sql
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;

-- Stores table
CREATE TABLE stores (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    platform VARCHAR(50), -- 'woocommerce', 'surecart', etc.
    api_credentials JSONB,
    settings JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Products table with vector embeddings
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID REFERENCES stores(id) ON DELETE CASCADE,
    external_id VARCHAR(255) NOT NULL,
    title VARCHAR(500),
    description TEXT,
    meta_description VARCHAR(500),
    category VARCHAR(255),
    price DECIMAL(10,2),
    currency VARCHAR(3),
    sku VARCHAR(255),
    image_urls JSONB,
    keywords JSONB,
    
    -- Vector embeddings for semantic search
    title_embedding vector(1536),
    description_embedding vector(1536),
    
    -- SEO optimization tracking
    optimization_status VARCHAR(50) DEFAULT 'pending',
    optimization_score DECIMAL(3,2),
    last_optimized TIMESTAMP,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(store_id, external_id)
);

-- Optimization jobs tracking
CREATE TABLE optimization_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    store_id UUID REFERENCES stores(id),
    job_type VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    priority INTEGER DEFAULT 5,
    
    -- Progress tracking
    total_items INTEGER,
    processed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    
    -- Job metadata
    parameters JSONB,
    results JSONB,
    error_log JSONB,
    
    -- Timing
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Generated content versions
CREATE TABLE generated_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    job_id UUID REFERENCES optimization_jobs(id),
    
    content_type VARCHAR(100) NOT NULL, -- 'title', 'description', 'meta_description', etc.
    content TEXT NOT NULL,
    
    -- AI model tracking
    ai_model VARCHAR(100),
    ai_provider VARCHAR(50),
    tokens_used INTEGER,
    processing_time_ms INTEGER,
    
    -- A/B testing support
    variant_name VARCHAR(100),
    performance_score DECIMAL(5,4),
    is_active BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP DEFAULT NOW()
);

-- Content performance tracking
CREATE TABLE content_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id),
    content_id UUID REFERENCES generated_content(id),
    
    -- SEO metrics
    impressions INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    ctr DECIMAL(5,4),
    conversion_rate DECIMAL(5,4),
    
    -- Date tracking
    metric_date DATE,
    
    created_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(product_id, content_id, metric_date)
);
```

#### Indexes for Performance
```sql
-- Standard indexes
CREATE INDEX idx_products_store_id ON products(store_id);
CREATE INDEX idx_products_external_id ON products(external_id);
CREATE INDEX idx_products_optimization_status ON products(optimization_status);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_optimization_jobs_store_id ON optimization_jobs(store_id);
CREATE INDEX idx_optimization_jobs_status ON optimization_jobs(status);
CREATE INDEX idx_generated_content_product_id ON generated_content(product_id);
CREATE INDEX idx_generated_content_type ON generated_content(content_type);

-- Vector similarity indexes
CREATE INDEX idx_products_title_embedding ON products 
USING ivfflat (title_embedding vector_cosine_ops) WITH (lists = 100);

CREATE INDEX idx_products_description_embedding ON products 
USING ivfflat (description_embedding vector_cosine_ops) WITH (lists = 100);

-- JSONB indexes for fast keyword searches
CREATE INDEX idx_products_keywords ON products USING GIN(keywords);
CREATE INDEX idx_stores_settings ON stores USING GIN(settings);
```

## Vector Operations & Similarity Search

### 1. Semantic Product Search
```sql
-- Find similar products based on title embeddings
SELECT p.id, p.title, p.description,
       1 - (p.title_embedding <=> query_embedding) AS similarity
FROM products p
WHERE store_id = $1
ORDER BY p.title_embedding <=> query_embedding
LIMIT 10;
```

### 2. SQLAlchemy Vector Operations
```python
from sqlalchemy import func, text
from pgvector.sqlalchemy import Vector

class Product(Base):
    __tablename__ = 'products'
    
    id = Column(UUID(as_uuid=True), primary_key=True)
    title = Column(String(500))
    description = Column(Text)
    title_embedding = Column(Vector(1536))
    description_embedding = Column(Vector(1536))

# Find similar products
async def find_similar_products(session: AsyncSession, query_embedding, limit=10):
    result = await session.execute(
        select(Product)
        .order_by(Product.title_embedding.cosine_distance(query_embedding))
        .limit(limit)
    )
    return result.scalars().all()
```

### 3. Embedding Generation Integration
```python
import openai
from typing import List

async def generate_embeddings(texts: List[str]) -> List[List[float]]:
    """Generate embeddings using OpenAI API via OpenRouter"""
    client = openai.AsyncOpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=os.getenv("OPENROUTER_API_KEY")
    )
    
    response = await client.embeddings.create(
        model="text-embedding-ada-002",
        input=texts
    )
    
    return [item.embedding for item in response.data]

async def update_product_embeddings(session: AsyncSession, product_id: UUID):
    """Update product embeddings after content changes"""
    product = await session.get(Product, product_id)
    
    if product:
        embeddings = await generate_embeddings([
            product.title or "",
            product.description or ""
        ])
        
        product.title_embedding = embeddings[0]
        product.description_embedding = embeddings[1]
        
        await session.commit()
```

## Performance Optimization

### 1. Connection Pooling
```python
# Optimized async engine configuration
engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,
    max_overflow=0,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

### 2. Batch Operations
```python
async def bulk_insert_products(session: AsyncSession, products_data: List[dict]):
    """Efficient bulk insert for products"""
    await session.execute(
        insert(Product),
        products_data
    )
    await session.commit()
```

### 3. Vector Index Optimization
```sql
-- Tune vector index for better performance
ALTER INDEX idx_products_title_embedding SET (lists = 1000);
REINDEX INDEX idx_products_title_embedding;
```

## Backup & Migration Strategies

### 1. Database Migrations (Alembic)
```python
# alembic/versions/001_initial_schema.py
from alembic import op
import sqlalchemy as sa
from pgvector.sqlalchemy import Vector

def upgrade():
    # Enable extensions
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    op.execute('CREATE EXTENSION IF NOT EXISTS vector')
    
    # Create tables
    op.create_table('stores', ...)
    op.create_table('products', ...)
```

### 2. Data Backup
```bash
# Backup with vector data
pg_dump -h localhost -U user -d gridspoke -v -f backup.sql

# Restore
psql -h localhost -U user -d gridspoke -f backup.sql
```

## Integration with Application

### 1. Database Service Layer
```python
# api/services/database.py
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
import uuid

class ProductService:
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create_product(self, store_id: uuid.UUID, product_data: dict) -> Product:
        product = Product(store_id=store_id, **product_data)
        self.session.add(product)
        await self.session.commit()
        await self.session.refresh(product)
        return product
    
    async def find_similar_products(
        self, 
        query_embedding: List[float], 
        store_id: Optional[uuid.UUID] = None,
        limit: int = 10
    ) -> List[Product]:
        query = select(Product).order_by(
            Product.title_embedding.cosine_distance(query_embedding)
        )
        
        if store_id:
            query = query.where(Product.store_id == store_id)
            
        result = await self.session.execute(query.limit(limit))
        return result.scalars().all()
```

### 2. Health Checks
```python
# api/health.py
async def check_database_health():
    try:
        async with AsyncSessionLocal() as session:
            await session.execute(text("SELECT 1"))
            return {"status": "healthy", "database": "connected"}
    except Exception as e:
        return {"status": "unhealthy", "database": str(e)}
```

## Monitoring & Observability

### 1. Query Performance
```sql
-- Monitor slow queries
SELECT query, mean_exec_time, calls
FROM pg_stat_statements
WHERE mean_exec_time > 1000
ORDER BY mean_exec_time DESC;
```

### 2. Vector Index Usage
```sql
-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read
FROM pg_stat_user_indexes
WHERE indexname LIKE '%embedding%';
```
