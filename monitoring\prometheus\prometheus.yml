# GridSpoke Prometheus Configuration
# Phase 8: Monitoring configuration for GridSpoke services

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'gridspoke-monitor'
    environment: 'production'  # Override via env variables

# Rules for alerting
rule_files:
  - "rules/*.yml"

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Scrape configurations
scrape_configs:
  
  # =============================================================================
  # GRIDSPOKE APPLICATION METRICS
  # =============================================================================
  
  # Main GridSpoke API metrics
  - job_name: 'gridspoke-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/api/v1/monitoring/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    scheme: http
    honor_labels: true
    params:
      format: ['prometheus']

  # GridSpoke custom business metrics
  - job_name: 'gridspoke-metrics'
    static_configs:
      - targets: ['gridspoke-metrics:8090']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # GridSpoke health checks
  - job_name: 'gridspoke-health'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/api/v1/monitoring/health'
    scrape_interval: 30s
    scrape_timeout: 10s
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'gridspoke_health_.*'
        target_label: __name__
        replacement: '${1}'

  # =============================================================================
  # INFRASTRUCTURE METRICS
  # =============================================================================

  # System metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'gridspoke-host'

  # Container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metric_relabel_configs:
      - source_labels: [container_label_com_docker_compose_service]
        target_label: service
      - source_labels: [container_label_com_docker_compose_project]
        target_label: project

  # =============================================================================
  # DATABASE AND STORAGE METRICS
  # =============================================================================

  # PostgreSQL metrics
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    relabel_configs:
      - source_labels: [__address__]
        target_label: database
        replacement: 'gridspoke-postgres'

  # Redis metrics
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: redis_instance
        replacement: 'gridspoke-redis'

  # =============================================================================
  # WEB SERVER METRICS
  # =============================================================================

  # Nginx metrics (if using nginx)
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 15s
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'nginx_.*'
        target_label: component
        replacement: 'web-server'

  # =============================================================================
  # EXTERNAL SERVICE MONITORING
  # =============================================================================

  # Blackbox exporter for external endpoint monitoring
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://openrouter.ai/api/v1/models  # OpenRouter API health
        - https://api.wordpress.org/stats/      # WordPress API health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # SSL certificate monitoring
  - job_name: 'blackbox-ssl'
    metrics_path: /probe
    params:
      module: [tcp_connect]
    static_configs:
      - targets:
        - openrouter.ai:443
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # =============================================================================
  # MONITORING INFRASTRUCTURE
  # =============================================================================

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s

  # Alertmanager monitoring
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 30s

  # Grafana monitoring
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

# =============================================================================
# REMOTE WRITE (Optional - for long-term storage)
# =============================================================================

# Uncomment for remote storage solutions like Grafana Cloud, DataDog, etc.
# remote_write:
#   - url: "https://your-remote-write-endpoint"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
#     write_relabel_configs:
#       - source_labels: [__name__]
#         regex: 'gridspoke_.*'
#         action: keep

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

storage:
  tsdb:
    retention.time: 15d      # Keep 15 days of data
    retention.size: 10GB     # Maximum storage size
    wal-compression: true    # Compress WAL files
    no-lockfile: false       # Use lockfile for safety

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable features
enable_features:
  - exemplar-storage        # Enable exemplar storage for tracing correlation
  - memory-snapshot-on-shutdown  # Create memory snapshot on shutdown
  - expand-external-labels  # Expand external labels in queries
