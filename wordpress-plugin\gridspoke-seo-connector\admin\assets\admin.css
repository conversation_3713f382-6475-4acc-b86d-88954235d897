/* GridSpoke SEO Connector Admin Styles */

/* General <PERSON><PERSON> */
.wrap .gridspoke-settings-header {
    margin-bottom: 30px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.gridspoke-settings-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.gridspoke-settings-actions .button {
    margin-right: 10px;
}

/* Info Cards */
.gridspoke-settings-info {
    margin-top: 40px;
}

.gridspoke-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.gridspoke-info-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.gridspoke-info-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.gridspoke-webhook-url {
    display: block;
    background: #f6f7f7;
    padding: 10px;
    border-radius: 3px;
    margin: 10px 0;
    word-break: break-all;
    font-family: Consolas, Monaco, monospace;
    font-size: 12px;
}

.gridspoke-platform-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.gridspoke-platform-list li {
    padding: 8px 0;
    display: flex;
    align-items: center;
}

.gridspoke-platform-list .dashicons {
    margin-right: 8px;
}

.platform-active {
    color: #46b450;
}

.platform-inactive {
    color: #dc3232;
}

.gridspoke-info-table {
    width: 100%;
}

.gridspoke-info-table td {
    padding: 5px 0;
    vertical-align: top;
}

.gridspoke-info-table td:first-child {
    font-weight: 600;
    width: 40%;
}

/* Quick Actions */
.gridspoke-quick-actions {
    margin-top: 40px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.gridspoke-action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.gridspoke-action-buttons .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Help Section */
.gridspoke-help-section {
    margin-top: 40px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.gridspoke-help-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.gridspoke-help-links .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Status Badges */
.gridspoke-status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    color: #fff;
}

.gridspoke-status-badge.completed {
    background-color: #46b450;
}

.gridspoke-status-badge.pending {
    background-color: #ffb900;
}

.gridspoke-status-badge.failed {
    background-color: #dc3232;
}

.gridspoke-status-badge.not-optimized {
    background-color: #8f98a1;
}

.gridspoke-status-badge.no-id {
    background-color: #ddd;
    color: #666;
}

/* Dashboard Widget */
.gridspoke-dashboard-widget {
    padding: 0;
}

.gridspoke-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.gridspoke-stat-item {
    text-align: center;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.gridspoke-stat-number {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #0073aa;
    margin-bottom: 5px;
}

.gridspoke-stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.gridspoke-last-activity {
    margin: 15px 0;
    padding: 10px;
    background: #f0f0f1;
    border-radius: 3px;
    font-size: 13px;
}

.gridspoke-widget-actions {
    text-align: center;
    margin-top: 15px;
}

/* Product Meta Box */
.gridspoke-status.success {
    color: #46b450;
}

.gridspoke-status.warning {
    color: #ffb900;
}

.gridspoke-actions {
    margin-top: 15px;
}

.gridspoke-actions .button {
    margin-right: 5px;
    margin-bottom: 5px;
}

/* Dashboard Page */
.gridspoke-dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.gridspoke-dashboard-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.gridspoke-dashboard-number {
    display: block;
    font-size: 36px;
    font-weight: 600;
    color: #0073aa;
    margin-bottom: 10px;
}

.gridspoke-dashboard-label {
    display: block;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.gridspoke-chart-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 30px;
}

.gridspoke-chart-container h3 {
    margin-top: 0;
    margin-bottom: 20px;
}

/* Logs Page */
.gridspoke-logs-filters {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.gridspoke-logs-filters .form-table {
    margin: 0;
}

.gridspoke-logs-filters .form-table th {
    padding-left: 0;
}

.gridspoke-logs-table {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.gridspoke-logs-table .wp-list-table {
    border: none;
}

.gridspoke-log-level {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    color: #fff;
}

.gridspoke-log-level.debug {
    background-color: #8f98a1;
}

.gridspoke-log-level.info {
    background-color: #0073aa;
}

.gridspoke-log-level.warning {
    background-color: #ffb900;
}

.gridspoke-log-level.error {
    background-color: #dc3232;
}

.gridspoke-log-context {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.gridspoke-log-context-full {
    max-width: none;
    white-space: pre-wrap;
    font-family: Consolas, Monaco, monospace;
    font-size: 12px;
    background: #f6f7f7;
    padding: 10px;
    border-radius: 3px;
    margin-top: 10px;
}

/* Tools Page */
.gridspoke-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.gridspoke-tool-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.gridspoke-tool-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
}

.gridspoke-tool-actions {
    margin-top: 15px;
}

.gridspoke-tool-actions .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 782px) {
    .gridspoke-info-grid {
        grid-template-columns: 1fr;
    }
    
    .gridspoke-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .gridspoke-dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .gridspoke-action-buttons {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .gridspoke-help-links {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .gridspoke-dashboard-stats {
        grid-template-columns: 1fr;
    }
}

/* Loading States */
.gridspoke-loading {
    opacity: 0.6;
    pointer-events: none;
}

.gridspoke-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ddd;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: gridspoke-spin 1s linear infinite;
}

@keyframes gridspoke-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notices */
.gridspoke-notice {
    margin: 15px 0;
}

.gridspoke-notice.inline {
    display: inline-block;
    margin-left: 10px;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
}

.gridspoke-notice.success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.gridspoke-notice.error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.gridspoke-notice.warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* Form Enhancements */
.gridspoke-field-group {
    margin-bottom: 20px;
}

.gridspoke-field-label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
}

.gridspoke-field-description {
    font-size: 13px;
    color: #666;
    margin-top: 5px;
    line-height: 1.4;
}

.gridspoke-field-required {
    color: #dc3232;
}

/* Tooltips */
.gridspoke-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.gridspoke-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

.gridspoke-tooltip:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    z-index: 1000;
}

/* Code Blocks */
.gridspoke-code {
    background: #f6f7f7;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 10px;
    font-family: Consolas, Monaco, monospace;
    font-size: 12px;
    overflow-x: auto;
}

/* Progress Bars */
.gridspoke-progress {
    width: 100%;
    height: 20px;
    background: #f0f0f1;
    border-radius: 10px;
    overflow: hidden;
}

.gridspoke-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.gridspoke-progress-text {
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}
