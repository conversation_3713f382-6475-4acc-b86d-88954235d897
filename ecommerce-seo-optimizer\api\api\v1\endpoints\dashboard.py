"""
Dashboard endpoints.
"""
from typing import Any, Union, Dict, List
from fastapi import APIRouter, Depends, Query
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from core.database import get_db_session
from core.security import get_current_user
from models.user import User as UserModel
from models.store import Store as StoreModel
from models.product import Product as ProductModel
from models.optimization_job import OptimizationJob, JobStatus
import structlog

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get("/stats")
async def get_dashboard_stats(
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get dashboard statistics."""
    
    # Get all stores for the current user
    store_stmt = select(StoreModel).where(StoreModel.owner_id == current_user.id)
    store_result = await db.execute(store_stmt)
    stores = store_result.scalars().all()

    if not stores:
        logger.info("No stores found for user", user_id=current_user.id)
        return {
            "totalProducts": 0,
            "optimizedProducts": 0,
            "activeJobs": 0,
            "averageSeoScore": 0
        }

    # Prioritize WordPress/WooCommerce stores for dashboard display
    # Look for stores with "WordPress" in the name or platform "woocommerce"
    wordpress_stores = [s for s in stores if 'wordpress' in s.name.lower() or s.platform == 'woocommerce']
    
    # If we have WordPress stores, prefer them. Otherwise use all stores.
    if wordpress_stores:
        # Further prioritize stores with "wordpress" in the name
        named_wordpress_stores = [s for s in wordpress_stores if 'wordpress' in s.name.lower()]
        if named_wordpress_stores:
            target_stores = named_wordpress_stores
            logger.info("Using WordPress stores for dashboard", user_id=current_user.id, store_count=len(target_stores))
        else:
            target_stores = wordpress_stores
            logger.info("Using WooCommerce stores for dashboard", user_id=current_user.id, store_count=len(target_stores))
    else:
        target_stores = stores
        logger.info("Using all stores for dashboard", user_id=current_user.id, store_count=len(target_stores))

    store_ids = [store.id for store in target_stores]

    # Get total products
    total_products_stmt = select(func.count(ProductModel.id)).where(ProductModel.store_id.in_(store_ids))
    total_products_result = await db.execute(total_products_stmt)
    total_products = total_products_result.scalar_one()

    # Get optimized products
    optimized_products_stmt = select(func.count(ProductModel.id)).where(
        ProductModel.store_id.in_(store_ids),
        ProductModel.optimization_status == 'completed'
    )
    optimized_products_result = await db.execute(optimized_products_stmt)
    optimized_products = optimized_products_result.scalar_one()

    # Get active jobs
    active_jobs_stmt = select(func.count(OptimizationJob.id)).where(
        OptimizationJob.store_id.in_(store_ids),
        OptimizationJob.status.in_([JobStatus.IN_PROGRESS, JobStatus.QUEUED, JobStatus.PENDING])
    )
    active_jobs_result = await db.execute(active_jobs_stmt)
    active_jobs = active_jobs_result.scalar_one()

    # Get average SEO score
    avg_seo_score_stmt = select(func.avg(ProductModel.optimization_score)).where(
        ProductModel.store_id.in_(store_ids),
        ProductModel.optimization_score.isnot(None)
    )
    avg_seo_score_result = await db.execute(avg_seo_score_stmt)
    average_seo_score = avg_seo_score_result.scalar_one() or 0

    stats = {
        "totalProducts": total_products,
        "optimizedProducts": optimized_products,
        "activeJobs": active_jobs,
        "averageSeoScore": round(average_seo_score, 2)
    }
    
    logger.info("Dashboard stats calculated", user_id=current_user.id, stats=stats, 
                store_count=len(target_stores), stores_used=[s.name for s in target_stores])
    return stats


@router.get("/activity")
async def get_recent_activity(
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user),
    store_id: Union[str, None] = Query(None),
    limit: int = Query(20, ge=1, le=100)
) -> Any:
    """Return recent activity for the user or a specific store.

    Activity is composed from:
    - Recently optimized products (last_optimized)
    - Recent optimization jobs (created/started/completed)
    """
    # Gather stores in scope
    store_stmt = select(StoreModel).where(StoreModel.owner_id == current_user.id)
    store_result = await db.execute(store_stmt)
    stores = store_result.scalars().all()
    if not stores:
        return []

    valid_store_ids = {str(s.id) for s in stores}
    target_ids = valid_store_ids
    if store_id and str(store_id) in valid_store_ids:
        target_ids = {str(store_id)}

    # Recent optimized products
    prod_stmt = (
        select(
            ProductModel.title,
            ProductModel.last_optimized
        )
        .where(
            ProductModel.store_id.in_(list(target_ids)),
            ProductModel.last_optimized.isnot(None)
        )
        .order_by(ProductModel.last_optimized.desc())
        .limit(limit)
    )
    prod_result = await db.execute(prod_stmt)
    prod_rows = prod_result.all()

    product_events = [
        {
            "type": "product_optimized",
            "message": f"Product '{(title or 'Untitled')}' optimized",
            "timestamp": (ts.isoformat() if isinstance(ts, datetime) else ts)
        }
        for (title, ts) in prod_rows
        if ts is not None
    ]

    # Recent jobs
    job_stmt = (
        select(
            OptimizationJob.title,
            OptimizationJob.status,
            OptimizationJob.completed_at,
            OptimizationJob.started_at,
            OptimizationJob.created_at,
        )
        .where(OptimizationJob.store_id.in_(list(target_ids)))
        .order_by(OptimizationJob.created_at.desc())
        .limit(limit)
    )
    job_result = await db.execute(job_stmt)
    job_rows = job_result.all()

    def _job_ts(row):
        completed_at, started_at, created_at = row[2], row[3], row[4]
        return completed_at or started_at or created_at

    job_events = []
    for title, status, completed_at, started_at, created_at in job_rows:
        ts = completed_at or started_at or created_at
        if not ts:
            continue
        if status == JobStatus.COMPLETED:
            msg = f"Job '{title}' completed"
        elif status in (JobStatus.IN_PROGRESS, JobStatus.QUEUED, JobStatus.PENDING):
            msg = f"Job '{title}' started"
        elif status == JobStatus.FAILED:
            msg = f"Job '{title}' failed"
        else:
            msg = f"Job '{title}' updated"
        job_events.append({
            "type": "job",
            "message": msg,
            "timestamp": (ts.isoformat() if isinstance(ts, datetime) else ts)
        })

    # Merge and sort
    merged = product_events + job_events
    merged.sort(key=lambda e: e.get("timestamp") or "", reverse=True)
    return merged[:limit]


@router.get("/progress")
async def get_progress_series(
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user),
    store_id: Union[str, None] = Query(None),
    days: int = Query(30, ge=1, le=365)
) -> Any:
    """Return daily optimized product counts for the last N days."""
    # Determine stores
    store_stmt = select(StoreModel).where(StoreModel.owner_id == current_user.id)
    store_result = await db.execute(store_stmt)
    stores = store_result.scalars().all()
    if not stores:
        return {"labels": [], "data": []}

    valid_store_ids = {str(s.id) for s in stores}
    target_ids = valid_store_ids
    if store_id and str(store_id) in valid_store_ids:
        target_ids = {str(store_id)}

    since = datetime.utcnow() - timedelta(days=days)
    prod_stmt = (
        select(ProductModel.last_optimized)
        .where(
            ProductModel.store_id.in_(list(target_ids)),
            ProductModel.last_optimized.isnot(None),
            ProductModel.last_optimized >= since
        )
    )
    res = await db.execute(prod_stmt)
    timestamps = [row[0] for row in res.all() if row[0] is not None]

    # Bucket by day in Python for cross-DB compatibility
    buckets: Dict[str, int] = {}
    for ts in timestamps:
        day_str = ts.date().isoformat()
        buckets[day_str] = buckets.get(day_str, 0) + 1

    # Build contiguous series for the last N days
    labels: List[str] = []
    data: List[int] = []
    for i in range(days):
        day = (datetime.utcnow() - timedelta(days=(days - 1 - i))).date().isoformat()
        labels.append(day)
        data.append(buckets.get(day, 0))

    return {"labels": labels, "data": data}


@router.get("/job-status")
async def get_job_status_distribution(
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user),
    store_id: Union[str, None] = Query(None)
) -> Any:
    """Return current job counts by status for a store or all user stores."""
    store_stmt = select(StoreModel).where(StoreModel.owner_id == current_user.id)
    store_result = await db.execute(store_stmt)
    stores = store_result.scalars().all()
    if not stores:
        return {}

    valid_store_ids = {str(s.id) for s in stores}
    target_ids = valid_store_ids
    if store_id and str(store_id) in valid_store_ids:
        target_ids = {str(store_id)}

    status_counts = {s.value: 0 for s in JobStatus}

    jobs_stmt = select(OptimizationJob.status).where(OptimizationJob.store_id.in_(list(target_ids)))
    jobs_res = await db.execute(jobs_stmt)
    for (status,) in jobs_res.all():
        if isinstance(status, JobStatus):
            status_counts[status.value] = status_counts.get(status.value, 0) + 1
        else:
            # For safety if DB returns string
            status_counts[str(status)] = status_counts.get(str(status), 0) + 1

    return status_counts
