#!/usr/bin/env python3
"""
Comprehensive audit verification tests for GridSpoke.
This script tests the specific issues mentioned in CODEBASE_AUDIT_FINDINGS.md
"""

import sys
import os
import asyncio
import json
import pytest
import httpx
from typing import Dict, Any

# Add the API directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'ecommerce-seo-optimizer', 'api'))

class AuditVerificationTests:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.api_prefix = "/api/v1"
        self.test_results = {}
    
    async def test_health_endpoint(self):
        """Test if health endpoint exists and works (audit claimed it was missing)"""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                if response.status_code == 200:
                    self.test_results["health_endpoint"] = {
                        "status": "PASS", 
                        "audit_claim": "Missing health endpoint",
                        "reality": "Health endpoint exists and works",
                        "response": response.json()
                    }
                else:
                    self.test_results["health_endpoint"] = {
                        "status": "FAIL",
                        "audit_claim": "Missing health endpoint", 
                        "reality": f"Health endpoint returns {response.status_code}",
                        "response": response.text
                    }
            except Exception as e:
                self.test_results["health_endpoint"] = {
                    "status": "FAIL",
                    "audit_claim": "Missing health endpoint",
                    "reality": f"Health endpoint error: {str(e)}"
                }
    
    async def test_job_management_endpoints(self):
        """Test if job management endpoints are properly implemented (audit claimed they were TODOs)"""
        async with httpx.AsyncClient() as client:
            try:
                # Test getting jobs (without auth for now)
                response = await client.get(f"{self.base_url}{self.api_prefix}/jobs/?store_id=00000000-0000-0000-0000-000000000000")
                
                if "coming soon" in response.text.lower():
                    self.test_results["job_endpoints"] = {
                        "status": "FAIL",
                        "audit_claim": "Job endpoints return 'coming soon' placeholders",
                        "reality": "Audit claim is correct - endpoints still have placeholders",
                        "response": response.text
                    }
                else:
                    self.test_results["job_endpoints"] = {
                        "status": "PASS",
                        "audit_claim": "Job endpoints return 'coming soon' placeholders", 
                        "reality": "Job endpoints appear to be implemented (no 'coming soon' message)",
                        "response": response.text[:200]
                    }
            except Exception as e:
                self.test_results["job_endpoints"] = {
                    "status": "ERROR",
                    "audit_claim": "Job endpoints return 'coming soon' placeholders",
                    "reality": f"Error testing job endpoints: {str(e)}"
                }
    
    def test_celery_task_imports(self):
        """Test if Celery tasks can be imported (audit claimed they were missing)"""
        try:
            # Test optimization task imports
            from workers.tasks.optimization_tasks import optimize_single_product, optimize_product_batch
            self.test_results["celery_optimization_tasks"] = {
                "status": "PASS",
                "audit_claim": "Missing Celery task implementation",
                "reality": "Optimization tasks can be imported successfully"
            }
        except ImportError as e:
            self.test_results["celery_optimization_tasks"] = {
                "status": "FAIL", 
                "audit_claim": "Missing Celery task implementation",
                "reality": f"Cannot import optimization tasks: {str(e)}"
            }
        
        try:
            # Test product task imports (what the products.py endpoint actually uses)
            from workers.tasks.product_tasks import optimize_single_product as product_optimize_single
            self.test_results["celery_product_tasks"] = {
                "status": "PASS",
                "audit_claim": "Missing Celery task implementation",
                "reality": "Product tasks can be imported successfully"
            }
        except ImportError as e:
            self.test_results["celery_product_tasks"] = {
                "status": "FAIL",
                "audit_claim": "Missing Celery task implementation", 
                "reality": f"Cannot import product tasks: {str(e)}"
            }
    
    def test_crud_operations(self):
        """Test if CRUD operations exist (audit claimed they were missing)"""
        try:
            from crud import crud_job
            # Check if methods exist
            methods = ['create_job', 'get_job', 'get_jobs_by_store', 'update_job', 'cancel_job']
            existing_methods = [method for method in methods if hasattr(crud_job, method)]
            
            if len(existing_methods) == len(methods):
                self.test_results["job_crud"] = {
                    "status": "PASS",
                    "audit_claim": "No actual CRUD operations implemented",
                    "reality": "All CRUD operations are implemented",
                    "methods_found": existing_methods
                }
            else:
                missing = [method for method in methods if method not in existing_methods]
                self.test_results["job_crud"] = {
                    "status": "PARTIAL",
                    "audit_claim": "No actual CRUD operations implemented",
                    "reality": f"Some CRUD operations missing: {missing}",
                    "methods_found": existing_methods
                }
        except ImportError as e:
            self.test_results["job_crud"] = {
                "status": "FAIL",
                "audit_claim": "No actual CRUD operations implemented",
                "reality": f"Cannot import CRUD operations: {str(e)}"
            }
    
    def test_job_schemas(self):
        """Test if job schemas exist (audit claimed they were missing)"""
        try:
            from schemas.job import OptimizationJob, OptimizationJobCreate, JobCancellationResponse
            self.test_results["job_schemas"] = {
                "status": "PASS",
                "audit_claim": "No schemas defined for job data structures",
                "reality": "Job schemas are properly defined"
            }
        except ImportError as e:
            self.test_results["job_schemas"] = {
                "status": "FAIL",
                "audit_claim": "No schemas defined for job data structures",
                "reality": f"Cannot import job schemas: {str(e)}"
            }
    
    def test_placeholder_implementations(self):
        """Test if service implementations are still placeholders"""
        placeholder_services = [
            'services.ab_testing',
            'services.competitor_analysis', 
            'services.keyword_research',
            'services.multilanguage'
        ]
        
        results = {}
        for service in placeholder_services:
            try:
                module = __import__(f'api.{service}', fromlist=[''])
                # Check if module has placeholder methods
                source = ""
                try:
                    import inspect
                    source = inspect.getsource(module)
                except:
                    pass
                
                if "placeholder" in source.lower():
                    results[service] = "Still has placeholders"
                else:
                    results[service] = "Appears to be implemented"
            except ImportError:
                results[service] = "Cannot import"
        
        self.test_results["placeholder_services"] = {
            "audit_claim": "Service files contain placeholder methods",
            "reality": results
        }
    
    async def run_all_tests(self):
        """Run all verification tests"""
        print("Running GridSpoke Audit Verification Tests...")
        print("=" * 50)
        
        # Run async tests
        await self.test_health_endpoint()
        await self.test_job_management_endpoints()
        
        # Run sync tests
        self.test_celery_task_imports()
        self.test_crud_operations()
        self.test_job_schemas()
        self.test_placeholder_implementations()
        
        # Generate report
        self.generate_report()
    
    def generate_report(self):
        """Generate a comprehensive audit verification report"""
        print("\nAUDIT VERIFICATION REPORT")
        print("=" * 50)
        
        passes = 0
        fails = 0
        errors = 0
        
        for test_name, result in self.test_results.items():
            print(f"\n{test_name.upper()}:")
            print(f"  Audit Claim: {result.get('audit_claim', 'N/A')}")
            print(f"  Test Result: {result.get('status', 'N/A')}")
            print(f"  Reality: {result.get('reality', 'N/A')}")
            
            if result.get('status') == 'PASS':
                passes += 1
            elif result.get('status') == 'FAIL':
                fails += 1
            else:
                errors += 1
            
            if 'response' in result:
                print(f"  Response: {str(result['response'])[:100]}...")
        
        print(f"\n" + "="*50)
        print(f"SUMMARY: {passes} PASS | {fails} FAIL | {errors} ERROR")
        print(f"Total Tests: {len(self.test_results)}")
        
        # Save detailed results to file
        with open('audit_verification_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        print(f"\nDetailed results saved to: audit_verification_results.json")

async def main():
    """Main entry point"""
    verifier = AuditVerificationTests()
    await verifier.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
