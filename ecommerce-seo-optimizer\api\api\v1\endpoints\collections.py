"""
Smart Collections API endpoints for automatic collection creation and category optimization.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from ...services.collections_service import (
    SmartCollectionsService,
    CollectionRule,
    CategoryOptimization,
    InternalLinkSuggestion
)
from ...core.auth import get_current_user
from ...core.database import get_db
from ...crud.store import get_store_by_id

router = APIRouter(prefix="/collections", tags=["smart-collections"])


class CreateCollectionsRequest(BaseModel):
    """Request model for creating automatic collections"""
    store_id: str
    custom_rules: Optional[List[CollectionRule]] = None


class OptimizeCategoriesRequest(BaseModel):
    """Request model for optimizing category pages"""
    store_id: str
    categories: Optional[List[str]] = None


class InternalLinksRequest(BaseModel):
    """Request model for generating internal links"""
    store_id: str
    max_suggestions: int = Field(default=50, ge=1, le=200)


class CollectionResponse(BaseModel):
    """Response model for collection operations"""
    id: str
    name: str
    product_count: int
    seo_title: str
    meta_description: str
    description: str


class InternalLinksResponse(BaseModel):
    """Response model for internal linking suggestions"""
    suggestions: List[InternalLinkSuggestion]
    total_count: int
    store_id: str


@router.post("/create-automatic", response_model=List[CollectionResponse])
async def create_automatic_collections(
    request: CreateCollectionsRequest,
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Create automatic collections based on product attributes.
    
    This endpoint analyzes products in a store and creates collections like:
    - Best Sellers
    - New Arrivals  
    - Sale Items
    - High Rated
    - Price-based collections
    """
    
    # Verify store exists and user has access
    store = get_store_by_id(db, request.store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Verify user owns the store
    if str(store.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Access denied to this store")
    
    try:
        service = SmartCollectionsService()
        collections = await service.create_automatic_collections(
            request.store_id,
            request.custom_rules
        )
        
        return [CollectionResponse(**collection) for collection in collections]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create collections: {str(e)}")


@router.post("/optimize-categories", response_model=List[CategoryOptimization])
async def optimize_category_pages(
    request: OptimizeCategoriesRequest,
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Generate SEO-optimized content for category pages.
    
    Creates optimized titles, meta descriptions, content, and schema markup
    for product category pages to improve search engine visibility.
    """
    
    # Verify store exists and user has access
    store = get_store_by_id(db, request.store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    if str(store.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Access denied to this store")
    
    try:
        service = SmartCollectionsService()
        optimizations = await service.optimize_category_pages(
            request.store_id,
            request.categories
        )
        
        return optimizations
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to optimize categories: {str(e)}")


@router.post("/internal-links", response_model=InternalLinksResponse)
async def generate_internal_linking_strategy(
    request: InternalLinksRequest,
    current_user = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Generate intelligent internal linking suggestions.
    
    Analyzes products and categories to suggest relevant internal links
    that can improve SEO and user navigation.
    """
    
    # Verify store exists and user has access
    store = get_store_by_id(db, request.store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    if str(store.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Access denied to this store")
    
    try:
        service = SmartCollectionsService()
        suggestions = await service.generate_internal_linking_strategy(
            request.store_id,
            request.max_suggestions
        )
        
        return InternalLinksResponse(
            suggestions=suggestions,
            total_count=len(suggestions),
            store_id=request.store_id
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate internal links: {str(e)}")


@router.post("/update-automatic/{store_id}")
async def update_automatic_collections(
    store_id: str,
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Update existing automatic collections with new products.
    
    Refreshes automatic collections by applying their rules to current products,
    adding new matching products and removing products that no longer match.
    """
    
    # Verify store exists and user has access
    store = get_store_by_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    if str(store.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Access denied to this store")
    
    try:
        service = SmartCollectionsService()
        
        # Run update in background
        background_tasks.add_task(
            service.update_collections_automatically,
            store_id
        )
        
        return {
            "message": "Collection update started",
            "store_id": store_id,
            "status": "processing"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update collections: {str(e)}")


@router.get("/rules/default", response_model=List[CollectionRule])
async def get_default_collection_rules():
    """
    Get the default collection rules used for automatic collection creation.
    
    Returns the predefined rules that can be customized when creating collections.
    """
    service = SmartCollectionsService()
    return service.default_rules


@router.get("/{store_id}/collections")
async def list_store_collections(
    store_id: str,
    current_user = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    List all collections for a store.
    
    Returns both manual and automatic collections with their metadata.
    """
    
    # Verify store exists and user has access
    store = get_store_by_id(db, store_id)
    if not store:
        raise HTTPException(status_code=404, detail="Store not found")
    
    if str(store.user_id) != str(current_user.id):
        raise HTTPException(status_code=403, detail="Access denied to this store")
    
    try:
        from ...models.collection import Collection
        from sqlalchemy import and_
        
        collections = db.query(Collection).filter(
            Collection.store_id == store_id
        ).all()
        
        return [
            {
                "id": str(collection.id),
                "name": collection.name,
                "handle": collection.handle,
                "description": collection.description,
                "is_auto_generated": collection.is_auto_generated,
                "collection_type": collection.collection_type,
                "product_count": len(collection.products),
                "created_at": collection.created_at,
                "updated_at": collection.updated_at
            }
            for collection in collections
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list collections: {str(e)}")


@router.delete("/{collection_id}")
async def delete_collection(
    collection_id: str,
    current_user = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Delete a collection.
    
    Removes the collection and all its product associations.
    """
    
    try:
        from ...models.collection import Collection
        
        collection = db.query(Collection).filter(
            Collection.id == collection_id
        ).first()
        
        if not collection:
            raise HTTPException(status_code=404, detail="Collection not found")
        
        # Verify user owns the store
        store = get_store_by_id(db, str(collection.store_id))
        if not store or str(store.user_id) != str(current_user.id):
            raise HTTPException(status_code=403, detail="Access denied")
        
        db.delete(collection)
        db.commit()
        
        return {
            "message": "Collection deleted successfully",
            "collection_id": collection_id
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete collection: {str(e)}")


@router.get("/{collection_id}/products")
async def get_collection_products(
    collection_id: str,
    current_user = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Get products in a collection.
    
    Returns all products associated with the collection in their defined order.
    """
    
    try:
        from ...models.collection import Collection, CollectionProduct
        from ...models.product import Product
        from sqlalchemy.orm import joinedload
        
        collection = db.query(Collection).filter(
            Collection.id == collection_id
        ).first()
        
        if not collection:
            raise HTTPException(status_code=404, detail="Collection not found")
        
        # Verify user owns the store
        store = get_store_by_id(db, str(collection.store_id))
        if not store or str(store.user_id) != str(current_user.id):
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Get products with their positions
        collection_products = db.query(
            Product, CollectionProduct.position
        ).join(
            CollectionProduct,
            Product.id == CollectionProduct.product_id
        ).filter(
            CollectionProduct.collection_id == collection_id
        ).order_by(
            CollectionProduct.position
        ).all()
        
        return {
            "collection_id": collection_id,
            "collection_name": collection.name,
            "products": [
                {
                    "id": str(product.id),
                    "title": product.title,
                    "price": float(product.price) if product.price else None,
                    "category": product.category,
                    "position": position,
                    "image_url": product.image_url
                }
                for product, position in collection_products
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get collection products: {str(e)}")
