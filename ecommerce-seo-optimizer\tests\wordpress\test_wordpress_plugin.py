# GridSpoke WordPress Plugin Testing
"""
Testing for GridSpoke WordPress plugin integration including
authentication, product sync, webhook handling, and UI components.
"""

import pytest
import json
import hashlib
import hmac
from unittest.mock import patch, MagicMock
from datetime import datetime


class TestWordPressPluginInstallation:
    """Test WordPress plugin installation and activation"""
    
    def test_plugin_activation_hook(self):
        """Test plugin activation creates necessary database tables and options"""
        # Mock WordPress functions
        with patch('wordpress.wp_create_table') as mock_create_table:
            with patch('wordpress.add_option') as mock_add_option:
                from tests.wordpress.mock_plugin import activate_gridspoke_plugin
                
                activate_gridspoke_plugin()
                
                # Should create tables for GridSpoke data
                expected_tables = ['gridspoke_settings', 'gridspoke_sync_log']
                for table in expected_tables:
                    mock_create_table.assert_any_call(table)
                
                # Should set default options
                expected_options = [
                    'gridspoke_api_url',
                    'gridspoke_api_key', 
                    'gridspoke_auto_sync',
                    'gridspoke_optimization_schedule'
                ]
                for option in expected_options:
                    mock_add_option.assert_any_call(option)
    
    def test_plugin_deactivation_cleanup(self):
        """Test plugin deactivation cleans up properly"""
        with patch('wordpress.wp_clear_scheduled_hooks') as mock_clear_hooks:
            with patch('wordpress.delete_transient') as mock_delete_transient:
                from tests.wordpress.mock_plugin import deactivate_gridspoke_plugin
                
                deactivate_gridspoke_plugin()
                
                # Should clear scheduled tasks
                mock_clear_hooks.assert_called_with('gridspoke_sync_products')
                
                # Should clear cache
                mock_delete_transient.assert_called()
    
    def test_plugin_uninstall_data_removal(self):
        """Test plugin uninstall removes all data"""
        with patch('wordpress.wp_drop_table') as mock_drop_table:
            with patch('wordpress.delete_option') as mock_delete_option:
                from tests.wordpress.mock_plugin import uninstall_gridspoke_plugin
                
                uninstall_gridspoke_plugin()
                
                # Should remove all tables and options
                mock_drop_table.assert_called()
                mock_delete_option.assert_called()


class TestWordPressAuthentication:
    """Test WordPress plugin authentication with GridSpoke API"""
    
    def test_api_key_validation(self):
        """Test API key validation against GridSpoke backend"""
        from tests.wordpress.mock_plugin import validate_api_credentials
        
        # Test valid API key
        with patch('wordpress.wp_remote_get') as mock_request:
            mock_request.return_value = {
                'response': {'code': 200},
                'body': json.dumps({
                    'status': 'valid',
                    'user_id': 'user_123',
                    'plan': 'premium'
                })
            }
            
            result = validate_api_credentials('valid_api_key', 'https://api.gridspoke.com')
            assert result['valid'] is True
            assert result['user_id'] == 'user_123'
        
        # Test invalid API key
        with patch('wordpress.wp_remote_get') as mock_request:
            mock_request.return_value = {
                'response': {'code': 401},
                'body': json.dumps({'error': 'Invalid API key'})
            }
            
            result = validate_api_credentials('invalid_key', 'https://api.gridspoke.com')
            assert result['valid'] is False
    
    def test_connection_test(self):
        """Test connection test functionality"""
        from tests.wordpress.mock_plugin import test_gridspoke_connection
        
        with patch('wordpress.wp_remote_get') as mock_request:
            # Test successful connection
            mock_request.return_value = {
                'response': {'code': 200},
                'body': json.dumps({'status': 'healthy', 'version': '1.0.0'})
            }
            
            result = test_gridspoke_connection('valid_key', 'https://api.gridspoke.com')
            assert result['connected'] is True
            assert result['version'] == '1.0.0'
        
        with patch('wordpress.wp_remote_get') as mock_request:
            # Test connection failure
            mock_request.return_value = {
                'response': {'code': 0},  # Connection failed
                'body': ''
            }
            
            result = test_gridspoke_connection('key', 'https://api.gridspoke.com')
            assert result['connected'] is False
    
    def test_api_key_encryption_storage(self):
        """Test API key is encrypted when stored in WordPress"""
        from tests.wordpress.mock_plugin import save_api_credentials
        
        with patch('wordpress.update_option') as mock_update:
            with patch('wordpress.wp_encrypt') as mock_encrypt:
                mock_encrypt.return_value = 'encrypted_api_key_data'
                
                save_api_credentials('plain_api_key', 'https://api.gridspoke.com')
                
                # API key should be encrypted before storage
                mock_encrypt.assert_called_with('plain_api_key')
                mock_update.assert_called_with('gridspoke_api_key', 'encrypted_api_key_data')


class TestWordPressProductSync:
    """Test product synchronization between WordPress and GridSpoke"""
    
    def test_initial_product_sync(self):
        """Test initial sync of all products to GridSpoke"""
        from tests.wordpress.mock_plugin import sync_all_products_to_gridspoke
        
        # Mock WooCommerce products
        mock_products = [
            {
                'id': 123,
                'name': 'Test Product 1',
                'description': 'Product description 1',
                'price': '29.99',
                'categories': [{'name': 'Electronics'}],
                'images': [{'src': 'https://example.com/image1.jpg'}]
            },
            {
                'id': 124,
                'name': 'Test Product 2', 
                'description': 'Product description 2',
                'price': '49.99',
                'categories': [{'name': 'Clothing'}],
                'images': [{'src': 'https://example.com/image2.jpg'}]
            }
        ]
        
        with patch('wordpress.wc_get_products') as mock_get_products:
            with patch('wordpress.wp_remote_post') as mock_post:
                mock_get_products.return_value = mock_products
                mock_post.return_value = {
                    'response': {'code': 201},
                    'body': json.dumps({'status': 'success', 'synced': 2})
                }
                
                result = sync_all_products_to_gridspoke()
                
                assert result['success'] is True
                assert result['synced_count'] == 2
                
                # Should make API call for each product
                assert mock_post.call_count == 2
    
    def test_incremental_product_sync(self):
        """Test syncing only modified products"""
        from tests.wordpress.mock_plugin import sync_modified_products
        
        with patch('wordpress.get_option') as mock_get_option:
            with patch('wordpress.wc_get_products') as mock_get_products:
                # Last sync timestamp
                mock_get_option.return_value = '2024-01-01 10:00:00'
                
                # Products modified since last sync
                mock_get_products.return_value = [
                    {
                        'id': 125,
                        'name': 'Updated Product',
                        'date_modified': '2024-01-02 11:00:00'
                    }
                ]
                
                with patch('wordpress.wp_remote_post') as mock_post:
                    mock_post.return_value = {
                        'response': {'code': 200},
                        'body': json.dumps({'status': 'updated'})
                    }
                    
                    result = sync_modified_products()
                    
                    assert result['updated_count'] == 1
                    
                    # Should only sync modified products
                    call_args = mock_get_products.call_args
                    assert 'date_modified' in str(call_args)
    
    def test_product_deletion_sync(self):
        """Test syncing product deletions to GridSpoke"""
        from tests.wordpress.mock_plugin import sync_product_deletion
        
        with patch('wordpress.wp_remote_delete') as mock_delete:
            mock_delete.return_value = {
                'response': {'code': 200},
                'body': json.dumps({'status': 'deleted'})
            }
            
            result = sync_product_deletion(123)
            
            assert result['success'] is True
            mock_delete.assert_called_once()
            
            # Verify correct API endpoint was called
            call_args = mock_delete.call_args
            assert '/products/123' in str(call_args)
    
    def test_sync_error_handling(self):
        """Test handling of sync errors and retry logic"""
        from tests.wordpress.mock_plugin import sync_product_with_retry
        
        with patch('wordpress.wp_remote_post') as mock_post:
            # First two attempts fail, third succeeds
            mock_post.side_effect = [
                {'response': {'code': 500}, 'body': 'Server Error'},
                {'response': {'code': 429}, 'body': 'Rate Limited'},
                {'response': {'code': 201}, 'body': json.dumps({'status': 'success'})}
            ]
            
            result = sync_product_with_retry({'id': 126, 'name': 'Test Product'})
            
            assert result['success'] is True
            assert mock_post.call_count == 3


class TestWordPressWebhooks:
    """Test WordPress webhook handling for GridSpoke integration"""
    
    def test_webhook_signature_verification(self):
        """Test webhook signature verification"""
        from tests.wordpress.mock_plugin import verify_gridspoke_webhook_signature
        
        payload = json.dumps({
            'action': 'optimization_complete',
            'product_id': 123,
            'optimized_data': {
                'title': 'Optimized Product Title',
                'description': 'Optimized description'
            }
        })
        
        secret = 'webhook_secret_key'
        
        # Generate valid signature
        signature = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        # Test valid signature
        assert verify_gridspoke_webhook_signature(payload, signature, secret) is True
        
        # Test invalid signature
        assert verify_gridspoke_webhook_signature(payload, 'invalid_sig', secret) is False
    
    def test_optimization_complete_webhook(self):
        """Test handling optimization complete webhook"""
        from tests.wordpress.mock_plugin import handle_optimization_complete_webhook
        
        webhook_data = {
            'action': 'optimization_complete',
            'product_id': 123,
            'job_id': 'job_456',
            'optimized_data': {
                'title': 'SEO Optimized Product Title',
                'description': 'SEO optimized product description with keywords',
                'meta_title': 'Meta Title for SEO',
                'meta_description': 'Meta description for search engines'
            },
            'seo_score': 89.5
        }
        
        with patch('wordpress.wc_update_product') as mock_update:
            with patch('wordpress.update_post_meta') as mock_update_meta:
                result = handle_optimization_complete_webhook(webhook_data)
                
                assert result['success'] is True
                
                # Should update product data
                mock_update.assert_called_once()
                update_args = mock_update.call_args[1]
                assert update_args['name'] == 'SEO Optimized Product Title'
                assert update_args['description'] == webhook_data['optimized_data']['description']
                
                # Should update SEO meta data
                meta_calls = mock_update_meta.call_args_list
                assert any('meta_title' in str(call) for call in meta_calls)
                assert any('meta_description' in str(call) for call in meta_calls)
    
    def test_bulk_optimization_progress_webhook(self):
        """Test handling bulk optimization progress webhook"""
        from tests.wordpress.mock_plugin import handle_bulk_progress_webhook
        
        progress_data = {
            'action': 'bulk_optimization_progress',
            'job_id': 'job_789',
            'progress': 65,
            'total_items': 100,
            'processed_items': 65,
            'current_item': 'Product ABC',
            'estimated_completion': '2024-01-15T14:30:00Z'
        }
        
        with patch('wordpress.update_option') as mock_update_option:
            with patch('wordpress.wp_schedule_single_event') as mock_schedule:
                result = handle_bulk_progress_webhook(progress_data)
                
                assert result['success'] is True
                
                # Should store progress data
                mock_update_option.assert_called()
                
                # Should schedule UI update notification
                mock_schedule.assert_called()
    
    def test_webhook_rate_limiting(self):
        """Test webhook rate limiting protection"""
        from tests.wordpress.mock_plugin import check_webhook_rate_limit
        
        with patch('wordpress.get_transient') as mock_get_transient:
            with patch('wordpress.set_transient') as mock_set_transient:
                # First webhook - should be allowed
                mock_get_transient.return_value = False
                
                result = check_webhook_rate_limit('192.168.1.1')
                assert result['allowed'] is True
                
                # Set transient for rate limiting
                mock_set_transient.assert_called()
                
                # Subsequent webhook within time window - should be rate limited
                mock_get_transient.return_value = 5  # 5 recent requests
                
                result = check_webhook_rate_limit('192.168.1.1')
                assert result['allowed'] is False


class TestWordPressAdminInterface:
    """Test WordPress admin interface components"""
    
    def test_admin_menu_registration(self):
        """Test admin menu registration"""
        from tests.wordpress.mock_plugin import register_gridspoke_admin_menu
        
        with patch('wordpress.add_menu_page') as mock_add_menu:
            with patch('wordpress.add_submenu_page') as mock_add_submenu:
                register_gridspoke_admin_menu()
                
                # Should create main menu page
                mock_add_menu.assert_called_once()
                menu_args = mock_add_menu.call_args[1]
                assert 'GridSpoke' in menu_args['page_title']
                
                # Should create submenus
                submenu_calls = mock_add_submenu.call_args_list
                expected_submenus = ['Settings', 'Products', 'Analytics', 'Logs']
                for submenu in expected_submenus:
                    assert any(submenu in str(call) for call in submenu_calls)
    
    def test_settings_page_rendering(self):
        """Test settings page rendering"""
        from tests.wordpress.mock_plugin import render_gridspoke_settings_page
        
        with patch('wordpress.get_option') as mock_get_option:
            mock_get_option.side_effect = lambda key, default: {
                'gridspoke_api_url': 'https://api.gridspoke.com',
                'gridspoke_auto_sync': '1',
                'gridspoke_optimization_schedule': 'daily'
            }.get(key, default)
            
            html_output = render_gridspoke_settings_page()
            
            # Should contain form elements
            assert '<form' in html_output
            assert 'api_url' in html_output
            assert 'auto_sync' in html_output
            assert 'optimization_schedule' in html_output
            
            # Should show current settings
            assert 'https://api.gridspoke.com' in html_output
            assert 'checked' in html_output  # For auto_sync checkbox
    
    def test_product_list_with_optimization_status(self):
        """Test product list showing optimization status"""
        from tests.wordpress.mock_plugin import render_products_with_optimization_status
        
        with patch('wordpress.wc_get_products') as mock_get_products:
            with patch('wordpress.get_post_meta') as mock_get_meta:
                mock_get_products.return_value = [
                    {'id': 123, 'name': 'Product 1'},
                    {'id': 124, 'name': 'Product 2'}
                ]
                
                # Mock optimization status
                mock_get_meta.side_effect = lambda product_id, key, single: {
                    (123, 'gridspoke_optimization_status'): 'optimized',
                    (123, 'gridspoke_seo_score'): '89.5',
                    (124, 'gridspoke_optimization_status'): 'pending'
                }.get((product_id, key), '')
                
                html_output = render_products_with_optimization_status()
                
                # Should show optimization status
                assert 'optimized' in html_output
                assert 'pending' in html_output
                assert '89.5' in html_output  # SEO score
    
    def test_bulk_optimization_interface(self):
        """Test bulk optimization interface"""
        from tests.wordpress.mock_plugin import render_bulk_optimization_interface
        
        with patch('wordpress.wc_get_product_categories') as mock_get_categories:
            mock_get_categories.return_value = [
                {'name': 'Electronics', 'count': 25},
                {'name': 'Clothing', 'count': 15}
            ]
            
            html_output = render_bulk_optimization_interface()
            
            # Should contain bulk optimization form
            assert '<form' in html_output
            assert 'bulk_optimize' in html_output
            
            # Should show category filters
            assert 'Electronics' in html_output
            assert 'Clothing' in html_output
            
            # Should have optimization type options
            assert 'optimization_type' in html_output
    
    def test_analytics_dashboard(self):
        """Test analytics dashboard rendering"""
        from tests.wordpress.mock_plugin import render_analytics_dashboard
        
        with patch('wordpress.wp_remote_get') as mock_get:
            mock_get.return_value = {
                'response': {'code': 200},
                'body': json.dumps({
                    'total_products': 150,
                    'optimized_products': 120,
                    'avg_seo_score': 87.3,
                    'total_cost': 45.67,
                    'last_optimization': '2024-01-15T10:30:00Z'
                })
            }
            
            html_output = render_analytics_dashboard()
            
            # Should display analytics data
            assert '150' in html_output  # Total products
            assert '120' in html_output  # Optimized products
            assert '87.3' in html_output  # Average SEO score
            assert '$45.67' in html_output  # Total cost


class TestWordPressUserInterface:
    """Test WordPress user interface components"""
    
    def test_product_edit_optimization_metabox(self):
        """Test optimization metabox on product edit page"""
        from tests.wordpress.mock_plugin import render_product_optimization_metabox
        
        with patch('wordpress.get_post_meta') as mock_get_meta:
            mock_get_meta.side_effect = lambda product_id, key, single: {
                'gridspoke_optimization_status': 'optimized',
                'gridspoke_seo_score': '91.2',
                'gridspoke_last_optimized': '2024-01-15 10:30:00',
                'gridspoke_optimization_suggestions': json.dumps([
                    'Consider adding more keywords to title',
                    'Product description could be longer'
                ])
            }.get(key, '')
            
            html_output = render_product_optimization_metabox(123)
            
            # Should show optimization status
            assert 'optimized' in html_output
            assert '91.2' in html_output
            
            # Should show suggestions
            assert 'Consider adding more keywords' in html_output
            
            # Should have optimize button
            assert 'optimize_product' in html_output
    
    def test_optimization_progress_notifications(self):
        """Test real-time optimization progress notifications"""
        from tests.wordpress.mock_plugin import render_optimization_progress_notifications
        
        with patch('wordpress.wp_localize_script') as mock_localize:
            render_optimization_progress_notifications()
            
            # Should enqueue JavaScript for notifications
            mock_localize.assert_called()
            script_data = mock_localize.call_args[1]['object_name']
            assert 'gridspoke' in script_data.lower()
    
    def test_optimization_history_display(self):
        """Test optimization history display"""
        from tests.wordpress.mock_plugin import render_optimization_history
        
        with patch('wordpress.wp_remote_get') as mock_get:
            mock_get.return_value = {
                'response': {'code': 200},
                'body': json.dumps({
                    'history': [
                        {
                            'timestamp': '2024-01-15T10:30:00Z',
                            'type': 'single_product',
                            'product_id': 123,
                            'seo_score_before': 65.0,
                            'seo_score_after': 89.5,
                            'cost': 0.12
                        },
                        {
                            'timestamp': '2024-01-14T15:20:00Z',
                            'type': 'bulk_optimization',
                            'products_count': 25,
                            'avg_score_improvement': 18.3,
                            'total_cost': 3.45
                        }
                    ]
                })
            }
            
            html_output = render_optimization_history()
            
            # Should display optimization history
            assert '2024-01-15' in html_output
            assert '89.5' in html_output  # SEO score
            assert '$0.12' in html_output  # Cost
            assert '25' in html_output  # Products count


class TestWordPressErrorHandling:
    """Test WordPress plugin error handling"""
    
    def test_api_connection_error_handling(self):
        """Test handling of API connection errors"""
        from tests.wordpress.mock_plugin import handle_api_connection_error
        
        with patch('wordpress.add_settings_error') as mock_add_error:
            with patch('wordpress.wp_remote_get') as mock_get:
                # Simulate connection timeout
                mock_get.return_value = {
                    'response': {'code': 0},
                    'body': ''
                }
                
                result = handle_api_connection_error()
                
                # Should display user-friendly error
                mock_add_error.assert_called()
                error_args = mock_add_error.call_args[1]
                assert 'connection' in error_args['message'].lower()
    
    def test_optimization_failure_handling(self):
        """Test handling of optimization failures"""
        from tests.wordpress.mock_plugin import handle_optimization_failure
        
        with patch('wordpress.update_post_meta') as mock_update_meta:
            with patch('wordpress.wp_schedule_single_event') as mock_schedule_retry:
                error_data = {
                    'product_id': 123,
                    'error_type': 'api_limit_exceeded',
                    'error_message': 'Monthly API limit exceeded',
                    'retry_after': 3600  # 1 hour
                }
                
                result = handle_optimization_failure(error_data)
                
                # Should update product meta with error status
                mock_update_meta.assert_called()
                
                # Should schedule retry if appropriate
                if error_data['error_type'] == 'api_limit_exceeded':
                    mock_schedule_retry.assert_called()
    
    def test_webhook_processing_error_handling(self):
        """Test handling of webhook processing errors"""
        from tests.wordpress.mock_plugin import handle_webhook_processing_error
        
        with patch('wordpress.error_log') as mock_error_log:
            with patch('wordpress.wp_mail') as mock_mail:
                error_data = {
                    'webhook_data': {'action': 'optimization_complete'},
                    'error': 'Product not found',
                    'timestamp': datetime.utcnow().isoformat()
                }
                
                handle_webhook_processing_error(error_data)
                
                # Should log error
                mock_error_log.assert_called()
                
                # Should optionally notify admin
                if 'critical' in error_data.get('severity', ''):
                    mock_mail.assert_called()


# Mock WordPress Plugin Classes
class MockWordPressPlugin:
    """Mock WordPress plugin for testing"""
    
    def __init__(self):
        self.options = {}
        self.hooks = {}
        self.menu_items = []
    
    def add_action(self, hook, callback):
        if hook not in self.hooks:
            self.hooks[hook] = []
        self.hooks[hook].append(callback)
    
    def add_menu_page(self, page_title, menu_title, capability, menu_slug, callback):
        self.menu_items.append({
            'type': 'menu',
            'title': page_title,
            'slug': menu_slug
        })
    
    def get_option(self, key, default=None):
        return self.options.get(key, default)
    
    def update_option(self, key, value):
        self.options[key] = value
        return True


# Test Fixtures for WordPress Testing
@pytest.fixture
def mock_wordpress_plugin():
    """Fixture providing mock WordPress plugin instance"""
    return MockWordPressPlugin()


@pytest.fixture
def mock_woocommerce_product():
    """Fixture providing mock WooCommerce product data"""
    return {
        'id': 123,
        'name': 'Test Product',
        'description': 'Test product description',
        'short_description': 'Short description',
        'price': '29.99',
        'regular_price': '29.99',
        'sale_price': '',
        'categories': [
            {'id': 15, 'name': 'Electronics'}
        ],
        'tags': [
            {'id': 25, 'name': 'wireless'},
            {'id': 26, 'name': 'bluetooth'}
        ],
        'images': [
            {
                'id': 456,
                'src': 'https://example.com/product-image.jpg',
                'alt': 'Product image'
            }
        ],
        'attributes': [
            {
                'name': 'Color',
                'options': ['Black', 'White']
            }
        ],
        'meta_data': [
            {
                'key': '_yoast_wpseo_title',
                'value': 'Existing SEO Title'
            },
            {
                'key': '_yoast_wpseo_metadesc', 
                'value': 'Existing meta description'
            }
        ],
        'date_created': '2024-01-01T10:00:00',
        'date_modified': '2024-01-15T14:30:00',
        'status': 'publish'
    }


@pytest.fixture
def mock_gridspoke_webhook_data():
    """Fixture providing mock GridSpoke webhook data"""
    return {
        'action': 'optimization_complete',
        'job_id': 'job_123456',
        'product_id': 123,
        'store_id': 'store_789',
        'optimized_data': {
            'title': 'SEO Optimized Product Title - Premium Quality',
            'description': 'Enhanced product description with targeted keywords and improved readability for better search engine optimization.',
            'short_description': 'Premium quality product with enhanced features.',
            'meta_title': 'SEO Optimized Product | Premium Quality Store',
            'meta_description': 'Discover premium quality products with enhanced features. Perfect for customers seeking excellence.',
            'alt_text': 'Premium quality product showcasing enhanced features',
            'keywords': ['premium', 'quality', 'enhanced', 'features'],
            'category_suggestions': ['Premium Products', 'Featured Items']
        },
        'optimization_details': {
            'seo_score_before': 45.2,
            'seo_score_after': 89.7,
            'improvements': [
                'Added target keywords to title',
                'Enhanced product description length and quality',
                'Improved meta descriptions for search engines',
                'Optimized image alt text'
            ],
            'model_used': 'anthropic/claude-3-opus',
            'processing_time': 4.2,
            'cost': 0.15
        },
        'timestamp': '2024-01-15T14:30:00Z'
    }
