# Environment Variables Template
# Copy this file to .env and fill in your actual values

# Database Configuration
POSTGRES_PASSWORD=your_secure_database_password_here

# API Security
SECRET_KEY=your_super_secret_key_minimum_32_characters_long

# AI Provider Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional: Additional AI Provider Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Email Configuration (for notifications)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=true

# External Service URLs (for production)
API_BASE_URL=http://localhost:8000
FRONTEND_URL=http://localhost:3000

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# WordPress Integration
WORDPRESS_WEBHOOK_SECRET=your_webhook_secret_here

# Monitoring (Optional)
SENTRY_DSN=
NEW_RELIC_LICENSE_KEY=

# Development Settings
RELOAD=true
WORKERS=1
