"""
Redis Client Utilities for GridSpoke Workers
Provides Redis connection management and helper functions.
"""

import os
import redis
import logging
from typing import Optional

logger = logging.getLogger(__name__)

# Global Redis client instance
_redis_client: Optional[redis.Redis] = None

def get_redis_client() -> redis.Redis:
    """
    Get Redis client instance with connection pooling.
    
    Returns:
        Redis client instance
    """
    global _redis_client
    
    if _redis_client is None:
        try:
            redis_url = os.environ.get('REDIS_URL', 'redis://redis:6379/0')
            
            # Parse Redis URL and create connection pool
            _redis_client = redis.from_url(
                redis_url,
                decode_responses=True,
                health_check_interval=30,
                socket_keepalive=True,
                socket_keepalive_options={},
                retry_on_timeout=True,
                max_connections=100
            )
            
            # Test connection
            _redis_client.ping()
            logger.info("Redis client connected successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            raise
    
    return _redis_client

def test_redis_connection() -> bool:
    """
    Test Redis connection health.
    
    Returns:
        True if connection is healthy, False otherwise
    """
    try:
        client = get_redis_client()
        client.ping()
        return True
    except Exception as e:
        logger.error(f"Redis connection test failed: {str(e)}")
        return False

def reset_redis_connection():
    """
    Reset Redis connection (useful for reconnecting after network issues).
    """
    global _redis_client
    
    if _redis_client:
        try:
            _redis_client.close()
        except Exception as e:
            logger.error(f"Error closing Redis connection: {str(e)}")
    
    _redis_client = None
    logger.info("Redis connection reset")

def get_redis_info() -> dict:
    """
    Get Redis server information.
    
    Returns:
        Dict containing Redis server info
    """
    try:
        client = get_redis_client()
        return client.info()
    except Exception as e:
        logger.error(f"Error getting Redis info: {str(e)}")
        return {}

def set_with_expiry(key: str, value: str, expiry_seconds: int) -> bool:
    """
    Set a key with expiration time.
    
    Args:
        key: Redis key
        value: Value to store
        expiry_seconds: Expiration time in seconds
    
    Returns:
        True if successful, False otherwise
    """
    try:
        client = get_redis_client()
        return client.setex(key, expiry_seconds, value)
    except Exception as e:
        logger.error(f"Error setting key {key} with expiry: {str(e)}")
        return False

def get_value(key: str) -> Optional[str]:
    """
    Get value by key.
    
    Args:
        key: Redis key
    
    Returns:
        Value if found, None otherwise
    """
    try:
        client = get_redis_client()
        return client.get(key)
    except Exception as e:
        logger.error(f"Error getting key {key}: {str(e)}")
        return None

def delete_key(key: str) -> bool:
    """
    Delete a key.
    
    Args:
        key: Redis key to delete
    
    Returns:
        True if successful, False otherwise
    """
    try:
        client = get_redis_client()
        return bool(client.delete(key))
    except Exception as e:
        logger.error(f"Error deleting key {key}: {str(e)}")
        return False

def delete_pattern(pattern: str) -> int:
    """
    Delete all keys matching a pattern.
    
    Args:
        pattern: Redis key pattern (supports wildcards)
    
    Returns:
        Number of keys deleted
    """
    try:
        client = get_redis_client()
        keys = client.keys(pattern)
        
        if keys:
            return client.delete(*keys)
        
        return 0
        
    except Exception as e:
        logger.error(f"Error deleting pattern {pattern}: {str(e)}")
        return 0

def publish_message(channel: str, message: str) -> bool:
    """
    Publish a message to a Redis channel.
    
    Args:
        channel: Redis channel name
        message: Message to publish
    
    Returns:
        True if successful, False otherwise
    """
    try:
        client = get_redis_client()
        client.publish(channel, message)
        return True
    except Exception as e:
        logger.error(f"Error publishing to channel {channel}: {str(e)}")
        return False

def increment_counter(key: str, amount: int = 1, expiry_seconds: Optional[int] = None) -> int:
    """
    Increment a counter and optionally set expiration.
    
    Args:
        key: Redis key for counter
        amount: Amount to increment by
        expiry_seconds: Optional expiration time
    
    Returns:
        New counter value
    """
    try:
        client = get_redis_client()
        
        # Use pipeline for atomic operations
        pipe = client.pipeline()
        pipe.incrby(key, amount)
        
        if expiry_seconds:
            pipe.expire(key, expiry_seconds)
        
        results = pipe.execute()
        return results[0]
        
    except Exception as e:
        logger.error(f"Error incrementing counter {key}: {str(e)}")
        return 0

def get_counter_value(key: str) -> int:
    """
    Get current counter value.
    
    Args:
        key: Redis key for counter
    
    Returns:
        Counter value (0 if key doesn't exist)
    """
    try:
        client = get_redis_client()
        value = client.get(key)
        return int(value) if value else 0
    except Exception as e:
        logger.error(f"Error getting counter {key}: {str(e)}")
        return 0

def add_to_set(key: str, value: str, expiry_seconds: Optional[int] = None) -> bool:
    """
    Add value to a Redis set.
    
    Args:
        key: Redis set key
        value: Value to add
        expiry_seconds: Optional expiration time
    
    Returns:
        True if successful, False otherwise
    """
    try:
        client = get_redis_client()
        
        pipe = client.pipeline()
        pipe.sadd(key, value)
        
        if expiry_seconds:
            pipe.expire(key, expiry_seconds)
        
        pipe.execute()
        return True
        
    except Exception as e:
        logger.error(f"Error adding to set {key}: {str(e)}")
        return False

def get_set_members(key: str) -> set:
    """
    Get all members of a Redis set.
    
    Args:
        key: Redis set key
    
    Returns:
        Set of members
    """
    try:
        client = get_redis_client()
        return client.smembers(key)
    except Exception as e:
        logger.error(f"Error getting set members for {key}: {str(e)}")
        return set()

def is_member_of_set(key: str, value: str) -> bool:
    """
    Check if value is member of Redis set.
    
    Args:
        key: Redis set key
        value: Value to check
    
    Returns:
        True if value is in set, False otherwise
    """
    try:
        client = get_redis_client()
        return client.sismember(key, value)
    except Exception as e:
        logger.error(f"Error checking set membership for {key}: {str(e)}")
        return False

def push_to_list(key: str, value: str, max_length: Optional[int] = None) -> bool:
    """
    Push value to Redis list (left push).
    
    Args:
        key: Redis list key
        value: Value to push
        max_length: Optional max length (will trim if exceeded)
    
    Returns:
        True if successful, False otherwise
    """
    try:
        client = get_redis_client()
        
        pipe = client.pipeline()
        pipe.lpush(key, value)
        
        if max_length:
            pipe.ltrim(key, 0, max_length - 1)
        
        pipe.execute()
        return True
        
    except Exception as e:
        logger.error(f"Error pushing to list {key}: {str(e)}")
        return False

def get_list_range(key: str, start: int = 0, end: int = -1) -> list:
    """
    Get range of values from Redis list.
    
    Args:
        key: Redis list key
        start: Start index
        end: End index (-1 for end of list)
    
    Returns:
        List of values
    """
    try:
        client = get_redis_client()
        return client.lrange(key, start, end)
    except Exception as e:
        logger.error(f"Error getting list range for {key}: {str(e)}")
        return []

def get_memory_usage() -> dict:
    """
    Get Redis memory usage statistics.
    
    Returns:
        Dict containing memory usage info
    """
    try:
        client = get_redis_client()
        info = client.info('memory')
        
        return {
            'used_memory': info.get('used_memory', 0),
            'used_memory_human': info.get('used_memory_human', '0B'),
            'used_memory_peak': info.get('used_memory_peak', 0),
            'used_memory_peak_human': info.get('used_memory_peak_human', '0B'),
            'memory_fragmentation_ratio': info.get('mem_fragmentation_ratio', 0)
        }
        
    except Exception as e:
        logger.error(f"Error getting memory usage: {str(e)}")
        return {}

def flush_database(confirm: bool = False) -> bool:
    """
    Flush all data from current Redis database.
    WARNING: This will delete all data!
    
    Args:
        confirm: Must be True to actually execute
    
    Returns:
        True if successful, False otherwise
    """
    if not confirm:
        logger.warning("flush_database called without confirmation")
        return False
    
    try:
        client = get_redis_client()
        client.flushdb()
        logger.warning("Redis database flushed")
        return True
    except Exception as e:
        logger.error(f"Error flushing database: {str(e)}")
        return False
