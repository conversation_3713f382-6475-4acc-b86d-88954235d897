# GridSpoke Load Testing Scripts
"""
Performance and load testing for GridSpoke using Locust.
Tests the system under realistic load conditions for bulk optimization workflows.
"""

from locust import HttpUser, task, between
import json
import random
import uuid
from datetime import datetime


class GridSpokeUser(HttpUser):
    """
    Base user class for GridSpoke load testing.
    Simulates realistic user behavior for ecommerce store optimization.
    """
    wait_time = between(1, 5)  # Wait 1-5 seconds between requests
    
    def on_start(self):
        """Setup authentication and initial data"""
        self.auth_token = None
        self.store_id = None
        self.product_ids = []
        self.setup_auth()
        self.setup_store()
    
    def setup_auth(self):
        """Authenticate user and get JWT token"""
        auth_data = {
            "email": f"loadtest_{uuid.uuid4().hex[:8]}@example.com",
            "password": "loadtest_password_123"
        }
        
        # Create test user (in real scenario, use pre-created accounts)
        response = self.client.post("/api/v1/auth/register", json=auth_data)
        if response.status_code == 201:
            # Login to get token
            login_response = self.client.post("/api/v1/auth/login", json=auth_data)
            if login_response.status_code == 200:
                self.auth_token = login_response.json()["access_token"]
            else:
                # Try direct login if user already exists
                login_response = self.client.post("/api/v1/auth/login", json=auth_data)
                if login_response.status_code == 200:
                    self.auth_token = login_response.json()["access_token"]
    
    def setup_store(self):
        """Create a test store for optimization"""
        if not self.auth_token:
            return
        
        store_data = {
            "name": f"LoadTest Store {uuid.uuid4().hex[:6]}",
            "url": f"https://loadtest-{uuid.uuid4().hex[:6]}.com",
            "platform": random.choice(["woocommerce", "surecart", "shopify"]),
            "api_key": f"test_api_key_{uuid.uuid4().hex}"
        }
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        response = self.client.post("/api/v1/stores", json=store_data, headers=headers)
        
        if response.status_code == 201:
            self.store_id = response.json()["id"]
            
            # Create some test products
            self.create_test_products()
    
    def create_test_products(self):
        """Create test products for optimization"""
        if not self.store_id or not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        for i in range(10):  # Create 10 products per store
            product_data = {
                "name": f"Load Test Product {i}",
                "description": f"Basic description for product {i}",
                "price": round(random.uniform(10.0, 999.99), 2),
                "category": random.choice(["electronics", "clothing", "books", "home", "sports"]),
                "store_id": self.store_id
            }
            
            response = self.client.post("/api/v1/products", json=product_data, headers=headers)
            if response.status_code == 201:
                self.product_ids.append(response.json()["id"])
    
    @task(5)
    def view_stores(self):
        """View store list - common operation"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        self.client.get("/api/v1/stores", headers=headers, name="GET /api/v1/stores")
    
    @task(10)
    def view_products(self):
        """View product list - very common operation"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        params = {
            "page": random.randint(1, 3),
            "per_page": 20,
            "category": random.choice(["", "electronics", "clothing"])
        }
        self.client.get("/api/v1/products", params=params, headers=headers, 
                       name="GET /api/v1/products")
    
    @task(8)
    def search_products(self):
        """Search products - common operation"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        search_terms = ["wireless", "premium", "organic", "professional", "luxury"]
        params = {
            "search": random.choice(search_terms),
            "page": 1,
            "per_page": 10
        }
        self.client.get("/api/v1/products", params=params, headers=headers,
                       name="GET /api/v1/products (search)")
    
    @task(15)
    def optimize_single_product(self):
        """Optimize single product - core functionality"""
        if not self.auth_token or not self.product_ids:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        product_id = random.choice(self.product_ids)
        
        optimization_data = {
            "product_id": product_id,
            "optimization_type": random.choice(["title", "description", "meta", "full"])
        }
        
        self.client.post("/api/v1/optimize/product", json=optimization_data, 
                        headers=headers, name="POST /api/v1/optimize/product")
    
    @task(3)
    def optimize_bulk_products(self):
        """Optimize multiple products - resource intensive"""
        if not self.auth_token or len(self.product_ids) < 3:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # Select random subset of products for bulk optimization
        selected_products = random.sample(self.product_ids, min(5, len(self.product_ids)))
        
        bulk_data = {
            "store_id": self.store_id,
            "product_ids": selected_products,
            "optimization_type": "full"
        }
        
        self.client.post("/api/v1/optimize/bulk", json=bulk_data, 
                        headers=headers, name="POST /api/v1/optimize/bulk")
    
    @task(6)
    def check_optimization_job(self):
        """Check optimization job status - monitoring operation"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # Use a random job ID (in real scenario, track actual job IDs)
        job_id = f"job_{uuid.uuid4().hex[:8]}"
        
        self.client.get(f"/api/v1/jobs/{job_id}", headers=headers, 
                       name="GET /api/v1/jobs/{id}")
    
    @task(4)
    def view_analytics(self):
        """View analytics dashboard - reporting operation"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # Test different analytics endpoints
        endpoints = [
            "/api/v1/analytics/overview",
            "/api/v1/analytics/performance",
            "/api/v1/analytics/costs"
        ]
        
        endpoint = random.choice(endpoints)
        self.client.get(endpoint, headers=headers, name=f"GET {endpoint}")
    
    @task(2)
    def webhook_wordpress(self):
        """Simulate WordPress webhook - integration testing"""
        webhook_data = {
            "action": "product_updated",
            "product_id": random.randint(1, 1000),
            "store_id": self.store_id or "store_123",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # WordPress webhooks typically include signature
        headers = {
            "X-WP-Signature": "webhook_signature_123",
            "Content-Type": "application/json"
        }
        
        self.client.post("/api/v1/webhooks/wordpress", json=webhook_data, 
                        headers=headers, name="POST /api/v1/webhooks/wordpress")


class HighVolumeUser(GridSpokeUser):
    """
    High-volume user simulating enterprise customers with large stores.
    More aggressive optimization patterns.
    """
    wait_time = between(0.5, 2)  # Faster interactions
    
    def create_test_products(self):
        """Create more test products for high-volume testing"""
        if not self.store_id or not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # Create 50 products for high-volume testing
        for i in range(50):
            product_data = {
                "name": f"Enterprise Product {i}",
                "description": f"Enterprise-level product description {i}",
                "price": round(random.uniform(50.0, 2999.99), 2),
                "category": random.choice(["enterprise", "professional", "industrial"]),
                "store_id": self.store_id
            }
            
            response = self.client.post("/api/v1/products", json=product_data, headers=headers)
            if response.status_code == 201:
                self.product_ids.append(response.json()["id"])
    
    @task(20)
    def bulk_optimize_large_batch(self):
        """Large batch optimization - enterprise use case"""
        if not self.auth_token or len(self.product_ids) < 10:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # Select larger batch for enterprise testing
        batch_size = min(20, len(self.product_ids))
        selected_products = random.sample(self.product_ids, batch_size)
        
        bulk_data = {
            "store_id": self.store_id,
            "product_ids": selected_products,
            "optimization_type": "full",
            "priority": "high"
        }
        
        self.client.post("/api/v1/optimize/bulk", json=bulk_data, 
                        headers=headers, name="POST /api/v1/optimize/bulk (large)")


class APIStressUser(HttpUser):
    """
    Stress testing user focusing on API limits and error handling.
    Tests system behavior under extreme load.
    """
    wait_time = between(0.1, 0.5)  # Very fast requests
    
    def on_start(self):
        """Minimal setup for stress testing"""
        self.auth_token = "stress_test_token_123"  # Use fixed token for speed
    
    @task(30)
    def rapid_product_requests(self):
        """Rapid product list requests to test rate limiting"""
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        self.client.get("/api/v1/products", headers=headers, 
                       name="GET /api/v1/products (stress)")
    
    @task(20)
    def concurrent_optimization_requests(self):
        """Concurrent optimization requests to test AI service limits"""
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        optimization_data = {
            "product_id": f"stress_product_{random.randint(1, 1000)}",
            "optimization_type": "title"
        }
        
        self.client.post("/api/v1/optimize/product", json=optimization_data, 
                        headers=headers, name="POST /api/v1/optimize/product (stress)")
    
    @task(10)
    def health_check_spam(self):
        """Rapid health checks to test system monitoring"""
        self.client.get("/health", name="GET /health (stress)")


class WebSocketLoadUser(HttpUser):
    """
    WebSocket-focused load testing for real-time features.
    
    Note: This is a simplified HTTP-based test. For full WebSocket testing,
    consider using specialized tools like websocket-client or artillery.io
    """
    wait_time = between(2, 8)
    
    @task(5)
    def simulate_websocket_connect(self):
        """Simulate WebSocket connection establishment"""
        # This would normally establish WebSocket connection
        # For HTTP testing, we test the WebSocket endpoint availability
        headers = {"Upgrade": "websocket", "Connection": "Upgrade"}
        self.client.get("/ws", headers=headers, name="WebSocket Connect Attempt")
    
    @task(15)
    def poll_job_status(self):
        """Polling-based job status (alternative to WebSocket)"""
        job_id = f"ws_job_{uuid.uuid4().hex[:8]}"
        self.client.get(f"/api/v1/jobs/{job_id}/status", 
                       name="GET /api/v1/jobs/{id}/status")


# Load Testing Scenarios
class QuickLoadTest(GridSpokeUser):
    """Quick load test for development - 5 minute test"""
    wait_time = between(1, 3)
    
    # Override to create fewer products
    def create_test_products(self):
        if not self.store_id or not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        for i in range(5):  # Only 5 products for quick test
            product_data = {
                "name": f"Quick Test Product {i}",
                "description": f"Quick test description {i}",
                "price": round(random.uniform(10.0, 100.0), 2),
                "category": "test",
                "store_id": self.store_id
            }
            
            response = self.client.post("/api/v1/products", json=product_data, headers=headers)
            if response.status_code == 201:
                self.product_ids.append(response.json()["id"])


# Custom Load Testing Tasks
class SpikeTestUser(GridSpokeUser):
    """
    Spike testing user for sudden load increases.
    Simulates Black Friday / high-traffic scenarios.
    """
    wait_time = between(0.1, 1)  # Very aggressive timing
    
    @task(25)
    def spike_optimization_requests(self):
        """High-frequency optimization during traffic spikes"""
        if not self.auth_token or not self.product_ids:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # Multiple rapid requests
        for _ in range(3):
            product_id = random.choice(self.product_ids)
            optimization_data = {
                "product_id": product_id,
                "optimization_type": "title"
            }
            
            self.client.post("/api/v1/optimize/product", json=optimization_data, 
                           headers=headers, name="POST /api/v1/optimize/product (spike)")


if __name__ == "__main__":
    """
    Usage examples:
    
    # Quick development test (5 users, 1 minute)
    locust -f locustfile.py --users 5 --spawn-rate 1 --run-time 1m --host http://localhost:8000
    
    # Standard load test (50 users, 10 minutes)
    locust -f locustfile.py --users 50 --spawn-rate 5 --run-time 10m --host http://localhost:8000
    
    # High-volume enterprise test (200 users, 30 minutes)
    locust -f locustfile.py --users 200 --spawn-rate 10 --run-time 30m --host http://localhost:8000 --user-classes HighVolumeUser
    
    # Stress test (500 users, 5 minutes)
    locust -f locustfile.py --users 500 --spawn-rate 50 --run-time 5m --host http://localhost:8000 --user-classes APIStressUser
    
    # WebSocket load test
    locust -f locustfile.py --users 100 --spawn-rate 10 --run-time 15m --host http://localhost:8000 --user-classes WebSocketLoadUser
    
    # Spike test (gradual ramp to 300 users)
    locust -f locustfile.py --users 300 --spawn-rate 25 --run-time 20m --host http://localhost:8000 --user-classes SpikeTestUser
    """
    
    print("GridSpoke Load Testing Scripts")
    print("==============================")
    print("Available User Classes:")
    print("- GridSpokeUser: Standard ecommerce user behavior")
    print("- HighVolumeUser: Enterprise customer with large stores")
    print("- APIStressUser: Rapid requests for stress testing")
    print("- WebSocketLoadUser: Real-time feature testing")
    print("- QuickLoadTest: Fast development testing")
    print("- SpikeTestUser: Traffic spike simulation")
    print("\nRun with: locust -f locustfile.py --host http://localhost:8000")
