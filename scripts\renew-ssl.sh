#!/bin/bash

# GridSpoke SSL Certificate Renewal Script
# Usage: ./scripts/renew-ssl.sh

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check certificate expiry
check_certificate_expiry() {
    local domain="$1"
    local cert_path="/etc/letsencrypt/live/$domain/fullchain.pem"
    
    if [[ -f "$cert_path" ]]; then
        local expiry_date=$(openssl x509 -enddate -noout -in "$cert_path" | cut -d= -f2)
        local expiry_epoch=$(date -d "$expiry_date" +%s)
        local current_epoch=$(date +%s)
        local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        echo "$days_until_expiry"
    else
        echo "0"
    fi
}

# Function to renew certificates
renew_certificates() {
    log_info "Attempting to renew SSL certificates..."
    
    # Try to renew certificates
    if docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" run --rm certbot renew --quiet; then
        log_success "Certificate renewal completed successfully"
        return 0
    else
        log_error "Certificate renewal failed"
        return 1
    fi
}

# Function to reload nginx
reload_nginx() {
    log_info "Reloading nginx configuration..."
    
    if docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" exec nginx nginx -s reload; then
        log_success "Nginx reloaded successfully"
    else
        log_error "Failed to reload nginx"
        return 1
    fi
}

# Function to send notification
send_notification() {
    local message="$1"
    local priority="$2"  # low, normal, high
    
    # Load environment variables
    source "$PROJECT_DIR/.env.prod" 2>/dev/null || true
    
    # Send email notification if configured
    if [[ -n "${SMTP_HOST:-}" ]] && [[ -n "${FROM_EMAIL:-}" ]]; then
        echo "$message" | mail -s "GridSpoke SSL Certificate Update" "$FROM_EMAIL" 2>/dev/null || true
    fi
    
    # Log to syslog
    logger -t gridspoke-ssl "$message"
    
    # Add webhook notification if configured
    if [[ -n "${WEBHOOK_URL:-}" ]]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"$message\",\"priority\":\"$priority\"}" \
            2>/dev/null || true
    fi
}

# Main function
main() {
    cd "$PROJECT_DIR"
    
    # Source environment variables
    if [[ ! -f ".env.prod" ]]; then
        log_error ".env.prod file not found"
        exit 1
    fi
    
    source ".env.prod"
    
    if [[ -z "${DOMAIN_NAME:-}" ]]; then
        log_error "DOMAIN_NAME not set in .env.prod"
        exit 1
    fi
    
    log_info "Starting SSL certificate renewal check for $DOMAIN_NAME"
    
    # Check certificate expiry
    days_until_expiry=$(check_certificate_expiry "$DOMAIN_NAME")
    
    log_info "Certificate expires in $days_until_expiry days"
    
    # Renew if certificate expires in less than 30 days
    if [[ "$days_until_expiry" -lt 30 ]]; then
        log_warning "Certificate expires in $days_until_expiry days, attempting renewal..."
        
        if renew_certificates; then
            reload_nginx
            send_notification "SSL certificate for $DOMAIN_NAME renewed successfully. New expiry: $(date -d "+90 days" +%Y-%m-%d)" "normal"
            log_success "SSL certificate renewal completed"
        else
            send_notification "SSL certificate renewal failed for $DOMAIN_NAME. Certificate expires in $days_until_expiry days!" "high"
            log_error "SSL certificate renewal failed"
            exit 1
        fi
    else
        log_info "Certificate is still valid for $days_until_expiry days, no renewal needed"
        
        # Send weekly notification if certificate expires in less than 7 days
        if [[ "$days_until_expiry" -lt 7 ]]; then
            send_notification "SSL certificate for $DOMAIN_NAME expires in $days_until_expiry days!" "high"
        fi
    fi
}

# Run main function
main "$@"
