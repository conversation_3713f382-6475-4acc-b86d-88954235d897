# GridSpoke - AI-Powered Ecommerce SEO Optimizer

## Project Overview

GridSpoke is a comprehensive, AI-powered SEO optimization platform for ecommerce stores. It's designed to automatically improve product listings (titles, descriptions, meta tags) to increase visibility and search engine ranking. The system is built with a modern, microservices-based architecture and is ready for production deployment.

The core components of the project are:

*   **Backend API**: A robust FastAPI application that serves as the central hub for all operations. It manages stores, products, users, and optimization jobs.
*   **AI Engine**: Utilizes the Mirascope framework with OpenRouter to leverage various large language models for content generation and optimization.
*   **Asynchronous Task Processing**: Employs Celery and Redis to handle long-running tasks like product syncing and AI-powered optimization without blocking the API.
*   **Database**: Uses PostgreSQL with the `pgvector` extension for efficient storage and querying of vector embeddings, likely for semantic search or similarity features.
*   **Frontend Dashboard**: A user-friendly, single-page application built with Tailwind CSS and Alpine.js for real-time monitoring and management of the optimization process.
*   **WordPress Plugin**: A connector for WooCommerce and SureCart stores that seamlessly integrates with the GridSpoke API, enabling automated product syncing and content updates.
*   **Monitoring and Observability**: A complete monitoring stack with Prometheus for metrics collection and Grafana for visualization and alerting.

The entire system is containerized using Docker and orchestrated with Docker Compose, ensuring a consistent and scalable deployment environment.

## Building and Running

The project is designed to be run with Docker and Docker Compose. To test the frontend application, you are equipped with the Playwright MCP - which allows you to open your own browser and interact with the frontend application. The following commands are based on the provided documentation and scripts.

### Prerequisites

*   Docker and Docker Compose installed.
*   A `.env.prod` file created from `.env.prod.example` with the necessary environment variables.

### Running the Application

The primary way to run the application is by using the provided deployment script:

```bash
# For the first-time deployment, which includes setting up SSL certificates:
./scripts/deploy.sh --first-time

# For subsequent deployments:
./scripts/deploy.sh
```

To run the application for local development, you can use the `docker-compose.yml` file (not the `prod` version):

```bash
# Start all services in detached mode
docker-compose up -d

# Stop all services
docker-compose down
```

### Running Tests

The project uses `pytest` for testing. To run the tests, you'll need to have the development dependencies installed.

```bash
# Install dependencies from requirements.txt
pip install -r ecommerce-seo-optimizer/api/requirements.txt

# Run the tests
pytest
```

## Development Conventions

Based on the project structure and files, the following development conventions can be inferred:

*   **Code Style**: The Python code follows the `black` code style for formatting and `isort` for import sorting.
*   **Type Hinting**: The code uses type hints, and `mypy` is included in the development dependencies for static type checking.
*   **API Specification**: The API is documented in `docs/API.md`, and the FastAPI application can generate an OpenAPI specification.
*   **Database Migrations**: `alembic` is used for managing database schema migrations.
*   **Logging**: The application uses `structlog` for structured, JSON-formatted logs, which is ideal for production environments.
*   **Modularity**: The backend code is organized into modules for `core`, `api`, `crud`, `models`, `schemas`, and `services`, promoting a clean and maintainable architecture.
*   **Asynchronous Code**: The FastAPI backend and database interactions are fully asynchronous, using `async/await` and `asyncpg`.
*   **WordPress Plugin Development**: The WordPress plugin follows the standard structure and best practices for WordPress development, including a main plugin class, hooks, and a clear separation of concerns.

## Recent Fixes and Improvements

### Backend API Fixes
1.  **Integrated AI Agent with Celery Tasks**: The Celery tasks in `api/workers/tasks/product_tasks.py` now correctly call the `ProductOptimizer` agent to perform AI-powered optimizations.
2.  **Implemented Multi-Queue Routing**: The Celery app is now configured to use multiple queues for different task priorities, improving task management and resource allocation.
3.  **Implemented Advanced Rate Limiting**: The `ProductOptimizer` agent now uses the `AIRateLimiter` to enforce token-based and cost-based rate limits on AI API calls.
4.  **Created Missing Endpoints**: Placeholder endpoints for `/stats` and `/analytics` have been created.

### Frontend Fixes
1.  **Implemented Products and Jobs Tabs**: The "Products" and "Jobs" tabs in the dashboard are now functional, displaying data from the API.
2.  **Consolidated JavaScript Files**: The conflicting dashboard JavaScript files have been consolidated into a single `dashboard.js` file, and the old files have been removed.
3.  **Removed Hardcoded URLs**: Hardcoded URLs in the frontend JavaScript have been replaced with a configurable base URL.

## Current Status

The GridSpoke codebase is now in a more complete and robust state. Here is the current status of the main components:

*   **Job Management System**: The job management system is now fully functional, with endpoints for creating, retrieving, and canceling jobs.
*   **Celery Task Processing**: The Celery task processing system is now fully integrated with the AI agents and the database.
*   **Monitoring Endpoints**: Placeholder monitoring endpoints have been created.
*   **Frontend Dashboard**: The frontend dashboard now has functional "Products" and "Jobs" tabs.
*   **WordPress Plugin Integration**: The WordPress plugin integration is ready for testing.

## Next Steps

Here are some suggestions for the next steps:

1.  **Flesh out the analytics endpoints**: The `/stats` and `/analytics` endpoints are still placeholders. The next step is to implement the logic to query the database and provide meaningful data to the frontend.
2.  **Enhance the `ContentGenerator` and `ImageAnalyzer` agents**: These agents are still placeholders. The next step is to implement the logic to generate blog posts, FAQs, and analyze images.
3.  **Testing**: The application now has a solid foundation, but it would benefit from a comprehensive test suite to ensure its stability and reliability.
