<?php
/**
 * Logs page template
 */

if (!defined('ABSPATH')) {
    exit;
}

// Display admin messages
GridSpoke_Admin::display_admin_message();

// Pagination setup
$page_links = paginate_links(array(
    'base' => add_query_arg('paged', '%#%'),
    'format' => '',
    'prev_text' => __('&laquo;', 'gridspoke-seo'),
    'next_text' => __('&raquo;', 'gridspoke-seo'),
    'total' => ceil($total_logs / $per_page),
    'current' => $page
));
?>

<div class="wrap">
    <h1><?php esc_html_e('GridSpoke SEO Logs', 'gridspoke-seo'); ?></h1>
    
    <!-- Filters -->
    <div class="gridspoke-logs-filters">
        <form method="get" action="">
            <input type="hidden" name="page" value="gridspoke-seo-logs" />
            
            <div class="gridspoke-filter-row">
                <label for="level"><?php esc_html_e('Log Level:', 'gridspoke-seo'); ?></label>
                <select name="level" id="level">
                    <option value=""><?php esc_html_e('All Levels', 'gridspoke-seo'); ?></option>
                    <option value="debug" <?php selected($level, 'debug'); ?>><?php esc_html_e('Debug', 'gridspoke-seo'); ?></option>
                    <option value="info" <?php selected($level, 'info'); ?>><?php esc_html_e('Info', 'gridspoke-seo'); ?></option>
                    <option value="warning" <?php selected($level, 'warning'); ?>><?php esc_html_e('Warning', 'gridspoke-seo'); ?></option>
                    <option value="error" <?php selected($level, 'error'); ?>><?php esc_html_e('Error', 'gridspoke-seo'); ?></option>
                </select>
                
                <label for="search"><?php esc_html_e('Search:', 'gridspoke-seo'); ?></label>
                <input type="text" name="search" id="search" value="<?php echo esc_attr($search); ?>" placeholder="<?php esc_attr_e('Search in messages...', 'gridspoke-seo'); ?>" />
                
                <?php submit_button(__('Filter', 'gridspoke-seo'), 'secondary', 'submit', false); ?>
                
                <?php if (!empty($level) || !empty($search)): ?>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=gridspoke-seo-logs')); ?>" class="button">
                        <?php esc_html_e('Clear Filters', 'gridspoke-seo'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>
    
    <!-- Log Actions -->
    <div class="gridspoke-logs-actions">
        <button type="button" id="clear-all-logs" class="button button-secondary" data-level="">
            <?php esc_html_e('Clear All Logs', 'gridspoke-seo'); ?>
        </button>
        
        <?php if (!empty($level)): ?>
            <button type="button" id="clear-level-logs" class="button button-secondary" data-level="<?php echo esc_attr($level); ?>">
                <?php printf(esc_html__('Clear %s Logs', 'gridspoke-seo'), ucfirst($level)); ?>
            </button>
        <?php endif; ?>
        
        <button type="button" id="export-logs" class="button button-secondary">
            <?php esc_html_e('Export to CSV', 'gridspoke-seo'); ?>
        </button>
        
        <button type="button" id="refresh-logs" class="button button-secondary">
            <?php esc_html_e('Refresh', 'gridspoke-seo'); ?>
        </button>
    </div>
    
    <!-- Logs Table -->
    <div class="gridspoke-logs-container">
        <?php if (!empty($logs)): ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col" class="gridspoke-log-timestamp"><?php esc_html_e('Timestamp', 'gridspoke-seo'); ?></th>
                        <th scope="col" class="gridspoke-log-level"><?php esc_html_e('Level', 'gridspoke-seo'); ?></th>
                        <th scope="col" class="gridspoke-log-message"><?php esc_html_e('Message', 'gridspoke-seo'); ?></th>
                        <th scope="col" class="gridspoke-log-context"><?php esc_html_e('Context', 'gridspoke-seo'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($logs as $log): ?>
                        <tr class="gridspoke-log-row gridspoke-log-<?php echo esc_attr($log['level']); ?>">
                            <td class="gridspoke-log-timestamp">
                                <span title="<?php echo esc_attr($log['timestamp']); ?>">
                                    <?php echo esc_html(human_time_diff(strtotime($log['timestamp']))); ?> ago
                                </span>
                            </td>
                            <td class="gridspoke-log-level">
                                <span class="gridspoke-log-level-badge gridspoke-log-level-<?php echo esc_attr($log['level']); ?>">
                                    <?php echo esc_html(ucfirst($log['level'])); ?>
                                </span>
                            </td>
                            <td class="gridspoke-log-message">
                                <?php echo esc_html($log['message']); ?>
                                
                                <?php if (!empty($log['user_id'])): ?>
                                    <div class="gridspoke-log-meta">
                                        <?php 
                                        $user = get_user_by('id', $log['user_id']);
                                        if ($user) {
                                            printf(
                                                esc_html__('User: %s', 'gridspoke-seo'),
                                                esc_html($user->display_name)
                                            );
                                        }
                                        ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($log['ip_address'])): ?>
                                    <div class="gridspoke-log-meta">
                                        <?php printf(esc_html__('IP: %s', 'gridspoke-seo'), esc_html($log['ip_address'])); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="gridspoke-log-context">
                                <?php if (!empty($log['context'])): ?>
                                    <details>
                                        <summary><?php esc_html_e('View Context', 'gridspoke-seo'); ?></summary>
                                        <pre class="gridspoke-log-context-data"><?php echo esc_html($log['context']); ?></pre>
                                    </details>
                                <?php else: ?>
                                    <span class="gridspoke-log-no-context"><?php esc_html_e('No context', 'gridspoke-seo'); ?></span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <?php if ($page_links): ?>
                <div class="tablenav">
                    <div class="tablenav-pages">
                        <?php echo $page_links; ?>
                    </div>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <div class="gridspoke-no-logs">
                <p><?php esc_html_e('No logs found matching your criteria.', 'gridspoke-seo'); ?></p>
                
                <?php if (!empty($level) || !empty($search)): ?>
                    <p>
                        <a href="<?php echo esc_url(admin_url('admin.php?page=gridspoke-seo-logs')); ?>" class="button">
                            <?php esc_html_e('View All Logs', 'gridspoke-seo'); ?>
                        </a>
                    </p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Log Statistics -->
    <div class="gridspoke-logs-stats">
        <h3><?php esc_html_e('Log Statistics (Last 7 Days)', 'gridspoke-seo'); ?></h3>
        <div class="gridspoke-stats-grid">
            <div class="gridspoke-stat-item">
                <span class="gridspoke-stat-number"><?php echo esc_html($log_stats['total'] ?? 0); ?></span>
                <span class="gridspoke-stat-label"><?php esc_html_e('Total Logs', 'gridspoke-seo'); ?></span>
            </div>
            
            <?php if (!empty($log_stats['by_level'])): ?>
                <?php foreach ($log_stats['by_level'] as $stat_level => $count): ?>
                    <div class="gridspoke-stat-item">
                        <span class="gridspoke-stat-number"><?php echo esc_html($count); ?></span>
                        <span class="gridspoke-stat-label"><?php echo esc_html(ucfirst($stat_level)); ?></span>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Clear logs functionality
    $('#clear-all-logs, #clear-level-logs').on('click', function() {
        var level = $(this).data('level');
        var message = level ? 
            '<?php esc_html_e('Are you sure you want to clear all logs of this level? This action cannot be undone.', 'gridspoke-seo'); ?>' :
            '<?php esc_html_e('Are you sure you want to clear all logs? This action cannot be undone.', 'gridspoke-seo'); ?>';
        
        if (!confirm(message)) {
            return;
        }
        
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_clear_logs',
                nonce: gridspoke_admin.nonce,
                level: level
            },
            beforeSend: function() {
                $(this).prop('disabled', true).text('<?php esc_html_e('Clearing...', 'gridspoke-seo'); ?>');
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data.message || '<?php esc_html_e('Failed to clear logs', 'gridspoke-seo'); ?>');
                }
            },
            error: function() {
                alert('<?php esc_html_e('An error occurred while clearing logs', 'gridspoke-seo'); ?>');
            },
            complete: function() {
                $(this).prop('disabled', false).text('<?php esc_html_e('Clear Logs', 'gridspoke-seo'); ?>');
            }
        });
    });
    
    // Export logs functionality
    $('#export-logs').on('click', function() {
        var level = '<?php echo esc_js($level); ?>';
        var search = '<?php echo esc_js($search); ?>';
        
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_export_logs',
                nonce: gridspoke_admin.nonce,
                level: level,
                search: search
            },
            beforeSend: function() {
                $('#export-logs').prop('disabled', true).text('<?php esc_html_e('Exporting...', 'gridspoke-seo'); ?>');
            },
            success: function(response) {
                if (response.success) {
                    // Create download link
                    var link = document.createElement('a');
                    link.href = response.data.download_url;
                    link.download = response.data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    alert(response.data.message || '<?php esc_html_e('Failed to export logs', 'gridspoke-seo'); ?>');
                }
            },
            error: function() {
                alert('<?php esc_html_e('An error occurred while exporting logs', 'gridspoke-seo'); ?>');
            },
            complete: function() {
                $('#export-logs').prop('disabled', false).text('<?php esc_html_e('Export to CSV', 'gridspoke-seo'); ?>');
            }
        });
    });
    
    // Refresh logs
    $('#refresh-logs').on('click', function() {
        location.reload();
    });
});
</script>
