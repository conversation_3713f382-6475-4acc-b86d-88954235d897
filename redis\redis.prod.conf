# GridSpoke Redis Production Configuration

# Network and Connection Settings
bind 127.0.0.1
port 6379
timeout 300
keepalive 300
tcp-backlog 511

# Authentication
requirepass REDIS_PASSWORD_PLACEHOLDER

# Memory Management
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence Settings
save 900 1
save 300 10
save 60 10000

# RDB Configuration
rdbcompression yes
rdbchecksum yes
dbfilename gridspoke.rdb
dir /data

# AOF Configuration (disabled for performance)
appendonly no

# Performance Tuning
hz 10
latency-monitor-threshold 100

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Security
protected-mode yes
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command EVAL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_9d85b7e8b1c2"

# Client Settings
maxclients 10000
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Logging
loglevel notice
logfile ""
syslog-enabled yes
syslog-ident gridspoke-redis

# Advanced Settings
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
stream-node-max-bytes 4096
stream-node-max-entries 100

# Lazy Freeing
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# Modules (if available)
# loadmodule /usr/lib/redis/modules/redisearch.so
# loadmodule /usr/lib/redis/modules/redisjson.so
