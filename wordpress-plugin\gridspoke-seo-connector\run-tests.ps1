# GridSpoke SEO Connector Test Runner for Windows
# This script sets up and runs the WordPress plugin tests

param(
    [string]$DbName = "gridspoke_wp_test",
    [string]$DbUser = "root",
    [string]$DbPass = "",
    [string]$DbHost = "localhost",
    [string]$WpVersion = "latest",
    [switch]$SkipDbCreate = $false,
    [switch]$Coverage = $false,
    [switch]$Verbose = $false
)

Write-Host "GridSpoke SEO Connector Test Runner" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Check if PHPUnit is available
$phpunit = Get-Command phpunit -ErrorAction SilentlyContinue
if (-not $phpunit) {
    Write-Host "PHPUnit not found. Please install PHPUnit globally or use Composer." -ForegroundColor Red
    Write-Host "Install with: composer global require phpunit/phpunit" -ForegroundColor Yellow
    exit 1
}

# Check if WordPress test environment is set up
$wpTestsDir = $env:WP_TESTS_DIR
if (-not $wpTestsDir) {
    $wpTestsDir = "$env:TEMP\wordpress-tests-lib"
    $env:WP_TESTS_DIR = $wpTestsDir
}

Write-Host "WordPress Tests Directory: $wpTestsDir" -ForegroundColor Cyan

# Check if test environment exists
if (-not (Test-Path "$wpTestsDir\includes\functions.php")) {
    Write-Host "WordPress test environment not found. Setting up..." -ForegroundColor Yellow
    
    # Create tests directory
    if (-not (Test-Path $wpTestsDir)) {
        New-Item -ItemType Directory -Path $wpTestsDir -Force | Out-Null
    }
    
    Write-Host "Please run the following commands to set up WordPress test environment:" -ForegroundColor Yellow
    Write-Host "1. Download WordPress test suite manually or use WSL/Git Bash to run:" -ForegroundColor White
    Write-Host "   bash bin/install-wp-tests.sh $DbName $DbUser $DbPass $DbHost $WpVersion" -ForegroundColor White
    Write-Host "2. Or set up manually following WordPress testing documentation" -ForegroundColor White
    
    exit 1
}

# Set environment variables
$env:WP_TESTS_DIR = $wpTestsDir
$env:WP_CORE_DIR = "$env:TEMP\wordpress"

Write-Host "Running GridSpoke SEO Connector tests..." -ForegroundColor Green

# Build PHPUnit command
$phpunitArgs = @()

if ($Coverage) {
    $phpunitArgs += "--coverage-html", "tests/coverage"
    $phpunitArgs += "--coverage-text"
}

if ($Verbose) {
    $phpunitArgs += "--verbose"
}

# Add configuration file
$phpunitArgs += "--configuration", "phpunit.xml"

# Run tests
try {
    Write-Host "Executing: phpunit $($phpunitArgs -join ' ')" -ForegroundColor Cyan
    
    & phpunit @phpunitArgs
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`nAll tests passed! ✅" -ForegroundColor Green
        
        if ($Coverage) {
            Write-Host "Coverage report generated in tests/coverage/" -ForegroundColor Cyan
        }
    } else {
        Write-Host "`nSome tests failed! ❌" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "Error running tests: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`nTest run completed." -ForegroundColor Green
