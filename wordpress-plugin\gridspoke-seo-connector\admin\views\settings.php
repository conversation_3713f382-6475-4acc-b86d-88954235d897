<?php
/**
 * Settings page template
 */

if (!defined('ABSPATH')) {
    exit;
}

// Display admin messages
GridSpoke_Admin::display_admin_message();
?>

<div class="wrap">
    <h1><?php esc_html_e('GridSpoke SEO Settings', 'gridspoke-seo'); ?></h1>
    
    <div class="gridspoke-settings-header">
        <p class="description">
            <?php esc_html_e('Configure your GridSpoke SEO Connector settings below. Make sure to test your API connection after making changes.', 'gridspoke-seo'); ?>
        </p>
    </div>

    <form method="post" action="options.php">
        <?php
        settings_fields('gridspoke_seo_settings_group');
        do_settings_sections('gridspoke_seo_settings');
        ?>
        
        <div class="gridspoke-settings-actions">
            <?php submit_button(__('Save Settings', 'gridspoke-seo'), 'primary', 'submit', false); ?>
            
            <button type="button" id="test-connection" class="button button-secondary">
                <?php esc_html_e('Test Connection', 'gridspoke-seo'); ?>
            </button>
            
            <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin-post.php?action=gridspoke_reset_settings'), 'gridspoke_reset_settings')); ?>" 
               class="button button-link-delete" 
               onclick="return confirm('<?php echo esc_js(__('Are you sure you want to reset all settings to defaults? This action cannot be undone.', 'gridspoke-seo')); ?>')">
                <?php esc_html_e('Reset to Defaults', 'gridspoke-seo'); ?>
            </a>
        </div>
    </form>
    
    <div id="connection-test-result" class="notice" style="display: none;">
        <p id="connection-test-message"></p>
    </div>
    
    <div class="gridspoke-settings-info">
        <h2><?php esc_html_e('Integration Information', 'gridspoke-seo'); ?></h2>
        
        <div class="gridspoke-info-grid">
            <div class="gridspoke-info-card">
                <h3><?php esc_html_e('Webhook URL', 'gridspoke-seo'); ?></h3>
                <p class="description"><?php esc_html_e('This URL is used by GridSpoke to send optimization results back to your site:', 'gridspoke-seo'); ?></p>
                <code class="gridspoke-webhook-url"><?php echo esc_url(rest_url('gridspoke-seo/v1/webhook')); ?></code>
                <button type="button" class="button button-small" onclick="navigator.clipboard.writeText('<?php echo esc_js(rest_url('gridspoke-seo/v1/webhook')); ?>')">
                    <?php esc_html_e('Copy', 'gridspoke-seo'); ?>
                </button>
            </div>
            
            <div class="gridspoke-info-card">
                <h3><?php esc_html_e('Supported Platforms', 'gridspoke-seo'); ?></h3>
                <ul class="gridspoke-platform-list">
                    <?php if (class_exists('WooCommerce')): ?>
                        <li class="platform-active">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <?php esc_html_e('WooCommerce', 'gridspoke-seo'); ?>
                        </li>
                    <?php else: ?>
                        <li class="platform-inactive">
                            <span class="dashicons dashicons-dismiss"></span>
                            <?php esc_html_e('WooCommerce (Not Installed)', 'gridspoke-seo'); ?>
                        </li>
                    <?php endif; ?>
                    
                    <?php if (defined('SURECART_PLUGIN_FILE')): ?>
                        <li class="platform-active">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <?php esc_html_e('SureCart', 'gridspoke-seo'); ?>
                        </li>
                    <?php else: ?>
                        <li class="platform-inactive">
                            <span class="dashicons dashicons-dismiss"></span>
                            <?php esc_html_e('SureCart (Not Installed)', 'gridspoke-seo'); ?>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <div class="gridspoke-info-card">
                <h3><?php esc_html_e('Plugin Information', 'gridspoke-seo'); ?></h3>
                <table class="gridspoke-info-table">
                    <tr>
                        <td><?php esc_html_e('Version:', 'gridspoke-seo'); ?></td>
                        <td><?php echo esc_html(GRIDSPOKE_SEO_VERSION); ?></td>
                    </tr>
                    <tr>
                        <td><?php esc_html_e('WordPress Version:', 'gridspoke-seo'); ?></td>
                        <td><?php echo esc_html(get_bloginfo('version')); ?></td>
                    </tr>
                    <tr>
                        <td><?php esc_html_e('PHP Version:', 'gridspoke-seo'); ?></td>
                        <td><?php echo esc_html(PHP_VERSION); ?></td>
                    </tr>
                    <tr>
                        <td><?php esc_html_e('Site URL:', 'gridspoke-seo'); ?></td>
                        <td><?php echo esc_html(home_url()); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <?php
    $settings = GridSpoke_SEO_Connector::get_settings();
    if (!empty($settings['api_key'])) {
        $api_client = GridSpoke_API_Client::get_instance();
        ?>
        <div class="gridspoke-quick-actions">
            <h2><?php esc_html_e('Quick Actions', 'gridspoke-seo'); ?></h2>
            
            <div class="gridspoke-action-buttons">
                <button type="button" id="sync-all-products" class="button button-secondary">
                    <span class="dashicons dashicons-update"></span>
                    <?php esc_html_e('Sync All Products', 'gridspoke-seo'); ?>
                </button>
                
                <a href="<?php echo esc_url(admin_url('admin.php?page=gridspoke-seo')); ?>" class="button button-secondary">
                    <span class="dashicons dashicons-chart-line"></span>
                    <?php esc_html_e('View Dashboard', 'gridspoke-seo'); ?>
                </a>
                
                <a href="<?php echo esc_url(admin_url('admin.php?page=gridspoke-seo-logs')); ?>" class="button button-secondary">
                    <span class="dashicons dashicons-list-view"></span>
                    <?php esc_html_e('View Logs', 'gridspoke-seo'); ?>
                </a>
                
                <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin-post.php?action=gridspoke_register_webhook'), 'gridspoke_register_webhook')); ?>" 
                   class="button button-secondary">
                    <span class="dashicons dashicons-admin-links"></span>
                    <?php esc_html_e('Register Webhook', 'gridspoke-seo'); ?>
                </a>
            </div>
        </div>
        <?php
    }
    ?>
    
    <div class="gridspoke-help-section">
        <h2><?php esc_html_e('Need Help?', 'gridspoke-seo'); ?></h2>
        <p>
            <?php 
            printf(
                esc_html__('Visit our %1$sdocumentation%2$s or %3$scontact support%2$s if you need assistance with the GridSpoke SEO Connector.', 'gridspoke-seo'),
                '<a href="https://gridspoke.com/docs" target="_blank">',
                '</a>',
                '<a href="https://gridspoke.com/support" target="_blank">'
            );
            ?>
        </p>
        
        <div class="gridspoke-help-links">
            <a href="https://gridspoke.com/docs" target="_blank" class="button button-secondary">
                <span class="dashicons dashicons-book"></span>
                <?php esc_html_e('Documentation', 'gridspoke-seo'); ?>
            </a>
            
            <a href="https://gridspoke.com/support" target="_blank" class="button button-secondary">
                <span class="dashicons dashicons-sos"></span>
                <?php esc_html_e('Get Support', 'gridspoke-seo'); ?>
            </a>
            
            <a href="https://gridspoke.com/api" target="_blank" class="button button-secondary">
                <span class="dashicons dashicons-admin-tools"></span>
                <?php esc_html_e('API Documentation', 'gridspoke-seo'); ?>
            </a>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Show/Hide API key
    $('#show-api-key').on('click', function() {
        var $input = $('#api_key');
        var $button = $(this);
        
        if ($input.attr('type') === 'password') {
            $input.attr('type', 'text');
            $button.text('<?php echo esc_js(__('Hide', 'gridspoke-seo')); ?>');
        } else {
            $input.attr('type', 'password');
            $button.text('<?php echo esc_js(__('Show', 'gridspoke-seo')); ?>');
        }
    });
    
    // Test connection
    $('#test-connection').on('click', function() {
        var $button = $(this);
        var $result = $('#connection-test-result');
        var $message = $('#connection-test-message');
        
        $button.prop('disabled', true).text('<?php echo esc_js(__('Testing...', 'gridspoke-seo')); ?>');
        $result.hide();
        
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_test_connection',
                nonce: gridspoke_admin.nonce
            },
            success: function(response) {
                $result.removeClass('notice-error notice-success');
                
                if (response.success) {
                    $result.addClass('notice-success');
                    $message.text(response.data.message);
                } else {
                    $result.addClass('notice-error');
                    $message.text(response.data.message || 'Connection failed');
                }
                
                $result.show();
            },
            error: function() {
                $result.removeClass('notice-success').addClass('notice-error');
                $message.text('<?php echo esc_js(__('An error occurred while testing the connection.', 'gridspoke-seo')); ?>');
                $result.show();
            },
            complete: function() {
                $button.prop('disabled', false).text('<?php echo esc_js(__('Test Connection', 'gridspoke-seo')); ?>');
            }
        });
    });
    
    // Sync all products
    $('#sync-all-products').on('click', function() {
        var $button = $(this);
        
        if (!confirm('<?php echo esc_js(__('This will sync all products from your store to GridSpoke. Continue?', 'gridspoke-seo')); ?>')) {
            return;
        }
        
        $button.prop('disabled', true);
        $button.find('span').removeClass('dashicons-update').addClass('dashicons-update');
        
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_sync_all_products',
                nonce: gridspoke_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message);
                } else {
                    alert(response.data.message || '<?php echo esc_js(__('Sync failed', 'gridspoke-seo')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred while syncing products.', 'gridspoke-seo')); ?>');
            },
            complete: function() {
                $button.prop('disabled', false);
            }
        });
    });
});
</script>
