# GridSpoke Analytics Service - Business Intelligence and Reporting
# Phase 8: Comprehensive analytics for SEO performance and business metrics

import asyncio
import logging
from datetime import datetime, timedelta, date
from typing import Dict, Any, List, Optional, Union
from enum import Enum
import json
from pydantic import BaseModel, Field
from sqlalchemy import text, func, and_, or_
from sqlalchemy.orm import joinedload
import pandas as pd
import numpy as np
from api.core.database import get_db_session
from api.models.base import Product, Store, OptimizationJob, GeneratedContent
from api.monitoring.metrics import (
    seo_improvement_score, record_seo_improvement,
    ai_cost_usd_total, optimization_requests_total
)
import aioredis
from api.core.config import settings

logger = logging.getLogger(__name__)

# =============================================================================
# ANALYTICS MODELS
# =============================================================================

class TimeRange(str, Enum):
    LAST_24H = "24h"
    LAST_7D = "7d"
    LAST_30D = "30d"
    LAST_90D = "90d"
    LAST_YEAR = "1y"
    CUSTOM = "custom"

class MetricType(str, Enum):
    OPTIMIZATION_COUNT = "optimization_count"
    SEO_SCORE = "seo_score"
    CONTENT_QUALITY = "content_quality"
    AI_COST = "ai_cost"
    PROCESSING_TIME = "processing_time"
    ERROR_RATE = "error_rate"
    USER_ENGAGEMENT = "user_engagement"

class AnalyticsFilter(BaseModel):
    """Flexible analytics filter model."""
    time_range: TimeRange = TimeRange.LAST_30D
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    store_ids: Optional[List[str]] = None
    optimization_types: Optional[List[str]] = None
    ai_models: Optional[List[str]] = None
    metric_types: Optional[List[MetricType]] = None

class TimeSeriesPoint(BaseModel):
    """Single point in time series data."""
    timestamp: datetime
    value: float
    metadata: Optional[Dict[str, Any]] = None

class MetricSummary(BaseModel):
    """Summary statistics for a metric."""
    current_value: float
    previous_value: Optional[float] = None
    change_percent: Optional[float] = None
    trend: str = "stable"  # up, down, stable
    total_count: Optional[int] = None

class AnalyticsReport(BaseModel):
    """Comprehensive analytics report."""
    title: str
    description: str
    time_range: TimeRange
    generated_at: datetime
    metrics: Dict[str, MetricSummary]
    time_series: Dict[str, List[TimeSeriesPoint]]
    insights: List[str]
    recommendations: List[str]

# =============================================================================
# CORE ANALYTICS ENGINE
# =============================================================================

class GridSpokeAnalytics:
    """Main analytics engine for GridSpoke business intelligence."""
    
    def __init__(self):
        self.redis = None
        self.cache_ttl = 3600  # 1 hour cache for analytics
    
    async def _get_redis(self):
        """Get Redis connection for caching."""
        if not self.redis:
            self.redis = aioredis.from_url(settings.REDIS_URL)
        return self.redis
    
    async def _get_cache_key(self, prefix: str, filters: AnalyticsFilter) -> str:
        """Generate cache key for analytics query."""
        filter_hash = hash(str(filters.dict()))
        return f"analytics:{prefix}:{filter_hash}"
    
    async def _get_time_range_filter(self, filters: AnalyticsFilter) -> tuple[datetime, datetime]:
        """Convert time range to start/end dates."""
        end_date = filters.end_date or datetime.utcnow()
        
        if filters.time_range == TimeRange.CUSTOM:
            start_date = filters.start_date or (end_date - timedelta(days=30))
        else:
            time_deltas = {
                TimeRange.LAST_24H: timedelta(hours=24),
                TimeRange.LAST_7D: timedelta(days=7),
                TimeRange.LAST_30D: timedelta(days=30),
                TimeRange.LAST_90D: timedelta(days=90),
                TimeRange.LAST_YEAR: timedelta(days=365)
            }
            start_date = end_date - time_deltas[filters.time_range]
        
        return start_date, end_date
    
    # =============================================================================
    # BUSINESS METRICS ANALYTICS
    # =============================================================================
    
    async def get_optimization_metrics(self, filters: AnalyticsFilter) -> Dict[str, Any]:
        """Get product optimization performance metrics."""
        cache_key = await self._get_cache_key("optimization_metrics", filters)
        redis = await self._get_redis()
        
        # Try cache first
        cached = await redis.get(cache_key)
        if cached:
            return json.loads(cached)
        
        start_date, end_date = await self._get_time_range_filter(filters)
        
        async with get_db_session() as session:
            # Base query with filters
            query = session.query(Product).filter(
                Product.last_optimized.between(start_date, end_date)
            )
            
            if filters.store_ids:
                query = query.filter(Product.store_id.in_(filters.store_ids))
            
            # Get optimization statistics
            stats_query = await session.execute(text("""
                SELECT 
                    COUNT(*) as total_optimizations,
                    COUNT(CASE WHEN optimization_status = 'completed' THEN 1 END) as successful_optimizations,
                    COUNT(CASE WHEN optimization_status = 'failed' THEN 1 END) as failed_optimizations,
                    AVG(CASE WHEN optimization_status = 'completed' THEN 1.0 ELSE 0.0 END) as success_rate,
                    COUNT(DISTINCT store_id) as active_stores,
                    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_processing_time_seconds
                FROM products 
                WHERE last_optimized BETWEEN :start_date AND :end_date
                    AND (:store_ids IS NULL OR store_id = ANY(:store_ids))
            """), {
                "start_date": start_date,
                "end_date": end_date,
                "store_ids": filters.store_ids
            })
            
            stats = stats_query.fetchone()
            
            # Get time series data for optimization trends
            time_series_query = await session.execute(text("""
                SELECT 
                    DATE_TRUNC('hour', last_optimized) as hour,
                    COUNT(*) as optimization_count,
                    AVG(CASE WHEN optimization_status = 'completed' THEN 1.0 ELSE 0.0 END) as success_rate
                FROM products 
                WHERE last_optimized BETWEEN :start_date AND :end_date
                    AND (:store_ids IS NULL OR store_id = ANY(:store_ids))
                GROUP BY DATE_TRUNC('hour', last_optimized)
                ORDER BY hour
            """), {
                "start_date": start_date,
                "end_date": end_date,
                "store_ids": filters.store_ids
            })
            
            time_series_data = time_series_query.fetchall()
            
            result = {
                "summary": {
                    "total_optimizations": stats.total_optimizations or 0,
                    "successful_optimizations": stats.successful_optimizations or 0,
                    "failed_optimizations": stats.failed_optimizations or 0,
                    "success_rate": round((stats.success_rate or 0) * 100, 2),
                    "active_stores": stats.active_stores or 0,
                    "avg_processing_time_minutes": round((stats.avg_processing_time_seconds or 0) / 60, 2)
                },
                "time_series": [
                    {
                        "timestamp": row.hour.isoformat(),
                        "optimization_count": row.optimization_count,
                        "success_rate": round(row.success_rate * 100, 2)
                    }
                    for row in time_series_data
                ],
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "duration_days": (end_date - start_date).days
                }
            }
            
        # Cache result
        await redis.setex(cache_key, self.cache_ttl, json.dumps(result, default=str))
        
        return result
    
    async def get_seo_performance_metrics(self, filters: AnalyticsFilter) -> Dict[str, Any]:
        """Get SEO improvement and performance metrics."""
        cache_key = await self._get_cache_key("seo_performance", filters)
        redis = await self._get_redis()
        
        cached = await redis.get(cache_key)
        if cached:
            return json.loads(cached)
        
        start_date, end_date = await self._get_time_range_filter(filters)
        
        async with get_db_session() as session:
            # Get SEO improvement metrics
            seo_query = await session.execute(text("""
                SELECT 
                    store_id,
                    COUNT(*) as optimized_products,
                    AVG(CASE WHEN seo_score_before IS NOT NULL AND seo_score_after IS NOT NULL 
                        THEN seo_score_after - seo_score_before ELSE NULL END) as avg_score_improvement,
                    COUNT(CASE WHEN seo_score_after > seo_score_before THEN 1 END) as improved_products,
                    AVG(CASE WHEN keywords IS NOT NULL THEN jsonb_array_length(keywords) ELSE 0 END) as avg_keywords_per_product
                FROM products p
                WHERE last_optimized BETWEEN :start_date AND :end_date
                    AND (:store_ids IS NULL OR store_id = ANY(:store_ids))
                GROUP BY store_id
            """), {
                "start_date": start_date,
                "end_date": end_date,
                "store_ids": filters.store_ids
            })
            
            seo_stats = seo_query.fetchall()
            
            # Get content quality metrics
            content_query = await session.execute(text("""
                SELECT 
                    ai_model,
                    content_type,
                    COUNT(*) as content_count,
                    AVG(quality_score) as avg_quality_score,
                    SUM(tokens_used) as total_tokens
                FROM generated_content gc
                WHERE created_at BETWEEN :start_date AND :end_date
                GROUP BY ai_model, content_type
                ORDER BY content_count DESC
            """), {
                "start_date": start_date,
                "end_date": end_date
            })
            
            content_stats = content_query.fetchall()
            
            # Calculate overall metrics
            total_optimized = sum(row.optimized_products for row in seo_stats)
            total_improved = sum(row.improved_products for row in seo_stats)
            improvement_rate = (total_improved / total_optimized * 100) if total_optimized > 0 else 0
            
            avg_score_improvement = np.mean([
                row.avg_score_improvement for row in seo_stats 
                if row.avg_score_improvement is not None
            ]) if seo_stats else 0
            
            result = {
                "summary": {
                    "total_optimized_products": total_optimized,
                    "products_with_improvement": total_improved,
                    "improvement_rate_percent": round(improvement_rate, 2),
                    "avg_seo_score_improvement": round(avg_score_improvement or 0, 2),
                    "avg_keywords_per_product": round(np.mean([
                        row.avg_keywords_per_product for row in seo_stats
                    ]) if seo_stats else 0, 1)
                },
                "by_store": [
                    {
                        "store_id": row.store_id,
                        "optimized_products": row.optimized_products,
                        "improved_products": row.improved_products,
                        "improvement_rate": round(row.improved_products / row.optimized_products * 100, 2),
                        "avg_score_improvement": round(row.avg_score_improvement or 0, 2),
                        "avg_keywords": round(row.avg_keywords_per_product or 0, 1)
                    }
                    for row in seo_stats
                ],
                "content_quality": [
                    {
                        "ai_model": row.ai_model,
                        "content_type": row.content_type,
                        "content_count": row.content_count,
                        "avg_quality_score": round(row.avg_quality_score or 0, 2),
                        "total_tokens": row.total_tokens
                    }
                    for row in content_stats
                ]
            }
        
        await redis.setex(cache_key, self.cache_ttl, json.dumps(result, default=str))
        return result
    
    async def get_cost_analytics(self, filters: AnalyticsFilter) -> Dict[str, Any]:
        """Get AI API cost and ROI analytics."""
        cache_key = await self._get_cache_key("cost_analytics", filters)
        redis = await self._get_redis()
        
        cached = await redis.get(cache_key)
        if cached:
            return json.loads(cached)
        
        start_date, end_date = await self._get_time_range_filter(filters)
        
        async with get_db_session() as session:
            # Get cost breakdown by AI model and content type
            cost_query = await session.execute(text("""
                SELECT 
                    ai_model,
                    content_type,
                    COUNT(*) as request_count,
                    SUM(tokens_used) as total_tokens,
                    SUM(cost_usd) as total_cost_usd,
                    AVG(cost_usd) as avg_cost_per_request,
                    AVG(tokens_used) as avg_tokens_per_request
                FROM generated_content
                WHERE created_at BETWEEN :start_date AND :end_date
                    AND cost_usd IS NOT NULL
                GROUP BY ai_model, content_type
                ORDER BY total_cost_usd DESC
            """), {
                "start_date": start_date,
                "end_date": end_date
            })
            
            cost_stats = cost_query.fetchall()
            
            # Get daily cost trends
            daily_cost_query = await session.execute(text("""
                SELECT 
                    DATE(created_at) as date,
                    SUM(cost_usd) as daily_cost,
                    SUM(tokens_used) as daily_tokens,
                    COUNT(*) as daily_requests
                FROM generated_content
                WHERE created_at BETWEEN :start_date AND :end_date
                    AND cost_usd IS NOT NULL
                GROUP BY DATE(created_at)
                ORDER BY date
            """), {
                "start_date": start_date,
                "end_date": end_date
            })
            
            daily_trends = daily_cost_query.fetchall()
            
            # Calculate total costs and efficiency metrics
            total_cost = sum(row.total_cost_usd for row in cost_stats)
            total_tokens = sum(row.total_tokens for row in cost_stats)
            total_requests = sum(row.request_count for row in cost_stats)
            
            # Calculate cost per optimization (approximation)
            cost_per_optimization = (total_cost / total_requests) if total_requests > 0 else 0
            tokens_per_dollar = (total_tokens / total_cost) if total_cost > 0 else 0
            
            result = {
                "summary": {
                    "total_cost_usd": round(total_cost, 4),
                    "total_tokens": total_tokens,
                    "total_requests": total_requests,
                    "avg_cost_per_optimization": round(cost_per_optimization, 4),
                    "tokens_per_dollar": round(tokens_per_dollar, 0),
                    "daily_average_cost": round(total_cost / max((end_date - start_date).days, 1), 4)
                },
                "by_model_and_type": [
                    {
                        "ai_model": row.ai_model,
                        "content_type": row.content_type,
                        "request_count": row.request_count,
                        "total_cost_usd": round(row.total_cost_usd, 4),
                        "avg_cost_per_request": round(row.avg_cost_per_request, 4),
                        "total_tokens": row.total_tokens,
                        "avg_tokens_per_request": round(row.avg_tokens_per_request, 0),
                        "cost_efficiency": round(row.total_tokens / row.total_cost_usd, 0) if row.total_cost_usd > 0 else 0
                    }
                    for row in cost_stats
                ],
                "daily_trends": [
                    {
                        "date": row.date.isoformat(),
                        "cost_usd": round(row.daily_cost, 4),
                        "tokens": row.daily_tokens,
                        "requests": row.daily_requests,
                        "cost_per_request": round(row.daily_cost / row.daily_requests, 4) if row.daily_requests > 0 else 0
                    }
                    for row in daily_trends
                ]
            }
        
        await redis.setex(cache_key, self.cache_ttl, json.dumps(result, default=str))
        return result
    
    # =============================================================================
    # WORDPRESS INTEGRATION ANALYTICS
    # =============================================================================
    
    async def get_wordpress_integration_metrics(self, filters: AnalyticsFilter) -> Dict[str, Any]:
        """Get WordPress integration performance and health metrics."""
        start_date, end_date = await self._get_time_range_filter(filters)
        
        async with get_db_session() as session:
            # WordPress sync statistics
            wp_query = await session.execute(text("""
                SELECT 
                    store_id,
                    sync_type,
                    COUNT(*) as sync_count,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_syncs,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_syncs,
                    AVG(EXTRACT(EPOCH FROM (completed_at - started_at))) as avg_sync_duration_seconds
                FROM wordpress_sync_logs
                WHERE created_at BETWEEN :start_date AND :end_date
                    AND (:store_ids IS NULL OR store_id = ANY(:store_ids))
                GROUP BY store_id, sync_type
            """), {
                "start_date": start_date,
                "end_date": end_date,
                "store_ids": filters.store_ids
            })
            
            wp_stats = wp_query.fetchall()
            
            result = {
                "summary": {
                    "total_syncs": sum(row.sync_count for row in wp_stats),
                    "successful_syncs": sum(row.successful_syncs for row in wp_stats),
                    "failed_syncs": sum(row.failed_syncs for row in wp_stats),
                    "success_rate_percent": 0,
                    "avg_sync_duration_minutes": 0
                },
                "by_store_and_type": []
            }
            
            if wp_stats:
                total_syncs = result["summary"]["total_syncs"]
                successful_syncs = result["summary"]["successful_syncs"]
                
                result["summary"]["success_rate_percent"] = round(
                    (successful_syncs / total_syncs * 100) if total_syncs > 0 else 0, 2
                )
                
                avg_duration = np.mean([
                    row.avg_sync_duration_seconds for row in wp_stats 
                    if row.avg_sync_duration_seconds is not None
                ])
                result["summary"]["avg_sync_duration_minutes"] = round(avg_duration / 60, 2) if avg_duration else 0
                
                result["by_store_and_type"] = [
                    {
                        "store_id": row.store_id,
                        "sync_type": row.sync_type,
                        "sync_count": row.sync_count,
                        "success_rate": round(row.successful_syncs / row.sync_count * 100, 2),
                        "avg_duration_minutes": round((row.avg_sync_duration_seconds or 0) / 60, 2)
                    }
                    for row in wp_stats
                ]
            
        return result
    
    # =============================================================================
    # COMPREHENSIVE REPORTING
    # =============================================================================
    
    async def generate_comprehensive_report(self, filters: AnalyticsFilter) -> AnalyticsReport:
        """Generate comprehensive analytics report with insights and recommendations."""
        logger.info(f"Generating comprehensive analytics report for {filters.time_range}")
        
        # Gather all metrics concurrently
        optimization_metrics, seo_metrics, cost_metrics, wp_metrics = await asyncio.gather(
            self.get_optimization_metrics(filters),
            self.get_seo_performance_metrics(filters),
            self.get_cost_analytics(filters),
            self.get_wordpress_integration_metrics(filters)
        )
        
        # Generate insights based on data
        insights = self._generate_insights(optimization_metrics, seo_metrics, cost_metrics, wp_metrics)
        recommendations = self._generate_recommendations(optimization_metrics, seo_metrics, cost_metrics, wp_metrics)
        
        # Create metric summaries
        metrics = {
            "total_optimizations": MetricSummary(
                current_value=optimization_metrics["summary"]["total_optimizations"],
                trend="up" if optimization_metrics["summary"]["total_optimizations"] > 0 else "stable"
            ),
            "success_rate": MetricSummary(
                current_value=optimization_metrics["summary"]["success_rate"],
                trend="up" if optimization_metrics["summary"]["success_rate"] > 80 else "down"
            ),
            "seo_improvement": MetricSummary(
                current_value=seo_metrics["summary"]["avg_seo_score_improvement"],
                trend="up" if seo_metrics["summary"]["avg_seo_score_improvement"] > 0 else "stable"
            ),
            "total_cost": MetricSummary(
                current_value=cost_metrics["summary"]["total_cost_usd"],
                trend="stable"
            ),
            "cost_efficiency": MetricSummary(
                current_value=cost_metrics["summary"]["tokens_per_dollar"],
                trend="up" if cost_metrics["summary"]["tokens_per_dollar"] > 1000 else "stable"
            )
        }
        
        # Convert time series data
        time_series = {
            "optimizations": [
                TimeSeriesPoint(
                    timestamp=datetime.fromisoformat(point["timestamp"]),
                    value=point["optimization_count"]
                )
                for point in optimization_metrics.get("time_series", [])
            ],
            "costs": [
                TimeSeriesPoint(
                    timestamp=datetime.fromisoformat(point["date"]),
                    value=point["cost_usd"]
                )
                for point in cost_metrics.get("daily_trends", [])
            ]
        }
        
        return AnalyticsReport(
            title=f"GridSpoke Analytics Report - {filters.time_range.value.upper()}",
            description=f"Comprehensive SEO optimization performance report",
            time_range=filters.time_range,
            generated_at=datetime.utcnow(),
            metrics=metrics,
            time_series=time_series,
            insights=insights,
            recommendations=recommendations
        )
    
    def _generate_insights(self, optimization_metrics, seo_metrics, cost_metrics, wp_metrics) -> List[str]:
        """Generate data-driven insights from analytics."""
        insights = []
        
        # Optimization performance insights
        success_rate = optimization_metrics["summary"]["success_rate"]
        if success_rate > 95:
            insights.append(f"Excellent optimization success rate of {success_rate}% indicates stable AI processing pipeline.")
        elif success_rate < 80:
            insights.append(f"Optimization success rate of {success_rate}% is below target. Consider investigating error patterns.")
        
        # SEO improvement insights
        improvement_rate = seo_metrics["summary"]["improvement_rate_percent"]
        if improvement_rate > 70:
            insights.append(f"Strong SEO improvement rate of {improvement_rate}% shows effective optimization algorithms.")
        elif improvement_rate < 50:
            insights.append(f"SEO improvement rate of {improvement_rate}% suggests room for AI prompt optimization.")
        
        # Cost efficiency insights
        cost_per_opt = cost_metrics["summary"]["avg_cost_per_optimization"]
        if cost_per_opt < 0.05:
            insights.append(f"Excellent cost efficiency at ${cost_per_opt:.4f} per optimization.")
        elif cost_per_opt > 0.20:
            insights.append(f"High cost per optimization (${cost_per_opt:.4f}) may require model optimization.")
        
        # WordPress integration insights
        wp_success_rate = wp_metrics["summary"]["success_rate_percent"]
        if wp_success_rate < 90:
            insights.append(f"WordPress sync success rate of {wp_success_rate}% indicates integration issues.")
        
        return insights
    
    def _generate_recommendations(self, optimization_metrics, seo_metrics, cost_metrics, wp_metrics) -> List[str]:
        """Generate actionable recommendations based on analytics."""
        recommendations = []
        
        # Performance recommendations
        if optimization_metrics["summary"]["success_rate"] < 85:
            recommendations.append("Implement additional error handling and retry logic for failed optimizations.")
        
        if optimization_metrics["summary"]["avg_processing_time_minutes"] > 5:
            recommendations.append("Consider optimizing AI model selection or prompt complexity to reduce processing time.")
        
        # Cost optimization recommendations
        tokens_per_dollar = cost_metrics["summary"]["tokens_per_dollar"]
        if tokens_per_dollar < 500:
            recommendations.append("Evaluate switching to more cost-effective AI models for simple optimization tasks.")
        
        daily_cost = cost_metrics["summary"]["daily_average_cost"]
        if daily_cost > 50:
            recommendations.append("Implement smart batching and caching strategies to reduce API costs.")
        
        # SEO improvement recommendations
        if seo_metrics["summary"]["improvement_rate_percent"] < 60:
            recommendations.append("A/B test different AI prompts to improve SEO optimization effectiveness.")
        
        if seo_metrics["summary"]["avg_keywords_per_product"] < 5:
            recommendations.append("Enhance keyword generation algorithms to include more relevant long-tail keywords.")
        
        # WordPress integration recommendations
        if wp_metrics["summary"]["success_rate_percent"] < 95:
            recommendations.append("Investigate WordPress webhook reliability and implement better error recovery.")
        
        return recommendations

# Global analytics instance
analytics_engine = GridSpokeAnalytics()

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

async def get_dashboard_metrics(time_range: TimeRange = TimeRange.LAST_24H, 
                               store_id: Optional[str] = None) -> Dict[str, Any]:
    """Get key metrics for dashboard display."""
    filters = AnalyticsFilter(
        time_range=time_range,
        store_ids=[store_id] if store_id else None
    )
    
    # Get essential metrics quickly
    optimization_metrics, cost_metrics = await asyncio.gather(
        analytics_engine.get_optimization_metrics(filters),
        analytics_engine.get_cost_analytics(filters)
    )
    
    return {
        "optimizations_today": optimization_metrics["summary"]["total_optimizations"],
        "success_rate": optimization_metrics["summary"]["success_rate"],
        "cost_today": cost_metrics["summary"]["total_cost_usd"],
        "active_stores": optimization_metrics["summary"]["active_stores"],
        "avg_processing_time": optimization_metrics["summary"]["avg_processing_time_minutes"]
    }

async def track_user_activity(user_id: str, action: str, metadata: Dict[str, Any] = None):
    """Track user activity for engagement analytics."""
    redis = await analytics_engine._get_redis()
    
    activity_data = {
        "user_id": user_id,
        "action": action,
        "timestamp": datetime.utcnow().isoformat(),
        "metadata": metadata or {}
    }
    
    # Store in Redis for real-time analytics
    await redis.lpush("user_activity", json.dumps(activity_data))
    await redis.expire("user_activity", 86400 * 7)  # Keep for 7 days

# Export main components
__all__ = [
    'TimeRange',
    'MetricType', 
    'AnalyticsFilter',
    'AnalyticsReport',
    'analytics_engine',
    'get_dashboard_metrics',
    'track_user_activity'
]
