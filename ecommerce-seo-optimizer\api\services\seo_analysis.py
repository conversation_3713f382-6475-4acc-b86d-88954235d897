"""
SEO Analysis Service - Advanced SEO Analysis and Optimization
Provides comprehensive SEO analysis, scoring, and optimization recommendations.
"""

import re
import math
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from urllib.parse import urlparse
import asyncio

from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from ..models.product import Product
from ..schemas.product import ProductResponse
from ..agents.seo_optimizer import SEOOptimizer
from ..core.config import settings


class SEOScoreComponent(BaseModel):
    """Individual SEO score component"""
    name: str
    score: float = Field(..., ge=0, le=100)
    weight: float = Field(..., ge=0, le=1)
    issues: List[str] = []
    recommendations: List[str] = []


class SEOAnalysisResult(BaseModel):
    """Complete SEO analysis result"""
    overall_score: float = Field(..., ge=0, le=100)
    components: List[SEOScoreComponent]
    critical_issues: List[str] = []
    priority_recommendations: List[str] = []
    keyword_analysis: Dict[str, Any] = {}
    content_analysis: Dict[str, Any] = {}
    technical_analysis: Dict[str, Any] = {}


class KeywordDensity(BaseModel):
    """Keyword density analysis"""
    keyword: str
    count: int
    density: float
    relevance_score: float


class ContentAnalysisResult(BaseModel):
    """Content analysis result"""
    word_count: int
    readability_score: float
    keyword_densities: List[KeywordDensity]
    heading_structure: Dict[str, int]
    content_quality_score: float


class TechnicalSEOResult(BaseModel):
    """Technical SEO analysis result"""
    title_optimization: Dict[str, Any]
    meta_description_optimization: Dict[str, Any]
    url_optimization: Dict[str, Any]
    image_optimization: Dict[str, Any]
    schema_markup_score: float


class CanonicalURLAnalysis(BaseModel):
    """Canonical URL analysis"""
    has_canonical: bool
    canonical_url: Optional[str] = None
    issues: List[str] = []
    recommendations: List[str] = []


class SEOAnalysisService:
    """Main SEO analysis service"""
    
    def __init__(self):
        self.seo_optimizer = SEOOptimizer()
        
        # SEO scoring weights
        self.score_weights = {
            "title": 0.20,
            "meta_description": 0.15,
            "content_quality": 0.25,
            "keyword_optimization": 0.20,
            "technical_seo": 0.15,
            "schema_markup": 0.05
        }
        
    async def analyze_product_seo(
        self,
        db: AsyncSession,
        product: ProductResponse,
        target_keywords: List[str],
        store_url: Optional[str] = None
    ) -> SEOAnalysisResult:
        """Perform comprehensive SEO analysis on a product"""
        
        components = []
        
        # 1. Title optimization analysis
        title_component = await self._analyze_title(product, target_keywords)
        components.append(title_component)
        
        # 2. Meta description analysis
        meta_component = await self._analyze_meta_description(product, target_keywords)
        components.append(meta_component)
        
        # 3. Content quality analysis
        content_component = await self._analyze_content_quality(product, target_keywords)
        components.append(content_component)
        
        # 4. Keyword optimization analysis
        keyword_component = await self._analyze_keyword_optimization(product, target_keywords)
        components.append(keyword_component)
        
        # 5. Technical SEO analysis
        technical_component = await self._analyze_technical_seo(product, store_url)
        components.append(technical_component)
        
        # 6. Schema markup analysis
        schema_component = await self._analyze_schema_markup(product)
        components.append(schema_component)
        
        # Calculate overall score
        overall_score = self._calculate_overall_score(components)
        
        # Generate priority recommendations
        priority_recommendations = self._generate_priority_recommendations(components)
        
        # Extract critical issues
        critical_issues = self._extract_critical_issues(components)
        
        # Detailed analyses
        keyword_analysis = await self._detailed_keyword_analysis(product, target_keywords)
        content_analysis = await self._detailed_content_analysis(product)
        technical_analysis = await self._detailed_technical_analysis(product)
        
        return SEOAnalysisResult(
            overall_score=overall_score,
            components=components,
            critical_issues=critical_issues,
            priority_recommendations=priority_recommendations,
            keyword_analysis=keyword_analysis,
            content_analysis=content_analysis,
            technical_analysis=technical_analysis
        )
        
    async def _analyze_title(
        self,
        product: ProductResponse,
        target_keywords: List[str]
    ) -> SEOScoreComponent:
        """Analyze product title for SEO optimization"""
        issues = []
        recommendations = []
        score = 100.0
        
        title = product.title or ""
        
        # Check title length
        if len(title) == 0:
            issues.append("Missing product title")
            score -= 50
        elif len(title) < 30:
            issues.append("Title too short (< 30 characters)")
            score -= 20
        elif len(title) > 60:
            issues.append("Title too long (> 60 characters)")
            score -= 15
            
        # Check keyword inclusion
        if target_keywords:
            keywords_found = 0
            for keyword in target_keywords:
                if keyword.lower() in title.lower():
                    keywords_found += 1
                    
            if keywords_found == 0:
                issues.append("No target keywords found in title")
                score -= 30
            elif keywords_found < len(target_keywords) * 0.5:
                issues.append("Low keyword coverage in title")
                score -= 15
                
        # Check for duplicate words
        words = title.lower().split()
        if len(words) != len(set(words)):
            issues.append("Duplicate words in title")
            score -= 10
            
        # Check for keyword stuffing
        if len(words) > 0:
            word_frequency = {}
            for word in words:
                word_frequency[word] = word_frequency.get(word, 0) + 1
            max_frequency = max(word_frequency.values())
            if max_frequency > 3:
                issues.append("Potential keyword stuffing detected")
                score -= 20
                
        # Generate recommendations
        if len(title) < 30:
            recommendations.append("Expand title to 30-60 characters for better SEO")
        if len(title) > 60:
            recommendations.append("Shorten title to under 60 characters")
        if target_keywords and keywords_found == 0:
            recommendations.append(f"Include target keywords: {', '.join(target_keywords[:3])}")
            
        return SEOScoreComponent(
            name="Title Optimization",
            score=max(0, score),
            weight=self.score_weights["title"],
            issues=issues,
            recommendations=recommendations
        )
        
    async def _analyze_meta_description(
        self,
        product: ProductResponse,
        target_keywords: List[str]
    ) -> SEOScoreComponent:
        """Analyze meta description for SEO optimization"""
        issues = []
        recommendations = []
        score = 100.0
        
        meta_desc = product.meta_description or ""
        
        # Check meta description length
        if len(meta_desc) == 0:
            issues.append("Missing meta description")
            score -= 40
        elif len(meta_desc) < 120:
            issues.append("Meta description too short (< 120 characters)")
            score -= 20
        elif len(meta_desc) > 160:
            issues.append("Meta description too long (> 160 characters)")
            score -= 15
            
        # Check keyword inclusion
        if target_keywords and meta_desc:
            keywords_found = 0
            for keyword in target_keywords:
                if keyword.lower() in meta_desc.lower():
                    keywords_found += 1
                    
            if keywords_found == 0:
                issues.append("No target keywords found in meta description")
                score -= 25
                
        # Check for call-to-action
        cta_words = ["buy", "shop", "order", "get", "find", "discover", "explore", "learn"]
        has_cta = any(word in meta_desc.lower() for word in cta_words)
        if not has_cta:
            issues.append("Missing call-to-action in meta description")
            score -= 10
            
        # Generate recommendations
        if len(meta_desc) == 0:
            recommendations.append("Add compelling meta description (120-160 characters)")
        if len(meta_desc) < 120:
            recommendations.append("Expand meta description to 120-160 characters")
        if len(meta_desc) > 160:
            recommendations.append("Shorten meta description to under 160 characters")
        if not has_cta:
            recommendations.append("Include call-to-action words like 'shop', 'buy', 'discover'")
            
        return SEOScoreComponent(
            name="Meta Description",
            score=max(0, score),
            weight=self.score_weights["meta_description"],
            issues=issues,
            recommendations=recommendations
        )
        
    async def _analyze_content_quality(
        self,
        product: ProductResponse,
        target_keywords: List[str]
    ) -> SEOScoreComponent:
        """Analyze content quality for SEO"""
        issues = []
        recommendations = []
        score = 100.0
        
        description = product.description or ""
        
        # Check content length
        word_count = len(description.split())
        if word_count == 0:
            issues.append("Missing product description")
            score -= 50
        elif word_count < 50:
            issues.append("Content too short (< 50 words)")
            score -= 30
        elif word_count < 100:
            issues.append("Content could be more detailed (< 100 words)")
            score -= 15
            
        # Check keyword density
        if target_keywords and description:
            total_words = len(description.split())
            for keyword in target_keywords:
                keyword_count = description.lower().count(keyword.lower())
                if total_words > 0:
                    density = (keyword_count / total_words) * 100
                    
                    if density == 0:
                        issues.append(f"Keyword '{keyword}' not found in content")
                        score -= 10
                    elif density > 3:
                        issues.append(f"Keyword '{keyword}' density too high ({density:.1f}%)")
                        score -= 15
                        
        # Check for duplicate content (simple check)
        sentences = description.split('.')
        unique_sentences = set(sentence.strip().lower() for sentence in sentences if sentence.strip())
        if len(sentences) > 1 and len(unique_sentences) < len(sentences) * 0.8:
            issues.append("Potential duplicate content detected")
            score -= 20
            
        # Generate recommendations
        if word_count < 100:
            recommendations.append("Expand product description to 100+ words")
        if target_keywords:
            recommendations.append("Naturally incorporate target keywords throughout content")
        recommendations.append("Focus on benefits, features, and unique selling points")
        
        return SEOScoreComponent(
            name="Content Quality",
            score=max(0, score),
            weight=self.score_weights["content_quality"],
            issues=issues,
            recommendations=recommendations
        )
        
    async def _analyze_keyword_optimization(
        self,
        product: ProductResponse,
        target_keywords: List[str]
    ) -> SEOScoreComponent:
        """Analyze keyword optimization"""
        issues = []
        recommendations = []
        score = 100.0
        
        if not target_keywords:
            issues.append("No target keywords defined")
            score -= 30
            return SEOScoreComponent(
                name="Keyword Optimization",
                score=max(0, score),
                weight=self.score_weights["keyword_optimization"],
                issues=issues,
                recommendations=["Define target keywords for optimization"]
            )
            
        # Combine all text content
        all_content = " ".join([
            product.title or "",
            product.description or "",
            product.meta_description or "",
            product.category or ""
        ]).lower()
        
        # Analyze each keyword
        keyword_scores = []
        for keyword in target_keywords:
            keyword_score = 100.0
            
            # Check presence in different locations
            in_title = keyword.lower() in (product.title or "").lower()
            in_description = keyword.lower() in (product.description or "").lower()
            in_meta = keyword.lower() in (product.meta_description or "").lower()
            
            # Scoring based on placement
            if not in_title:
                keyword_score -= 30
                issues.append(f"Keyword '{keyword}' missing from title")
            if not in_description:
                keyword_score -= 20
                issues.append(f"Keyword '{keyword}' missing from description")
            if not in_meta:
                keyword_score -= 15
                issues.append(f"Keyword '{keyword}' missing from meta description")
                
            keyword_scores.append(max(0, keyword_score))
            
        # Average keyword score
        if keyword_scores:
            score = sum(keyword_scores) / len(keyword_scores)
            
        # Generate recommendations
        recommendations.append("Include primary keywords in title, description, and meta tags")
        recommendations.append("Use long-tail keyword variations naturally")
        recommendations.append("Focus on user intent and semantic keywords")
        
        return SEOScoreComponent(
            name="Keyword Optimization", 
            score=max(0, score),
            weight=self.score_weights["keyword_optimization"],
            issues=issues,
            recommendations=recommendations
        )
        
    async def _analyze_technical_seo(
        self,
        product: ProductResponse,
        store_url: Optional[str]
    ) -> SEOScoreComponent:
        """Analyze technical SEO factors"""
        issues = []
        recommendations = []
        score = 100.0
        
        # Check URL structure
        if hasattr(product, 'slug') and product.slug:
            slug = product.slug
            if len(slug) > 50:
                issues.append("URL slug too long")
                score -= 10
            if not re.match(r'^[a-z0-9-]+$', slug):
                issues.append("URL slug contains invalid characters")
                score -= 15
        else:
            issues.append("Missing URL slug")
            score -= 20
            
        # Check image optimization
        if hasattr(product, 'images') and product.images:
            for i, image_url in enumerate(product.images[:5]):  # Check first 5 images
                if not image_url:
                    continue
                    
                # Check for alt text (would need image metadata)
                # For now, assume missing alt text
                issues.append(f"Image {i+1} may be missing alt text")
                score -= 5
                
                # Check file format
                if not any(ext in image_url.lower() for ext in ['.jpg', '.jpeg', '.png', '.webp']):
                    issues.append(f"Image {i+1} may not be optimized format")
                    score -= 5
        else:
            issues.append("No product images found")
            score -= 30
            
        # Check for missing structured data
        # This would be checked against actual page implementation
        recommendations.append("Implement Schema.org Product markup")
        recommendations.append("Add breadcrumb navigation")
        recommendations.append("Optimize images with descriptive alt text")
        
        return SEOScoreComponent(
            name="Technical SEO",
            score=max(0, score),
            weight=self.score_weights["technical_seo"],
            issues=issues,
            recommendations=recommendations
        )
        
    async def _analyze_schema_markup(
        self,
        product: ProductResponse
    ) -> SEOScoreComponent:
        """Analyze schema markup implementation"""
        issues = []
        recommendations = []
        score = 0.0  # Start at 0, add points for implementation
        
        # Check for required product fields for schema
        required_fields = ["title", "description", "price", "currency"]
        missing_fields = []
        
        for field in required_fields:
            if not getattr(product, field, None):
                missing_fields.append(field)
                
        if missing_fields:
            issues.append(f"Missing required fields for schema: {', '.join(missing_fields)}")
        else:
            score += 50  # Base score for having required fields
            
        # Check for recommended fields
        recommended_fields = ["brand", "sku", "images", "category"]
        present_recommended = 0
        
        for field in recommended_fields:
            if getattr(product, field, None):
                present_recommended += 1
                score += 12.5  # 50 points total for recommended fields
                
        # Generate recommendations
        if missing_fields:
            recommendations.append(f"Add missing required fields: {', '.join(missing_fields)}")
        recommendations.append("Implement complete Product schema markup")
        recommendations.append("Add Review and Rating schema if available")
        recommendations.append("Include Offer schema with availability and shipping")
        
        return SEOScoreComponent(
            name="Schema Markup",
            score=min(100, score),
            weight=self.score_weights["schema_markup"],
            issues=issues,
            recommendations=recommendations
        )
        
    def _calculate_overall_score(self, components: List[SEOScoreComponent]) -> float:
        """Calculate weighted overall SEO score"""
        total_score = 0.0
        total_weight = 0.0
        
        for component in components:
            total_score += component.score * component.weight
            total_weight += component.weight
            
        return round(total_score / total_weight if total_weight > 0 else 0.0, 1)
        
    def _generate_priority_recommendations(
        self, 
        components: List[SEOScoreComponent]
    ) -> List[str]:
        """Generate priority recommendations based on impact"""
        all_recommendations = []
        
        # Weight recommendations by component score and impact
        for component in components:
            impact = (100 - component.score) * component.weight
            for rec in component.recommendations:
                all_recommendations.append((rec, impact))
                
        # Sort by impact and return top recommendations
        all_recommendations.sort(key=lambda x: x[1], reverse=True)
        return [rec[0] for rec in all_recommendations[:5]]
        
    def _extract_critical_issues(
        self,
        components: List[SEOScoreComponent]
    ) -> List[str]:
        """Extract critical issues that need immediate attention"""
        critical_issues = []
        
        for component in components:
            # Issues from components with low scores are critical
            if component.score < 50:
                critical_issues.extend(component.issues)
                
        return critical_issues[:5]  # Return top 5 critical issues
        
    async def _detailed_keyword_analysis(
        self,
        product: ProductResponse,
        target_keywords: List[str]
    ) -> Dict[str, Any]:
        """Perform detailed keyword analysis"""
        all_content = " ".join([
            product.title or "",
            product.description or "",
            product.meta_description or ""
        ])
        
        words = re.findall(r'\w+', all_content.lower())
        total_words = len(words)
        word_frequency = {}
        
        for word in words:
            word_frequency[word] = word_frequency.get(word, 0) + 1
            
        # Calculate keyword densities
        keyword_densities = []
        for keyword in target_keywords:
            count = all_content.lower().count(keyword.lower())
            density = (count / total_words * 100) if total_words > 0 else 0
            
            keyword_densities.append({
                "keyword": keyword,
                "count": count,
                "density": round(density, 2),
                "optimal_range": "1-3%"
            })
            
        return {
            "total_words": total_words,
            "unique_words": len(word_frequency),
            "keyword_densities": keyword_densities,
            "top_words": sorted(word_frequency.items(), key=lambda x: x[1], reverse=True)[:10]
        }
        
    async def _detailed_content_analysis(
        self,
        product: ProductResponse
    ) -> Dict[str, Any]:
        """Perform detailed content analysis"""
        description = product.description or ""
        
        # Basic metrics
        word_count = len(description.split())
        sentence_count = len([s for s in description.split('.') if s.strip()])
        avg_words_per_sentence = word_count / sentence_count if sentence_count > 0 else 0
        
        # Readability (simple approximation)
        # Flesch Reading Ease approximation
        avg_sentence_length = avg_words_per_sentence
        readability_score = 206.835 - (1.015 * avg_sentence_length)
        readability_score = max(0, min(100, readability_score))
        
        return {
            "word_count": word_count,
            "sentence_count": sentence_count,
            "avg_words_per_sentence": round(avg_words_per_sentence, 1),
            "readability_score": round(readability_score, 1),
            "readability_level": self._get_readability_level(readability_score)
        }
        
    async def _detailed_technical_analysis(
        self,
        product: ProductResponse
    ) -> Dict[str, Any]:
        """Perform detailed technical analysis"""
        return {
            "title": {
                "length": len(product.title or ""),
                "optimal_range": "30-60 characters",
                "status": self._get_length_status(len(product.title or ""), 30, 60)
            },
            "meta_description": {
                "length": len(product.meta_description or ""),
                "optimal_range": "120-160 characters", 
                "status": self._get_length_status(len(product.meta_description or ""), 120, 160)
            },
            "images": {
                "count": len(getattr(product, 'images', [])),
                "has_alt_text": False,  # Would need to check actual implementation
                "formats": ["jpg", "png"]  # Would need to analyze actual images
            }
        }
        
    def _get_readability_level(self, score: float) -> str:
        """Get readability level from Flesch score"""
        if score >= 90:
            return "Very Easy"
        elif score >= 80:
            return "Easy"
        elif score >= 70:
            return "Fairly Easy"
        elif score >= 60:
            return "Standard"
        elif score >= 50:
            return "Fairly Difficult"
        elif score >= 30:
            return "Difficult"
        else:
            return "Very Difficult"
            
    def _get_length_status(self, length: int, min_optimal: int, max_optimal: int) -> str:
        """Get status for content length"""
        if length == 0:
            return "Missing"
        elif length < min_optimal:
            return "Too Short"
        elif length > max_optimal:
            return "Too Long"
        else:
            return "Optimal"


class CanonicalURLService:
    """Service for managing canonical URLs"""
    
    @staticmethod
    def generate_canonical_url(
        store_url: str,
        product_id: str,
        product_slug: Optional[str] = None
    ) -> str:
        """Generate canonical URL for a product"""
        base_url = store_url.rstrip('/')
        
        if product_slug:
            return f"{base_url}/product/{product_slug}"
        else:
            return f"{base_url}/product/{product_id}"
            
    @staticmethod
    def analyze_canonical_url(
        current_url: str,
        canonical_url: str
    ) -> CanonicalURLAnalysis:
        """Analyze canonical URL setup"""
        issues = []
        recommendations = []
        
        # Parse URLs
        current_parsed = urlparse(current_url)
        canonical_parsed = urlparse(canonical_url)
        
        # Check protocol consistency
        if current_parsed.scheme != canonical_parsed.scheme:
            issues.append("Protocol mismatch between current and canonical URL")
            
        # Check domain consistency
        if current_parsed.netloc != canonical_parsed.netloc:
            issues.append("Domain mismatch between current and canonical URL")
            
        # Check for unnecessary parameters
        if current_parsed.query and not canonical_parsed.query:
            recommendations.append("Remove tracking parameters from canonical URL")
            
        return CanonicalURLAnalysis(
            has_canonical=True,
            canonical_url=canonical_url,
            issues=issues,
            recommendations=recommendations
        )
