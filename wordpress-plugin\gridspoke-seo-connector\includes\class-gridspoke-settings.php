<?php
/**
 * Settings management for GridSpoke SEO Connector
 * 
 * Handles plugin settings, validation, and default values.
 */

if (!defined('ABSPATH')) {
    exit;
}

class GridSpoke_Settings {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Settings key
     */
    const SETTINGS_KEY = 'gridspoke_seo_settings';
    
    /**
     * Default settings
     */
    private $default_settings = array(
        'api_endpoint' => 'http://localhost:8000/api/v1',
        'api_key' => '',
        'email' => '',
        'password' => '',
        'auto_sync' => false,
        'sync_interval' => 'daily',
        'webhook_secret' => '',
        'log_level' => 'info',
        'admin_notifications' => true,
        'optimization_fields' => array('title', 'description', 'meta_description'),
        'ai_model_preference' => 'claude-3-sonnet',
        'optimization_priority' => 'normal',
        'language' => 'en',
        'batch_size' => 50,
        'rate_limit_delay' => 1,
        'cleanup_logs_days' => 30
    );
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_init', array($this, 'register_settings'));
    }
    
    /**
     * Register plugin settings
     */
    public function register_settings() {
        register_setting(
            'gridspoke_seo_settings_group',
            self::SETTINGS_KEY,
            array(
                'type' => 'array',
                'sanitize_callback' => array($this, 'sanitize_settings'),
                'default' => $this->default_settings
            )
        );
        
        // API Settings Section
        add_settings_section(
            'gridspoke_api_settings',
            __('API Configuration', 'gridspoke-seo'),
            array($this, 'api_settings_section_callback'),
            'gridspoke_seo_settings'
        );
        
        add_settings_field(
            'api_endpoint',
            __('API Endpoint', 'gridspoke-seo'),
            array($this, 'api_endpoint_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_api_settings'
        );
        
        add_settings_field(
            'email',
            __('Email', 'gridspoke-seo'),
            array($this, 'email_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_api_settings'
        );

        add_settings_field(
            'password',
            __('Password', 'gridspoke-seo'),
            array($this, 'password_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_api_settings'
        );
        
        // Sync Settings Section
        add_settings_section(
            'gridspoke_sync_settings',
            __('Sync Configuration', 'gridspoke-seo'),
            array($this, 'sync_settings_section_callback'),
            'gridspoke_seo_settings'
        );
        
        add_settings_field(
            'auto_sync',
            __('Auto Sync', 'gridspoke-seo'),
            array($this, 'auto_sync_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_sync_settings'
        );
        
        add_settings_field(
            'sync_interval',
            __('Sync Interval', 'gridspoke-seo'),
            array($this, 'sync_interval_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_sync_settings'
        );
        
        // Optimization Settings Section
        add_settings_section(
            'gridspoke_optimization_settings',
            __('Optimization Configuration', 'gridspoke-seo'),
            array($this, 'optimization_settings_section_callback'),
            'gridspoke_seo_settings'
        );
        
        add_settings_field(
            'optimization_fields',
            __('Fields to Optimize', 'gridspoke-seo'),
            array($this, 'optimization_fields_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_optimization_settings'
        );
        
        add_settings_field(
            'ai_model_preference',
            __('AI Model Preference', 'gridspoke-seo'),
            array($this, 'ai_model_preference_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_optimization_settings'
        );
        
        add_settings_field(
            'optimization_priority',
            __('Optimization Priority', 'gridspoke-seo'),
            array($this, 'optimization_priority_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_optimization_settings'
        );
        
        add_settings_field(
            'language',
            __('Content Language', 'gridspoke-seo'),
            array($this, 'language_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_optimization_settings'
        );
        
        // Advanced Settings Section
        add_settings_section(
            'gridspoke_advanced_settings',
            __('Advanced Configuration', 'gridspoke-seo'),
            array($this, 'advanced_settings_section_callback'),
            'gridspoke_seo_settings'
        );
        
        add_settings_field(
            'admin_notifications',
            __('Admin Notifications', 'gridspoke-seo'),
            array($this, 'admin_notifications_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_advanced_settings'
        );
        
        add_settings_field(
            'log_level',
            __('Log Level', 'gridspoke-seo'),
            array($this, 'log_level_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_advanced_settings'
        );
        
        add_settings_field(
            'batch_size',
            __('Batch Size', 'gridspoke-seo'),
            array($this, 'batch_size_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_advanced_settings'
        );
        
        add_settings_field(
            'rate_limit_delay',
            __('Rate Limit Delay (seconds)', 'gridspoke-seo'),
            array($this, 'rate_limit_delay_field_callback'),
            'gridspoke_seo_settings',
            'gridspoke_advanced_settings'
        );
    }
    
    /**
     * Get settings with defaults
     */
    public function get_settings() {
        $settings = get_option(self::SETTINGS_KEY, array());
        return wp_parse_args($settings, $this->default_settings);
    }
    
    /**
     * Get specific setting
     */
    public function get_setting($key, $default = null) {
        $settings = $this->get_settings();
        return $settings[$key] ?? $default;
    }
    
    /**
     * Update settings
     */
    public function update_settings($new_settings) {
        $current_settings = $this->get_settings();
        $updated_settings = wp_parse_args($new_settings, $current_settings);
        return update_option(self::SETTINGS_KEY, $updated_settings);
    }
    
    /**
     * Update specific setting
     */
    public function update_setting($key, $value) {
        $settings = $this->get_settings();
        $settings[$key] = $value;
        return $this->update_settings($settings);
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        // API settings
        $sanitized['api_endpoint'] = esc_url_raw(trailingslashit($input['api_endpoint'] ?? $this->default_settings['api_endpoint']));
        $sanitized['api_key'] = sanitize_text_field($input['api_key'] ?? '');
        $sanitized['email'] = sanitize_email($input['email'] ?? '');
        $sanitized['password'] = $input['password'] ?? ''; // Don't sanitize password to preserve special chars
        
        // Sync settings
        $sanitized['auto_sync'] = !empty($input['auto_sync']);
        $sanitized['sync_interval'] = in_array($input['sync_interval'] ?? '', array('hourly', 'twicedaily', 'daily', 'weekly')) 
            ? $input['sync_interval'] 
            : $this->default_settings['sync_interval'];
        
        // Optimization settings
        $available_fields = array('title', 'description', 'short_description', 'meta_description', 'meta_title', 'image_alt');
        $sanitized['optimization_fields'] = array_intersect($input['optimization_fields'] ?? array(), $available_fields);
        
        if (empty($sanitized['optimization_fields'])) {
            $sanitized['optimization_fields'] = $this->default_settings['optimization_fields'];
        }
        
        $sanitized['ai_model_preference'] = in_array($input['ai_model_preference'] ?? '', array('auto', 'gpt-4', 'gpt-3.5-turbo', 'claude-3', 'claude-2'))
            ? $input['ai_model_preference']
            : $this->default_settings['ai_model_preference'];
        
        $sanitized['optimization_priority'] = in_array($input['optimization_priority'] ?? '', array('low', 'normal', 'high'))
            ? $input['optimization_priority']
            : $this->default_settings['optimization_priority'];
        
        $sanitized['language'] = sanitize_text_field($input['language'] ?? $this->default_settings['language']);
        
        // Advanced settings
        $sanitized['admin_notifications'] = !empty($input['admin_notifications']);
        
        $sanitized['log_level'] = in_array($input['log_level'] ?? '', array('debug', 'info', 'warning', 'error'))
            ? $input['log_level']
            : $this->default_settings['log_level'];
        
        $sanitized['batch_size'] = max(1, min(200, intval($input['batch_size'] ?? $this->default_settings['batch_size'])));
        $sanitized['rate_limit_delay'] = max(0, min(60, intval($input['rate_limit_delay'] ?? $this->default_settings['rate_limit_delay'])));
        $sanitized['cleanup_logs_days'] = max(1, min(365, intval($input['cleanup_logs_days'] ?? $this->default_settings['cleanup_logs_days'])));
        
        // Generate webhook secret if not exists
        if (empty($input['webhook_secret'])) {
            $sanitized['webhook_secret'] = wp_generate_password(32, false);
        } else {
            $sanitized['webhook_secret'] = sanitize_text_field($input['webhook_secret']);
        }
        
        return $sanitized;
    }
    
    /**
     * Reset settings to defaults
     */
    public function reset_settings() {
        delete_option(self::SETTINGS_KEY);
        return $this->update_settings($this->default_settings);
    }
    
    /**
     * Validate API configuration
     */
    public function validate_api_config() {
        $settings = $this->get_settings();
        $errors = array();
        
        // Check API endpoint
        if (empty($settings['api_endpoint'])) {
            $errors[] = __('API endpoint is required', 'gridspoke-seo');
        } elseif (!filter_var($settings['api_endpoint'], FILTER_VALIDATE_URL)) {
            $errors[] = __('API endpoint must be a valid URL', 'gridspoke-seo');
        }
        
        // Check API key
        if (empty($settings['api_key'])) {
            $errors[] = __('API key is required', 'gridspoke-seo');
        } elseif (strlen($settings['api_key']) !== 64 || !ctype_xdigit($settings['api_key'])) {
            $errors[] = __('API key must be a 64-character hexadecimal string', 'gridspoke-seo');
        }
        
        return $errors;
    }
    
    /**
     * Section callbacks
     */
    public function api_settings_section_callback() {
        echo '<p>' . esc_html__('Configure your GridSpoke API connection settings.', 'gridspoke-seo') . '</p>';
    }
    
    public function sync_settings_section_callback() {
        echo '<p>' . esc_html__('Configure how and when your products are synced with GridSpoke.', 'gridspoke-seo') . '</p>';
    }
    
    public function optimization_settings_section_callback() {
        echo '<p>' . esc_html__('Configure optimization preferences and content generation settings.', 'gridspoke-seo') . '</p>';
    }
    
    public function advanced_settings_section_callback() {
        echo '<p>' . esc_html__('Advanced configuration options for power users.', 'gridspoke-seo') . '</p>';
    }
    
    /**
     * Field callbacks
     */
    public function api_endpoint_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['api_endpoint'];
        
        echo '<input type="url" id="api_endpoint" name="' . self::SETTINGS_KEY . '[api_endpoint]" value="' . esc_attr($value) . '" class="regular-text" required />';
        echo '<p class="description">' . esc_html__('The GridSpoke API endpoint URL.', 'gridspoke-seo') . '</p>';
    }
    
    public function email_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['email'];

        echo '<input type="email" id="email" name="' . self::SETTINGS_KEY . '[email]" value="' . esc_attr($value) . '" class="regular-text" required />';
        echo '<p class="description">' . esc_html__('Your GridSpoke account email address.', 'gridspoke-seo') . '</p>';
    }

    public function password_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['password'];

        echo '<input type="password" id="password" name="' . self::SETTINGS_KEY . '[password]" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<button type="button" id="show-password" class="button">' . esc_html__('Show', 'gridspoke-seo') . '</button>';
        echo '<p class="description">' . esc_html__('Your GridSpoke account password. Keep this secure and never share it publicly.', 'gridspoke-seo') . '</p>';

        if (!empty($value)) {
            echo '<div class="gridspoke-auth-status">';
            echo '<button type="button" id="test-authentication" class="button button-secondary">';
            echo esc_html__('Test Authentication', 'gridspoke-seo');
            echo '</button>';
            echo '<span id="auth-status"></span>';
            echo '</div>';
        }
    }
    
    public function auto_sync_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['auto_sync'];
        
        echo '<label><input type="checkbox" id="auto_sync" name="' . self::SETTINGS_KEY . '[auto_sync]" value="1" ' . checked($value, true, false) . ' />';
        echo esc_html__('Automatically sync products when they are created or updated', 'gridspoke-seo') . '</label>';
    }
    
    public function sync_interval_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['sync_interval'];
        
        $intervals = array(
            'hourly' => __('Hourly', 'gridspoke-seo'),
            'twicedaily' => __('Twice Daily', 'gridspoke-seo'),
            'daily' => __('Daily', 'gridspoke-seo'),
            'weekly' => __('Weekly', 'gridspoke-seo')
        );
        
        echo '<select id="sync_interval" name="' . self::SETTINGS_KEY . '[sync_interval]">';
        foreach ($intervals as $key => $label) {
            echo '<option value="' . esc_attr($key) . '" ' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . esc_html__('How often to automatically sync all products (if auto-sync is enabled).', 'gridspoke-seo') . '</p>';
    }
    
    public function optimization_fields_field_callback() {
        $settings = $this->get_settings();
        $selected_fields = $settings['optimization_fields'];
        
        $available_fields = array(
            'title' => __('Product Title', 'gridspoke-seo'),
            'description' => __('Product Description', 'gridspoke-seo'),
            'short_description' => __('Short Description', 'gridspoke-seo'),
            'meta_description' => __('Meta Description', 'gridspoke-seo'),
            'meta_title' => __('Meta Title', 'gridspoke-seo'),
            'image_alt' => __('Image Alt Text', 'gridspoke-seo')
        );
        
        echo '<fieldset>';
        foreach ($available_fields as $key => $label) {
            $checked = in_array($key, $selected_fields);
            echo '<label><input type="checkbox" name="' . self::SETTINGS_KEY . '[optimization_fields][]" value="' . esc_attr($key) . '" ' . checked($checked, true, false) . ' />';
            echo esc_html($label) . '</label><br />';
        }
        echo '</fieldset>';
        echo '<p class="description">' . esc_html__('Select which fields should be optimized by GridSpoke AI.', 'gridspoke-seo') . '</p>';
    }
    
    public function ai_model_preference_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['ai_model_preference'];
        
        $models = array(
            'auto' => __('Auto (Let GridSpoke choose)', 'gridspoke-seo'),
            'gpt-4' => __('GPT-4 (Premium)', 'gridspoke-seo'),
            'gpt-3.5-turbo' => __('GPT-3.5 Turbo (Fast)', 'gridspoke-seo'),
            'claude-3' => __('Claude 3 (Premium)', 'gridspoke-seo'),
            'claude-2' => __('Claude 2 (Balanced)', 'gridspoke-seo')
        );
        
        echo '<select id="ai_model_preference" name="' . self::SETTINGS_KEY . '[ai_model_preference]">';
        foreach ($models as $key => $label) {
            echo '<option value="' . esc_attr($key) . '" ' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . esc_html__('Preferred AI model for content generation. Auto mode optimizes for cost and quality.', 'gridspoke-seo') . '</p>';
    }
    
    public function optimization_priority_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['optimization_priority'];
        
        $priorities = array(
            'low' => __('Low (Bulk processing)', 'gridspoke-seo'),
            'normal' => __('Normal (Standard queue)', 'gridspoke-seo'),
            'high' => __('High (Fast processing)', 'gridspoke-seo')
        );
        
        echo '<select id="optimization_priority" name="' . self::SETTINGS_KEY . '[optimization_priority]">';
        foreach ($priorities as $key => $label) {
            echo '<option value="' . esc_attr($key) . '" ' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . esc_html__('Processing priority for optimization requests. Higher priority may cost more.', 'gridspoke-seo') . '</p>';
    }
    
    public function language_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['language'];
        
        $languages = array(
            'en' => __('English', 'gridspoke-seo'),
            'es' => __('Spanish', 'gridspoke-seo'),
            'fr' => __('French', 'gridspoke-seo'),
            'de' => __('German', 'gridspoke-seo'),
            'it' => __('Italian', 'gridspoke-seo'),
            'pt' => __('Portuguese', 'gridspoke-seo'),
            'nl' => __('Dutch', 'gridspoke-seo'),
            'ja' => __('Japanese', 'gridspoke-seo'),
            'ko' => __('Korean', 'gridspoke-seo'),
            'zh' => __('Chinese', 'gridspoke-seo')
        );
        
        echo '<select id="language" name="' . self::SETTINGS_KEY . '[language]">';
        foreach ($languages as $key => $label) {
            echo '<option value="' . esc_attr($key) . '" ' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . esc_html__('Language for generated content.', 'gridspoke-seo') . '</p>';
    }
    
    public function admin_notifications_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['admin_notifications'];
        
        echo '<label><input type="checkbox" id="admin_notifications" name="' . self::SETTINGS_KEY . '[admin_notifications]" value="1" ' . checked($value, true, false) . ' />';
        echo esc_html__('Send email notifications to admin when optimizations complete', 'gridspoke-seo') . '</label>';
    }
    
    public function log_level_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['log_level'];
        
        $levels = array(
            'debug' => __('Debug (Verbose)', 'gridspoke-seo'),
            'info' => __('Info (Normal)', 'gridspoke-seo'),
            'warning' => __('Warning (Important)', 'gridspoke-seo'),
            'error' => __('Error (Critical only)', 'gridspoke-seo')
        );
        
        echo '<select id="log_level" name="' . self::SETTINGS_KEY . '[log_level]">';
        foreach ($levels as $key => $label) {
            echo '<option value="' . esc_attr($key) . '" ' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . esc_html__('Minimum log level to record. Debug mode may impact performance.', 'gridspoke-seo') . '</p>';
    }
    
    public function batch_size_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['batch_size'];
        
        echo '<input type="number" id="batch_size" name="' . self::SETTINGS_KEY . '[batch_size]" value="' . esc_attr($value) . '" min="1" max="200" class="small-text" />';
        echo '<p class="description">' . esc_html__('Number of products to process in each batch (1-200).', 'gridspoke-seo') . '</p>';
    }
    
    public function rate_limit_delay_field_callback() {
        $settings = $this->get_settings();
        $value = $settings['rate_limit_delay'];
        
        echo '<input type="number" id="rate_limit_delay" name="' . self::SETTINGS_KEY . '[rate_limit_delay]" value="' . esc_attr($value) . '" min="0" max="60" class="small-text" />';
        echo '<p class="description">' . esc_html__('Delay between API requests in seconds to avoid rate limits (0-60).', 'gridspoke-seo') . '</p>';
    }
}
