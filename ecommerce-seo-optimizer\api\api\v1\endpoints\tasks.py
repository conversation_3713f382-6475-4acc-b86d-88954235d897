"""
Task Management API Endpoints for GridSpoke Ecommerce SEO Optimizer
Provides REST API endpoints for managing Celery tasks and monitoring job progress.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query, Path
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import json

# Import Celery app and tasks
from workers.celery_app import celery_app
# from workers.tasks.product_tasks import (
#     optimize_single_product,
#     optimize_product_batch
# )
# from workers.tasks.validation_tasks import (
#     validate_products,
#     calculate_seo_score
# )
# from workers.tasks.content_tasks import (
#     generate_blog_post,
#     generate_product_faqs,
#     generate_buyers_guide,
#     generate_meta_descriptions
# )
# from workers.tasks.scheduled_tasks import (
#     daily_optimization_run,
#     weekly_seo_analysis,
#     check_store_updates,
#     monthly_analytics_report
# )
from workers.utils.progress_tracker import ProgressTracker
from workers.utils.redis_client import get_redis_client

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(prefix="/api/v1/tasks", tags=["tasks"])

# Initialize progress tracker
progress_tracker = ProgressTracker()

# Request/Response Models
class TaskStartRequest(BaseModel):
    """Request model for starting a task."""
    task_type: str = Field(..., description="Type of task to start")
    parameters: Dict[str, Any] = Field(..., description="Task parameters")
    priority: int = Field(default=5, ge=1, le=10, description="Task priority (1=lowest, 10=highest)")
    store_id: Optional[str] = Field(None, description="Store ID for store-specific tasks")

class TaskResponse(BaseModel):
    """Response model for task information."""
    task_id: str
    task_type: str
    status: str
    progress: Optional[Dict[str, Any]] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    priority: int
    queue: str

class TaskListResponse(BaseModel):
    """Response model for task list."""
    tasks: List[TaskResponse]
    total: int
    page: int
    page_size: int

class TaskProgressResponse(BaseModel):
    """Response model for task progress."""
    task_id: str
    progress: Dict[str, Any]
    status: str
    timestamp: datetime

class QueueStats(BaseModel):
    """Response model for queue statistics."""
    queue_name: str
    pending_tasks: int
    active_tasks: int
    failed_tasks: int
    completed_tasks: int

class SystemStats(BaseModel):
    """Response model for system statistics."""
    total_tasks: int
    pending_tasks: int
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    queues: List[QueueStats]
    workers: List[Dict[str, Any]]

# Task type mapping - temporarily disabled due to Mirascope dependency
TASK_MAPPING = {
    # 'optimize_single_product': optimize_single_product,
    # 'optimize_product_batch': optimize_product_batch,
    # 'validate_products': validate_products,
    # 'calculate_seo_score': calculate_seo_score,
    # 'generate_blog_post': generate_blog_post,
    # 'generate_product_faqs': generate_product_faqs,
    # 'generate_buyers_guide': generate_buyers_guide,
    # 'generate_meta_descriptions': generate_meta_descriptions,
    # 'daily_optimization_run': daily_optimization_run,
    # 'weekly_seo_analysis': weekly_seo_analysis,
    # 'check_store_updates': check_store_updates,
    # 'monthly_analytics_report': monthly_analytics_report,
}

# Queue priority mapping
QUEUE_PRIORITY_MAPPING = {
    1: 'low_priority',
    2: 'low_priority',
    3: 'low_priority',
    4: 'default',
    5: 'default',
    6: 'default',
    7: 'high_priority',
    8: 'high_priority',
    9: 'urgent',
    10: 'urgent'
}

def get_queue_for_priority(priority: int) -> str:
    """Get queue name based on priority level."""
    return QUEUE_PRIORITY_MAPPING.get(priority, 'default')

async def get_task_info(task_id: str) -> Optional[TaskResponse]:
    """
    Get comprehensive task information.
    
    Args:
        task_id: Task ID to query
        
    Returns:
        TaskResponse or None if task not found
    """
    try:
        # Get task result from Celery
        result = celery_app.AsyncResult(task_id)
        
        # Get progress information from Redis
        progress_info = await progress_tracker.get_task_progress(task_id)
        
        # Get task metadata from Redis
        redis_client = get_redis_client()
        task_metadata_key = f"task_metadata:{task_id}"
        metadata = redis_client.hgetall(task_metadata_key)
        
        if not metadata:
            return None
        
        # Convert timestamps
        created_at = datetime.fromisoformat(metadata.get('created_at', datetime.utcnow().isoformat()))
        started_at = None
        completed_at = None
        
        if metadata.get('started_at'):
            started_at = datetime.fromisoformat(metadata['started_at'])
        
        if metadata.get('completed_at'):
            completed_at = datetime.fromisoformat(metadata['completed_at'])
        
        # Get result data
        result_data = None
        error_data = None
        
        if result.ready():
            try:
                if result.successful():
                    result_data = result.result
                else:
                    error_data = str(result.info)
            except Exception as e:
                error_data = str(e)
        
        return TaskResponse(
            task_id=task_id,
            task_type=metadata.get('task_type', 'unknown'),
            status=result.status,
            progress=progress_info,
            result=result_data,
            error=error_data,
            created_at=created_at,
            started_at=started_at,
            completed_at=completed_at,
            priority=int(metadata.get('priority', 5)),
            queue=metadata.get('queue', 'default')
        )
        
    except Exception as e:
        logger.error(f"Error getting task info for {task_id}: {str(e)}")
        return None

@router.post("/start", response_model=TaskResponse)
async def start_task(request: TaskStartRequest):
    """
    Start a new task.
    
    Args:
        request: Task start request
        
    Returns:
        Task information
    """
    try:
        # Validate task type
        if request.task_type not in TASK_MAPPING:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid task type: {request.task_type}. Available types: {list(TASK_MAPPING.keys())}"
            )
        
        # Get task function
        task_func = TASK_MAPPING[request.task_type]
        
        # Determine queue based on priority
        queue = get_queue_for_priority(request.priority)
        
        # Start the task
        result = task_func.apply_async(
            kwargs=request.parameters,
            queue=queue,
            priority=request.priority
        )
        
        # Store task metadata in Redis
        redis_client = get_redis_client()
        task_metadata_key = f"task_metadata:{result.id}"
        metadata = {
            'task_type': request.task_type,
            'priority': request.priority,
            'queue': queue,
            'store_id': request.store_id or '',
            'created_at': datetime.utcnow().isoformat(),
            'parameters': json.dumps(request.parameters)
        }
        redis_client.hset(task_metadata_key, mapping=metadata)
        redis_client.expire(task_metadata_key, 86400 * 7)  # 7 days
        
        # Initialize progress tracking
        await progress_tracker.start_task_tracking(
            task_id=result.id,
            task_type=request.task_type,
            store_id=request.store_id
        )
        
        logger.info(f"Started task {result.id} of type {request.task_type} on queue {queue}")
        
        # Return task information
        task_info = await get_task_info(result.id)
        if task_info:
            return task_info
        else:
            # Fallback response
            return TaskResponse(
                task_id=result.id,
                task_type=request.task_type,
                status="PENDING",
                created_at=datetime.utcnow(),
                priority=request.priority,
                queue=queue
            )
            
    except Exception as e:
        logger.error(f"Error starting task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start task: {str(e)}")

@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(task_id: str = Path(..., description="Task ID")):
    """
    Get information about a specific task.
    
    Args:
        task_id: Task ID
        
    Returns:
        Task information
    """
    task_info = await get_task_info(task_id)
    
    if not task_info:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
    
    return task_info

@router.get("/{task_id}/progress", response_model=TaskProgressResponse)
async def get_task_progress(task_id: str = Path(..., description="Task ID")):
    """
    Get progress information for a specific task.
    
    Args:
        task_id: Task ID
        
    Returns:
        Task progress information
    """
    try:
        # Get progress from tracker
        progress_info = await progress_tracker.get_task_progress(task_id)
        
        if not progress_info:
            raise HTTPException(status_code=404, detail=f"No progress information found for task {task_id}")
        
        # Get task status
        result = celery_app.AsyncResult(task_id)
        
        return TaskProgressResponse(
            task_id=task_id,
            progress=progress_info,
            status=result.status,
            timestamp=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task progress: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get task progress: {str(e)}")

@router.post("/{task_id}/cancel")
async def cancel_task(task_id: str = Path(..., description="Task ID")):
    """
    Cancel a specific task.
    
    Args:
        task_id: Task ID to cancel
        
    Returns:
        Cancellation confirmation
    """
    try:
        # Revoke the task
        celery_app.control.revoke(task_id, terminate=True)
        
        # Update progress tracker
        await progress_tracker.update_task_status(task_id, "CANCELLED")
        
        logger.info(f"Cancelled task {task_id}")
        
        return {"message": f"Task {task_id} has been cancelled", "task_id": task_id}
        
    except Exception as e:
        logger.error(f"Error cancelling task {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel task: {str(e)}")

@router.get("/", response_model=TaskListResponse)
async def list_tasks(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    status: Optional[str] = Query(None, description="Filter by task status"),
    task_type: Optional[str] = Query(None, description="Filter by task type"),
    store_id: Optional[str] = Query(None, description="Filter by store ID")
):
    """
    List tasks with optional filtering and pagination.
    
    Args:
        page: Page number
        page_size: Number of tasks per page
        status: Optional status filter
        task_type: Optional task type filter
        store_id: Optional store ID filter
        
    Returns:
        Paginated list of tasks
    """
    try:
        redis_client = get_redis_client()
        
        # Get all task metadata keys
        pattern = "task_metadata:*"
        all_keys = redis_client.keys(pattern)
        
        tasks = []
        
        for key in all_keys:
            task_id = key.split(':')[1]
            
            # Get task metadata
            metadata = redis_client.hgetall(key)
            if not metadata:
                continue
            
            # Apply filters
            if status and metadata.get('status') != status:
                continue
            
            if task_type and metadata.get('task_type') != task_type:
                continue
            
            if store_id and metadata.get('store_id') != store_id:
                continue
            
            # Get task info
            task_info = await get_task_info(task_id)
            if task_info:
                tasks.append(task_info)
        
        # Sort by creation time (newest first)
        tasks.sort(key=lambda x: x.created_at, reverse=True)
        
        # Pagination
        total = len(tasks)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_tasks = tasks[start_idx:end_idx]
        
        return TaskListResponse(
            tasks=paginated_tasks,
            total=total,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        logger.error(f"Error listing tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list tasks: {str(e)}")

@router.get("/stats/system", response_model=SystemStats)
async def get_system_stats():
    """
    Get system-wide task statistics.
    
    Returns:
        System statistics including queue and worker information
    """
    try:
        # Get basic stats from Celery inspect
        inspect = celery_app.control.inspect()
        
        # Get active tasks
        active_tasks = inspect.active()
        total_active = sum(len(tasks) for tasks in active_tasks.values()) if active_tasks else 0
        
        # Get reserved tasks (pending)
        reserved_tasks = inspect.reserved()
        total_reserved = sum(len(tasks) for tasks in reserved_tasks.values()) if reserved_tasks else 0
        
        # Get worker stats
        worker_stats = inspect.stats()
        workers = []
        
        if worker_stats:
            for worker_name, stats in worker_stats.items():
                workers.append({
                    'name': worker_name,
                    'status': 'online',
                    'pool': stats.get('pool', {}),
                    'total_tasks': stats.get('total', {})
                })
        
        # Get queue statistics from Redis
        redis_client = get_redis_client()
        queue_stats = []
        
        queue_names = ['urgent', 'high_priority', 'default', 'low_priority']
        
        for queue_name in queue_names:
            # Get queue length (pending tasks)
            pending = redis_client.llen(f"celery:{queue_name}")
            
            # Get task counts from metadata (approximation)
            pattern = f"task_metadata:*"
            all_keys = redis_client.keys(pattern)
            
            queue_tasks = {'pending': 0, 'active': 0, 'completed': 0, 'failed': 0}
            
            for key in all_keys:
                metadata = redis_client.hgetall(key)
                if metadata.get('queue') == queue_name:
                    task_id = key.split(':')[1]
                    result = celery_app.AsyncResult(task_id)
                    
                    if result.status == 'PENDING':
                        queue_tasks['pending'] += 1
                    elif result.status in ['STARTED', 'RETRY']:
                        queue_tasks['active'] += 1
                    elif result.status == 'SUCCESS':
                        queue_tasks['completed'] += 1
                    elif result.status in ['FAILURE', 'REVOKED']:
                        queue_tasks['failed'] += 1
            
            queue_stats.append(QueueStats(
                queue_name=queue_name,
                pending_tasks=queue_tasks['pending'],
                active_tasks=queue_tasks['active'],
                failed_tasks=queue_tasks['failed'],
                completed_tasks=queue_tasks['completed']
            ))
        
        # Calculate totals
        total_pending = sum(q.pending_tasks for q in queue_stats)
        total_completed = sum(q.completed_tasks for q in queue_stats)
        total_failed = sum(q.failed_tasks for q in queue_stats)
        total_tasks = total_active + total_pending + total_completed + total_failed
        
        return SystemStats(
            total_tasks=total_tasks,
            pending_tasks=total_pending,
            active_tasks=total_active,
            completed_tasks=total_completed,
            failed_tasks=total_failed,
            queues=queue_stats,
            workers=workers
        )
        
    except Exception as e:
        logger.error(f"Error getting system stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get system stats: {str(e)}")

@router.post("/cleanup/completed")
async def cleanup_completed_tasks(
    older_than_days: int = Query(7, ge=1, le=30, description="Clean up tasks older than N days")
):
    """
    Clean up completed task metadata and results.
    
    Args:
        older_than_days: Remove completed tasks older than this many days
        
    Returns:
        Cleanup summary
    """
    try:
        redis_client = get_redis_client()
        cutoff_date = datetime.utcnow() - timedelta(days=older_than_days)
        
        # Get all task metadata keys
        pattern = "task_metadata:*"
        all_keys = redis_client.keys(pattern)
        
        cleaned_count = 0
        
        for key in all_keys:
            metadata = redis_client.hgetall(key)
            if not metadata:
                continue
            
            # Check if task is old enough
            created_at = datetime.fromisoformat(metadata.get('created_at', datetime.utcnow().isoformat()))
            
            if created_at < cutoff_date:
                task_id = key.split(':')[1]
                result = celery_app.AsyncResult(task_id)
                
                # Only clean up completed/failed tasks
                if result.status in ['SUCCESS', 'FAILURE', 'REVOKED']:
                    # Remove metadata
                    redis_client.delete(key)
                    
                    # Remove progress tracking
                    progress_key = f"task_progress:{task_id}"
                    redis_client.delete(progress_key)
                    
                    # Remove result if stored in Redis
                    result_key = f"celery-task-meta-{task_id}"
                    redis_client.delete(result_key)
                    
                    cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} completed tasks older than {older_than_days} days")
        
        return {
            "message": f"Successfully cleaned up {cleaned_count} completed tasks",
            "cleaned_count": cleaned_count,
            "cutoff_date": cutoff_date.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clean up tasks: {str(e)}")

@router.get("/types/available")
async def get_available_task_types():
    """
    Get list of available task types.
    
    Returns:
        List of available task types with descriptions
    """
    task_types = {
        'optimize_single_product': {
            'description': 'Optimize a single product with AI-generated content',
            'parameters': ['product_id', 'store_id', 'optimization_settings'],
            'queue_preference': 'default'
        },
        'optimize_product_batch': {
            'description': 'Optimize multiple products in a batch',
            'parameters': ['product_ids', 'store_id', 'optimization_settings'],
            'queue_preference': 'high_priority'
        },
        'validate_products': {
            'description': 'Validate product data and check for optimization opportunities',
            'parameters': ['product_ids', 'store_id'],
            'queue_preference': 'low_priority'
        },
        'calculate_seo_score': {
            'description': 'Calculate SEO score for products',
            'parameters': ['product_ids', 'store_id'],
            'queue_preference': 'default'
        },
        'generate_blog_post': {
            'description': 'Generate AI blog post content',
            'parameters': ['topic', 'store_id', 'target_keywords', 'word_count'],
            'queue_preference': 'default'
        },
        'generate_product_faqs': {
            'description': 'Generate FAQ content for products',
            'parameters': ['product_ids', 'store_id'],
            'queue_preference': 'default'
        },
        'generate_buyers_guide': {
            'description': 'Generate buyer\'s guide content',
            'parameters': ['category', 'store_id', 'products'],
            'queue_preference': 'default'
        },
        'generate_meta_descriptions': {
            'description': 'Generate meta descriptions for products',
            'parameters': ['product_ids', 'store_id'],
            'queue_preference': 'low_priority'
        },
        'daily_optimization_run': {
            'description': 'Scheduled daily optimization job',
            'parameters': ['store_id'],
            'queue_preference': 'high_priority'
        },
        'weekly_seo_analysis': {
            'description': 'Weekly SEO analysis and reporting',
            'parameters': ['store_id'],
            'queue_preference': 'default'
        },
        'check_store_updates': {
            'description': 'Check for store updates and sync changes',
            'parameters': ['store_id'],
            'queue_preference': 'low_priority'
        },
        'monthly_analytics_report': {
            'description': 'Generate monthly analytics report',
            'parameters': ['store_id', 'month', 'year'],
            'queue_preference': 'low_priority'
        }
    }
    
    return {
        'available_task_types': task_types,
        'total_types': len(task_types)
    }
