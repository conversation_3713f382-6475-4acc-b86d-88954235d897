"""
Store management endpoints.
"""
import uuid
from typing import Any, List
from urllib.parse import urlsplit
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.database import get_db_session
from core.security import get_current_user
from models.user import User as UserModel
from models.store import Store as StoreModel, StoreStatus, StorePlatform
from schemas.store import StoreCreate, Store, StoreUpdate
import structlog

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=list[Store])
async def get_stores(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get stores owned by the current user."""
    stmt = (
        select(StoreModel)
        .where(StoreModel.owner_id == current_user.id)
        .offset(skip)
        .limit(limit)
    )
    result = await db.execute(stmt)
    stores = result.scalars().all()
    return stores


@router.post("/", response_model=Store, status_code=status.HTTP_201_CREATED)
async def create_store(
    store_in: StoreCreate,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Create a new store.

    The incoming schema uses `store_url`; we extract domain portion for persistence.
    """
    # Derive normalized domain from URL (host + optional non-standard port, no path)
    store_url = str(store_in.store_url)
    parts = urlsplit(store_url)
    host = parts.hostname or ""
    port = parts.port
    # Include port only if present and non-standard for scheme
    if port and not ((parts.scheme == "http" and port == 80) or (parts.scheme == "https" and port == 443)):
        domain = f"{host}:{port}"
    else:
        domain = host
    # Basic duplicate domain check
    existing = await db.execute(select(StoreModel).where(StoreModel.domain == domain))
    if existing.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="Store domain already exists")

    db_obj = StoreModel(
        name=store_in.name,
        domain=domain,
        platform=StorePlatform(store_in.platform.value),  # align enums
        status=StoreStatus.PENDING,
        owner_id=current_user.id,
        api_credentials=store_in.api_credentials or {},
        settings=None,
    )
    db.add(db_obj)
    await db.commit()
    await db.refresh(db_obj)
    logger.info("Store created", store_id=str(db_obj.id), owner=str(current_user.id))
    return db_obj


@router.get("/{store_id}", response_model=Store)
async def get_store(
    store_id: uuid.UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get store by ID (must belong to user)."""
    result = await db.execute(select(StoreModel).where(StoreModel.id == store_id))
    store = result.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Store not found")
    return store

@router.patch("/{store_id}", response_model=Store)
async def update_store(
    store_id: uuid.UUID,
    store_in: StoreUpdate,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Update store metadata."""
    result = await db.execute(select(StoreModel).where(StoreModel.id == store_id))
    store = result.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Store not found")
    data = store_in.model_dump(exclude_unset=True)
    # Handle potential name / platform changes only; ignore store_url updates for now
    if "name" in data:
        store.name = data["name"]
    if "platform" in data and data["platform"] is not None:
        store.platform = StorePlatform(data["platform"].value)
    if "is_active" in data and data["is_active"] is not None:
        store.is_active = data["is_active"]
    if "api_credentials" in data and data["api_credentials"] is not None:
        store.api_credentials = data["api_credentials"]
    await db.commit()
    await db.refresh(store)
    return store
