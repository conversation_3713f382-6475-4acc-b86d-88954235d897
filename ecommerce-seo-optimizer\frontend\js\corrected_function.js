        validateCustomAiModel(modelName) {\n            // Safety check: Ensure the model name follows OpenRouter format (provider/model-name)\n            // and doesn't contain potentially dangerous characters\n            if (!modelName || typeof modelName !== 'string') {\n                return false;\n            }\n            \n            // Check length\n            if (modelName.length > 100) {\n                return false;\n            }\n            \n            // Check for valid format: provider/model-name\n            const modelRegex = /^[a-zA-Z0-9\\-_]+\/[a-zA-Z0-9\\-_.]+$/;\n            if (!modelRegex.test(modelName)) {\n                return false;\n            }\n            \n            // Check for potentially dangerous patterns\n            const dangerousPatterns = [\n                '..', '<', '>', '\"', \"'\", '`', '$', ';', '&', '|', \n                '(', ')', '[', ']', '{', '}', '\n', '\r', '\t'\n            ];\n            \n            for (const pattern of dangerousPatterns) {\n                if (modelName.includes(pattern)) {\n                    return false;\n                }\n            }\n            \n            // Check that it's not trying to access local files or system resources\n            const forbiddenPrefixes = ['file://', 'http://', 'https://', '/', '\\\\'];\n            for (const prefix of forbiddenPrefixes) {\n                if (modelName.toLowerCase().startsWith(prefix)) {\n                    return false;\n                }\n            }\n            \n            return true;\n        },