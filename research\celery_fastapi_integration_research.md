# Celery with FastAPI Integration Research
## GridSpoke Ecommerce SEO Optimizer Project - Phase 4

*Research Date: August 11, 2025*  
*Project Context: AI-powered ecommerce SEO optimization service*

---

## Research Overview

This research focuses on integrating Celery task queue with FastAPI for the GridSpoke ecommerce SEO optimizer, enabling asynchronous processing of thousands of products through AI-powered optimization workflows.

### Key Research Sources
- **TestDriven.io**: [Asynchronous Tasks with FastAPI and Celery](https://testdriven.io/blog/fastapi-and-celery/)
- **Celery Docs**: [Periodic Tasks](https://docs.celeryq.dev/en/stable/userguide/periodic-tasks.html)
- **Celery Docs**: [Django Integration](https://docs.celeryq.dev/en/stable/django/first-steps-with-django.html)
- **GitHub Examples**: [Celery Examples Repository](https://github.com/celery/celery/tree/main/examples)

---

## Core Architecture Patterns for GridSpoke

### 1. Task Organization Strategy

**Recommended Structure for GridSpoke:**
```
workers/
├── celery_app.py              # Main Celery application
├── celeryconfig.py            # Configuration settings
├── tasks/
│   ├── __init__.py
│   ├── product_tasks.py       # Product optimization tasks
│   ├── content_tasks.py       # Content generation tasks
│   ├── scheduled_tasks.py     # Celery Beat scheduled jobs
│   └── bulk_tasks.py          # Batch processing workflows
└── monitoring/
    ├── __init__.py
    └── progress_tracker.py    # Progress tracking utilities
```

### 2. Task Categories for Ecommerce SEO

Based on the GridSpoke requirements, tasks should be organized into:

1. **Product Optimization Tasks** (`product_tasks.py`):
   - Individual product SEO optimization
   - Batch product processing (1000+ products)
   - Product data validation and preprocessing
   - SEO score calculation and analytics

2. **Content Generation Tasks** (`content_tasks.py`):
   - Blog post generation
   - FAQ creation
   - Product description enhancement
   - Meta tag optimization

3. **Scheduled Tasks** (`scheduled_tasks.py`):
   - Daily optimization runs (2 AM schedule)
   - Weekly SEO performance reports
   - Monthly analytics aggregation
   - Store data synchronization

4. **Integration Tasks** (`integration_tasks.py`):
   - WordPress plugin sync
   - WooCommerce/SureCart data import
   - Third-party API integrations

---

## Implementation Patterns

### 1. Celery Application Setup

**`workers/celery_app.py`**:
```python
import os
from celery import Celery
from kombu import Queue

# Initialize Celery app for GridSpoke
celery_app = Celery('gridspoke_optimizer')

# Configure broker and backend (Redis)
celery_app.conf.broker_url = os.environ.get(
    "CELERY_BROKER_URL", 
    "redis://localhost:6379/0"
)
celery_app.conf.result_backend = os.environ.get(
    "CELERY_RESULT_BACKEND", 
    "redis://localhost:6379/0"
)

# Task routing for different queues
celery_app.conf.task_routes = {
    'workers.tasks.product_tasks.*': {'queue': 'product_optimization'},
    'workers.tasks.content_tasks.*': {'queue': 'content_generation'},
    'workers.tasks.scheduled_tasks.*': {'queue': 'scheduled_jobs'},
    'workers.tasks.bulk_tasks.*': {'queue': 'bulk_processing'},
}

# Queue definitions with priority
celery_app.conf.task_queues = (
    Queue('product_optimization', routing_key='product'),
    Queue('content_generation', routing_key='content'),
    Queue('scheduled_jobs', routing_key='scheduled'),
    Queue('bulk_processing', routing_key='bulk'),
)

# Auto-discover tasks from task modules
celery_app.autodiscover_tasks([
    'workers.tasks.product_tasks',
    'workers.tasks.content_tasks', 
    'workers.tasks.scheduled_tasks',
    'workers.tasks.bulk_tasks',
])
```

### 2. Progress Tracking with Redis

**Key Pattern for GridSpoke Dashboard**:
```python
from celery import current_task
import redis

def track_progress(current, total, status_message="Processing"):
    """Track task progress for real-time dashboard updates"""
    if current_task:
        progress = {
            'current': current,
            'total': total,
            'percentage': int((current / total) * 100),
            'status': status_message
        }
        current_task.update_state(
            state='PROGRESS',
            meta=progress
        )
        
        # Store in Redis for WebSocket broadcasting
        redis_client = redis.Redis.from_url(
            os.environ.get('REDIS_URL', 'redis://localhost:6379')
        )
        redis_client.set(
            f"task_progress:{current_task.request.id}",
            json.dumps(progress),
            ex=3600  # Expire after 1 hour
        )
```

### 3. Task Chains for Complex Workflows

**Ecommerce SEO Workflow Example**:
```python
from celery import chain, group, chord

# Complex optimization workflow
def optimize_store_products(store_id: str, product_ids: List[str]):
    """Chain tasks for complete store optimization"""
    
    # Step 1: Validate and preprocess products
    validation_job = validate_products.s(store_id, product_ids)
    
    # Step 2: Parallel optimization of product batches
    optimization_jobs = group(
        optimize_product_batch.s(store_id, batch) 
        for batch in chunk_list(product_ids, 50)
    )
    
    # Step 3: Generate analytics and reports
    reporting_job = generate_optimization_report.s(store_id)
    
    # Step 4: Sync back to WordPress/WooCommerce
    sync_job = sync_optimized_products.s(store_id)
    
    # Execute as a chain
    workflow = chain(
        validation_job,
        chord(optimization_jobs, reporting_job),
        sync_job
    )
    
    return workflow.apply_async()
```

---

## Celery Beat Scheduling for GridSpoke

### 1. Periodic Task Configuration

**`workers/celeryconfig.py`**:
```python
from celery.schedules import crontab
from datetime import timedelta

# Celery Beat schedule for GridSpoke automation
beat_schedule = {
    # Daily optimization run at 2 AM (off-peak hours)
    'daily-product-optimization': {
        'task': 'workers.tasks.scheduled_tasks.daily_optimization_run',
        'schedule': crontab(hour=2, minute=0),
        'options': {'queue': 'scheduled_jobs'}
    },
    
    # Weekly SEO performance analysis
    'weekly-seo-analysis': {
        'task': 'workers.tasks.scheduled_tasks.weekly_seo_analysis',
        'schedule': crontab(day_of_week=1, hour=3, minute=0),  # Monday 3 AM
        'options': {'queue': 'scheduled_jobs'}
    },
    
    # Hourly store data sync check
    'hourly-store-sync': {
        'task': 'workers.tasks.scheduled_tasks.check_store_updates',
        'schedule': crontab(minute=0),  # Every hour
        'options': {'queue': 'scheduled_jobs'}
    },
    
    # Monthly analytics aggregation
    'monthly-analytics': {
        'task': 'workers.tasks.scheduled_tasks.monthly_analytics_report',
        'schedule': crontab(day_of_month=1, hour=4, minute=0),  # 1st of month, 4 AM
        'options': {'queue': 'scheduled_jobs'}
    },
    
    # Clean up old task results (daily)
    'cleanup-old-results': {
        'task': 'workers.tasks.scheduled_tasks.cleanup_old_task_results',
        'schedule': crontab(hour=1, minute=30),
        'options': {'queue': 'scheduled_jobs'}
    }
}

# Timezone configuration
timezone = 'UTC'

# Task result expiration
result_expires = 3600  # 1 hour

# Task time limits
task_time_limit = 30 * 60  # 30 minutes hard limit
task_soft_time_limit = 25 * 60  # 25 minutes soft limit
```

### 2. Smart Scheduling Strategies

**Cost Optimization Through Scheduling**:
```python
# Different schedules based on OpenRouter API pricing
PEAK_HOURS = range(9, 18)  # 9 AM - 6 PM UTC
OFF_PEAK_HOURS = [2, 3, 4, 5]  # 2 AM - 5 AM UTC

def get_optimal_schedule_time():
    """Determine optimal time for AI processing based on API costs"""
    current_hour = datetime.utcnow().hour
    
    if current_hour in OFF_PEAK_HOURS:
        # Immediate processing during off-peak
        return 0
    else:
        # Schedule for next off-peak window
        return calculate_delay_to_off_peak()
```

---

## FastAPI Integration Patterns

### 1. Task Endpoint Architecture

**`api/api/v1/endpoints/tasks.py`**:
```python
from fastapi import APIRouter, HTTPException, Depends
from celery.result import AsyncResult
from typing import Dict, Any

router = APIRouter()

@router.post("/optimize/store/{store_id}", status_code=202)
async def optimize_store(
    store_id: str,
    options: OptimizationOptions,
    current_user: User = Depends(get_current_user)
):
    """Trigger store-wide product optimization"""
    
    # Validate store access
    store = await get_store_by_id(store_id)
    if not store or store.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="Store not found")
    
    # Start optimization task
    task = optimize_store_products.delay(
        store_id=store_id,
        options=options.dict(),
        user_id=current_user.id
    )
    
    return {
        "task_id": task.id,
        "status": "queued",
        "message": "Store optimization started"
    }

@router.get("/tasks/{task_id}/status")
async def get_task_status(task_id: str):
    """Get real-time task status and progress"""
    
    result = AsyncResult(task_id)
    
    if result.state == 'PENDING':
        response = {
            'task_id': task_id,
            'state': result.state,
            'current': 0,
            'total': 1,
            'status': 'Task is waiting to be processed'
        }
    elif result.state == 'PROGRESS':
        response = {
            'task_id': task_id,
            'state': result.state,
            'current': result.info.get('current', 0),
            'total': result.info.get('total', 1),
            'percentage': result.info.get('percentage', 0),
            'status': result.info.get('status', 'Processing...')
        }
    elif result.state == 'SUCCESS':
        response = {
            'task_id': task_id,
            'state': result.state,
            'result': result.result
        }
    else:  # FAILURE or other states
        response = {
            'task_id': task_id,
            'state': result.state,
            'error': str(result.info)
        }
    
    return response
```

### 2. WebSocket Integration for Real-time Updates

**`api/websocket/task_manager.py`**:
```python
from fastapi import WebSocket
import asyncio
import json

class TaskWebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        
    async def connect(self, websocket: WebSocket, task_id: str):
        await websocket.accept()
        self.active_connections[task_id] = websocket
        
    def disconnect(self, task_id: str):
        if task_id in self.active_connections:
            del self.active_connections[task_id]
            
    async def send_task_update(self, task_id: str, data: dict):
        if task_id in self.active_connections:
            try:
                await self.active_connections[task_id].send_text(
                    json.dumps(data)
                )
            except Exception:
                # Connection closed, clean up
                self.disconnect(task_id)

task_manager = TaskWebSocketManager()

@router.websocket("/ws/tasks/{task_id}")
async def websocket_task_updates(websocket: WebSocket, task_id: str):
    await task_manager.connect(websocket, task_id)
    try:
        # Send initial status
        result = AsyncResult(task_id)
        await task_manager.send_task_update(task_id, {
            'type': 'status_update',
            'task_id': task_id,
            'state': result.state
        })
        
        # Keep connection alive and listen for updates
        while True:
            await asyncio.sleep(1)
            # Check for Redis progress updates and broadcast
            
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        task_manager.disconnect(task_id)
```

---

## Error Handling and Retry Strategies

### 1. Task Retry Configuration

```python
from celery.exceptions import Retry

@celery_app.task(
    bind=True,
    autoretry_for=(OpenRouterAPIError, RedisConnectionError),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    retry_backoff=True,
    retry_jitter=True
)
def optimize_product_with_ai(self, product_id: str, store_id: str):
    """Optimize product with automatic retry logic"""
    try:
        # AI optimization logic
        result = ai_service.optimize_product(product_id)
        return result
        
    except RateLimitError as exc:
        # Handle rate limiting with exponential backoff
        countdown = min(300, (2 ** self.request.retries) * 60)
        raise self.retry(countdown=countdown, exc=exc)
        
    except OpenRouterAPIError as exc:
        # Log error and retry
        logger.error(f"OpenRouter API error for product {product_id}: {exc}")
        raise self.retry(exc=exc)
```

### 2. Circuit Breaker Pattern

```python
from circuitbreaker import circuit

@circuit(failure_threshold=5, recovery_timeout=300)
def call_openrouter_api(prompt: str, model: str):
    """API call with circuit breaker protection"""
    # OpenRouter API call logic
    pass

@celery_app.task(bind=True)
def safe_ai_optimization(self, product_data: dict):
    """AI optimization with circuit breaker"""
    try:
        result = call_openrouter_api(
            prompt=generate_optimization_prompt(product_data),
            model="anthropic/claude-3-opus"
        )
        return result
    except Exception as exc:
        # Circuit breaker is open, use fallback
        return fallback_optimization(product_data)
```

---

## Performance Optimization Strategies

### 1. Task Prioritization

```python
# High priority for individual user requests
optimize_single_product.apply_async(
    args=[product_id], 
    priority=9,
    queue='product_optimization'
)

# Lower priority for bulk background processing
optimize_bulk_products.apply_async(
    args=[product_ids], 
    priority=3,
    queue='bulk_processing'
)
```

### 2. Resource Management

```python
# Limit concurrent AI API calls to respect rate limits
@celery_app.task(
    bind=True,
    rate_limit='10/m',  # 10 per minute
    acks_late=True
)
def ai_optimization_task(self, product_data):
    """Rate-limited AI optimization task"""
    # Implementation
    pass

# Worker configuration in docker-compose.yml
worker:
  command: celery -A workers.celery_app worker --loglevel=info --concurrency=4
  environment:
    - CELERY_WORKER_PREFETCH_MULTIPLIER=1  # Prevent task hoarding
```

---

## Testing Strategies

### 1. Unit Testing Tasks

```python
import pytest
from unittest.mock import patch, MagicMock

def test_product_optimization_task():
    """Test product optimization task logic"""
    
    # Mock AI service
    with patch('workers.tasks.product_tasks.ai_service') as mock_ai:
        mock_ai.optimize_product.return_value = {
            'title': 'Optimized Title',
            'description': 'Optimized Description'
        }
        
        # Test task execution
        result = optimize_product.run(
            product_id='test-123',
            store_id='store-456'
        )
        
        assert result['title'] == 'Optimized Title'
        mock_ai.optimize_product.assert_called_once()

def test_task_retry_logic():
    """Test task retry behavior"""
    
    with patch('workers.tasks.product_tasks.ai_service') as mock_ai:
        # Simulate API failure
        mock_ai.optimize_product.side_effect = OpenRouterAPIError("API Down")
        
        with pytest.raises(Retry):
            optimize_product.run(product_id='test-123')
```

### 2. Integration Testing

```python
@pytest.mark.integration
def test_task_workflow_integration():
    """Test complete task workflow"""
    
    # Start optimization workflow
    result = optimize_store_products.delay(
        store_id='test-store',
        product_ids=['p1', 'p2', 'p3']
    )
    
    # Wait for completion
    final_result = result.get(timeout=300)
    
    # Verify results
    assert final_result['optimized_count'] == 3
    assert final_result['status'] == 'completed'
```

---

## Docker Configuration

### 1. Docker Compose Setup

```yaml
# docker-compose.yml additions for Celery
services:
  # Existing services...
  
  celery-worker:
    build: ./workers
    command: celery -A workers.celery_app worker --loglevel=info --concurrency=4
    volumes:
      - ./workers:/usr/src/app
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - redis
      - db

  celery-beat:
    build: ./workers
    command: celery -A workers.celery_app beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    volumes:
      - ./workers:/usr/src/app
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - redis
      - db

  flower:
    build: ./workers
    command: celery -A workers.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
```

### 2. Worker Dockerfile

```dockerfile
# workers/Dockerfile
FROM python:3.11-slim

WORKDIR /usr/src/app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["celery", "-A", "workers.celery_app", "worker", "--loglevel=info"]
```

---

## Monitoring and Observability

### 1. Flower Dashboard Integration

- **URL**: http://localhost:5555
- **Features**: Real-time worker monitoring, task history, broker statistics
- **GridSpoke Integration**: Custom views for ecommerce metrics

### 2. Custom Metrics Collection

```python
from prometheus_client import Counter, Histogram, Gauge

# Metrics for GridSpoke optimization
PRODUCTS_OPTIMIZED = Counter(
    'gridspoke_products_optimized_total',
    'Total products optimized',
    ['store_id', 'optimization_type']
)

OPTIMIZATION_DURATION = Histogram(
    'gridspoke_optimization_duration_seconds',
    'Time spent optimizing products',
    ['optimization_type']
)

ACTIVE_OPTIMIZATIONS = Gauge(
    'gridspoke_active_optimizations',
    'Currently running optimizations'
)
```

---

## Security Considerations

### 1. Task Authentication

```python
@celery_app.task(bind=True)
def secure_optimization_task(self, product_id: str, user_id: str, api_key_hash: str):
    """Secure task with user authentication"""
    
    # Verify API key
    if not verify_api_key_hash(api_key_hash, user_id):
        raise SecurityError("Invalid API key")
    
    # Verify user permissions
    if not user_has_access_to_product(user_id, product_id):
        raise PermissionError("User lacks access to product")
    
    # Proceed with optimization
    return perform_optimization(product_id)
```

### 2. Data Sanitization

```python
from pydantic import BaseModel, validator

class OptimizationTaskPayload(BaseModel):
    product_id: str
    store_id: str
    options: Dict[str, Any]
    
    @validator('product_id')
    def validate_product_id(cls, v):
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Invalid product ID format')
        return v
    
    @validator('options')
    def sanitize_options(cls, v):
        # Remove any potentially dangerous keys
        dangerous_keys = ['__', 'eval', 'exec', 'import']
        return {k: v for k, v in v.items() if not any(dk in str(k).lower() for dk in dangerous_keys)}
```

---

## Deployment Recommendations

### 1. Production Configuration

```python
# workers/celeryconfig.py (production)
import os

# Use environment-specific settings
if os.environ.get('ENVIRONMENT') == 'production':
    # Production Redis cluster
    broker_url = 'redis-cluster://redis-cluster:6379/0'
    result_backend = 'redis-cluster://redis-cluster:6379/0'
    
    # Stricter limits
    task_time_limit = 1800  # 30 minutes
    task_soft_time_limit = 1500  # 25 minutes
    
    # Enable task compression
    task_compression = 'gzip'
    result_compression = 'gzip'
    
    # Worker optimizations
    worker_prefetch_multiplier = 1
    worker_max_tasks_per_child = 1000
```

### 2. Scaling Strategy

```bash
# Scale workers based on queue length
docker-compose up --scale celery-worker=4

# Different worker types for different queues
docker-compose run -d --name ai-worker celery-worker celery -A workers.celery_app worker --queues=product_optimization,content_generation --concurrency=2

docker-compose run -d --name bulk-worker celery-worker celery -A workers.celery_app worker --queues=bulk_processing --concurrency=8
```

---

## Implementation Checklist for GridSpoke

### Phase 4.1: Basic Task Queue Setup
- [ ] Create `workers/` directory structure
- [ ] Implement `celery_app.py` with Redis configuration
- [ ] Create basic product optimization tasks
- [ ] Set up Docker Compose with Celery worker and beat
- [ ] Implement progress tracking with Redis

### Phase 4.2: Task Organization
- [ ] Create task modules: `product_tasks.py`, `content_tasks.py`, `scheduled_tasks.py`
- [ ] Implement task chains for complex workflows
- [ ] Add error handling and retry logic
- [ ] Create task priority queues

### Phase 4.3: Scheduling and Automation
- [ ] Configure Celery Beat with `celeryconfig.py`
- [ ] Implement scheduled optimization jobs (daily 2 AM)
- [ ] Add periodic analytics and reporting tasks
- [ ] Create cleanup and maintenance tasks

### Phase 4.4: FastAPI Integration
- [ ] Create task management endpoints in `api/api/v1/endpoints/tasks.py`
- [ ] Implement WebSocket manager for real-time updates
- [ ] Add task status tracking and progress reporting
- [ ] Create admin endpoints for task monitoring

### Phase 4.5: Monitoring and Production
- [ ] Set up Flower dashboard
- [ ] Implement custom metrics collection
- [ ] Add comprehensive logging
- [ ] Create testing suite for tasks
- [ ] Configure production deployment settings

---

## Next Steps

After implementing the Celery task queue system, the GridSpoke project will be ready for:

1. **Phase 5**: Frontend Dashboard - Real-time task monitoring and job management
2. **Phase 6**: WordPress Plugin - Triggering optimization jobs from WordPress admin
3. **Phase 7**: Advanced Features - A/B testing, analytics, and reporting
4. **Phase 8**: Monitoring - Full observability and alerting system

The Celery integration provides the foundation for scalable, asynchronous processing of thousands of ecommerce products while maintaining responsive user experience and cost-effective AI API usage.
