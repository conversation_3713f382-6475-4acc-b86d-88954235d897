"""
Redis client utility for GridSpoke.
Provides a singleton Redis client for various services.
"""
import logging
from typing import Optional
import os

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis library not available")

logger = logging.getLogger(__name__)

class RedisClientManager:
    """Manages Redis client connections."""
    
    _instance = None
    _client = None
    
    def __new__(cls):
        """Singleton pattern implementation."""
        if cls._instance is None:
            cls._instance = super(RedisClientManager, cls).__new__(cls)
        return cls._instance
    
    def get_client(self) -> Optional:
        """
        Get Redis client instance.
        
        Returns:
            Redis client or None if not available
        """
        if not REDIS_AVAILABLE:
            return None
            
        if self._client is None:
            try:
                # Get Redis configuration from environment
                redis_url = os.getenv('REDIS_URL')
                
                if redis_url:
                    # Parse Redis URL if provided
                    from urllib.parse import urlparse
                    parsed = urlparse(redis_url)
                    redis_host = parsed.hostname or 'localhost'
                    redis_port = parsed.port or 6379
                    redis_db = int(parsed.path.lstrip('/')) if parsed.path else 0
                    redis_password = parsed.password
                    
                    # Create Redis client using URL
                    self._client = redis.from_url(
                        redis_url,
                        decode_responses=True,
                        socket_connect_timeout=5,
                        socket_timeout=5,
                        retry_on_timeout=True
                    )
                else:
                    # Fall back to individual environment variables
                    redis_host = os.getenv('REDIS_HOST', 'localhost')
                    redis_port = int(os.getenv('REDIS_PORT', 6379))
                    redis_db = int(os.getenv('REDIS_DB', 0))
                    redis_password = os.getenv('REDIS_PASSWORD')
                    
                    # Create Redis client
                    self._client = redis.Redis(
                        host=redis_host,
                        port=redis_port,
                        db=redis_db,
                        password=redis_password,
                        decode_responses=True,
                        socket_connect_timeout=5,
                        socket_timeout=5,
                        retry_on_timeout=True
                    )
                
                # Test connection
                self._client.ping()
                logger.info(f"Connected to Redis at {redis_host}:{redis_port}")
                
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                self._client = None
        
        return self._client

# Global function to get Redis client
def get_redis_client() -> Optional:
    """
    Get Redis client instance.
    
    Returns:
        Redis client or None if not available
    """
    manager = RedisClientManager()
    return manager.get_client()

# Convenience functions for common Redis operations
def set_with_ttl(key: str, value: str, ttl_seconds: int) -> bool:
    """
    Set a key with TTL in Redis.
    
    Args:
        key: Key to set
        value: Value to set
        ttl_seconds: TTL in seconds
        
    Returns:
        True if successful, False otherwise
    """
    client = get_redis_client()
    if client:
        try:
            client.setex(key, ttl_seconds, value)
            return True
        except Exception as e:
            logger.error(f"Failed to set key {key} in Redis: {e}")
    return False

def get_key(key: str) -> Optional[str]:
    """
    Get a key from Redis.
    
    Args:
        key: Key to get
        
    Returns:
        Value or None if not found or error
    """
    client = get_redis_client()
    if client:
        try:
            return client.get(key)
        except Exception as e:
            logger.error(f"Failed to get key {key} from Redis: {e}")
    return None

def delete_key(key: str) -> bool:
    """
    Delete a key from Redis.
    
    Args:
        key: Key to delete
        
    Returns:
        True if successful, False otherwise
    """
    client = get_redis_client()
    if client:
        try:
            client.delete(key)
            return True
        except Exception as e:
            logger.error(f"Failed to delete key {key} from Redis: {e}")
    return False

def increment_key(key: str, amount: int = 1) -> Optional[int]:
    """
    Increment a key in Redis.
    
    Args:
        key: Key to increment
        amount: Amount to increment by
        
    Returns:
        New value or None if error
    """
    client = get_redis_client()
    if client:
        try:
            return client.incrby(key, amount)
        except Exception as e:
            logger.error(f"Failed to increment key {key} in Redis: {e}")
    return None

def add_to_set(key: str, *values) -> Optional[int]:
    """
    Add values to a Redis set.
    
    Args:
        key: Set key
        values: Values to add
        
    Returns:
        Number of elements added or None if error
    """
    client = get_redis_client()
    if client:
        try:
            return client.sadd(key, *values)
        except Exception as e:
            logger.error(f"Failed to add to set {key} in Redis: {e}")
    return None

def get_set_members(key: str) -> Optional[set]:
    """
    Get all members of a Redis set.
    
    Args:
        key: Set key
        
    Returns:
        Set of members or None if error
    """
    client = get_redis_client()
    if client:
        try:
            return client.smembers(key)
        except Exception as e:
            logger.error(f"Failed to get set members {key} from Redis: {e}")
    return None