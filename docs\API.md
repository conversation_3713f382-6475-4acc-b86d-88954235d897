# GridSpoke API Documentation

## Overview

GridSpoke provides a RESTful API for AI-powered ecommerce SEO optimization. The API enables automated optimization of product titles, descriptions, meta tags, and other SEO elements using advanced language models.

**Base URL**: `/api/v1`
**Authentication**: JW<PERSON>er <PERSON>ken
**Response Format**: JSON

## Authentication Endpoints

### POST /auth/register

Register a new user account.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "secure_password",
  "full_name": "<PERSON>"
}
```

**Response** (201 Created):
```json
{
  "id": "uuid-here",
  "email": "<EMAIL>",
  "full_name": "<PERSON>",
  "is_active": true,
  "is_superuser": false,
  "created_at": "2024-01-15T10:30:00Z"
}
```

### POST /auth/login

Authenticate and receive access token.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

**Response** (200 OK):
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user": {
    "id": "uuid-here",
    "email": "<EMAIL>",
    "full_name": "John Doe"
  }
}
```

### POST /auth/refresh

Refresh an expired access token.

**Request Body**:
```json
{
  "refresh_token": "refresh_token_here"
}
```

### POST /auth/logout

Invalidate current access token.

**Headers**: `Authorization: Bearer TOKEN`

**Response** (200 OK):
```json
{
  "message": "Successfully logged out"
}
```

### GET /users/me

Get the current user's profile.

**Headers**: `Authorization: Bearer TOKEN`

## Store Management

### GET /stores/

List all stores for the authenticated user.

**Headers**: `Authorization: Bearer TOKEN`

### POST /stores/

Create a new store connection.

**Headers**: `Authorization: Bearer TOKEN`

**Request Body**:
```json
{
  "name": "My Online Store",
  "store_url": "https://mystore.com",
  "platform": "woocommerce",
  "api_credentials": {
    "api_key": "wc_ck_your_key_here",
    "api_secret": "wc_cs_your_secret_here"
  }
}
```

### GET /stores/{store_id}

Get store by ID.

**Headers**: `Authorization: Bearer TOKEN`

### PATCH /stores/{store_id}

Update store metadata.

**Headers**: `Authorization: Bearer TOKEN`

## Product Management

### GET /products/

List products for a specific store.

**Headers**: `Authorization: Bearer TOKEN`

**Query Parameters**:
- `store_id` (uuid): Store identifier (required)

### GET /products/{product_id}

Get detailed product information.

**Headers**: `Authorization: Bearer TOKEN`

### POST /products/{product_id}/optimize

Queues an optimization task for a single product.

**Headers**: `Authorization: Bearer TOKEN`

**Request Body**:
```json
{
  "optimization_types": ["title", "description"]
}
```

### POST /products/bulk/optimize

Queues an optimization task for multiple products.

**Headers**: `Authorization: Bearer TOKEN`

**Request Body**:
```json
{
  "product_ids": ["uuid1", "uuid2"],
  "optimization_request": {
    "optimization_types": ["title", "description"]
  }
}
```

## Job Management

### GET /jobs/

List optimization jobs for a specific store.

**Headers**: `Authorization: Bearer TOKEN`

**Query Parameters**:
- `store_id` (uuid): Store identifier (required)

### GET /jobs/{job_id}

Get detailed job information.

**Headers**: `Authorization: Bearer TOKEN`

### POST /jobs/{job_id}/cancel

Cancel a running optimization job.

**Headers**: `Authorization: Bearer TOKEN`

## Analytics

### GET /analytics/overview

Get comprehensive analytics overview for the user's stores.

**Headers**: `Authorization: Bearer TOKEN`

**Query Parameters**:
- `store_id` (uuid): Optional store identifier
- `date_range` (str): 7d, 30d, 90d, 1y

## Dashboard

### GET /dashboard/stats

Get dashboard statistics.

**Headers**: `Authorization: Bearer TOKEN`