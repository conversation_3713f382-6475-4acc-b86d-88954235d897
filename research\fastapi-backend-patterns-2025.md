# FastAPI Backend Patterns and Best Practices for 2025

## Executive Summary

This research document compiles the latest FastAPI patterns and best practices from official documentation sources for implementing a modern backend API foundation. Key focus areas include FastAPI project structure, SQLAlchemy 2.0 with async support, JWT authentication, and Pydantic model patterns.

## 1. FastAPI Project Structure and Organization

### Modern Project Architecture (2025)
```
api/
├── main.py                 # FastAPI app initialization
├── core/
│   ├── config.py          # Configuration management
│   ├── database.py        # Database connection and session
│   └── security.py        # Authentication and security
├── models/                # SQLAlchemy models (table models)
│   ├── __init__.py
│   ├── base.py           # Base model class
│   ├── store.py          # Store model
│   ├── product.py        # Product model
│   └── optimization.py   # Optimization job models
├── schemas/               # Pydantic schemas (data models)
│   ├── __init__.py
│   ├── store.py          # Store schemas
│   ├── product.py        # Product schemas
│   └── optimization.py   # Optimization job schemas
├── crud/                  # Database operations
│   ├── __init__.py
│   ├── base.py           # Base CRUD class
│   ├── store.py          # Store CRUD operations
│   └── product.py        # Product CRUD operations
├── api/
│   └── v1/
│       ├── __init__.py
│       ├── api.py        # API router aggregation
│       └── endpoints/
│           ├── __init__.py
│           ├── auth.py   # Authentication endpoints
│           ├── stores.py # Store endpoints
│           └── products.py # Product endpoints
├── services/              # Business logic layer
│   ├── __init__.py
│   ├── auth_service.py   # Authentication business logic
│   └── product_service.py # Product business logic
└── requirements.txt
```

### Key Architectural Principles

1. **Separation of Concerns**
   - Models: Database table definitions (SQLAlchemy)
   - Schemas: Request/response validation (Pydantic)
   - CRUD: Database operations
   - Services: Business logic
   - API: HTTP endpoints

2. **Dependency Injection Pattern**
   ```python
   from typing import Annotated
   from fastapi import Depends
   
   # Session dependency
   SessionDep = Annotated[Session, Depends(get_session)]
   
   # Current user dependency
   CurrentUser = Annotated[User, Depends(get_current_user)]
   ```

3. **API Versioning**
   - Use `/api/v1/` prefix for all endpoints
   - Prepare for future API versions
   - Maintain backward compatibility

## 2. SQLAlchemy 2.0 Async Patterns

### Modern Model Declaration (2025)
```python
from typing import List, Optional
from sqlalchemy import String, ForeignKey
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

class Base(DeclarativeBase):
    """Base class for all database models"""
    pass

class Store(Base):
    __tablename__ = "stores"
    
    # Primary key with UUID
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4
    )
    
    # Required fields
    name: Mapped[str] = mapped_column(String(100), index=True)
    domain: Mapped[str] = mapped_column(String(255), unique=True)
    
    # Optional fields
    description: Mapped[Optional[str]]
    
    # Relationships
    products: Mapped[List["Product"]] = relationship(
        back_populates="store", 
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"Store(id={self.id!r}, name={self.name!r})"

class Product(Base):
    __tablename__ = "products"
    
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4
    )
    
    # Foreign key
    store_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("stores.id"))
    
    # Product fields
    title: Mapped[str] = mapped_column(String(255))
    description: Mapped[Optional[str]]
    price: Mapped[Optional[float]]
    
    # Relationship
    store: Mapped["Store"] = relationship(back_populates="products")
```

### Async Database Configuration
```python
# core/database.py
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from core.config import settings

# Async engine for PostgreSQL
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_pre_ping=True,
    pool_recycle=3600,
)

# Async session factory
async_session = async_sessionmaker(
    engine, 
    class_=AsyncSession, 
    expire_on_commit=False
)

async def get_session() -> AsyncSession:
    """Dependency to get database session"""
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()

async def create_tables():
    """Create all tables"""
    from models.base import Base
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
```

### Modern Async CRUD Operations
```python
# crud/base.py
from typing import Generic, TypeVar, Type, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy import select, update, delete
from pydantic import BaseModel

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)

class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model
    
    async def get(
        self, 
        db: AsyncSession, 
        id: uuid.UUID
    ) -> Optional[ModelType]:
        """Get single record by ID"""
        result = await db.execute(
            select(self.model).where(self.model.id == id)
        )
        return result.scalar_one_or_none()
    
    async def get_multi(
        self, 
        db: AsyncSession, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[ModelType]:
        """Get multiple records with pagination"""
        result = await db.execute(
            select(self.model).offset(skip).limit(limit)
        )
        return list(result.scalars().all())
    
    async def create(
        self, 
        db: AsyncSession, 
        obj_in: CreateSchemaType
    ) -> ModelType:
        """Create new record"""
        obj_data = obj_in.model_dump()
        db_obj = self.model(**obj_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def update(
        self, 
        db: AsyncSession, 
        db_obj: ModelType, 
        obj_in: UpdateSchemaType
    ) -> ModelType:
        """Update existing record"""
        obj_data = obj_in.model_dump(exclude_unset=True)
        for field, value in obj_data.items():
            setattr(db_obj, field, value)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def remove(
        self, 
        db: AsyncSession, 
        id: uuid.UUID
    ) -> Optional[ModelType]:
        """Delete record by ID"""
        obj = await self.get(db, id)
        if obj:
            await db.delete(obj)
            await db.commit()
        return obj
```

## 3. Pydantic Schema Patterns (2025)

### Base Schema Classes
```python
# schemas/base.py
from pydantic import BaseModel, ConfigDict
from typing import Optional
import uuid
from datetime import datetime

class BaseSchema(BaseModel):
    """Base schema with common configuration"""
    model_config = ConfigDict(
        from_attributes=True,  # For ORM integration
        validate_assignment=True,  # Validate on assignment
        use_enum_values=True,  # Use enum values not names
        str_strip_whitespace=True,  # Strip whitespace
    )

class TimestampMixin(BaseModel):
    """Mixin for timestamp fields"""
    created_at: datetime
    updated_at: Optional[datetime] = None

class UUIDMixin(BaseModel):
    """Mixin for UUID primary key"""
    id: uuid.UUID
```

### Schema Inheritance Patterns
```python
# schemas/store.py
from schemas.base import BaseSchema, UUIDMixin, TimestampMixin
from typing import Optional, List

# Base store schema with common fields
class StoreBase(BaseSchema):
    name: str
    domain: str
    description: Optional[str] = None

# Schema for creating stores (no ID)
class StoreCreate(StoreBase):
    """Schema for creating new stores"""
    pass

# Schema for updating stores (all fields optional)
class StoreUpdate(BaseSchema):
    """Schema for updating stores"""
    name: Optional[str] = None
    domain: Optional[str] = None
    description: Optional[str] = None

# Schema for public API responses
class StorePublic(StoreBase, UUIDMixin, TimestampMixin):
    """Schema for public store data"""
    pass

# Schema with relationships
class StoreWithProducts(StorePublic):
    """Store schema including products"""
    products: List["ProductPublic"] = []
```

### Advanced Pydantic Features (2025)
```python
from pydantic import BaseModel, Field, field_validator, computed_field
from typing import Annotated
import re

class ProductCreate(BaseSchema):
    title: Annotated[str, Field(min_length=1, max_length=255)]
    description: Optional[str] = None
    price: Annotated[float, Field(gt=0)] = None
    tags: List[str] = []
    
    @field_validator('title')
    @classmethod
    def validate_title(cls, v: str) -> str:
        """Validate product title"""
        if not v.strip():
            raise ValueError('Title cannot be empty')
        return v.strip()
    
    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v: List[str]) -> List[str]:
        """Validate and clean tags"""
        return [tag.strip().lower() for tag in v if tag.strip()]
    
    @computed_field
    @property
    def slug(self) -> str:
        """Generate URL-friendly slug from title"""
        return re.sub(r'[^a-zA-Z0-9]+', '-', self.title.lower()).strip('-')
```

## 4. JWT Authentication Implementation

### Security Configuration
```python
# core/security.py
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from passlib.hash import bcrypt
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from core.config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

# JWT settings
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: dict):
    """Create JWT refresh token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash password"""
    return pwd_context.hash(password)

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_session)
) -> User:
    """Get current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(
            credentials.credentials, 
            settings.SECRET_KEY, 
            algorithms=[ALGORITHM]
        )
        username: str = payload.get("sub")
        token_type: str = payload.get("type")
        
        if username is None or token_type != "access":
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    user = await get_user_by_username(db, username=username)
    if user is None:
        raise credentials_exception
    
    return user
```

### Authentication Endpoints
```python
# api/v1/endpoints/auth.py
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from schemas.auth import Token, UserCreate, UserPublic
from services.auth_service import AuthService

router = APIRouter()

@router.post("/register", response_model=UserPublic)
async def register(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_session),
    auth_service: AuthService = Depends()
):
    """Register new user"""
    return await auth_service.register(db, user_in)

@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_session),
    auth_service: AuthService = Depends()
):
    """Login user and return tokens"""
    return await auth_service.authenticate(db, form_data.username, form_data.password)

@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_token: str,
    db: AsyncSession = Depends(get_session),
    auth_service: AuthService = Depends()
):
    """Refresh access token"""
    return await auth_service.refresh_token(db, refresh_token)

@router.get("/me", response_model=UserPublic)
async def read_users_me(
    current_user: User = Depends(get_current_user)
):
    """Get current user profile"""
    return current_user
```

## 5. FastAPI Application Setup

### Main Application Configuration
```python
# main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
from api.v1.api import api_router
from core.config import settings
from core.database import create_tables

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await create_tables()
    yield
    # Shutdown
    pass

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="AI-Powered Ecommerce SEO Optimization Service",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan
)

# Security middleware
app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=settings.ALLOWED_HOSTS
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "ecommerce-seo-api"}
```

### Configuration Management
```python
# core/config.py
from pydantic_settings import BaseSettings
from typing import List, Optional
import secrets

class Settings(BaseSettings):
    # API Configuration
    PROJECT_NAME: str = "Ecommerce SEO API"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # Security
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Database
    DATABASE_URL: str
    DATABASE_ECHO: bool = False
    
    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    
    # OpenRouter API
    OPENROUTER_API_KEY: str
    OPENROUTER_BASE_URL: str = "https://openrouter.ai/api/v1"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

## 6. Error Handling and Validation

### Custom Exception Handlers
```python
# core/exceptions.py
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import ValidationError
import logging

logger = logging.getLogger(__name__)

class CustomHTTPException(HTTPException):
    """Custom HTTP exception with additional context"""
    def __init__(self, status_code: int, detail: str, context: dict = None):
        super().__init__(status_code=status_code, detail=detail)
        self.context = context or {}

async def validation_exception_handler(request: Request, exc: ValidationError):
    """Handle Pydantic validation errors"""
    logger.error(f"Validation error: {exc}")
    return JSONResponse(
        status_code=422,
        content={
            "detail": "Validation Error",
            "errors": exc.errors()
        }
    )

async def custom_http_exception_handler(request: Request, exc: CustomHTTPException):
    """Handle custom HTTP exceptions"""
    logger.error(f"Custom HTTP error: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.detail,
            "context": exc.context
        }
    )
```

## 7. Dependencies and Services

### Service Layer Pattern
```python
# services/auth_service.py
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from core.security import verify_password, get_password_hash, create_access_token, create_refresh_token
from crud.user import user_crud
from schemas.auth import UserCreate, Token
from models.user import User

class AuthService:
    """Authentication service layer"""
    
    async def register(self, db: AsyncSession, user_in: UserCreate) -> User:
        """Register new user"""
        # Check if user exists
        existing_user = await user_crud.get_by_email(db, email=user_in.email)
        if existing_user:
            raise CustomHTTPException(
                status_code=400,
                detail="User with this email already exists"
            )
        
        # Hash password and create user
        hashed_password = get_password_hash(user_in.password)
        user_data = user_in.model_dump()
        user_data["hashed_password"] = hashed_password
        del user_data["password"]
        
        return await user_crud.create(db, obj_in=user_data)
    
    async def authenticate(self, db: AsyncSession, email: str, password: str) -> Token:
        """Authenticate user and return tokens"""
        user = await user_crud.get_by_email(db, email=email)
        if not user or not verify_password(password, user.hashed_password):
            raise CustomHTTPException(
                status_code=401,
                detail="Incorrect email or password"
            )
        
        access_token = create_access_token(data={"sub": user.email})
        refresh_token = create_refresh_token(data={"sub": user.email})
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer"
        )
```

## 8. Testing Patterns

### Test Setup and Fixtures
```python
# tests/conftest.py
import pytest
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from fastapi.testclient import TestClient
from main import app
from core.database import get_session
from models.base import Base

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine"""
    engine = create_async_engine(TEST_DATABASE_URL, echo=True)
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Cleanup
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    await engine.dispose()

@pytest.fixture
async def test_session(test_engine):
    """Create test database session"""
    async_session = async_sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session

@pytest.fixture
def test_client(test_session):
    """Create test client with database override"""
    def override_get_session():
        yield test_session
    
    app.dependency_overrides[get_session] = override_get_session
    with TestClient(app) as client:
        yield client
    app.dependency_overrides.clear()
```

## 9. Performance and Optimization

### Database Performance
- Use connection pooling with proper settings
- Implement eager loading for relationships with `selectinload()`
- Use database indexes on frequently queried columns
- Implement pagination for large datasets

### API Performance
- Use async/await throughout the application
- Implement response caching where appropriate
- Use dependency injection for efficient resource management
- Monitor and log slow queries

### Security Best Practices
- Always hash passwords using bcrypt
- Implement proper JWT token validation
- Use HTTPS in production
- Validate all input data with Pydantic
- Implement rate limiting
- Use environment variables for sensitive configuration

## 10. Migration Strategy

### Database Migrations with Alembic
```python
# alembic/env.py
from sqlalchemy.ext.asyncio import create_async_engine
from alembic import context
from models.base import Base

config = context.config
target_metadata = Base.metadata

def run_migrations_online():
    """Run migrations in 'online' mode with async engine"""
    connectable = create_async_engine(config.get_main_option("sqlalchemy.url"))
    
    async def do_run_migrations(connection):
        context.configure(connection=connection, target_metadata=target_metadata)
        async with context.begin_transaction():
            context.run_migrations()
    
    with connectable.connect() as connection:
        asyncio.run(do_run_migrations(connection))
```

## Conclusion

This research provides a comprehensive foundation for implementing a modern FastAPI backend using 2025 best practices. Key takeaways:

1. **Modern SQLAlchemy 2.0**: Use async patterns with proper type annotations
2. **Pydantic V2**: Leverage new validation features and configuration patterns  
3. **Project Structure**: Implement clear separation of concerns
4. **Security**: Use JWT with proper validation and password hashing
5. **Performance**: Async throughout with proper database patterns
6. **Testing**: Comprehensive test setup with proper fixtures

This foundation supports the ecommerce SEO optimization service requirements while maintaining scalability, security, and maintainability.
