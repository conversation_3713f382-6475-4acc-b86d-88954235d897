// WebSocket Fixes for GridSpoke Frontend
// This file contains fixes for WebSocket connection issues

// Store the original WebSocket manager
const OriginalWebSocketClient = window.WebSocketClient;

// Enhanced WebSocket client with better error handling
class EnhancedWebSocketClient {
    constructor() {
        this.socket = null;
        this.listeners = new Map();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        this.isConnecting = false;
        this.isDisabled = false;
    }

    async connect(url) {
        if (this.isConnecting || this.isDisabled) return;
        
        this.isConnecting = true;
        
        try {
            console.log('Attempting WebSocket connection to:', url);
            
            // Check if WebSocket endpoint exists first
            const wsUrl = new URL(url);
            const httpUrl = `http://${wsUrl.host}/api/v1/health`;
            
            // Test if the API is available
            const response = await fetch(httpUrl);
            if (!response.ok) {
                throw new Error('API server not available');
            }
            
            // Try WebSocket connection with timeout
            this.socket = new WebSocket(url);
            
            // Set a connection timeout
            const connectionTimeout = setTimeout(() => {
                if (this.socket && this.socket.readyState === WebSocket.CONNECTING) {
                    console.warn('WebSocket connection timeout');
                    this.socket.close();
                }
            }, 10000);
            
            this.socket.onopen = () => {
                clearTimeout(connectionTimeout);
                console.log('WebSocket connected successfully');
                this.reconnectAttempts = 0;
                this.isConnecting = false;
                this.emit('connect');
            };
            
            this.socket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.emit('message', data);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };
            
            this.socket.onerror = (error) => {
                clearTimeout(connectionTimeout);
                console.warn('WebSocket error (this is expected if WebSocket not implemented):', error);
                this.isConnecting = false;
                this.emit('error', error);
                
                // Don't retry if it's a 404 - WebSocket endpoint doesn't exist
                if (this.reconnectAttempts === 0) {
                    console.info('WebSocket endpoint not available - disabling WebSocket features');
                    this.isDisabled = true;
                }
            };
            
            this.socket.onclose = (event) => {
                clearTimeout(connectionTimeout);
                this.isConnecting = false;
                console.log('WebSocket connection closed');
                this.emit('close', event);
                
                // Only attempt reconnect if not disabled and not a normal closure
                if (!this.isDisabled && event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.scheduleReconnect(url);
                }
            };
            
        } catch (error) {
            console.warn('WebSocket connection failed:', error.message);
            this.isConnecting = false;
            this.isDisabled = true; // Disable after first failure
        }
    }

    scheduleReconnect(url) {
        if (this.isDisabled) return;
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts;
        
        console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            if (!this.isDisabled) {
                this.connect(url);
            }
        }, delay);
    }

    subscribe(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
        console.log(`Subscribed to WebSocket event: ${event}`);
    }

    unsubscribe(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
                console.log(`Unsubscribed from WebSocket event: ${event}`);
            }
        }
    }

    emit(event, data = null) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in WebSocket event handler for ${event}:`, error);
                }
            });
        }
    }

    send(data) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(data));
        } else {
            console.warn('WebSocket not connected, cannot send data');
        }
    }

    disconnect() {
        this.isDisabled = true;
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        console.log('WebSocket disconnected manually');
    }

    isConnected() {
        return this.socket && this.socket.readyState === WebSocket.OPEN;
    }
}

// Replace the global WebSocket client
window.WebSocketClient = new EnhancedWebSocketClient();

// Provide backward compatibility
if (OriginalWebSocketClient) {
    // Copy any existing methods or properties
    Object.keys(OriginalWebSocketClient).forEach(key => {
        if (typeof OriginalWebSocketClient[key] === 'function' && !window.WebSocketClient[key]) {
            window.WebSocketClient[key] = OriginalWebSocketClient[key];
        }
    });
}

// Enhanced dashboard WebSocket connection with fallback
if (window.dashboardApp && window.dashboardApp.connectWebSocket) {
    const originalConnectWebSocket = window.dashboardApp.connectWebSocket;
    window.dashboardApp.connectWebSocket = function() {
        console.log('Enhanced WebSocket connection starting...');
        
        try {
            // Try the original connection method first
            return originalConnectWebSocket.call(this);
        } catch (error) {
            console.warn('WebSocket connection failed, continuing without real-time updates:', error);
            // Continue without WebSocket - the app should still work
            return Promise.resolve();
        }
    };
}

// Disable WebSocket features gracefully if not available
if (window.dashboard) {
    const originalInit = window.dashboard.init;
    if (originalInit) {
        window.dashboard.init = function() {
            // Call original init
            const result = originalInit.call(this);
            
            // Add warning about WebSocket
            if (window.WebSocketClient && window.WebSocketClient.isDisabled) {
                console.info('Real-time updates disabled - WebSocket endpoint not available');
                
                // Show user notification if Utils.showToast is available
                if (window.Utils && window.Utils.showToast) {
                    window.Utils.showToast('Real-time updates unavailable', 'warning');
                }
            }
            
            return result;
        };
    }
}

console.log('WebSocket fixes loaded successfully');
