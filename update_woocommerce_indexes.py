#!/usr/bin/env python3
"""
Script to automatically update line indexes in WooCommerce REST API documentation files.
This script scans all markdown files in the woocommerce_rest_api directory,
finds all header lines (#, ##, ###), and updates the Line-Index section accordingly.
"""

import os
import re
from pathlib import Path

def extract_headers_with_lines(content):
    """Extract all headers with their line numbers from markdown content."""
    headers = []
    lines = content.split('\n')
    
    for line_num, line in enumerate(lines, 1):
        # Match markdown headers (#, ##, ###, etc.)
        header_match = re.match(r'^(#{1,6})\s+(.+)$', line.strip())
        if header_match:
            level = len(header_match.group(1))
            title = header_match.group(2).strip()
            # Convert title to a format suitable for the index
            index_title = title.replace(':', '').replace('(', '').replace(')', '').replace('/', ' ')
            headers.append({
                'level': level,
                'title': title,
                'index_title': index_title,
                'line_number': line_num
            })
    
    return headers

def find_yaml_index_section(content):
    """Find the Line-Index section in YAML frontmatter."""
    lines = content.split('\n')
    in_yaml = False
    index_start = -1
    index_end = -1
    
    for i, line in enumerate(lines):
        if line.strip() == '---' and not in_yaml:
            in_yaml = True
            yaml_start = i
        elif line.strip() == '---' and in_yaml:
            yaml_end = i
            break
        elif in_yaml and 'Line-Index:' in line:
            index_start = i
    
    if index_start != -1:
        # Find the end of the Line-Index section
        for i in range(index_start + 1, yaml_end):
            if ':' in lines[i] and 'Line-Index' not in lines[i]:
                continue
            elif lines[i].strip() == '---' or (':' not in lines[i] and lines[i].strip()):
                index_end = i - 1
                break
        if index_end == -1:
            index_end = yaml_end - 1
    
    return index_start, index_end

def generate_line_index_content(headers):
    """Generate the Line-Index content from headers."""
    if not headers:
        return ""
    
    # Filter out the first YAML header and focus on actual content headers
    content_headers = [h for h in headers if h['line_number'] > 10]  # Skip YAML frontmatter
    
    if not content_headers:
        return ""
    
    # Create index entries for main headers (## level)
    index_entries = []
    for header in content_headers:
        if header['level'] == 2:  # ## headers
            # Create a clean index title
            clean_title = header['index_title']
            index_entries.append(f"  - {clean_title}: {header['line_number']}")
    
    return '\n'.join(index_entries)

def update_file_index(file_path):
    """Update the line index in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract all headers with line numbers
        headers = extract_headers_with_lines(content)
        
        # Find YAML frontmatter boundaries
        lines = content.split('\n')
        yaml_start = -1
        yaml_end = -1
        
        for i, line in enumerate(lines):
            if line.strip() == '---':
                if yaml_start == -1:
                    yaml_start = i
                elif yaml_start != -1:  # Second --- found
                    yaml_end = i
                    break
        
        if yaml_start == -1 or yaml_end == -1:
            print(f"Warning: No complete YAML frontmatter found in {file_path}")
            return False
        
        # Extract the topic from existing YAML
        topic_line = ""
        line_index_start = -1
        line_index_end = -1
        
        for i in range(yaml_start + 1, yaml_end):
            if lines[i].startswith('Topic:'):
                topic_line = lines[i]
            elif lines[i].strip() == 'Line-Index:':
                line_index_start = i
        
        # Find where Line-Index section ends
        if line_index_start != -1:
            for i in range(line_index_start + 1, yaml_end):
                if not lines[i].strip().startswith('  - ') and lines[i].strip() != '':
                    line_index_end = i - 1
                    break
            if line_index_end == -1:
                line_index_end = yaml_end - 1
        
        # Generate new YAML frontmatter with updated Line-Index
        new_headers = [h for h in headers if h['line_number'] > yaml_end]
        index_entries = []
        
        for header in new_headers:
            if header['level'] == 2:  # ## headers
                clean_title = header['index_title']
                index_entries.append(f"  - {clean_title}: {header['line_number']}")
        
        if index_entries:
            # Build new YAML frontmatter
            new_yaml_lines = ["---"]
            if topic_line:
                new_yaml_lines.append(topic_line)
            else:
                # Extract topic from filename
                topic = file_path.stem.replace('_', ' ').title()
                new_yaml_lines.append(f"Topic: WooCommerce REST API {topic}")
            
            new_yaml_lines.append("Line-Index:")
            new_yaml_lines.extend(index_entries)
            new_yaml_lines.append("---")
            
            # Replace the old YAML frontmatter and content after it
            new_content = '\n'.join(new_yaml_lines) + '\n' + '\n'.join(lines[yaml_end + 1:])
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"Updated {file_path}")
            print("  Index entries:")
            for entry in index_entries:
                print(f"    {entry}")
            return True
        else:
            print(f"No headers found to index in {file_path}")
            return False
            
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to process all files."""
    # Directory containing the WooCommerce REST API documentation
    docs_dir = Path("ecommerce-seo-optimizer/research/woocommerce_rest_api")
    
    if not docs_dir.exists():
        print(f"Directory {docs_dir} not found!")
        return
    
    # Process all markdown files
    updated_files = 0
    total_files = 0
    
    for file_path in docs_dir.glob("*.md"):
        total_files += 1
        print(f"\nProcessing {file_path.name}...")
        if update_file_index(file_path):
            updated_files += 1
    
    print(f"\nSummary: Updated {updated_files} out of {total_files} files")

if __name__ == "__main__":
    main()
