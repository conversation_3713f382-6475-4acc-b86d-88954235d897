# GridSpoke Test Fixtures and Utilities
"""
Common test fixtures and utilities for GridSpoke testing suite.
Provides shared test data, mock objects, and helper functions.
"""

import pytest
import json
import asyncio
from datetime import datetime, timedelta
from unittest.mock import MagicMock, AsyncMock
from typing import Dict, List, Any, Optional
import tempfile
import os


# ============================================================================
# Database and Session Fixtures
# ============================================================================

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def async_client():
    """Async HTTP client for testing FastAPI endpoints"""
    try:
        from httpx import AsyncClient
        from api.main import app
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    except ImportError:
        pytest.skip("httpx not available for async client testing")


@pytest.fixture
def test_database_url():
    """Temporary SQLite database URL for testing"""
    temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
    temp_db.close()
    
    database_url = f"sqlite:///{temp_db.name}"
    
    yield database_url
    
    # Cleanup
    os.unlink(temp_db.name)


# ============================================================================
# Sample Data Fixtures
# ============================================================================

@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "id": "user_test_123",
        "email": "<EMAIL>",
        "password_hash": "$2b$12$encrypted_password_hash",
        "first_name": "Test",
        "last_name": "User",
        "is_active": True,
        "is_superuser": False,
        "plan": "premium",
        "api_key": "test_api_key_456",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }


@pytest.fixture
def sample_store_data():
    """Sample store data for testing"""
    return {
        "id": "store_test_789",
        "name": "GridSpoke Test Store",
        "url": "https://gridspoke-test-store.com",
        "platform": "woocommerce",
        "api_key": "wc_test_api_key_encrypted",
        "api_secret": "wc_test_api_secret_encrypted",
        "webhook_secret": "webhook_secret_123",
        "auto_sync": True,
        "optimization_schedule": "daily",
        "user_id": "user_test_123",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "settings": {
            "optimization_types": ["title", "description", "meta"],
            "seo_target_score": 85,
            "max_daily_optimizations": 100
        }
    }


@pytest.fixture
def sample_product_data():
    """Sample product data for testing"""
    return {
        "id": "product_test_101",
        "name": "Premium Wireless Bluetooth Headphones",
        "description": "High-quality wireless headphones with noise cancellation",
        "short_description": "Premium wireless headphones",
        "price": 199.99,
        "sale_price": 149.99,
        "sku": "PWH-001",
        "category": "Electronics",
        "subcategory": "Audio",
        "tags": ["wireless", "bluetooth", "headphones", "premium"],
        "images": [
            {
                "id": "img_1",
                "url": "https://example.com/headphones-main.jpg",
                "alt": "Premium wireless headphones front view"
            },
            {
                "id": "img_2", 
                "url": "https://example.com/headphones-side.jpg",
                "alt": "Premium wireless headphones side view"
            }
        ],
        "attributes": {
            "color": "Black",
            "brand": "AudioTech",
            "model": "AT-WH-Pro",
            "warranty": "2 years"
        },
        "seo_data": {
            "meta_title": "Premium Wireless Bluetooth Headphones | AudioTech",
            "meta_description": "Experience premium audio with our wireless headphones",
            "keywords": ["wireless headphones", "bluetooth audio", "premium sound"]
        },
        "store_id": "store_test_789",
        "external_id": "wc_product_12345",
        "status": "published",
        "stock_quantity": 50,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }


@pytest.fixture
def sample_optimization_job_data():
    """Sample optimization job data for testing"""
    return {
        "id": "job_test_202",
        "user_id": "user_test_123",
        "store_id": "store_test_789",
        "product_ids": ["product_test_101", "product_test_102", "product_test_103"],
        "optimization_type": "full",
        "status": "processing",
        "priority": "normal",
        "total_items": 3,
        "processed_items": 1,
        "failed_items": 0,
        "progress": 33.33,
        "celery_task_id": "celery_task_xyz789",
        "started_at": datetime.utcnow(),
        "estimated_completion": datetime.utcnow() + timedelta(minutes=10),
        "settings": {
            "ai_model": "anthropic/claude-3-opus",
            "creativity_level": 0.7,
            "target_seo_score": 85,
            "preserve_brand_voice": True
        },
        "metadata": {
            "triggered_by": "manual",
            "user_agent": "GridSpoke Dashboard v1.0",
            "ip_address": "*************"
        },
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }


@pytest.fixture
def sample_optimization_result_data():
    """Sample optimization result data for testing"""
    return {
        "id": "result_test_303",
        "job_id": "job_test_202",
        "product_id": "product_test_101",
        "optimization_type": "full",
        "original_data": {
            "title": "Wireless Headphones",
            "description": "Basic wireless headphones",
            "meta_title": "",
            "meta_description": ""
        },
        "optimized_data": {
            "title": "Premium Wireless Bluetooth Headphones - Noise Cancelling | AudioTech",
            "description": "Experience premium audio quality with our professional-grade wireless headphones featuring advanced noise cancellation technology, 30-hour battery life, and crystal-clear sound reproduction perfect for audiophiles and professionals.",
            "meta_title": "Premium Wireless Bluetooth Headphones | Noise Cancelling Audio",
            "meta_description": "Discover premium wireless headphones with advanced noise cancellation. 30-hour battery, crystal-clear sound. Perfect for professionals and audiophiles.",
            "keywords": ["premium wireless headphones", "noise cancelling", "bluetooth audio", "professional headphones"],
            "alt_texts": {
                "img_1": "Premium wireless bluetooth headphones with noise cancellation technology",
                "img_2": "Side view of professional wireless headphones showing comfortable design"
            }
        },
        "seo_analysis": {
            "score_before": 45.2,
            "score_after": 89.7,
            "improvements": [
                "Enhanced title with target keywords",
                "Expanded description with benefits",
                "Added compelling meta descriptions",
                "Optimized image alt texts for accessibility",
                "Improved keyword density and relevance"
            ],
            "keyword_density": {
                "wireless": 0.08,
                "headphones": 0.06,
                "premium": 0.04,
                "bluetooth": 0.03
            }
        },
        "ai_metrics": {
            "model_used": "anthropic/claude-3-opus",
            "tokens_used": {
                "prompt": 450,
                "completion": 280,
                "total": 730
            },
            "processing_time": 4.2,
            "cost": 0.15,
            "creativity_score": 0.75
        },
        "applied": False,
        "applied_at": None,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }


# ============================================================================
# Mock API Response Fixtures
# ============================================================================

@pytest.fixture
def mock_openrouter_response():
    """Mock OpenRouter API response for testing"""
    return {
        "choices": [
            {
                "message": {
                    "role": "assistant",
                    "content": json.dumps({
                        "title": "Premium Wireless Bluetooth Headphones - Noise Cancelling",
                        "description": "Experience premium audio quality with advanced noise cancellation",
                        "meta_title": "Premium Wireless Headphones | Noise Cancelling Audio",
                        "meta_description": "Discover premium wireless headphones with advanced noise cancellation",
                        "keywords": ["premium headphones", "wireless audio", "noise cancelling"],
                        "seo_score": 89.5
                    })
                }
            }
        ],
        "usage": {
            "prompt_tokens": 450,
            "completion_tokens": 280,
            "total_tokens": 730
        },
        "model": "anthropic/claude-3-opus",
        "created": int(datetime.utcnow().timestamp())
    }


@pytest.fixture
def mock_wordpress_product():
    """Mock WordPress/WooCommerce product data"""
    return {
        "id": 12345,
        "name": "Test WordPress Product",
        "type": "simple",
        "status": "publish",
        "description": "Test product description from WordPress",
        "short_description": "Short description",
        "sku": "WP-TEST-001",
        "price": "99.99",
        "regular_price": "99.99",
        "sale_price": "",
        "categories": [
            {"id": 15, "name": "Electronics", "slug": "electronics"}
        ],
        "tags": [
            {"id": 25, "name": "test", "slug": "test"},
            {"id": 26, "name": "wordpress", "slug": "wordpress"}
        ],
        "images": [
            {
                "id": 456,
                "src": "https://test-store.com/wp-content/uploads/product.jpg",
                "name": "product.jpg",
                "alt": "Test product image"
            }
        ],
        "attributes": [],
        "meta_data": [
            {"key": "_yoast_wpseo_title", "value": "Test Product SEO Title"},
            {"key": "_yoast_wpseo_metadesc", "value": "Test product meta description"}
        ],
        "date_created": "2024-01-15T10:30:00",
        "date_modified": "2024-01-15T14:20:00"
    }


# ============================================================================
# Mock Service Fixtures
# ============================================================================

@pytest.fixture
def mock_celery_task():
    """Mock Celery task for testing"""
    task = MagicMock()
    task.id = "celery_task_mock_123"
    task.state = "PENDING"
    task.result = None
    task.info = {}
    
    # Mock task methods
    task.delay.return_value = task
    task.apply_async.return_value = task
    task.get.return_value = {"status": "completed", "result": "success"}
    
    return task


@pytest.fixture
def mock_websocket_manager():
    """Mock WebSocket manager for testing"""
    manager = MagicMock()
    manager.active_connections = {}
    
    # Mock async methods
    manager.connect = AsyncMock()
    manager.disconnect = MagicMock()
    manager.send_personal_message = AsyncMock()
    manager.broadcast = AsyncMock()
    
    return manager


@pytest.fixture
def mock_ai_service():
    """Mock AI service for testing"""
    service = MagicMock()
    
    # Mock optimization methods
    service.optimize_product_content.return_value = {
        "title": "AI Optimized Product Title",
        "description": "AI optimized product description with enhanced SEO",
        "meta_title": "AI Optimized Meta Title",
        "meta_description": "AI optimized meta description for search engines",
        "seo_score": 87.5,
        "tokens_used": 650,
        "cost": 0.12
    }
    
    service.generate_blog_content.return_value = {
        "title": "Ultimate Guide to Product Category",
        "content": "Comprehensive blog content about the product category...",
        "word_count": 1200,
        "reading_time": 5
    }
    
    return service


# ============================================================================
# Test Data Generators
# ============================================================================

@pytest.fixture
def performance_test_products():
    """Generate multiple products for performance testing"""
    products = []
    
    for i in range(100):
        product = {
            "id": f"perf_product_{i}",
            "name": f"Performance Test Product {i}",
            "description": f"Description for performance test product {i}",
            "price": 29.99 + (i * 0.1),
            "category": ["electronics", "clothing", "books", "sports"][i % 4],
            "optimization_status": "pending"
        }
        products.append(product)
    
    return products


# ============================================================================
# Authentication Fixtures
# ============================================================================

@pytest.fixture
def auth_headers():
    """Authentication headers for API testing"""
    return {
        "Authorization": "Bearer test_jwt_token_for_testing",
        "Content-Type": "application/json"
    }


@pytest.fixture
def admin_auth_headers():
    """Admin authentication headers for testing"""
    return {
        "Authorization": "Bearer admin_jwt_token_for_testing",
        "Content-Type": "application/json"
    }


@pytest.fixture
def api_key_headers():
    """API key headers for testing"""
    return {
        "X-API-Key": "test_api_key_for_testing",
        "Content-Type": "application/json"
    }


# ============================================================================
# Model Factory Fixtures
# ============================================================================

@pytest.fixture
def create_test_user(db_session):
    """Factory fixture for creating test users"""
    def _create_user(**kwargs):
        try:
            from api.models import User
            
            user_data = {
                "id": f"test_user_{datetime.utcnow().microsecond}",
                "email": f"test_{datetime.utcnow().microsecond}@example.com",
                "password_hash": "hashed_password",
                "is_active": True,
                **kwargs
            }
            
            user = User(**user_data)
            db_session.add(user)
            db_session.commit()
            db_session.refresh(user)
            
            return user
        except ImportError:
            # Return mock user if models not available
            return MagicMock(**kwargs)
    
    return _create_user


@pytest.fixture
def create_test_store(db_session):
    """Factory fixture for creating test stores"""
    def _create_store(**kwargs):
        try:
            from api.models import Store
            
            store_data = {
                "id": f"test_store_{datetime.utcnow().microsecond}",
                "name": f"Test Store {datetime.utcnow().microsecond}",
                "url": f"https://test-store-{datetime.utcnow().microsecond}.com",
                "platform": "woocommerce",
                "user_id": "test_user_123",
                **kwargs
            }
            
            store = Store(**store_data)
            db_session.add(store)
            db_session.commit()
            db_session.refresh(store)
            
            return store
        except ImportError:
            # Return mock store if models not available
            return MagicMock(**kwargs)
    
    return _create_store


@pytest.fixture
def create_test_product(db_session):
    """Factory fixture for creating test products"""
    def _create_product(**kwargs):
        try:
            from api.models import Product
            
            product_data = {
                "id": f"test_product_{datetime.utcnow().microsecond}",
                "name": f"Test Product {datetime.utcnow().microsecond}",
                "description": "Test product description",
                "price": 29.99,
                "store_id": "test_store_123",
                **kwargs
            }
            
            product = Product(**product_data)
            db_session.add(product)
            db_session.commit()
            db_session.refresh(product)
            
            return product
        except ImportError:
            # Return mock product if models not available
            return MagicMock(**kwargs)
    
    return _create_product


# ============================================================================
# Utility Fixtures
# ============================================================================

@pytest.fixture
def temp_file():
    """Create temporary file for testing"""
    temp_file = tempfile.NamedTemporaryFile(delete=False)
    temp_file.close()
    
    yield temp_file.name
    
    # Cleanup
    os.unlink(temp_file.name)


@pytest.fixture
def mock_email_service():
    """Mock email service for testing"""
    email_service = MagicMock()
    
    email_service.send_notification.return_value = {"status": "sent", "message_id": "msg_123"}
    email_service.send_optimization_complete.return_value = {"status": "sent"}
    email_service.send_error_alert.return_value = {"status": "sent"}
    
    return email_service


@pytest.fixture
def mock_cache_service():
    """Mock cache service for testing"""
    cache_service = MagicMock()
    
    # Mock cache methods
    cache_service.get.return_value = None
    cache_service.set.return_value = True
    cache_service.delete.return_value = True
    cache_service.exists.return_value = False
    cache_service.ttl.return_value = 3600
    
    return cache_service


# ============================================================================
# Test Configuration
# ============================================================================

@pytest.fixture(scope="session")
def test_config():
    """Test configuration settings"""
    return {
        "DATABASE_URL": "sqlite:///test.db",
        "REDIS_URL": "redis://localhost:6379/15",  # Test Redis database
        "SECRET_KEY": "test_secret_key_for_testing",
        "OPENROUTER_API_KEY": "test_openrouter_api_key",
        "JWT_SECRET_KEY": "test_jwt_secret_key",
        "JWT_ALGORITHM": "HS256",
        "JWT_EXPIRE_MINUTES": 30,
        "CELERY_BROKER_URL": "redis://localhost:6379/14",
        "CELERY_RESULT_BACKEND": "redis://localhost:6379/14",
        "TESTING": True,
        "LOG_LEVEL": "DEBUG"
    }


# ============================================================================
# Parametrized Test Data
# ============================================================================

@pytest.fixture(params=[
    {"optimization_type": "title", "expected_fields": ["title"]},
    {"optimization_type": "description", "expected_fields": ["description"]},
    {"optimization_type": "meta", "expected_fields": ["meta_title", "meta_description"]},
    {"optimization_type": "full", "expected_fields": ["title", "description", "meta_title", "meta_description"]}
])
def optimization_type_params(request):
    """Parametrized optimization types for testing"""
    return request.param


@pytest.fixture(params=["woocommerce", "surecart", "shopify"])
def platform_params(request):
    """Parametrized platform types for testing"""
    return request.param


@pytest.fixture(params=[
    {"model": "anthropic/claude-3-opus", "cost_per_token": 0.000015},
    {"model": "openai/gpt-4", "cost_per_token": 0.00003},
    {"model": "anthropic/claude-3-sonnet", "cost_per_token": 0.000003}
])
def ai_model_params(request):
    """Parametrized AI model configurations for testing"""
    return request.param


# ============================================================================
# Cleanup Fixtures
# ============================================================================

@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Automatic cleanup after each test"""
    yield
    
    # Cleanup code here
    # Clear any test files, reset mocks, etc.
    pass


# ============================================================================
# Mark Configurations
# ============================================================================

# Custom pytest marks for organizing tests
pytest_plugins = []

def pytest_configure(config):
    """Configure custom pytest marks"""
    config.addinivalue_line("markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')")
    config.addinivalue_line("markers", "integration: marks tests as integration tests")
    config.addinivalue_line("markers", "unit: marks tests as unit tests")
    config.addinivalue_line("markers", "api: marks tests as API tests")
    config.addinivalue_line("markers", "websocket: marks tests as WebSocket tests")
    config.addinivalue_line("markers", "security: marks tests as security tests")
    config.addinivalue_line("markers", "load: marks tests as load/performance tests")
    config.addinivalue_line("markers", "e2e: marks tests as end-to-end tests")


# ============================================================================
# Test Data Files
# ============================================================================

@pytest.fixture
def sample_csv_data():
    """Sample CSV data for bulk import testing"""
    return """id,name,description,price,category
prod_001,Test Product 1,Description for product 1,29.99,Electronics
prod_002,Test Product 2,Description for product 2,49.99,Clothing
prod_003,Test Product 3,Description for product 3,19.99,Books"""


@pytest.fixture
def sample_json_export():
    """Sample JSON export data for testing"""
    return {
        "export_version": "1.0",
        "timestamp": datetime.utcnow().isoformat(),
        "total_products": 3,
        "products": [
            {
                "id": "prod_001",
                "name": "Exported Product 1",
                "optimization_status": "optimized",
                "seo_score": 87.5
            },
            {
                "id": "prod_002", 
                "name": "Exported Product 2",
                "optimization_status": "pending",
                "seo_score": None
            }
        ]
    }
