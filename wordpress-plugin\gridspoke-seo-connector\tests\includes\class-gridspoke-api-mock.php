<?php
/**
 * API Mock for testing GridSpoke SEO Connector
 */

class GridSpoke_API_Mock {
    
    /**
     * Recorded requests
     */
    private $requests = array();
    
    /**
     * Mocked responses
     */
    private $responses = array();
    
    /**
     * Default responses
     */
    private $default_responses = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->setup_default_responses();
        $this->setup_hooks();
    }
    
    /**
     * Set up WordPress hooks to intercept HTTP requests
     */
    private function setup_hooks() {
        add_filter('pre_http_request', array($this, 'intercept_http_request'), 10, 3);
    }
    
    /**
     * Set up default API responses
     */
    private function setup_default_responses() {
        $this->default_responses = array(
            'health' => array(
                'status_code' => 200,
                'body' => json_encode(array('status' => 'healthy', 'version' => '1.0.0'))
            ),
            'auth/login' => array(
                'status_code' => 200,
                'body' => json_encode(array(
                    'access_token' => 'mock_access_token_' . uniqid(),
                    'refresh_token' => 'mock_refresh_token_' . uniqid(),
                    'user' => array(
                        'id' => 'user_' . uniqid(),
                        'email' => '<EMAIL>',
                        'name' => 'Test User'
                    )
                ))
            ),
            'auth/refresh' => array(
                'status_code' => 200,
                'body' => json_encode(array(
                    'access_token' => 'mock_access_token_refreshed_' . uniqid(),
                    'refresh_token' => 'mock_refresh_token_refreshed_' . uniqid()
                ))
            ),
            'auth/me' => array(
                'status_code' => 200,
                'body' => json_encode(array(
                    'id' => 'user_' . uniqid(),
                    'email' => '<EMAIL>',
                    'name' => 'Test User'
                ))
            ),
            'stores' => array(
                'status_code' => 200,
                'body' => json_encode(array())
            ),
            'products/bulk/sync' => array(
                'status_code' => 200,
                'body' => json_encode(array(
                    'message' => 'Bulk sync completed',
                    'created' => 5,
                    'updated' => 3,
                    'errors' => array(),
                    'total_processed' => 8,
                    'product_mappings' => array(
                        array('external_id' => '1', 'gridspoke_id' => 'uuid_1'),
                        array('external_id' => '2', 'gridspoke_id' => 'uuid_2')
                    )
                ))
            ),
            'products/bulk/optimize' => array(
                'status_code' => 200,
                'body' => json_encode(array(
                    'job_id' => 'job_' . uniqid(),
                    'message' => 'Bulk optimization queued for 5 products',
                    'estimated_completion' => null
                ))
            )
        );
    }
    
    /**
     * Intercept HTTP requests to GridSpoke API
     */
    public function intercept_http_request($preempt, $parsed_args, $url) {
        // Only intercept requests to our API
        if (strpos($url, 'localhost:8000/api/v1') === false && strpos($url, 'api.gridspoke.com') === false) {
            return false; // Let the request proceed normally
        }
        
        // Record the request
        $this->record_request($parsed_args, $url);
        
        // Extract endpoint from URL
        $endpoint = $this->extract_endpoint($url);
        
        // Get mocked response
        $response = $this->get_mocked_response($endpoint, $parsed_args);
        
        // Return WordPress HTTP response format
        return array(
            'headers' => array('content-type' => 'application/json'),
            'body' => $response['body'],
            'response' => array(
                'code' => $response['status_code'],
                'message' => $this->get_status_message($response['status_code'])
            ),
            'cookies' => array(),
            'filename' => null
        );
    }
    
    /**
     * Record HTTP request for testing
     */
    private function record_request($parsed_args, $url) {
        $this->requests[] = array(
            'method' => $parsed_args['method'],
            'url' => $url,
            'endpoint' => $this->extract_endpoint($url),
            'headers' => $parsed_args['headers'],
            'data' => isset($parsed_args['body']) ? json_decode($parsed_args['body'], true) : null,
            'timestamp' => time()
        );
    }
    
    /**
     * Extract endpoint from URL
     */
    private function extract_endpoint($url) {
        $parsed = parse_url($url);
        $path = $parsed['path'];
        
        // Remove /api/v1 prefix
        $endpoint = preg_replace('#^/api/v1/?#', '', $path);
        
        return $endpoint;
    }
    
    /**
     * Get mocked response for endpoint
     */
    private function get_mocked_response($endpoint, $parsed_args) {
        $method = $parsed_args['method'];
        $key = $method . ':' . $endpoint;
        
        // Check for specific mocked response
        if (isset($this->responses[$key])) {
            return $this->responses[$key];
        }
        
        // Check for endpoint-only response
        if (isset($this->responses[$endpoint])) {
            return $this->responses[$endpoint];
        }
        
        // Check default responses
        if (isset($this->default_responses[$endpoint])) {
            return $this->default_responses[$endpoint];
        }
        
        // Default 404 response
        return array(
            'status_code' => 404,
            'body' => json_encode(array('detail' => 'Endpoint not found'))
        );
    }
    
    /**
     * Get HTTP status message
     */
    private function get_status_message($status_code) {
        $messages = array(
            200 => 'OK',
            201 => 'Created',
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            500 => 'Internal Server Error'
        );
        
        return $messages[$status_code] ?? 'Unknown';
    }
    
    /**
     * Mock a specific response
     */
    public function mock_response($endpoint, $response, $method = null) {
        $key = $method ? $method . ':' . $endpoint : $endpoint;
        $this->responses[$key] = $response;
    }
    
    /**
     * Get recorded requests
     */
    public function get_requests() {
        return $this->requests;
    }
    
    /**
     * Get last request
     */
    public function get_last_request() {
        return end($this->requests);
    }
    
    /**
     * Find request by endpoint
     */
    public function find_request($endpoint, $method = null) {
        foreach ($this->requests as $request) {
            if ($request['endpoint'] === $endpoint) {
                if ($method === null || $request['method'] === $method) {
                    return $request;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Reset recorded requests and responses
     */
    public function reset() {
        $this->requests = array();
        $this->responses = array();
    }
    
    /**
     * Mock authentication success
     */
    public function mock_auth_success() {
        $this->mock_response('auth/login', array(
            'status_code' => 200,
            'body' => json_encode(array(
                'access_token' => 'test_access_token',
                'refresh_token' => 'test_refresh_token',
                'user' => array(
                    'id' => 'test_user_id',
                    'email' => '<EMAIL>',
                    'name' => 'Test User'
                )
            ))
        ));
        
        $this->mock_response('auth/me', array(
            'status_code' => 200,
            'body' => json_encode(array(
                'id' => 'test_user_id',
                'email' => '<EMAIL>',
                'name' => 'Test User'
            ))
        ));
    }
    
    /**
     * Mock authentication failure
     */
    public function mock_auth_failure() {
        $this->mock_response('auth/login', array(
            'status_code' => 401,
            'body' => json_encode(array('detail' => 'Invalid credentials'))
        ));
    }
    
    /**
     * Mock store creation
     */
    public function mock_store_creation() {
        $this->mock_response('stores', array(
            'status_code' => 201,
            'body' => json_encode(array(
                'id' => 'test_store_id',
                'name' => 'Test Store',
                'domain' => 'example.com',
                'platform' => 'woocommerce'
            ))
        ), 'POST');
    }
    
    /**
     * Mock sync success
     */
    public function mock_sync_success($created = 1, $updated = 0) {
        $this->mock_response('products/bulk/sync', array(
            'status_code' => 200,
            'body' => json_encode(array(
                'message' => 'Bulk sync completed',
                'created' => $created,
                'updated' => $updated,
                'errors' => array(),
                'total_processed' => $created + $updated,
                'product_mappings' => array(
                    array('external_id' => '1', 'gridspoke_id' => 'uuid_1')
                )
            ))
        ));
    }
    
    /**
     * Mock optimization success
     */
    public function mock_optimization_success() {
        $this->mock_response('products/bulk/optimize', array(
            'status_code' => 200,
            'body' => json_encode(array(
                'job_id' => 'test_job_id',
                'message' => 'Optimization queued successfully',
                'estimated_completion' => null
            ))
        ));
    }
}
