/**
 * GridSpoke SEO Admin Styles
 */

/* Dashboard Grid Layout */
.gridspoke-dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.gridspoke-dashboard-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.gridspoke-dashboard-card h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    color: #1d2327;
}

.gridspoke-full-width {
    grid-column: 1 / -1;
}

/* Statistics Grid */
.gridspoke-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.gridspoke-stat-item {
    text-align: center;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 4px;
}

.gridspoke-stat-number {
    display: block;
    font-size: 32px;
    font-weight: bold;
    color: #2271b1;
    line-height: 1;
}

.gridspoke-stat-label {
    display: block;
    font-size: 12px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 5px;
}

/* Actions Grid */
.gridspoke-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

/* Status Indicators */
.gridspoke-status-success {
    color: #00a32a;
    font-weight: 600;
}

.gridspoke-status-error {
    color: #d63638;
    font-weight: 600;
}

.gridspoke-status-warning {
    color: #dba617;
    font-weight: 600;
}

.gridspoke-status-pending {
    color: #72aee6;
    font-weight: 600;
}

.gridspoke-status-unknown {
    color: #646970;
    font-weight: 600;
}

.gridspoke-status-loading {
    color: #2271b1;
    font-weight: 600;
}

/* Status Grid */
.gridspoke-status-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
}

.gridspoke-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
}

.gridspoke-status-item:last-child {
    border-bottom: none;
}

.gridspoke-status-label {
    font-weight: 600;
    color: #1d2327;
}

/* Tools Layout */
.gridspoke-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.gridspoke-tool-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.gridspoke-tool-card h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    color: #1d2327;
    border-bottom: 1px solid #f0f0f1;
    padding-bottom: 10px;
}

.gridspoke-tool-section {
    margin-bottom: 25px;
}

.gridspoke-tool-section:last-child {
    margin-bottom: 0;
}

.gridspoke-tool-section h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #1d2327;
}

.gridspoke-tool-section p {
    margin-bottom: 15px;
    color: #646970;
}

/* Tool Results */
.gridspoke-tool-result {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
}

.gridspoke-tool-result.success {
    background: #d1e7dd;
    border: 1px solid #badbcc;
    color: #0f5132;
}

.gridspoke-tool-result.error {
    background: #f8d7da;
    border: 1px solid #f5c2c7;
    color: #842029;
}

/* Progress Bar */
.gridspoke-progress-bar {
    position: relative;
    width: 100%;
    height: 20px;
    background: #f0f0f1;
    border-radius: 10px;
    overflow: hidden;
    margin: 15px 0;
}

.gridspoke-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2271b1, #72aee6);
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0%;
}

.gridspoke-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: #1d2327;
}

/* Forms */
.gridspoke-form-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.gridspoke-form-row label {
    font-weight: 600;
    min-width: 80px;
}

.gridspoke-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.gridspoke-checkbox-group label {
    font-weight: normal;
    min-width: auto;
}

/* Webhook Info */
.gridspoke-webhook-info,
.gridspoke-webhook-secret {
    margin: 15px 0;
    padding: 10px;
    background: #f6f7f7;
    border-radius: 4px;
}

.gridspoke-webhook-info code,
.gridspoke-webhook-secret code {
    background: #fff;
    padding: 5px 8px;
    border-radius: 3px;
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
}

/* System Info */
.gridspoke-system-info {
    margin-top: 15px;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 4px;
}

.gridspoke-system-info h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #1d2327;
}

.gridspoke-system-info ul {
    margin: 0 0 15px 0;
    padding-left: 20px;
}

.gridspoke-system-info li {
    margin-bottom: 5px;
}

/* Logs */
.gridspoke-logs-filters {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.gridspoke-filter-row {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.gridspoke-logs-actions {
    margin-bottom: 20px;
}

.gridspoke-logs-actions .button {
    margin-right: 10px;
}

.gridspoke-logs-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.gridspoke-log-level-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.gridspoke-log-level-debug {
    background: #f0f0f1;
    color: #646970;
}

.gridspoke-log-level-info {
    background: #d1e7dd;
    color: #0f5132;
}

.gridspoke-log-level-warning {
    background: #fff3cd;
    color: #664d03;
}

.gridspoke-log-level-error {
    background: #f8d7da;
    color: #842029;
}

.gridspoke-log-context-data {
    background: #f6f7f7;
    padding: 10px;
    border-radius: 3px;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
}

.gridspoke-log-meta {
    font-size: 12px;
    color: #646970;
    margin-top: 5px;
}

.gridspoke-no-logs {
    text-align: center;
    padding: 40px;
    color: #646970;
}

/* Error List */
.gridspoke-error-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.gridspoke-error-list li {
    padding: 10px;
    border-bottom: 1px solid #f0f0f1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.gridspoke-error-time {
    font-size: 12px;
    color: #646970;
    white-space: nowrap;
    margin-right: 15px;
}

.gridspoke-error-message {
    flex: 1;
    color: #d63638;
}

/* Authentication Status */
.gridspoke-auth-status {
    margin-top: 10px;
    padding: 10px;
    background: #f6f7f7;
    border-radius: 4px;
}

/* Meta Box Notices */
.gridspoke-metabox-notice {
    margin: 0 0 15px 0;
    padding: 8px 12px;
    border-radius: 4px;
}

.gridspoke-notice-success {
    background: #d1e7dd;
    border: 1px solid #badbcc;
    color: #0f5132;
}

.gridspoke-notice-error {
    background: #f8d7da;
    border: 1px solid #f5c2c7;
    color: #842029;
}

/* Responsive Design */
@media (max-width: 768px) {
    .gridspoke-dashboard-grid,
    .gridspoke-tools-grid {
        grid-template-columns: 1fr;
    }
    
    .gridspoke-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .gridspoke-filter-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .gridspoke-form-row {
        flex-direction: column;
        align-items: flex-start;
    }
}
