"""
Store model for ecommerce website connections.
"""
import uuid
from typing import Optional, List, Dict, Any
from enum import Enum
from sqlalchemy import String, Boolean, ForeignKey, Index, DateTime, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from models.base import BaseModel
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from models.user import User
    from models.product import Product
    from models.optimization_job import OptimizationJob
import structlog

logger = structlog.get_logger(__name__)


class StorePlatform(str, Enum):
    """Supported ecommerce platforms."""
    WOOCOMMERCE = "woocommerce"
    SURECART = "surecart"
    SHOPIFY = "shopify"
    CUSTOM = "custom"


class StoreStatus(str, Enum):
    """Store connection status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    ERROR = "error"


class Store(BaseModel):
    """Store model representing an ecommerce website."""
    
    __tablename__ = "stores"
    
    # Basic Information
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True
    )
    
    domain: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        unique=True,
        index=True
    )
    
    platform: Mapped[StorePlatform] = mapped_column(
        SQLEnum(StorePlatform),
        nullable=False,
        index=True
    )
    
    status: Mapped[StoreStatus] = mapped_column(
        SQLEnum(StoreStatus),
        default=StoreStatus.PENDING,
        nullable=False,
        index=True
    )
    
    # Owner relationship
    owner_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Configuration
    api_credentials: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB,
        nullable=True,
        comment="Encrypted API credentials for store connection"
    )
    
    webhook_url: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        comment="Webhook URL for receiving updates"
    )
    
    settings: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB,
        nullable=True,
        default=dict,
        comment="Store-specific optimization settings"
    )
    
    # Statistics
    total_products: Mapped[int] = mapped_column(
        default=0,
        nullable=False
    )
    
    optimized_products: Mapped[int] = mapped_column(
        default=0,
        nullable=False
    )
    
    last_sync: Mapped[Optional[DateTime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True
    )
    
    # Status flags
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True
    )
    
    sync_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False
    )
    
    # Relationships
    owner: Mapped["User"] = relationship(
        "User",
        back_populates="stores"
    )
    
    products: Mapped[List["Product"]] = relationship(
        "Product",
        back_populates="store",
        cascade="all, delete-orphan"
    )
    
    optimization_jobs: Mapped[List["OptimizationJob"]] = relationship(
        "OptimizationJob",
        back_populates="store",
        cascade="all, delete-orphan"
    )
    
    # Database indexes
    __table_args__ = (
        Index("ix_stores_owner_platform", "owner_id", "platform"),
        Index("ix_stores_status_active", "status", "is_active"),
        Index("ix_stores_domain_active", "domain", "is_active"),
    )
    
    def __repr__(self) -> str:
        return f"<Store(id={self.id}, name={self.name}, domain={self.domain})>"
    
    @property
    def optimization_rate(self) -> float:
        """Calculate percentage of products optimized."""
        if self.total_products == 0:
            return 0.0
        return (self.optimized_products / self.total_products) * 100
    
    def is_connected(self) -> bool:
        """Check if store has valid API connection."""
        return (
            self.status == StoreStatus.ACTIVE and 
            self.is_active and 
            self.api_credentials is not None
        )
    
    def get_default_settings(self) -> Dict[str, Any]:
        """Get default optimization settings for this store."""
        return {
            "auto_optimize": True,
            "schedule_enabled": True,
            "schedule_time": "02:00",  # 2 AM
            "batch_size": 50,
            "models": {
                "title": "anthropic/claude-3-haiku",
                "description": "anthropic/claude-3-haiku",
                "premium": "anthropic/claude-3-opus"
            },
            "seo_settings": {
                "max_title_length": 60,
                "max_meta_length": 155,
                "include_keywords": True,
                "optimize_images": True
            }
        }
    
    def update_product_count(self, total: int, optimized: int) -> None:
        """Update product statistics."""
        self.total_products = total
        self.optimized_products = optimized
        logger.info(
            "Store product count updated",
            store_id=str(self.id),
            total=total,
            optimized=optimized,
            rate=self.optimization_rate
        )

    # ------------------------------------------------------------------
    # Bridging properties for existing Pydantic schemas expecting fields
    # like `store_url` and optional `description` (not persisted).
    # ------------------------------------------------------------------
    @property
    def store_url(self) -> str:
        """Best-effort full URL constructed from domain.

        The request schema currently provides `store_url`; we persist only the
        domain portion. For responses, rebuild a canonical https URL.
        """
        # If the domain already appears to include protocol just return it
        if self.domain.startswith("http://") or self.domain.startswith("https://"):
            return self.domain
        return f"https://{self.domain}" if self.domain else ""

    @property
    def description(self) -> None:  # type: ignore[override]
        """Placeholder for schema compatibility (not stored)."""
        return None
