/**
 * GridSpoke SEO Optimizer - Jobs Management Module
 * Handles optimization job monitoring, progress tracking, and real-time updates
 */

const Jobs = {
    // Module state
    state: {
        jobs: [],
        activeJobs: new Map(),
        currentFilters: { status: 'all' },
        currentSort: { field: 'created_at', direction: 'desc' },
        currentPage: 1,
        pageSize: 20,
        totalJobs: 0,
        isLoading: false,
        autoRefresh: true,
        refreshInterval: null
    },

    // DOM elements
    elements: {
        container: null,
        filterPanel: null,
        jobsList: null
    },

    /**
     * Initialize jobs module
     */
    init() {
        this.bindElements();
        this.setupEventListeners();
        this.setupWebSocketSubscriptions();
        this.loadJobs();
        this.startAutoRefresh();
    },

    /**
     * Bind DOM elements
     */
    bindElements() {
        this.elements.container = document.getElementById('jobs-container');
    },

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Auto-refresh toggle would be handled here
    },

    /**
     * Setup WebSocket subscriptions for real-time updates
     */
    setupWebSocketSubscriptions() {
        if (WebSocketClient) {
            WebSocketClient.subscribe('jobs', this.handleWebSocketUpdate.bind(this));
            WebSocketClient.subscribe('job_progress', this.handleJobProgress.bind(this));
        }
    },

    /**
     * Load jobs from API
     */
    async loadJobs(options = {}) {
        if (this.state.isLoading) return;

        this.state.isLoading = true;
        this.showLoading();

        try {
            // Resolve store ID from dashboard or API
            let storeId = options.store_id;
            if (!storeId) {
                const dashboard = window.dashboardAppInstance;
                storeId = dashboard?.settings?.selectedStoreId;
            }
            if (!storeId) {
                try {
                    const store = await API.getCurrentStore();
                    if (store && store.id) storeId = store.id;
                } catch (_) { /* ignore */ }
            }
            if (!storeId) {
                // Final fallback to known test store
                storeId = 'dbb3d2b0-0be2-41e0-947b-47b809fc5da3';
            }

            const params = {
                store_id: storeId,
                page: this.state.currentPage,
                size: this.state.pageSize,
                sort_by: this.state.currentSort.field,
                sort_direction: this.state.currentSort.direction,
                ...this.state.currentFilters,
                ...options
            };

            const response = await API.getJobs(params);
            
            this.state.jobs = response.items || response.data || [];
            this.state.totalJobs = response.total || this.state.jobs.length;
            
            this.renderJobs();
            
        } catch (error) {
            console.error('Error loading jobs:', error);
            const msg = (error && error.message) ? error.message : 'Failed to load jobs';
            Utils.showToast('Failed to load jobs: ' + msg, 'error');
            this.showError(msg);
        } finally {
            this.state.isLoading = false;
            this.hideLoading();
        }
    },

    /**
     * Render jobs interface
     */
    renderJobs() {
        if (!this.elements.container) return;

        const jobsContent = document.getElementById('jobs-content');
        if (!jobsContent) return;

        if (this.state.jobs.length === 0) {
            jobsContent.innerHTML = `
                <div class="text-center py-8">
                    <p class="text-gray-500">No jobs found</p>
                    <p class="text-sm text-gray-400 mt-2">Start an optimization to see jobs here</p>
                </div>
            `;
            return;
        }

        const tableHtml = `
            <div class="overflow-x-auto">
                <table class="table-gridspoke">
                    <thead>
                        <tr>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="name">
                                Job
                                ${this.getSortIcon('name')}
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="status">
                                Status
                                ${this.getSortIcon('status')}
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="progress">
                                Progress
                                ${this.getSortIcon('progress')}
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="created_at">
                                Created
                                ${this.getSortIcon('created_at')}
                            </th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.renderJobRows()}
                    </tbody>
                </table>
            </div>
        `;

        jobsContent.innerHTML = tableHtml;
        this.bindJobsEvents();
    },

    /**
     * Render active jobs summary
     */
    renderActiveJobsSummary() {
        const activeJobs = this.state.jobs.filter(job => ['pending', 'running'].includes(job.status));
        
        if (activeJobs.length === 0) {
            return '';
        }

        return `
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">Active Jobs</h4>
                <div class="space-y-4">
                    ${activeJobs.map(job => this.renderActiveJobCard(job)).join('')}
                </div>
            </div>
        `;
    },

    /**
     * Render active job card with progress
     */
    renderActiveJobCard(job) {
        const progress = job.progress || 0;
        const progressText = job.progress_text || 'Processing...';
        
        return `
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start mb-3">
                    <div>
                        <h5 class="font-medium text-gray-900">${Utils.escapeHtml(job.name || job.job_type)}</h5>
                        <p class="text-sm text-gray-600">${job.product_count || 0} products</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="status-badge ${Utils.getStatusColor(job.status)}">
                            ${job.status}
                        </span>
                        ${job.status === 'running' ? this.renderJobActions(job.id) : ''}
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div class="mb-2">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                        <span>${progressText}</span>
                        <span>${progress}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            class="bg-gridspoke-primary h-2 rounded-full transition-all duration-300 progress-bar" 
                            style="width: ${progress}%"
                        ></div>
                    </div>
                </div>
                
                <!-- Job Details -->
                <div class="flex justify-between text-xs text-gray-500">
                    <span>Started: ${Utils.formatRelativeTime(job.created_at)}</span>
                    <span>ETA: ${this.calculateETA(job)}</span>
                </div>
            </div>
        `;
    },

    /**
     * Render job actions
     */
    renderJobActions(jobId) {
        return `
            <div class="flex items-center space-x-1">
                <button 
                    onclick="Jobs.pauseJob('${jobId}')" 
                    class="text-gray-400 hover:text-gray-600 p-1"
                    title="Pause Job"
                >
                    <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </button>
                <button 
                    onclick="Jobs.cancelJob('${jobId}')" 
                    class="text-red-400 hover:text-red-600 p-1"
                    title="Cancel Job"
                >
                    <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
    },

    /**
     * Render jobs list
     */
    renderJobsList() {
        if (this.state.jobs.length === 0) {
            return `
                <div class="text-center py-8 text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No jobs found</h3>
                    <p class="mt-1 text-sm text-gray-500">Start optimizing products to see jobs here.</p>
                </div>
            `;
        }

        return this.state.jobs.map(job => this.renderJobRow(job)).join('');
    },

    /**
     * Render individual job row
     */
    renderJobRow(job) {
        return `
            <div class="px-6 py-4 hover:bg-gray-50">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3">
                            <h4 class="text-sm font-medium text-gray-900">
                                ${Utils.escapeHtml(job.name || job.job_type)}
                            </h4>
                            <span class="status-badge ${Utils.getStatusColor(job.status)}">
                                ${job.status}
                            </span>
                        </div>
                        
                        <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                            <span>${job.product_count || 0} products</span>
                            <span>•</span>
                            <span>Created ${Utils.formatRelativeTime(job.created_at)}</span>
                            ${job.completed_at ? `<span>• Completed ${Utils.formatRelativeTime(job.completed_at)}</span>` : ''}
                            ${job.duration ? `<span>• Duration: ${this.formatDuration(job.duration)}</span>` : ''}
                        </div>
                        
                        ${job.error_message ? `
                            <div class="mt-2 text-sm text-red-600">
                                Error: ${Utils.escapeHtml(job.error_message)}
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        ${this.renderJobRowActions(job)}
                    </div>
                </div>
                
                ${job.status === 'running' && job.progress !== undefined ? `
                    <div class="mt-3">
                        <div class="flex justify-between text-xs text-gray-600 mb-1">
                            <span>${job.progress_text || 'Processing...'}</span>
                            <span>${job.progress}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-1">
                            <div 
                                class="bg-gridspoke-primary h-1 rounded-full transition-all duration-300 progress-bar" 
                                style="width: ${job.progress}%"
                            ></div>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    },

    /**
     * Render job row actions
     */
    renderJobRowActions(job) {
        let actions = [];

        // View details action
        actions.push(`
            <button 
                onclick="Jobs.viewJob('${job.id}')"
                class="text-gridspoke-primary hover:text-blue-600 text-sm"
            >
                View
            </button>
        `);

        // Status-specific actions
        if (job.status === 'running') {
            actions.push(`
                <button 
                    onclick="Jobs.cancelJob('${job.id}')"
                    class="text-red-600 hover:text-red-700 text-sm"
                >
                    Cancel
                </button>
            `);
        } else if (job.status === 'failed') {
            actions.push(`
                <button 
                    onclick="Jobs.retryJob('${job.id}')"
                    class="text-gridspoke-primary hover:text-blue-600 text-sm"
                >
                    Retry
                </button>
            `);
        }

        // Logs action
        actions.push(`
            <button 
                onclick="Jobs.viewLogs('${job.id}')"
                class="text-gray-600 hover:text-gray-700 text-sm"
            >
                Logs
            </button>
        `);

        return actions.join('');
    },

    /**
     * Render pagination controls
     */
    renderPaginationControls() {
        const totalPages = Math.ceil(this.state.totalJobs / this.state.pageSize);
        
        if (totalPages <= 1) return '';

        // Similar pagination logic as Products module
        return `
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing ${((this.state.currentPage - 1) * this.state.pageSize) + 1} to ${Math.min(this.state.currentPage * this.state.pageSize, this.state.totalJobs)} of ${this.state.totalJobs} jobs
                </div>
                <div class="flex items-center space-x-1">
                    <!-- Pagination buttons would go here -->
                </div>
            </div>
        `;
    },

    /**
     * Bind jobs event listeners
     */
    bindJobsEvents() {
        // Status filter
        const statusFilter = document.getElementById('job-status-filter');
        if (statusFilter) {
            statusFilter.value = this.state.currentFilters.status || 'all';
            statusFilter.addEventListener('change', (e) => {
                this.state.currentFilters.status = e.target.value === 'all' ? undefined : e.target.value;
                this.state.currentPage = 1;
                this.loadJobs();
            });
        }

        // Auto-refresh toggle
        const autoRefreshToggle = document.getElementById('auto-refresh-toggle');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', (e) => {
                this.state.autoRefresh = e.target.checked;
                if (this.state.autoRefresh) {
                    this.startAutoRefresh();
                } else {
                    this.stopAutoRefresh();
                }
            });
        }

        // Refresh button
        const refreshBtn = document.getElementById('refresh-jobs-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadJobs());
        }
    },

    /**
     * Start auto-refresh
     */
    startAutoRefresh() {
        this.stopAutoRefresh(); // Clear existing interval
        
        if (this.state.autoRefresh) {
            this.state.refreshInterval = setInterval(() => {
                this.loadJobs();
            }, 5000); // Refresh every 5 seconds
        }
    },

    /**
     * Stop auto-refresh
     */
    stopAutoRefresh() {
        if (this.state.refreshInterval) {
            clearInterval(this.state.refreshInterval);
            this.state.refreshInterval = null;
        }
    },

    /**
     * View job details
     */
    async viewJob(jobId) {
        try {
            const job = await API.getJob(jobId);
            this.showJobModal(job);
        } catch (error) {
            Utils.showToast('Failed to load job details', 'error');
        }
    },

    /**
     * Cancel job
     */
    async cancelJob(jobId) {
        if (!confirm('Are you sure you want to cancel this job?')) {
            return;
        }

        try {
            await API.cancelJob(jobId);
            Utils.showToast('Job cancelled successfully', 'success');
            this.loadJobs();
        } catch (error) {
            Utils.showToast('Failed to cancel job', 'error');
        }
    },

    /**
     * Retry failed job
     */
    async retryJob(jobId) {
        try {
            await API.retryJob(jobId);
            Utils.showToast('Job retry started', 'success');
            this.loadJobs();
        } catch (error) {
            Utils.showToast('Failed to retry job', 'error');
        }
    },

    /**
     * View job logs
     */
    async viewLogs(jobId) {
        try {
            const logs = await API.getJobLogs(jobId);
            this.showLogsModal(logs);
        } catch (error) {
            Utils.showToast('Failed to load job logs', 'error');
        }
    },

    /**
     * Calculate ETA for running job
     */
    calculateETA(job) {
        if (!job.progress || job.progress === 0) {
            return 'Calculating...';
        }

        const elapsed = Date.now() - new Date(job.created_at).getTime();
        const totalEstimated = (elapsed / job.progress) * 100;
        const remaining = totalEstimated - elapsed;

        if (remaining <= 0) {
            return 'Almost done';
        }

        const minutes = Math.ceil(remaining / (1000 * 60));
        if (minutes < 60) {
            return `${minutes}m`;
        }
        
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return `${hours}h ${remainingMinutes}m`;
    },

    /**
     * Format job duration
     */
    formatDuration(seconds) {
        if (seconds < 60) {
            return `${seconds}s`;
        }
        
        const minutes = Math.floor(seconds / 60);
        if (minutes < 60) {
            return `${minutes}m ${seconds % 60}s`;
        }
        
        const hours = Math.floor(minutes / 60);
        return `${hours}h ${minutes % 60}m`;
    },

    /**
     * Handle WebSocket job updates
     */
    handleWebSocketUpdate(data) {
        if (data.type === 'job_status_changed') {
            const jobIndex = this.state.jobs.findIndex(j => j.id === data.job_id);
            if (jobIndex !== -1) {
                this.state.jobs[jobIndex] = { ...this.state.jobs[jobIndex], ...data.job };
                this.renderJobs();
            }
        } else if (data.type === 'new_job_created') {
            this.loadJobs(); // Refresh to get new job
        }
    },

    /**
     * Handle WebSocket job progress updates
     */
    handleJobProgress(data) {
        const jobIndex = this.state.jobs.findIndex(j => j.id === data.job_id);
        if (jobIndex !== -1) {
            this.state.jobs[jobIndex].progress = data.progress;
            this.state.jobs[jobIndex].progress_text = data.progress_text;
            
            // Update only progress bars without full re-render
            this.updateJobProgress(data.job_id, data.progress, data.progress_text);
        }
    },

    /**
     * Update job progress without full re-render
     */
    updateJobProgress(jobId, progress, progressText) {
        const progressBars = document.querySelectorAll(`[data-job-id="${jobId}"] .progress-bar`);
        progressBars.forEach(bar => {
            bar.style.width = `${progress}%`;
        });

        const progressTexts = document.querySelectorAll(`[data-job-id="${jobId}"] .progress-text`);
        progressTexts.forEach(text => {
            text.textContent = progressText || 'Processing...';
        });

        const progressPercentages = document.querySelectorAll(`[data-job-id="${jobId}"] .progress-percentage`);
        progressPercentages.forEach(percentage => {
            percentage.textContent = `${progress}%`;
        });
    },

    /**
     * Show loading state
     */
    showLoading() {
        if (this.elements.container) {
            this.elements.container.innerHTML = `
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gridspoke-primary"></div>
                    <p class="mt-2 text-gray-600">Loading jobs...</p>
                </div>
            `;
        }
    },

    /**
     * Hide loading state
     */
    hideLoading() {
        // Loading state is hidden when jobs are rendered
    },

    /**
     * Show error state
     */
    showError(message) {
        if (this.elements.container) {
            this.elements.container.innerHTML = `
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Error loading jobs</h3>
                    <p class="mt-1 text-sm text-gray-500">${message}</p>
                    <button onclick="Jobs.loadJobs()" class="mt-4 bg-gridspoke-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
                        Try Again
                    </button>
                </div>
            `;
        }
    },

    /**
     * Handle update from parent component
     */
    handleUpdate(data) {
        this.handleWebSocketUpdate(data);
    },

    /**
     * Cleanup when module is destroyed
     */
    destroy() {
        this.stopAutoRefresh();
        
        if (WebSocketClient) {
            WebSocketClient.unsubscribe('jobs');
            WebSocketClient.unsubscribe('job_progress');
        }
    }
};

// Export for global access
window.Jobs = Jobs;
