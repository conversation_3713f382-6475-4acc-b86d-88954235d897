#!/usr/bin/env python3
"""
Utility script to create a test user and get authentication token for GridSpoke WordPress plugin.
"""

import requests
import json
import sys
import os

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def create_test_user():
    """Create a test user via the API."""
    print("Creating test user...")
    
    # First try to register a new user
    register_data = {
        "email": "<EMAIL>",
        "password": "wordpress123",
        "full_name": "WordPress Plugin User"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/register",
            json=register_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            print("[OK] Test user created successfully")
            return response.json()
        elif response.status_code == 400:
            print("[INFO] User may already exist, trying to login...")
            return None
        else:
            print(f"[ERROR] Failed to create user: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"[ERROR] Error creating user: {e}")
        return None

def login_user():
    """Login to get authentication token."""
    print("Logging in to get authentication token...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "wordpress123"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("[OK] Login successful")
            print(f"Access Token: {data['access_token']}")
            return data['access_token']
        else:
            print(f"[ERROR] Login failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"[ERROR] Error during login: {e}")
        return None

def test_api_connection(token):
    """Test API connection with the token."""
    print("Testing API connection with token...")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/users/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        if response.status_code == 200:
            print("[OK] API connection test successful")
            user_data = response.json()
            print(f"Authenticated as: {user_data['email']}")
            return True
        else:
            print(f"[ERROR] API connection test failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error testing API connection: {e}")
        return False

def main():
    """Main function to create user and get token."""
    print("GridSpoke WordPress Plugin Setup Utility")
    print("=" * 50)
    
    # Try to create user first
    user_data = create_test_user()
    
    # Login to get token
    token = login_user()
    
    if token:
        # Test the connection
        if test_api_connection(token):
            print("\n[SUCCESS] Setup Complete!")
            print("=" * 50)
            print("Use these credentials in your WordPress plugin:")
            print(f"Email: <EMAIL>")
            print(f"Password: wordpress123")
            print(f"API Token: {token}")
            print(f"API Endpoint: {API_BASE_URL}")
            print("\nNote: Store these securely and update your WordPress plugin settings.")
            return True
        else:
            print("[ERROR] Setup failed - API connection test failed")
            return False
    else:
        print("[ERROR] Setup failed - Could not obtain authentication token")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)