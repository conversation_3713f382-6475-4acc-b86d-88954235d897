#!/bin/bash

# GridSpoke Celery Task Queue Stop Script
# This script stops all Celery components for the GridSpoke Ecommerce SEO Optimizer

set -e

echo "🛑 Stopping GridSpoke Celery Task Queue System..."

# Function to stop service by PID file
stop_service() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "🔸 Stopping $service_name (PID: $pid)..."
            kill -TERM "$pid"
            
            # Wait for graceful shutdown
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                echo "🔸 Force stopping $service_name..."
                kill -KILL "$pid"
            fi
            
            echo "✅ $service_name stopped"
        else
            echo "⚠️  $service_name PID file exists but process not running"
        fi
        rm -f "$pid_file"
    else
        echo "⚠️  $service_name PID file not found"
    fi
}

# Stop Flower
stop_service "Flower" "/tmp/celery_flower.pid"

# Stop Celery Beat
stop_service "Celery Beat" "/tmp/celery_beat.pid"

# Stop Celery Worker
stop_service "Celery Worker" "/tmp/celery_worker.pid"

# Clean up any remaining Celery processes
echo "🧹 Cleaning up remaining Celery processes..."
pkill -f "celery.*worker" 2>/dev/null || true
pkill -f "celery.*beat" 2>/dev/null || true
pkill -f "celery.*flower" 2>/dev/null || true

# Clean up schedule file
rm -f /tmp/celerybeat-schedule

echo ""
echo "✅ GridSpoke Celery Task Queue System stopped successfully!"
echo ""
echo "📁 Log files preserved:"
echo "  - Worker: /tmp/celery_worker.log"
echo "  - Beat: /tmp/celery_beat.log"
echo "  - Flower: /tmp/celery_flower.log"
echo ""
echo "🚀 To start services again, run: ./scripts/start_celery.sh"
echo ""
