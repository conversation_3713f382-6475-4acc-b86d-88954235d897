"""
Base Agent class for AI-powered content generation.

This module provides the foundational architecture for all AI agents used in
GridSpoke's ecommerce SEO optimization service.
"""

import os
import time
import asyncio
import logging
from typing import Dict, Any, Optional, List, Union
from abc import ABC, abstractmethod
from datetime import datetime

import redis
from pydantic import BaseModel, Field
# from mirascope.core import BaseCallParams  # TODO: Fix Mirascope import

from ..core.config import settings


# Configure logging
logger = logging.getLogger(__name__)


class AgentConfig(BaseModel):
    """Configuration for AI agents."""
    
    api_key: str = Field(default_factory=lambda: settings.OPENROUTER_API_KEY)
    base_url: str = Field(default="https://openrouter.ai/api/v1")
    site_url: str = Field(default="https://gridspoke.com")
    site_name: str = Field(default="GridSpoke SEO Optimizer")
    default_model: str = Field(default="openai/gpt-4o")
    max_retries: int = Field(default=3)
    retry_delay: float = Field(default=1.0)
    timeout: int = Field(default=30)
    user_api_key: Optional[str] = Field(default=None)
    custom_model: Optional[str] = Field(default=None)
    
    def get_effective_api_key(self) -> str:
        """Get the effective API key to use (user-provided or system default)."""
        return self.user_api_key if self.user_api_key else self.api_key


class OptimizationResult(BaseModel):
    """Standard result format for optimization operations."""
    
    success: bool
    content: Optional[Dict[str, Any]] = None
    model_used: Optional[str] = None
    tokens_used: Optional[int] = None
    processing_time: Optional[float] = None
    error_message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class AgentState(BaseModel):
    """Base state management for agents."""
    
    agent_id: str
    current_task: Optional[str] = None
    task_progress: float = 0.0
    total_tokens_used: int = 0
    total_requests: int = 0
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    error_count: int = 0
    
    def update_progress(self, progress: float):
        """Update task progress."""
        self.task_progress = min(100.0, max(0.0, progress))
        self.last_activity = datetime.utcnow()
    
    def increment_usage(self, tokens: int = 0):
        """Increment usage statistics."""
        self.total_tokens_used += tokens
        self.total_requests += 1
        self.last_activity = datetime.utcnow()
    
    def increment_error(self):
        """Increment error count."""
        self.error_count += 1
        self.last_activity = datetime.utcnow()


class BaseAgent(ABC):
    """
    Abstract base class for all AI agents.
    
    Provides common functionality including:
    - OpenRouter integration
    - Retry logic with exponential backoff
    - Rate limiting
    - Token usage tracking
    - Error handling and logging
    - State management
    """
    
    def __init__(self, config: Optional[AgentConfig] = None):
        """Initialize the base agent."""
        self.config = config or AgentConfig()
        self.state = AgentState(agent_id=f"{self.__class__.__name__}_{int(time.time())}")
        
        # Initialize Redis for caching and state management
        try:
            self.redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
            self.redis_client.ping()  # Test connection
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}. Caching will be disabled.")
            self.redis_client = None
        
        # Setup logging
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def get_openrouter_headers(self) -> Dict[str, str]:
        """Get OpenRouter-specific headers for API attribution."""
        headers = {
            "HTTP-Referer": self.config.site_url,
            "X-Title": self.config.site_name,
        }
        
        # Use user's API key if provided
        if self.config.user_api_key:
            headers["Authorization"] = f"Bearer {self.config.user_api_key}"
        elif self.config.api_key:
            headers["Authorization"] = f"Bearer {self.config.api_key}"
            
        return headers
    
    async def call_with_retry(
        self,
        func,
        *args,
        max_retries: Optional[int] = None,
        **kwargs
    ) -> Any:
        """
        Execute a function with exponential backoff retry logic.
        
        Args:
            func: The function to execute
            *args: Positional arguments for the function
            max_retries: Maximum number of retries (defaults to config)
            **kwargs: Keyword arguments for the function
            
        Returns:
            Function result
            
        Raises:
            Exception: If all retries fail
        """
        max_retries = max_retries or self.config.max_retries
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                start_time = time.time()
                result = await func(*args, **kwargs)
                processing_time = time.time() - start_time
                
                # Log successful call
                self.logger.debug(f"Successful call in {processing_time:.2f}s on attempt {attempt + 1}")
                
                return result
                
            except Exception as e:
                last_exception = e
                self.state.increment_error()
                
                if attempt < max_retries:
                    # Calculate exponential backoff delay
                    delay = self.config.retry_delay * (2 ** attempt)
                    self.logger.warning(
                        f"Attempt {attempt + 1} failed: {e}. Retrying in {delay:.2f}s..."
                    )
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"All {max_retries + 1} attempts failed. Last error: {e}")
        
        # All retries failed
        raise last_exception
    
    def cache_key(self, prefix: str, data: Dict[str, Any]) -> str:
        """Generate a cache key from input data."""
        import hashlib
        import json
        
        # Create a stable hash from the data
        data_str = json.dumps(data, sort_keys=True)
        data_hash = hashlib.md5(data_str.encode()).hexdigest()
        
        return f"{prefix}:{self.__class__.__name__}:{data_hash}"
    
    async def get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Retrieve cached result if available."""
        if not self.redis_client:
            return None
        
        try:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                import json
                return json.loads(cached_data)
        except Exception as e:
            self.logger.warning(f"Cache retrieval failed: {e}")
        
        return None
    
    async def cache_result(self, cache_key: str, result: Dict[str, Any], ttl: int = 3600):
        """Cache the result with TTL."""
        if not self.redis_client:
            return
        
        try:
            import json
            self.redis_client.setex(cache_key, ttl, json.dumps(result))
        except Exception as e:
            self.logger.warning(f"Cache storage failed: {e}")
    
    async def track_usage(self, model: str, tokens_used: int, processing_time: float):
        """Track token usage and performance metrics."""
        self.state.increment_usage(tokens_used)
        
        # Store metrics in Redis for monitoring
        if self.redis_client:
            try:
                metrics_key = f"metrics:{self.__class__.__name__}:daily:{datetime.utcnow().strftime('%Y-%m-%d')}"
                pipe = self.redis_client.pipeline()
                pipe.hincrby(metrics_key, "total_requests", 1)
                pipe.hincrby(metrics_key, "total_tokens", tokens_used)
                pipe.hincrbyfloat(metrics_key, "total_time", processing_time)
                pipe.hset(metrics_key, "last_model", model)
                pipe.expire(metrics_key, 86400 * 7)  # Keep for 7 days
                pipe.execute()
            except Exception as e:
                self.logger.warning(f"Metrics tracking failed: {e}")
    
    def select_model(self, content_type: str, complexity: str = "standard") -> str:
        """
        Select the optimal model based on content type and complexity.
        
        Args:
            content_type: Type of content being generated
            complexity: Complexity level (economy, standard, premium)
            
        Returns:
            Model identifier for OpenRouter
        """
        # If user has specified a custom model, use it
        if self.custom_model:
            return self.custom_model
            
        model_tiers = {
            "economy": "openai/gpt-3.5-turbo",
            "standard": "openai/gpt-4o",
            "premium": "anthropic/claude-3-opus"
        }
        
        # Default model selection logic
        if content_type in ["product_description", "blog_post"] and complexity == "premium":
            return model_tiers["premium"]
        elif content_type in ["title", "meta_description", "keywords"]:
            return model_tiers["standard"]
        else:
            return model_tiers["economy"]
    
    @abstractmethod
    async def process(self, input_data: Dict[str, Any], **kwargs) -> OptimizationResult:
        """
        Abstract method that must be implemented by all agents.
        
        Args:
            input_data: The input data to process
            **kwargs: Additional processing parameters
            
        Returns:
            OptimizationResult with the processing outcome
        """
        pass
    
    def get_state(self) -> Dict[str, Any]:
        """Get current agent state."""
        return self.state.model_dump()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the agent."""
        health_status = {
            "agent_class": self.__class__.__name__,
            "status": "healthy",
            "redis_connected": self.redis_client is not None,
            "last_activity": self.state.last_activity.isoformat(),
            "total_requests": self.state.total_requests,
            "total_tokens": self.state.total_tokens_used,
            "error_count": self.state.error_count,
            "error_rate": self.state.error_count / max(1, self.state.total_requests)
        }
        
        # Test Redis connection if available
        if self.redis_client:
            try:
                self.redis_client.ping()
                health_status["redis_connected"] = True
            except Exception:
                health_status["redis_connected"] = False
                health_status["status"] = "degraded"
        
        # Check error rate
        if health_status["error_rate"] > 0.1:  # More than 10% errors
            health_status["status"] = "degraded"
        
        return health_status
