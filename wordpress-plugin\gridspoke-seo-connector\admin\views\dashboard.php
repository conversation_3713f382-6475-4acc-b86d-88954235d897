<?php
/**
 * Dashboard page template
 */

if (!defined('ABSPATH')) {
    exit;
}

// Display admin messages
GridSpoke_Admin::display_admin_message();

$settings = GridSpoke_SEO_Connector::get_settings();
// Modern configured-state logic: consider API key OR tokens OR credentials OR linked store
$has_api_key = !empty($settings['api_key']);
$has_tokens = !empty(get_transient('gridspoke_access_token')) || !empty(get_option('gridspoke_refresh_token'));
$has_credentials = !empty($settings['email']) && !empty($settings['password']);
$has_store = !empty(get_option('gridspoke_store_id'));
$configured = $has_api_key || $has_tokens || $has_credentials || $has_store;
?>

<div class="wrap">
    <h1><?php esc_html_e('GridSpoke SEO Dashboard', 'gridspoke-seo'); ?></h1>
    
    <div class="gridspoke-dashboard-grid">
        <!-- Quick Stats -->
        <div class="gridspoke-dashboard-card">
            <h2><?php esc_html_e('Optimization Statistics', 'gridspoke-seo'); ?></h2>
            <div class="gridspoke-stats-grid">
                <div class="gridspoke-stat-item">
                    <span class="gridspoke-stat-number" id="completed-optimizations"><?php echo esc_html($optimization_stats['completed_optimizations'] ?? 0); ?></span>
                    <span class="gridspoke-stat-label"><?php esc_html_e('Completed', 'gridspoke-seo'); ?></span>
                </div>
                <div class="gridspoke-stat-item">
                    <span class="gridspoke-stat-number" id="pending-optimizations"><?php echo esc_html($optimization_stats['pending_optimizations'] ?? 0); ?></span>
                    <span class="gridspoke-stat-label"><?php esc_html_e('Pending', 'gridspoke-seo'); ?></span>
                </div>
                <div class="gridspoke-stat-item">
                    <span class="gridspoke-stat-number" id="failed-optimizations"><?php echo esc_html($optimization_stats['failed_optimizations'] ?? 0); ?></span>
                    <span class="gridspoke-stat-label"><?php esc_html_e('Failed', 'gridspoke-seo'); ?></span>
                </div>
                <div class="gridspoke-stat-item">
                    <span class="gridspoke-stat-number" id="total-optimizations"><?php echo esc_html($optimization_stats['total_optimizations'] ?? 0); ?></span>
                    <span class="gridspoke-stat-label"><?php esc_html_e('Total', 'gridspoke-seo'); ?></span>
                </div>
            </div>
            
            <?php if ($configured): ?>
                <button type="button" id="refresh-stats" class="button button-secondary">
                    <?php esc_html_e('Refresh Statistics', 'gridspoke-seo'); ?>
                </button>
            <?php endif; ?>
        </div>
        
        <!-- Quick Actions -->
        <div class="gridspoke-dashboard-card">
            <h2><?php esc_html_e('Quick Actions', 'gridspoke-seo'); ?></h2>
            <div class="gridspoke-actions-grid">
                <?php if ($configured): ?>
                    <button type="button" id="sync-all-products" class="button button-primary">
                        <?php esc_html_e('Sync All Products', 'gridspoke-seo'); ?>
                    </button>
                    
                    <?php if (class_exists('WooCommerce')): ?>
                        <a href="<?php echo esc_url(admin_url('edit.php?post_type=product')); ?>" class="button button-secondary">
                            <?php esc_html_e('Manage WooCommerce Products', 'gridspoke-seo'); ?>
                        </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo esc_url(admin_url('admin.php?page=gridspoke-seo-logs')); ?>" class="button button-secondary">
                        <?php esc_html_e('View Logs', 'gridspoke-seo'); ?>
                    </a>
                    
                    <a href="<?php echo esc_url(admin_url('admin.php?page=gridspoke-seo-tools')); ?>" class="button button-secondary">
                        <?php esc_html_e('Tools & Utilities', 'gridspoke-seo'); ?>
                    </a>
                <?php else: ?>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=gridspoke-seo-settings')); ?>" class="button button-primary">
                        <?php esc_html_e('Configure Settings', 'gridspoke-seo'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="gridspoke-dashboard-card gridspoke-full-width">
            <h2><?php esc_html_e('Recent Activity', 'gridspoke-seo'); ?></h2>
            <div id="recent-activity">
                <?php if (!empty($log_stats['recent_errors'])): ?>
                    <h3><?php esc_html_e('Recent Errors', 'gridspoke-seo'); ?></h3>
                    <ul class="gridspoke-error-list">
                        <?php foreach ($log_stats['recent_errors'] as $error): ?>
                            <li>
                                <span class="gridspoke-error-time"><?php echo esc_html(human_time_diff(strtotime($error['timestamp']))); ?> ago</span>
                                <span class="gridspoke-error-message"><?php echo esc_html($error['message']); ?></span>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <p><?php esc_html_e('No recent errors. Everything looks good!', 'gridspoke-seo'); ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="gridspoke-dashboard-card">
            <h2><?php esc_html_e('System Status', 'gridspoke-seo'); ?></h2>
            <div class="gridspoke-status-grid">
                <div class="gridspoke-status-item">
                    <span class="gridspoke-status-label"><?php esc_html_e('API Connection', 'gridspoke-seo'); ?></span>
                    <span class="gridspoke-status-value" id="api-status">
                        <?php if ($configured): ?>
                            <span class="gridspoke-status-unknown"><?php esc_html_e('Testing...', 'gridspoke-seo'); ?></span>
                        <?php else: ?>
                            <span class="gridspoke-status-error"><?php esc_html_e('Not Configured', 'gridspoke-seo'); ?></span>
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="gridspoke-status-item">
                    <span class="gridspoke-status-label"><?php esc_html_e('WooCommerce', 'gridspoke-seo'); ?></span>
                    <span class="gridspoke-status-value">
                        <?php if (class_exists('WooCommerce')): ?>
                            <span class="gridspoke-status-success"><?php esc_html_e('Active', 'gridspoke-seo'); ?></span>
                        <?php else: ?>
                            <span class="gridspoke-status-warning"><?php esc_html_e('Not Installed', 'gridspoke-seo'); ?></span>
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="gridspoke-status-item">
                    <span class="gridspoke-status-label"><?php esc_html_e('SureCart', 'gridspoke-seo'); ?></span>
                    <span class="gridspoke-status-value">
                        <?php if (defined('SURECART_PLUGIN_FILE')): ?>
                            <span class="gridspoke-status-success"><?php esc_html_e('Active', 'gridspoke-seo'); ?></span>
                        <?php else: ?>
                            <span class="gridspoke-status-warning"><?php esc_html_e('Not Installed', 'gridspoke-seo'); ?></span>
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="gridspoke-status-item">
                    <span class="gridspoke-status-label"><?php esc_html_e('Auto Sync', 'gridspoke-seo'); ?></span>
                    <span class="gridspoke-status-value">
                        <?php if (!empty($settings['auto_sync'])): ?>
                            <span class="gridspoke-status-success"><?php esc_html_e('Enabled', 'gridspoke-seo'); ?></span>
                        <?php else: ?>
                            <span class="gridspoke-status-warning"><?php esc_html_e('Disabled', 'gridspoke-seo'); ?></span>
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            
            <?php if ($configured): ?>
                <button type="button" id="test-api-connection" class="button button-secondary">
                    <?php esc_html_e('Test API Connection', 'gridspoke-seo'); ?>
                </button>
            <?php endif; ?>
        </div>
        
        <!-- Optimization Chart -->
        <?php if ($configured): ?>
        <div class="gridspoke-dashboard-card gridspoke-full-width">
            <h2><?php esc_html_e('Optimization Trends', 'gridspoke-seo'); ?></h2>
            <canvas id="optimization-chart" width="400" height="200"></canvas>
        </div>
        <?php endif; ?>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Auto-refresh stats every 30 seconds if API is configured
    <?php if ($configured): ?>
    setInterval(function() {
        refreshStats();
    }, 30000);
    
    // Test API connection on page load
    testApiConnection();
    <?php endif; ?>
    
    function refreshStats() {
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_get_optimization_stats',
                nonce: gridspoke_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#completed-optimizations').text(response.data.completed_optimizations || 0);
                    $('#pending-optimizations').text(response.data.pending_optimizations || 0);
                    $('#failed-optimizations').text(response.data.failed_optimizations || 0);
                    $('#total-optimizations').text(response.data.total_optimizations || 0);
                }
            }
        });
    }
    
    function testApiConnection() {
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_test_connection',
                nonce: gridspoke_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#api-status').html('<span class="gridspoke-status-success"><?php esc_html_e('Connected', 'gridspoke-seo'); ?></span>');
                } else {
                    $('#api-status').html('<span class="gridspoke-status-error"><?php esc_html_e('Connection Failed', 'gridspoke-seo'); ?></span>');
                }
            },
            error: function() {
                $('#api-status').html('<span class="gridspoke-status-error"><?php esc_html_e('Connection Failed', 'gridspoke-seo'); ?></span>');
            }
        });
    }
});
</script>
