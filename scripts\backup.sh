#!/bin/bash

# GridSpoke Backup Script
# Usage: ./scripts/backup.sh [--upload-to-s3]

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$PROJECT_DIR/backups"
DATE=$(date +%Y-%m-%d)
TIMESTAMP=$(date +%H%M%S)
UPLOAD_TO_S3=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --upload-to-s3)
            UPLOAD_TO_S3=true
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --upload-to-s3  Upload backup to S3 after creation"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check if .env.prod exists
    if [[ ! -f "$PROJECT_DIR/.env.prod" ]]; then
        log_error ".env.prod file not found."
        exit 1
    fi
    
    # Source environment variables
    source "$PROJECT_DIR/.env.prod"
    
    # Check if services are running
    if ! docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" ps | grep -q "Up"; then
        log_error "GridSpoke services are not running."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Function to create backup directory structure
create_backup_structure() {
    log_info "Creating backup directory structure..."
    
    mkdir -p "$BACKUP_DIR/$DATE"
    mkdir -p "$BACKUP_DIR/$DATE/database"
    mkdir -p "$BACKUP_DIR/$DATE/redis"
    mkdir -p "$BACKUP_DIR/$DATE/static"
    mkdir -p "$BACKUP_DIR/$DATE/config"
    
    log_success "Backup directory structure created"
}

# Function to backup database
backup_database() {
    log_info "Backing up PostgreSQL database..."
    
    source "$PROJECT_DIR/.env.prod"
    
    # Create database dump
    docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" exec -T db pg_dump \
        -U "$POSTGRES_USER" \
        -d "$POSTGRES_DB" \
        --no-password \
        --clean \
        --if-exists \
        --verbose \
        > "$BACKUP_DIR/$DATE/database/gridspoke_db_$TIMESTAMP.sql"
    
    # Create schema-only dump
    docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" exec -T db pg_dump \
        -U "$POSTGRES_USER" \
        -d "$POSTGRES_DB" \
        --no-password \
        --schema-only \
        --clean \
        --if-exists \
        > "$BACKUP_DIR/$DATE/database/gridspoke_schema_$TIMESTAMP.sql"
    
    # Compress database dumps
    gzip "$BACKUP_DIR/$DATE/database/gridspoke_db_$TIMESTAMP.sql"
    gzip "$BACKUP_DIR/$DATE/database/gridspoke_schema_$TIMESTAMP.sql"
    
    # Create database stats
    docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" exec -T db psql \
        -U "$POSTGRES_USER" \
        -d "$POSTGRES_DB" \
        -c "SELECT 
            schemaname,
            tablename,
            n_tup_ins as inserts,
            n_tup_upd as updates,
            n_tup_del as deletes,
            n_live_tup as live_tuples,
            n_dead_tup as dead_tuples
        FROM pg_stat_user_tables
        ORDER BY n_live_tup DESC;" \
        > "$BACKUP_DIR/$DATE/database/table_stats_$TIMESTAMP.txt"
    
    log_success "Database backup completed"
}

# Function to backup Redis data
backup_redis() {
    log_info "Backing up Redis data..."
    
    # Create Redis dump
    docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" exec -T redis redis-cli --rdb - > "$BACKUP_DIR/$DATE/redis/redis_dump_$TIMESTAMP.rdb"
    
    # Get Redis info
    docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" exec -T redis redis-cli INFO > "$BACKUP_DIR/$DATE/redis/redis_info_$TIMESTAMP.txt"
    
    # Get Redis config
    docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" exec -T redis redis-cli CONFIG GET "*" > "$BACKUP_DIR/$DATE/redis/redis_config_$TIMESTAMP.txt"
    
    # Compress Redis dump
    gzip "$BACKUP_DIR/$DATE/redis/redis_dump_$TIMESTAMP.rdb"
    
    log_success "Redis backup completed"
}

# Function to backup static files and media
backup_static_files() {
    log_info "Backing up static files and media..."
    
    # Copy static files from Docker volume
    docker run --rm \
        -v gridspoke_static_volume:/source:ro \
        -v "$BACKUP_DIR/$DATE/static:/backup" \
        alpine:latest \
        sh -c "cd /source && tar czf /backup/static_files_$TIMESTAMP.tar.gz ."
    
    # Copy media files from Docker volume
    docker run --rm \
        -v gridspoke_media_volume:/source:ro \
        -v "$BACKUP_DIR/$DATE/static:/backup" \
        alpine:latest \
        sh -c "cd /source && tar czf /backup/media_files_$TIMESTAMP.tar.gz ."
    
    log_success "Static files backup completed"
}

# Function to backup configuration files
backup_configuration() {
    log_info "Backing up configuration files..."
    
    # Copy configuration files
    cp "$PROJECT_DIR/.env.prod" "$BACKUP_DIR/$DATE/config/env.prod.backup"
    cp "$PROJECT_DIR/docker-compose.prod.yml" "$BACKUP_DIR/$DATE/config/"
    cp -r "$PROJECT_DIR/nginx" "$BACKUP_DIR/$DATE/config/"
    
    # Copy SSL certificates if they exist
    if [[ -d "$PROJECT_DIR/certbot/conf" ]]; then
        cp -r "$PROJECT_DIR/certbot/conf" "$BACKUP_DIR/$DATE/config/ssl_certificates"
    fi
    
    # Create system info
    cat > "$BACKUP_DIR/$DATE/config/system_info_$TIMESTAMP.txt" << EOF
Backup Date: $(date)
GridSpoke Version: $(grep "VERSION=" "$PROJECT_DIR/.env.prod" | cut -d'=' -f2 || echo "unknown")
Docker Version: $(docker --version)
Docker Compose Version: $(docker-compose --version)
Host OS: $(uname -a)

Running Containers:
$(docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" ps)

Container Images:
$(docker-compose -f "$PROJECT_DIR/docker-compose.prod.yml" images)

Volume Usage:
$(docker system df -v | grep gridspoke || echo "No GridSpoke volumes found")
EOF
    
    log_success "Configuration backup completed"
}

# Function to create backup manifest
create_backup_manifest() {
    log_info "Creating backup manifest..."
    
    cat > "$BACKUP_DIR/$DATE/MANIFEST.txt" << EOF
GridSpoke Backup Manifest
========================

Backup Date: $(date)
Backup Location: $BACKUP_DIR/$DATE

Contents:
---------
Database:
  - Full database dump: database/gridspoke_db_$TIMESTAMP.sql.gz
  - Schema only dump: database/gridspoke_schema_$TIMESTAMP.sql.gz
  - Table statistics: database/table_stats_$TIMESTAMP.txt

Redis:
  - Redis dump: redis/redis_dump_$TIMESTAMP.rdb.gz
  - Redis info: redis/redis_info_$TIMESTAMP.txt
  - Redis config: redis/redis_config_$TIMESTAMP.txt

Static Files:
  - Static files: static/static_files_$TIMESTAMP.tar.gz
  - Media files: static/media_files_$TIMESTAMP.tar.gz

Configuration:
  - Environment variables: config/env.prod.backup
  - Docker Compose config: config/docker-compose.prod.yml
  - Nginx configuration: config/nginx/
  - SSL certificates: config/ssl_certificates/ (if exists)
  - System information: config/system_info_$TIMESTAMP.txt

File Checksums:
--------------
$(find "$BACKUP_DIR/$DATE" -type f -exec sha256sum {} \; | sort)

Total Backup Size: $(du -sh "$BACKUP_DIR/$DATE" | cut -f1)
EOF
    
    log_success "Backup manifest created"
}

# Function to create compressed archive
create_archive() {
    log_info "Creating compressed backup archive..."
    
    cd "$BACKUP_DIR"
    tar czf "gridspoke_backup_$DATE-$TIMESTAMP.tar.gz" "$DATE/"
    
    # Calculate and save checksum
    sha256sum "gridspoke_backup_$DATE-$TIMESTAMP.tar.gz" > "gridspoke_backup_$DATE-$TIMESTAMP.tar.gz.sha256"
    
    # Create a latest backup symlink
    ln -sf "gridspoke_backup_$DATE-$TIMESTAMP.tar.gz" "gridspoke_backup_latest.tar.gz"
    ln -sf "gridspoke_backup_$DATE-$TIMESTAMP.tar.gz.sha256" "gridspoke_backup_latest.tar.gz.sha256"
    
    log_success "Backup archive created: gridspoke_backup_$DATE-$TIMESTAMP.tar.gz"
}

# Function to upload to S3
upload_to_s3() {
    if [[ $UPLOAD_TO_S3 == false ]]; then
        return 0
    fi
    
    log_info "Uploading backup to S3..."
    
    source "$PROJECT_DIR/.env.prod"
    
    # Check if AWS CLI is available
    if ! command -v aws >/dev/null 2>&1; then
        log_warning "AWS CLI not found. Skipping S3 upload."
        return 0
    fi
    
    # Configure AWS credentials
    export AWS_ACCESS_KEY_ID="$S3_ACCESS_KEY"
    export AWS_SECRET_ACCESS_KEY="$S3_SECRET_KEY"
    export AWS_DEFAULT_REGION="$S3_REGION"
    
    # Upload backup archive
    aws s3 cp "$BACKUP_DIR/gridspoke_backup_$DATE-$TIMESTAMP.tar.gz" \
        "s3://$S3_BACKUP_BUCKET/backups/gridspoke_backup_$DATE-$TIMESTAMP.tar.gz" \
        --storage-class STANDARD_IA
    
    # Upload checksum
    aws s3 cp "$BACKUP_DIR/gridspoke_backup_$DATE-$TIMESTAMP.tar.gz.sha256" \
        "s3://$S3_BACKUP_BUCKET/backups/gridspoke_backup_$DATE-$TIMESTAMP.tar.gz.sha256"
    
    # Update latest backup
    aws s3 cp "$BACKUP_DIR/gridspoke_backup_$DATE-$TIMESTAMP.tar.gz" \
        "s3://$S3_BACKUP_BUCKET/backups/gridspoke_backup_latest.tar.gz"
    
    log_success "Backup uploaded to S3"
}

# Function to cleanup old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups..."
    
    # Keep last 7 daily backups locally
    find "$BACKUP_DIR" -name "gridspoke_backup_*.tar.gz" -mtime +7 -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "gridspoke_backup_*.tar.gz.sha256" -mtime +7 -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "20*" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
    
    # Cleanup S3 backups if enabled
    if [[ $UPLOAD_TO_S3 == true ]] && command -v aws >/dev/null 2>&1; then
        source "$PROJECT_DIR/.env.prod"
        export AWS_ACCESS_KEY_ID="$S3_ACCESS_KEY"
        export AWS_SECRET_ACCESS_KEY="$S3_SECRET_KEY"
        export AWS_DEFAULT_REGION="$S3_REGION"
        
        # Keep last 30 backups in S3
        aws s3 ls "s3://$S3_BACKUP_BUCKET/backups/" --recursive | \
            awk '{print $4}' | \
            grep "gridspoke_backup_" | \
            head -n -30 | \
            xargs -I {} aws s3 rm "s3://$S3_BACKUP_BUCKET/{}" 2>/dev/null || true
    fi
    
    log_success "Old backups cleaned up"
}

# Function to verify backup integrity
verify_backup() {
    log_info "Verifying backup integrity..."
    
    # Verify archive can be extracted
    if tar -tzf "$BACKUP_DIR/gridspoke_backup_$DATE-$TIMESTAMP.tar.gz" >/dev/null 2>&1; then
        log_success "Backup archive is valid"
    else
        log_error "Backup archive is corrupted!"
        exit 1
    fi
    
    # Verify checksum
    if sha256sum -c "$BACKUP_DIR/gridspoke_backup_$DATE-$TIMESTAMP.tar.gz.sha256" >/dev/null 2>&1; then
        log_success "Backup checksum is valid"
    else
        log_error "Backup checksum verification failed!"
        exit 1
    fi
    
    log_success "Backup integrity verified"
}

# Function to display backup summary
show_summary() {
    local backup_size=$(du -sh "$BACKUP_DIR/gridspoke_backup_$DATE-$TIMESTAMP.tar.gz" | cut -f1)
    local backup_path="$BACKUP_DIR/gridspoke_backup_$DATE-$TIMESTAMP.tar.gz"
    
    log_success "Backup completed successfully!"
    
    echo ""
    echo "📦 Backup Summary:"
    echo "   File: gridspoke_backup_$DATE-$TIMESTAMP.tar.gz"
    echo "   Size: $backup_size"
    echo "   Path: $backup_path"
    echo ""
    echo "🔐 Security:"
    echo "   SHA256: $(cat "$backup_path.sha256" | cut -d' ' -f1)"
    echo ""
    echo "📁 Contents:"
    echo "   ✓ PostgreSQL database dump"
    echo "   ✓ Redis data dump"
    echo "   ✓ Static files and media"
    echo "   ✓ Configuration files"
    echo "   ✓ SSL certificates (if available)"
    echo ""
    
    if [[ $UPLOAD_TO_S3 == true ]]; then
        echo "☁️  S3 Upload: Completed"
    else
        echo "💾 Local Backup Only"
    fi
    
    echo ""
    echo "🔧 Restore command:"
    echo "   ./scripts/restore.sh $backup_path"
    echo ""
}

# Main backup function
main() {
    cd "$PROJECT_DIR"
    
    log_info "Starting GridSpoke backup process..."
    
    check_prerequisites
    create_backup_structure
    backup_database
    backup_redis
    backup_static_files
    backup_configuration
    create_backup_manifest
    create_archive
    upload_to_s3
    verify_backup
    cleanup_old_backups
    show_summary
    
    log_success "Backup process completed successfully!"
}

# Trap errors
trap 'log_error "Backup failed! Check the logs above for details."' ERR

# Run main function
main "$@"
