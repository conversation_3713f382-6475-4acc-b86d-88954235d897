# GridSpoke Health Check System
# Phase 8: Comprehensive health monitoring for all services

import asyncio
import time
import logging
from typing import Dict, Any, List, Optional
from enum import Enum
from pydantic import BaseModel
from datetime import datetime, timedelta
import aioredis
import httpx
from sqlalchemy import text
from api.core.database import get_db_session
from api.core.config import settings

logger = logging.getLogger(__name__)

# =============================================================================
# HEALTH CHECK MODELS
# =============================================================================

class HealthStatus(str, Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

class ComponentHealth(BaseModel):
    """Health status for a single component."""
    name: str
    status: HealthStatus
    message: str
    last_checked: datetime
    response_time_ms: Optional[float] = None
    details: Optional[Dict[str, Any]] = None

class SystemHealth(BaseModel):
    """Overall system health status."""
    status: HealthStatus
    timestamp: datetime
    components: List[ComponentHealth]
    uptime_seconds: float
    version: str = "1.0.0"

# =============================================================================
# INDIVIDUAL HEALTH CHECKERS
# =============================================================================

class HealthChecker:
    """Base class for health checkers."""
    
    def __init__(self, name: str, timeout: float = 5.0):
        self.name = name
        self.timeout = timeout
    
    async def check(self) -> ComponentHealth:
        """Perform health check and return status."""
        start_time = time.time()
        
        try:
            details = await asyncio.wait_for(
                self._perform_check(), 
                timeout=self.timeout
            )
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name=self.name,
                status=HealthStatus.HEALTHY,
                message="OK",
                last_checked=datetime.utcnow(),
                response_time_ms=response_time,
                details=details
            )
            
        except asyncio.TimeoutError:
            return ComponentHealth(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Timeout after {self.timeout}s",
                last_checked=datetime.utcnow(),
                response_time_ms=(time.time() - start_time) * 1000
            )
            
        except Exception as e:
            logger.error(f"Health check failed for {self.name}: {e}")
            return ComponentHealth(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=str(e),
                last_checked=datetime.utcnow(),
                response_time_ms=(time.time() - start_time) * 1000
            )
    
    async def _perform_check(self) -> Dict[str, Any]:
        """Override this method in subclasses."""
        raise NotImplementedError

class DatabaseHealthChecker(HealthChecker):
    """Check PostgreSQL database health."""
    
    def __init__(self):
        super().__init__("database", timeout=10.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        async with get_db_session() as session:
            # Test basic connectivity
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
            
            # Check database size and connections
            db_stats = await session.execute(text("""
                SELECT 
                    pg_database_size(current_database()) as db_size_bytes,
                    (SELECT count(*) FROM pg_stat_activity) as total_connections,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections
            """))
            
            stats = db_stats.fetchone()
            
            # Check for recent activity
            table_stats = await session.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins + n_tup_upd + n_tup_del as total_operations
                FROM pg_stat_user_tables 
                ORDER BY total_operations DESC 
                LIMIT 5
            """))
            
            recent_activity = [
                {
                    "table": f"{row.schemaname}.{row.tablename}",
                    "operations": row.total_operations
                }
                for row in table_stats.fetchall()
            ]
            
            return {
                "database_size_mb": round(stats.db_size_bytes / 1024 / 1024, 2),
                "total_connections": stats.total_connections,
                "active_connections": stats.active_connections,
                "recent_activity": recent_activity
            }

class RedisHealthChecker(HealthChecker):
    """Check Redis health."""
    
    def __init__(self):
        super().__init__("redis", timeout=5.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        redis = aioredis.from_url(settings.REDIS_URL)
        
        try:
            # Test basic connectivity
            await redis.ping()
            
            # Get Redis info
            info = await redis.info()
            memory_info = await redis.info("memory")
            
            # Test set/get operation
            test_key = "health_check_test"
            test_value = str(time.time())
            await redis.set(test_key, test_value, ex=60)
            retrieved_value = await redis.get(test_key)
            
            assert retrieved_value.decode() == test_value
            
            return {
                "redis_version": info.get("redis_version"),
                "uptime_seconds": info.get("uptime_in_seconds"),
                "connected_clients": info.get("connected_clients"),
                "used_memory_mb": round(memory_info.get("used_memory", 0) / 1024 / 1024, 2),
                "memory_usage_ratio": memory_info.get("used_memory_rss") / memory_info.get("total_system_memory", 1)
            }
            
        finally:
            await redis.close()

class CeleryHealthChecker(HealthChecker):
    """Check Celery worker health."""
    
    def __init__(self):
        super().__init__("celery", timeout=10.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        from celery import Celery
        
        # Import Celery app (adjust import path as needed)
        try:
            from workers.celery_app import celery_app
        except ImportError:
            # Fallback for development
            celery_app = Celery('gridspoke')
            celery_app.config_from_object('workers.celeryconfig')
        
        # Get active workers
        inspect = celery_app.control.inspect()
        
        # Check if workers are responding
        stats = inspect.stats()
        active_queues = inspect.active_queues()
        active_tasks = inspect.active()
        
        if not stats:
            raise Exception("No Celery workers are responding")
        
        worker_count = len(stats)
        total_active_tasks = sum(len(tasks) for tasks in (active_tasks or {}).values())
        
        # Get queue information from Redis
        redis = aioredis.from_url(settings.REDIS_URL)
        try:
            queue_lengths = {}
            for queue in ['default', 'optimization', 'content_generation', 'wordpress_sync']:
                length = await redis.llen(f"celery:{queue}")
                queue_lengths[queue] = length
        finally:
            await redis.close()
        
        return {
            "active_workers": worker_count,
            "active_tasks": total_active_tasks,
            "queue_lengths": queue_lengths,
            "worker_details": list(stats.keys()) if stats else []
        }

class OpenRouterHealthChecker(HealthChecker):
    """Check OpenRouter API health."""
    
    def __init__(self):
        super().__init__("openrouter_api", timeout=15.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        if not settings.OPENROUTER_API_KEY:
            raise Exception("OpenRouter API key not configured")
        
        async with httpx.AsyncClient() as client:
            # Test API connectivity with a simple models list request
            response = await client.get(
                "https://openrouter.ai/api/v1/models",
                headers={
                    "Authorization": f"Bearer {settings.OPENROUTER_API_KEY}",
                    "HTTP-Referer": "https://gridspoke.ai",
                    "X-Title": "GridSpoke Health Check"
                }
            )
            
            if response.status_code != 200:
                raise Exception(f"API returned status {response.status_code}")
            
            models_data = response.json()
            available_models = len(models_data.get("data", []))
            
            # Check rate limits from headers
            rate_limit_headers = {
                "requests_remaining": response.headers.get("x-ratelimit-requests-remaining"),
                "tokens_remaining": response.headers.get("x-ratelimit-tokens-remaining"),
                "reset_requests": response.headers.get("x-ratelimit-requests-reset"),
                "reset_tokens": response.headers.get("x-ratelimit-tokens-reset")
            }
            
            return {
                "api_status": "connected",
                "available_models": available_models,
                "rate_limits": rate_limit_headers
            }

class WordPressIntegrationHealthChecker(HealthChecker):
    """Check WordPress integration health by testing webhook endpoints."""
    
    def __init__(self):
        super().__init__("wordpress_integration", timeout=10.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        # Check if webhook endpoints are accessible
        async with httpx.AsyncClient() as client:
            # Test our webhook endpoint
            webhook_url = f"{settings.BASE_URL}/api/v1/webhooks/wordpress/test"
            
            try:
                response = await client.get(webhook_url)
                webhook_accessible = response.status_code in [200, 404]  # 404 is OK for test endpoint
            except Exception:
                webhook_accessible = False
            
            # Get recent WordPress sync statistics from database
            async with get_db_session() as session:
                recent_syncs = await session.execute(text("""
                    SELECT 
                        COUNT(*) as total_syncs,
                        COUNT(CASE WHEN created_at > NOW() - INTERVAL '1 hour' THEN 1 END) as recent_syncs,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_syncs
                    FROM wordpress_sync_logs 
                    WHERE created_at > NOW() - INTERVAL '24 hours'
                """))
                
                sync_stats = recent_syncs.fetchone()
            
            return {
                "webhook_accessible": webhook_accessible,
                "total_syncs_24h": sync_stats.total_syncs if sync_stats else 0,
                "recent_syncs_1h": sync_stats.recent_syncs if sync_stats else 0,
                "success_rate": (
                    sync_stats.successful_syncs / max(sync_stats.total_syncs, 1) 
                    if sync_stats and sync_stats.total_syncs > 0 
                    else 0
                )
            }

class FileSystemHealthChecker(HealthChecker):
    """Check file system health and storage."""
    
    def __init__(self):
        super().__init__("filesystem", timeout=5.0)
    
    async def _perform_check(self) -> Dict[str, Any]:
        import shutil
        import os
        
        # Check disk space
        disk_usage = shutil.disk_usage("/")
        free_space_gb = disk_usage.free / (1024**3)
        total_space_gb = disk_usage.total / (1024**3)
        used_space_gb = disk_usage.used / (1024**3)
        usage_percent = (used_space_gb / total_space_gb) * 100
        
        # Check if critical directories exist and are writable
        critical_paths = [
            "/tmp",
            "/app/logs" if os.path.exists("/app") else ".",
            "/app/uploads" if os.path.exists("/app") else "."
        ]
        
        path_status = {}
        for path in critical_paths:
            try:
                if os.path.exists(path):
                    # Test write access
                    test_file = os.path.join(path, f"health_check_{time.time()}")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)
                    path_status[path] = "writable"
                else:
                    path_status[path] = "missing"
            except Exception as e:
                path_status[path] = f"error: {str(e)}"
        
        # Check if disk usage is critical
        if usage_percent > 90:
            raise Exception(f"Disk usage critical: {usage_percent:.1f}%")
        elif usage_percent > 80:
            logger.warning(f"Disk usage high: {usage_percent:.1f}%")
        
        return {
            "free_space_gb": round(free_space_gb, 2),
            "total_space_gb": round(total_space_gb, 2),
            "used_space_gb": round(used_space_gb, 2),
            "usage_percent": round(usage_percent, 1),
            "path_status": path_status
        }

# =============================================================================
# HEALTH CHECK MANAGER
# =============================================================================

class HealthCheckManager:
    """Manages all health checks for GridSpoke services."""
    
    def __init__(self):
        self.checkers = [
            DatabaseHealthChecker(),
            RedisHealthChecker(),
            CeleryHealthChecker(),
            OpenRouterHealthChecker(),
            WordPressIntegrationHealthChecker(),
            FileSystemHealthChecker()
        ]
        self.startup_time = time.time()
    
    async def check_all(self) -> SystemHealth:
        """Perform all health checks and return system status."""
        logger.info("Performing comprehensive health check")
        
        # Run all health checks concurrently
        health_results = await asyncio.gather(
            *[checker.check() for checker in self.checkers],
            return_exceptions=True
        )
        
        components = []
        for i, result in enumerate(health_results):
            if isinstance(result, Exception):
                # If a health check itself failed
                components.append(ComponentHealth(
                    name=self.checkers[i].name,
                    status=HealthStatus.UNKNOWN,
                    message=f"Health check failed: {str(result)}",
                    last_checked=datetime.utcnow()
                ))
            else:
                components.append(result)
        
        # Determine overall system health
        overall_status = self._determine_overall_status(components)
        
        return SystemHealth(
            status=overall_status,
            timestamp=datetime.utcnow(),
            components=components,
            uptime_seconds=time.time() - self.startup_time
        )
    
    async def check_component(self, component_name: str) -> ComponentHealth:
        """Check health of a specific component."""
        for checker in self.checkers:
            if checker.name == component_name:
                return await checker.check()
        
        raise ValueError(f"Unknown component: {component_name}")
    
    def _determine_overall_status(self, components: List[ComponentHealth]) -> HealthStatus:
        """Determine overall system health based on component health."""
        if not components:
            return HealthStatus.UNKNOWN
        
        # Count component statuses
        status_counts = {}
        for component in components:
            status_counts[component.status] = status_counts.get(component.status, 0) + 1
        
        # Determine overall health
        total_components = len(components)
        unhealthy_count = status_counts.get(HealthStatus.UNHEALTHY, 0)
        degraded_count = status_counts.get(HealthStatus.DEGRADED, 0)
        
        if unhealthy_count > 0:
            # If any critical component is unhealthy, system is unhealthy
            critical_components = ['database', 'redis']
            for component in components:
                if (component.name in critical_components and 
                    component.status == HealthStatus.UNHEALTHY):
                    return HealthStatus.UNHEALTHY
            
            # If non-critical components are unhealthy, system is degraded
            return HealthStatus.DEGRADED
        
        if degraded_count > 0:
            return HealthStatus.DEGRADED
        
        return HealthStatus.HEALTHY
    
    def get_component_names(self) -> List[str]:
        """Get list of all component names."""
        return [checker.name for checker in self.checkers]

# =============================================================================
# READINESS AND LIVENESS PROBES
# =============================================================================

async def liveness_probe() -> Dict[str, Any]:
    """Simple liveness probe for Kubernetes/Docker health checks."""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime_seconds": time.time() - health_manager.startup_time
    }

async def readiness_probe() -> Dict[str, Any]:
    """Readiness probe to check if service can handle requests."""
    try:
        # Check critical components only
        critical_checkers = [
            checker for checker in health_manager.checkers 
            if checker.name in ['database', 'redis']
        ]
        
        results = await asyncio.gather(
            *[checker.check() for checker in critical_checkers],
            return_exceptions=True
        )
        
        for result in results:
            if isinstance(result, Exception):
                raise Exception(f"Critical component check failed: {result}")
            if result.status == HealthStatus.UNHEALTHY:
                raise Exception(f"Critical component unhealthy: {result.name}")
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise Exception(f"Service not ready: {str(e)}")

# Global health check manager
health_manager = HealthCheckManager()

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

async def get_health_summary() -> Dict[str, Any]:
    """Get a simplified health summary for monitoring dashboards."""
    system_health = await health_manager.check_all()
    
    return {
        "overall_status": system_health.status.value,
        "timestamp": system_health.timestamp.isoformat(),
        "uptime_hours": round(system_health.uptime_seconds / 3600, 2),
        "healthy_components": len([c for c in system_health.components if c.status == HealthStatus.HEALTHY]),
        "total_components": len(system_health.components),
        "issues": [
            {
                "component": c.name,
                "status": c.status.value,
                "message": c.message
            }
            for c in system_health.components 
            if c.status != HealthStatus.HEALTHY
        ]
    }

def is_healthy(component_health: ComponentHealth) -> bool:
    """Check if a component is healthy."""
    return component_health.status == HealthStatus.HEALTHY

def is_system_healthy(system_health: SystemHealth) -> bool:
    """Check if the overall system is healthy."""
    return system_health.status == HealthStatus.HEALTHY

# Export main components
__all__ = [
    'HealthStatus',
    'ComponentHealth', 
    'SystemHealth',
    'health_manager',
    'liveness_probe',
    'readiness_probe',
    'get_health_summary',
    'is_healthy',
    'is_system_healthy'
]
