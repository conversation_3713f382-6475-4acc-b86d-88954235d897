# GridSpoke Sentry Integration and Error Tracking
# Phase 8: Production-ready error monitoring with custom GridSpoke context

import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
import logging
from typing import Dict, Any, Optional, Callable
from functools import wraps
import asyncio
import json
from datetime import datetime
import traceback
from api.core.config import settings

logger = logging.getLogger(__name__)

# =============================================================================
# SENTRY CONFIGURATION
# =============================================================================

def configure_sentry():
    """Configure Sentry for GridSpoke error tracking."""
    
    if not settings.SENTRY_DSN:
        logger.warning("Sentry DSN not configured - error tracking disabled")
        return
    
    # Configure logging integration
    logging_integration = LoggingIntegration(
        level=logging.INFO,        # Capture info and above as breadcrumbs
        event_level=logging.ERROR  # Send errors as events
    )
    
    # Initialize Sentry
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        environment=settings.ENVIRONMENT,
        release=f"gridspoke@{settings.APP_VERSION}",
        
        # Integrations
        integrations=[
            FastApiIntegration(auto_enabling_integrations=False),
            SqlalchemyIntegration(),
            RedisIntegration(),
            CeleryIntegration(),
            logging_integration,
        ],
        
        # Performance monitoring
        traces_sample_rate=0.1 if settings.ENVIRONMENT == "production" else 1.0,
        
        # Error sampling
        sample_rate=1.0,
        
        # Custom configuration
        before_send=before_send_filter,
        before_send_transaction=before_send_transaction_filter,
        
        # Additional options
        attach_stacktrace=True,
        send_default_pii=False,  # Don't send personally identifiable information
        max_breadcrumbs=50,
        debug=settings.ENVIRONMENT == "development",
        
        # Custom tags
        default_integrations=False,  # We specify integrations explicitly
    )
    
    # Set global tags
    sentry_sdk.set_tag("service", "gridspoke-api")
    sentry_sdk.set_tag("component", "backend")
    
    logger.info("Sentry error tracking configured successfully")

# =============================================================================
# CUSTOM FILTERS AND PROCESSORS
# =============================================================================

def before_send_filter(event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Filter and modify events before sending to Sentry."""
    
    # Skip certain expected errors
    if 'exc_info' in hint:
        exc_type, exc_value, tb = hint['exc_info']
        
        # Skip validation errors (they're handled gracefully)
        if exc_type.__name__ in ['ValidationError', 'RequestValidationError']:
            return None
        
        # Skip HTTP client errors (4xx) unless they're authentication issues
        if hasattr(exc_value, 'status_code'):
            status_code = getattr(exc_value, 'status_code', 0)
            if 400 <= status_code < 500 and status_code != 401:
                return None
    
    # Add GridSpoke-specific context
    event = _add_gridspoke_context(event)
    
    # Sanitize sensitive data
    event = _sanitize_sensitive_data(event)
    
    return event

def before_send_transaction_filter(event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Filter performance transactions before sending."""
    
    # Skip health check endpoints to reduce noise
    if event.get('transaction', '').endswith('/health') or event.get('transaction', '').endswith('/metrics'):
        return None
    
    # Add custom context for performance monitoring
    event = _add_performance_context(event)
    
    return event

def _add_gridspoke_context(event: Dict[str, Any]) -> Dict[str, Any]:
    """Add GridSpoke-specific context to Sentry events."""
    
    # Add custom tags
    if 'tags' not in event:
        event['tags'] = {}
    
    event['tags'].update({
        'gridspoke.service': 'api',
        'gridspoke.version': settings.APP_VERSION,
        'gridspoke.environment': settings.ENVIRONMENT
    })
    
    # Add custom context sections
    if 'contexts' not in event:
        event['contexts'] = {}
    
    # GridSpoke application context
    event['contexts']['gridspoke'] = {
        'service_name': 'GridSpoke API',
        'service_type': 'ecommerce_seo_optimization',
        'features_enabled': {
            'ai_optimization': True,
            'wordpress_integration': bool(settings.WORDPRESS_WEBHOOK_SECRET),
            'monitoring': bool(settings.PROMETHEUS_ENABLED),
        }
    }
    
    return event

def _add_performance_context(event: Dict[str, Any]) -> Dict[str, Any]:
    """Add performance-specific context."""
    
    if 'contexts' not in event:
        event['contexts'] = {}
    
    # Performance context
    event['contexts']['performance'] = {
        'operation_type': _extract_operation_type(event),
        'ai_model_used': _extract_ai_model(event),
        'optimization_stage': _extract_optimization_stage(event)
    }
    
    return event

def _sanitize_sensitive_data(event: Dict[str, Any]) -> Dict[str, Any]:
    """Remove sensitive data from Sentry events."""
    
    sensitive_keys = [
        'password', 'api_key', 'secret', 'token', 'authorization',
        'openrouter_api_key', 'database_url', 'redis_url'
    ]
    
    def sanitize_dict(data):
        if isinstance(data, dict):
            return {
                key: '[REDACTED]' if any(sensitive in key.lower() for sensitive in sensitive_keys)
                else sanitize_dict(value)
                for key, value in data.items()
            }
        elif isinstance(data, list):
            return [sanitize_dict(item) for item in data]
        else:
            return data
    
    return sanitize_dict(event)

def _extract_operation_type(event: Dict[str, Any]) -> str:
    """Extract operation type from transaction event."""
    transaction = event.get('transaction', '')
    
    if '/optimize' in transaction:
        return 'product_optimization'
    elif '/content' in transaction:
        return 'content_generation'
    elif '/wordpress' in transaction:
        return 'wordpress_sync'
    elif '/analytics' in transaction:
        return 'analytics_query'
    else:
        return 'general_api'

def _extract_ai_model(event: Dict[str, Any]) -> Optional[str]:
    """Extract AI model information from event context."""
    # This would be set by our AI service decorators
    return event.get('contexts', {}).get('ai', {}).get('model')

def _extract_optimization_stage(event: Dict[str, Any]) -> Optional[str]:
    """Extract optimization stage from event context."""
    return event.get('contexts', {}).get('optimization', {}).get('stage')

# =============================================================================
# CONTEXT MANAGERS AND DECORATORS
# =============================================================================

class GridSpokeErrorContext:
    """Context manager for adding GridSpoke-specific error context."""
    
    def __init__(self, operation: str, **context_data):
        self.operation = operation
        self.context_data = context_data
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.utcnow()
        
        # Set Sentry context
        sentry_sdk.set_tag("gridspoke.operation", self.operation)
        
        # Add breadcrumb
        sentry_sdk.add_breadcrumb(
            message=f"Starting {self.operation}",
            category="gridspoke.operation",
            level="info",
            data=self.context_data
        )
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.utcnow() - self.start_time).total_seconds()
        
        if exc_type is None:
            # Success
            sentry_sdk.add_breadcrumb(
                message=f"Completed {self.operation} in {duration:.2f}s",
                category="gridspoke.operation",
                level="info",
                data={"duration_seconds": duration}
            )
        else:
            # Error occurred
            sentry_sdk.set_context("gridspoke_error", {
                "operation": self.operation,
                "duration_seconds": duration,
                "context_data": self.context_data
            })
        
        # Clear operation tag
        sentry_sdk.set_tag("gridspoke.operation", None)

def track_ai_operation(model: str, operation_type: str):
    """Decorator to track AI operations with Sentry."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            with sentry_sdk.configure_scope() as scope:
                scope.set_tag("ai.model", model)
                scope.set_tag("ai.operation", operation_type)
                scope.set_context("ai", {
                    "model": model,
                    "operation_type": operation_type,
                    "provider": "openrouter"
                })
                
                start_time = datetime.utcnow()
                
                try:
                    result = await func(*args, **kwargs)
                    
                    # Track successful AI operation
                    duration = (datetime.utcnow() - start_time).total_seconds()
                    sentry_sdk.add_breadcrumb(
                        message=f"AI operation completed: {operation_type}",
                        category="ai.operation",
                        level="info",
                        data={
                            "model": model,
                            "duration_seconds": duration,
                            "success": True
                        }
                    )
                    
                    return result
                    
                except Exception as e:
                    # Track AI operation failure
                    duration = (datetime.utcnow() - start_time).total_seconds()
                    sentry_sdk.set_context("ai_error", {
                        "model": model,
                        "operation_type": operation_type,
                        "duration_seconds": duration,
                        "error_type": type(e).__name__
                    })
                    raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            with sentry_sdk.configure_scope() as scope:
                scope.set_tag("ai.model", model)
                scope.set_tag("ai.operation", operation_type)
                
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    sentry_sdk.set_context("ai_error", {
                        "model": model,
                        "operation_type": operation_type,
                        "error_type": type(e).__name__
                    })
                    raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

def track_database_operation(table: str, operation: str):
    """Decorator to track database operations."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            with sentry_sdk.configure_scope() as scope:
                scope.set_tag("db.table", table)
                scope.set_tag("db.operation", operation)
                
                sentry_sdk.add_breadcrumb(
                    message=f"Database {operation} on {table}",
                    category="db.operation",
                    level="info"
                )
                
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    sentry_sdk.set_context("database_error", {
                        "table": table,
                        "operation": operation,
                        "error_type": type(e).__name__
                    })
                    raise
        
        return wrapper
    return decorator

def track_wordpress_operation(store_id: str, operation: str):
    """Decorator to track WordPress integration operations."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            with sentry_sdk.configure_scope() as scope:
                scope.set_tag("wordpress.store_id", store_id[:8])  # Truncated for privacy
                scope.set_tag("wordpress.operation", operation)
                
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    sentry_sdk.set_context("wordpress_error", {
                        "store_id": store_id[:8],
                        "operation": operation,
                        "error_type": type(e).__name__
                    })
                    raise
        
        return wrapper
    return decorator

# =============================================================================
# CUSTOM ERROR REPORTING FUNCTIONS
# =============================================================================

async def report_optimization_error(product_id: str, store_id: str, 
                                   ai_model: str, error: Exception, 
                                   context: Dict[str, Any] = None):
    """Report optimization-specific errors with rich context."""
    
    with sentry_sdk.configure_scope() as scope:
        # Set user context (anonymized)
        scope.set_user({
            "id": store_id[:8],  # Truncated store ID
            "store_type": context.get("store_type", "unknown") if context else "unknown"
        })
        
        # Set custom context
        scope.set_context("optimization_error", {
            "product_id": product_id[:8],  # Truncated for privacy
            "store_id": store_id[:8],
            "ai_model": ai_model,
            "optimization_stage": context.get("stage", "unknown") if context else "unknown",
            "product_type": context.get("product_type", "unknown") if context else "unknown",
        })
        
        # Add breadcrumbs
        sentry_sdk.add_breadcrumb(
            message="Product optimization failed",
            category="optimization.error",
            level="error",
            data={
                "ai_model": ai_model,
                "error_type": type(error).__name__
            }
        )
        
        # Capture exception
        sentry_sdk.capture_exception(error)

async def report_wordpress_sync_error(store_id: str, sync_type: str, 
                                     error: Exception, products_affected: int = 0):
    """Report WordPress synchronization errors."""
    
    with sentry_sdk.configure_scope() as scope:
        scope.set_context("wordpress_sync_error", {
            "store_id": store_id[:8],
            "sync_type": sync_type,
            "products_affected": products_affected,
            "error_type": type(error).__name__
        })
        
        sentry_sdk.add_breadcrumb(
            message=f"WordPress sync failed: {sync_type}",
            category="wordpress.sync_error",
            level="error",
            data={"products_affected": products_affected}
        )
        
        sentry_sdk.capture_exception(error)

async def report_cost_anomaly(cost_data: Dict[str, Any], threshold_exceeded: str):
    """Report unusual cost patterns to Sentry."""
    
    sentry_sdk.set_context("cost_anomaly", {
        "threshold_exceeded": threshold_exceeded,
        "cost_data": cost_data,
        "detected_at": datetime.utcnow().isoformat()
    })
    
    sentry_sdk.add_breadcrumb(
        message=f"Cost anomaly detected: {threshold_exceeded}",
        category="cost.anomaly",
        level="warning",
        data=cost_data
    )
    
    # Create a custom message for cost anomalies
    sentry_sdk.capture_message(
        f"Cost anomaly detected: {threshold_exceeded}",
        level="warning"
    )

# =============================================================================
# PERFORMANCE MONITORING
# =============================================================================

def track_performance(operation_name: str, tags: Dict[str, str] = None):
    """Decorator to track performance metrics in Sentry."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            with sentry_sdk.start_transaction(
                op=operation_name,
                name=func.__name__,
                tags=tags or {}
            ) as transaction:
                
                # Add function-specific tags
                transaction.set_tag("function", func.__name__)
                transaction.set_tag("module", func.__module__)
                
                try:
                    result = await func(*args, **kwargs)
                    transaction.set_status("ok")
                    return result
                except Exception as e:
                    transaction.set_status("internal_error")
                    transaction.set_tag("error_type", type(e).__name__)
                    raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            with sentry_sdk.start_transaction(
                op=operation_name,
                name=func.__name__,
                tags=tags or {}
            ) as transaction:
                
                try:
                    result = func(*args, **kwargs)
                    transaction.set_status("ok")
                    return result
                except Exception as e:
                    transaction.set_status("internal_error")
                    transaction.set_tag("error_type", type(e).__name__)
                    raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

# =============================================================================
# INITIALIZATION
# =============================================================================

def initialize_error_tracking():
    """Initialize Sentry error tracking for GridSpoke."""
    configure_sentry()
    
    # Set up global error handlers
    import sys
    
    def handle_exception(exc_type, exc_value, exc_traceback):
        """Handle uncaught exceptions."""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        logger.error(
            "Uncaught exception",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
        sentry_sdk.capture_exception((exc_type, exc_value, exc_traceback))
    
    sys.excepthook = handle_exception
    
    logger.info("GridSpoke error tracking initialized")

# Export main components
__all__ = [
    'configure_sentry',
    'initialize_error_tracking',
    'GridSpokeErrorContext',
    'track_ai_operation',
    'track_database_operation', 
    'track_wordpress_operation',
    'track_performance',
    'report_optimization_error',
    'report_wordpress_sync_error',
    'report_cost_anomaly'
]
