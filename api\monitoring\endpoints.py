# GridSpoke Monitoring Endpoints - FastAPI routes for health and metrics
# Phase 8: Production-ready monitoring endpoints

from fastapi import APIRouter, HTTPException, Depends, Response, status
from fastapi.responses import PlainTextResponse
from typing import Dict, Any, Optional, List
import asyncio
import json
from datetime import datetime
from pydantic import BaseModel

from api.monitoring.health import (
    health_manager, liveness_probe, readiness_probe, 
    get_health_summary, is_system_healthy, HealthStatus
)
from api.monitoring.metrics import metrics_manager, gridspoke_registry
from api.services.analytics import analytics_engine, get_dashboard_metrics, TimeRange
from api.services.reporting import reporting_engine, generate_daily_summary_report
from api.core.security import get_current_user  # Assuming this exists
from api.core.database import get_db_session

router = APIRouter()

# =============================================================================
# HEALTH CHECK ENDPOINTS
# =============================================================================

@router.get("/health", summary="System Health Check")
async def health_check():
    """
    Comprehensive system health check for all GridSpoke components.
    Returns detailed health status for monitoring and debugging.
    """
    try:
        system_health = await health_manager.check_all()
        
        status_code = status.HTTP_200_OK
        if system_health.status == HealthStatus.DEGRADED:
            status_code = status.HTTP_206_PARTIAL_CONTENT
        elif system_health.status == HealthStatus.UNHEALTHY:
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        
        return Response(
            content=system_health.json(),
            status_code=status_code,
            media_type="application/json"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )

@router.get("/health/liveness", summary="Liveness Probe")
async def liveness():
    """
    Kubernetes/Docker liveness probe endpoint.
    Returns 200 if the service is alive and running.
    """
    try:
        probe_result = await liveness_probe()
        return probe_result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Liveness probe failed: {str(e)}"
        )

@router.get("/health/readiness", summary="Readiness Probe")
async def readiness():
    """
    Kubernetes/Docker readiness probe endpoint.
    Returns 200 if the service is ready to handle requests.
    """
    try:
        probe_result = await readiness_probe()
        return probe_result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service not ready: {str(e)}"
        )

@router.get("/health/summary", summary="Health Summary")
async def health_summary():
    """
    Simplified health summary for dashboards and monitoring.
    """
    try:
        summary = await get_health_summary()
        return summary
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health summary failed: {str(e)}"
        )

@router.get("/health/{component}", summary="Component Health")
async def component_health(component: str):
    """
    Check health of a specific component.
    """
    try:
        component_result = await health_manager.check_component(component)
        
        status_code = status.HTTP_200_OK
        if component_result.status == HealthStatus.UNHEALTHY:
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        elif component_result.status == HealthStatus.DEGRADED:
            status_code = status.HTTP_206_PARTIAL_CONTENT
        
        return Response(
            content=component_result.json(),
            status_code=status_code,
            media_type="application/json"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Component health check failed: {str(e)}"
        )

# =============================================================================
# METRICS ENDPOINTS
# =============================================================================

@router.get("/metrics", response_class=PlainTextResponse, summary="Prometheus Metrics")
async def prometheus_metrics():
    """
    Prometheus metrics endpoint in OpenMetrics format.
    Used by Prometheus for scraping GridSpoke business and technical metrics.
    """
    try:
        metrics_data = metrics_manager.get_metrics()
        return PlainTextResponse(
            content=metrics_data,
            media_type="text/plain; version=0.0.4; charset=utf-8"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Metrics collection failed: {str(e)}"
        )

@router.get("/metrics/dashboard", summary="Dashboard Metrics")
async def dashboard_metrics(
    time_range: TimeRange = TimeRange.LAST_24H,
    store_id: Optional[str] = None,
    current_user = Depends(get_current_user)
):
    """
    Get key metrics formatted for dashboard display.
    """
    try:
        metrics = await get_dashboard_metrics(time_range, store_id)
        return metrics
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Dashboard metrics failed: {str(e)}"
        )

@router.get("/metrics/cost", summary="Cost Metrics")
async def cost_metrics(
    days: int = 30,
    current_user = Depends(get_current_user)
):
    """
    Get detailed cost analysis metrics.
    """
    try:
        from api.services.analytics import AnalyticsFilter, TimeRange
        
        filters = AnalyticsFilter(time_range=TimeRange.LAST_30D)
        cost_data = await analytics_engine.get_cost_analytics(filters)
        
        return {
            "summary": cost_data["summary"],
            "daily_average": cost_data["summary"]["daily_average_cost"],
            "efficiency_score": cost_data["summary"]["tokens_per_dollar"],
            "top_models": cost_data["by_model_and_type"][:5]
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cost metrics failed: {str(e)}"
        )

# =============================================================================
# ANALYTICS ENDPOINTS
# =============================================================================

class AnalyticsRequest(BaseModel):
    time_range: TimeRange = TimeRange.LAST_30D
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    store_ids: Optional[List[str]] = None
    include_charts: bool = False

@router.post("/analytics/optimization", summary="Optimization Analytics")
async def optimization_analytics(
    request: AnalyticsRequest,
    current_user = Depends(get_current_user)
):
    """
    Get detailed optimization performance analytics.
    """
    try:
        from api.services.analytics import AnalyticsFilter
        
        filters = AnalyticsFilter(
            time_range=request.time_range,
            start_date=request.start_date,
            end_date=request.end_date,
            store_ids=request.store_ids
        )
        
        metrics = await analytics_engine.get_optimization_metrics(filters)
        return metrics
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Optimization analytics failed: {str(e)}"
        )

@router.post("/analytics/seo", summary="SEO Performance Analytics")
async def seo_analytics(
    request: AnalyticsRequest,
    current_user = Depends(get_current_user)
):
    """
    Get SEO improvement and performance analytics.
    """
    try:
        from api.services.analytics import AnalyticsFilter
        
        filters = AnalyticsFilter(
            time_range=request.time_range,
            start_date=request.start_date,
            end_date=request.end_date,
            store_ids=request.store_ids
        )
        
        metrics = await analytics_engine.get_seo_performance_metrics(filters)
        return metrics
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"SEO analytics failed: {str(e)}"
        )

@router.post("/analytics/comprehensive", summary="Comprehensive Analytics Report")
async def comprehensive_analytics(
    request: AnalyticsRequest,
    current_user = Depends(get_current_user)
):
    """
    Generate comprehensive analytics report with insights and recommendations.
    """
    try:
        from api.services.analytics import AnalyticsFilter
        
        filters = AnalyticsFilter(
            time_range=request.time_range,
            start_date=request.start_date,
            end_date=request.end_date,
            store_ids=request.store_ids
        )
        
        report = await analytics_engine.generate_comprehensive_report(filters)
        return report
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Comprehensive analytics failed: {str(e)}"
        )

# =============================================================================
# REPORTING ENDPOINTS
# =============================================================================

class ReportGenerationRequest(BaseModel):
    report_type: str
    format: str = "pdf"
    time_range: TimeRange = TimeRange.LAST_30D
    store_ids: Optional[List[str]] = None
    email_recipients: Optional[List[str]] = None

@router.post("/reports/generate", summary="Generate Report")
async def generate_report(
    request: ReportGenerationRequest,
    current_user = Depends(get_current_user)
):
    """
    Generate a custom report with specified parameters.
    """
    try:
        from api.services.reporting import (
            ReportConfig, ReportRequest, ReportDelivery, 
            ReportType, ReportFormat, DeliveryMethod
        )
        from api.services.analytics import AnalyticsFilter
        
        # Create report configuration
        filters = AnalyticsFilter(
            time_range=request.time_range,
            store_ids=request.store_ids
        )
        
        config = ReportConfig(
            report_type=ReportType(request.report_type),
            title=f"GridSpoke {request.report_type.replace('_', ' ').title()} Report",
            format=ReportFormat(request.format),
            analytics_filter=filters
        )
        
        # Configure delivery
        delivery_method = DeliveryMethod.EMAIL if request.email_recipients else DeliveryMethod.DOWNLOAD
        delivery = ReportDelivery(
            method=delivery_method,
            recipients=request.email_recipients
        )
        
        # Generate report
        report_request = ReportRequest(
            config=config,
            delivery=delivery,
            generated_by=current_user.get("email", "unknown")
        )
        
        generated_report = await reporting_engine.generate_report(report_request)
        return generated_report
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Report generation failed: {str(e)}"
        )

@router.get("/reports/daily-summary", summary="Daily Summary Report")
async def daily_summary_report(
    store_id: Optional[str] = None,
    current_user = Depends(get_current_user)
):
    """
    Generate and return today's daily summary report.
    """
    try:
        report = await generate_daily_summary_report(store_id)
        return report
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Daily summary report failed: {str(e)}"
        )

# =============================================================================
# SYSTEM STATUS ENDPOINTS
# =============================================================================

@router.get("/status", summary="System Status Overview")
async def system_status():
    """
    Get overall system status for monitoring dashboards.
    """
    try:
        # Get health summary
        health_summary = await get_health_summary()
        
        # Get key metrics
        dashboard_metrics = await get_dashboard_metrics(TimeRange.LAST_24H)
        
        # Combine into status overview
        system_status = {
            "status": health_summary["overall_status"],
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_hours": health_summary["uptime_hours"],
            "health": {
                "healthy_components": health_summary["healthy_components"],
                "total_components": health_summary["total_components"],
                "issues": health_summary["issues"]
            },
            "performance": {
                "optimizations_today": dashboard_metrics["optimizations_today"],
                "success_rate": dashboard_metrics["success_rate"],
                "avg_processing_time": dashboard_metrics["avg_processing_time"]
            },
            "cost": {
                "cost_today": dashboard_metrics["cost_today"],
                "active_stores": dashboard_metrics["active_stores"]
            }
        }
        
        return system_status
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"System status check failed: {str(e)}"
        )

@router.get("/status/components", summary="Component Status List")
async def component_status_list():
    """
    Get status of all monitored components.
    """
    try:
        component_names = health_manager.get_component_names()
        
        # Check all components in parallel
        component_checks = await asyncio.gather(
            *[health_manager.check_component(name) for name in component_names],
            return_exceptions=True
        )
        
        components = []
        for i, result in enumerate(component_checks):
            if isinstance(result, Exception):
                components.append({
                    "name": component_names[i],
                    "status": "error",
                    "message": str(result),
                    "last_checked": datetime.utcnow().isoformat()
                })
            else:
                components.append({
                    "name": result.name,
                    "status": result.status.value,
                    "message": result.message,
                    "last_checked": result.last_checked.isoformat(),
                    "response_time_ms": result.response_time_ms
                })
        
        return {"components": components}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Component status check failed: {str(e)}"
        )

# =============================================================================
# DEBUGGING AND MAINTENANCE ENDPOINTS
# =============================================================================

@router.post("/debug/trigger-health-check", summary="Trigger Health Check")
async def trigger_health_check(current_user = Depends(get_current_user)):
    """
    Manually trigger a comprehensive health check (for debugging).
    """
    try:
        health_result = await health_manager.check_all()
        return {
            "triggered_at": datetime.utcnow().isoformat(),
            "result": health_result
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check trigger failed: {str(e)}"
        )

@router.post("/debug/clear-metrics-cache", summary="Clear Metrics Cache")
async def clear_metrics_cache(current_user = Depends(get_current_user)):
    """
    Clear analytics metrics cache (for debugging/maintenance).
    """
    try:
        # Clear Redis cache for analytics
        redis = await analytics_engine._get_redis()
        keys = await redis.keys("analytics:*")
        if keys:
            await redis.delete(*keys)
        
        return {
            "cleared_at": datetime.utcnow().isoformat(),
            "cache_keys_cleared": len(keys)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cache clear failed: {str(e)}"
        )

# =============================================================================
# BACKUP AND RESTORE ENDPOINTS (Placeholder)
# =============================================================================

@router.post("/backup/create", summary="Create System Backup")
async def create_backup(current_user = Depends(get_current_user)):
    """
    Create a system backup (database, configurations, reports).
    """
    # This would be implemented based on specific backup requirements
    return {
        "message": "Backup functionality to be implemented",
        "status": "not_implemented"
    }

@router.post("/backup/restore", summary="Restore from Backup")
async def restore_backup(current_user = Depends(get_current_user)):
    """
    Restore system from backup.
    """
    # This would be implemented based on specific restore requirements
    return {
        "message": "Restore functionality to be implemented", 
        "status": "not_implemented"
    }

# Export router
__all__ = ["router"]
