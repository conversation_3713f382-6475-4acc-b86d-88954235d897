"""
Generated content CRUD operations.
"""
import uuid
from typing import Optional, List
from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone

from crud.base import CRUDBase
from models.generated_content import GeneratedContent, ContentType, ContentStatus, AIModel
from schemas.content import GeneratedContentCreate, GeneratedContentUpdate


class CRUDGeneratedContent(CRUDBase[GeneratedContent, GeneratedContentCreate, GeneratedContentUpdate]):
    """CRUD operations for GeneratedContent model."""
    
    async def get_by_product(
        self, 
        db: AsyncSession, 
        *, 
        product_id: uuid.UUID,
        content_type: Optional[ContentType] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[GeneratedContent]:
        """Get content by product ID."""
        query = select(GeneratedContent).where(GeneratedContent.product_id == product_id)
        
        if content_type:
            query = query.where(GeneratedContent.content_type == content_type)
        
        query = query.order_by(GeneratedContent.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_job(
        self,
        db: AsyncSession,
        *,
        job_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[GeneratedContent]:
        """Get content by job ID."""
        query = select(GeneratedContent).where(
            GeneratedContent.job_id == job_id
        ).order_by(GeneratedContent.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_status(
        self,
        db: AsyncSession,
        *,
        status: ContentStatus,
        skip: int = 0,
        limit: int = 100
    ) -> List[GeneratedContent]:
        """Get content by status."""
        query = select(GeneratedContent).where(
            GeneratedContent.status == status
        ).order_by(GeneratedContent.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_latest_by_product_and_type(
        self,
        db: AsyncSession,
        *,
        product_id: uuid.UUID,
        content_type: ContentType
    ) -> Optional[GeneratedContent]:
        """Get latest content for product and type."""
        query = select(GeneratedContent).where(
            and_(
                GeneratedContent.product_id == product_id,
                GeneratedContent.content_type == content_type
            )
        ).order_by(GeneratedContent.created_at.desc()).limit(1)
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_published_content(
        self,
        db: AsyncSession,
        *,
        product_id: uuid.UUID,
        content_type: Optional[ContentType] = None
    ) -> List[GeneratedContent]:
        """Get published content for product."""
        query = select(GeneratedContent).where(
            and_(
                GeneratedContent.product_id == product_id,
                GeneratedContent.is_published == True
            )
        )
        
        if content_type:
            query = query.where(GeneratedContent.content_type == content_type)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def approve_content(
        self,
        db: AsyncSession,
        *,
        content_id: uuid.UUID,
        reviewer_notes: Optional[str] = None,
        approval_score: Optional[int] = None
    ) -> Optional[GeneratedContent]:
        """Approve content."""
        content = await self.get(db, id=content_id)
        if content:
            content.approve(reviewer_notes, approval_score)
            db.add(content)
            await db.commit()
            await db.refresh(content)
        return content
    
    async def reject_content(
        self,
        db: AsyncSession,
        *,
        content_id: uuid.UUID,
        reviewer_notes: str,
        feedback_tags: Optional[List[str]] = None
    ) -> Optional[GeneratedContent]:
        """Reject content."""
        content = await self.get(db, id=content_id)
        if content:
            content.reject(reviewer_notes, feedback_tags)
            db.add(content)
            await db.commit()
            await db.refresh(content)
        return content
    
    async def publish_content(
        self,
        db: AsyncSession,
        *,
        content_id: uuid.UUID
    ) -> Optional[GeneratedContent]:
        """Publish content."""
        content = await self.get(db, id=content_id)
        if content:
            content.publish()
            db.add(content)
            await db.commit()
            await db.refresh(content)
        return content
    
    async def update_analytics(
        self,
        db: AsyncSession,
        *,
        content_id: uuid.UUID,
        ctr: Optional[float] = None,
        conversion_rate: Optional[float] = None,
        engagement: Optional[float] = None
    ) -> Optional[GeneratedContent]:
        """Update content analytics."""
        content = await self.get(db, id=content_id)
        if content:
            content.update_analytics(ctr, conversion_rate, engagement)
            db.add(content)
            await db.commit()
            await db.refresh(content)
        return content
    
    async def get_content_stats(
        self,
        db: AsyncSession,
        *,
        product_id: Optional[uuid.UUID] = None,
        job_id: Optional[uuid.UUID] = None
    ) -> dict:
        """Get content statistics."""
        base_query = select(func.count(GeneratedContent.id))
        
        if product_id:
            base_query = base_query.where(GeneratedContent.product_id == product_id)
        elif job_id:
            base_query = base_query.where(GeneratedContent.job_id == job_id)
        
        # Total content
        total_result = await db.execute(base_query)
        total_content = total_result.scalar()
        
        # Published content
        published_query = base_query.where(GeneratedContent.is_published == True)
        published_result = await db.execute(published_query)
        published_content = published_result.scalar()
        
        # Approved content
        approved_query = base_query.where(GeneratedContent.status == ContentStatus.APPROVED)
        approved_result = await db.execute(approved_query)
        approved_content = approved_result.scalar()
        
        # Average quality score
        quality_query = select(func.avg(GeneratedContent.seo_score)).where(
            GeneratedContent.seo_score.is_not(None)
        )
        if product_id:
            quality_query = quality_query.where(GeneratedContent.product_id == product_id)
        elif job_id:
            quality_query = quality_query.where(GeneratedContent.job_id == job_id)
        
        quality_result = await db.execute(quality_query)
        avg_quality = quality_result.scalar() or 0
        
        return {
            "total_content": total_content,
            "published_content": published_content,
            "approved_content": approved_content,
            "average_quality_score": round(avg_quality, 2),
            "approval_rate": (approved_content / total_content * 100) if total_content > 0 else 0
        }
    
    async def get_content_by_ai_model(
        self,
        db: AsyncSession,
        *,
        ai_model: AIModel,
        skip: int = 0,
        limit: int = 100
    ) -> List[GeneratedContent]:
        """Get content by AI model."""
        query = select(GeneratedContent).where(
            GeneratedContent.ai_model == ai_model
        ).order_by(GeneratedContent.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_high_performing_content(
        self,
        db: AsyncSession,
        *,
        min_ctr: float = 0.05,
        min_conversion: float = 0.02,
        limit: int = 100
    ) -> List[GeneratedContent]:
        """Get high-performing content."""
        query = select(GeneratedContent).where(
            and_(
                GeneratedContent.click_through_rate >= min_ctr,
                GeneratedContent.conversion_rate >= min_conversion,
                GeneratedContent.is_published == True
            )
        ).order_by(GeneratedContent.conversion_rate.desc()).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()


# Create instance for dependency injection
content_crud = CRUDGeneratedContent(GeneratedContent)
