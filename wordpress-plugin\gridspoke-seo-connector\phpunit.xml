<?xml version="1.0"?>
<phpunit
    bootstrap="tests/bootstrap.php"
    backupGlobals="false"
    colors="true"
    convertErrorsToExceptions="true"
    convertNoticesToExceptions="true"
    convertWarningsToExceptions="true"
    processIsolation="false"
    stopOnFailure="false"
    verbose="true"
>
    <testsuites>
        <testsuite name="GridSpoke SEO Connector">
            <directory>./tests/</directory>
        </testsuite>
    </testsuites>
    
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./includes/</directory>
            <directory suffix=".php">./admin/</directory>
            <exclude>
                <directory suffix=".php">./tests/</directory>
                <file>./gridspoke-seo-connector.php</file>
            </exclude>
        </whitelist>
    </filter>
    
    <logging>
        <log type="coverage-html" target="tests/coverage"/>
        <log type="coverage-text" target="php://stdout" showUncoveredFiles="false"/>
    </logging>
    
    <php>
        <const name="WP_TESTS_DOMAIN" value="example.org" />
        <const name="WP_TESTS_EMAIL" value="<EMAIL>" />
        <const name="WP_TESTS_TITLE" value="Test Blog" />
        <const name="WP_PHP_BINARY" value="php" />
        <const name="WP_TESTS_FORCE_KNOWN_BUGS" value="true" />
        
        <!-- Database settings for tests -->
        <const name="DB_NAME" value="wordpress_test" />
        <const name="DB_USER" value="root" />
        <const name="DB_PASSWORD" value="" />
        <const name="DB_HOST" value="localhost" />
        <const name="DB_CHARSET" value="utf8" />
        <const name="DB_COLLATE" value="" />
        
        <!-- WordPress test configuration -->
        <const name="WP_DEBUG" value="true" />
        <const name="WP_DEBUG_LOG" value="false" />
        <const name="WP_DEBUG_DISPLAY" value="false" />
        
        <!-- GridSpoke test configuration -->
        <const name="GRIDSPOKE_SEO_TEST_API_ENDPOINT" value="http://localhost:8000/api/v1" />
        <const name="GRIDSPOKE_SEO_TEST_EMAIL" value="<EMAIL>" />
        <const name="GRIDSPOKE_SEO_TEST_PASSWORD" value="test_password" />
    </php>
</phpunit>
