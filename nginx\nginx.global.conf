# Global Nginx configuration
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

# Load dynamic modules
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
    worker_rlimit_nofile 4096;
}

http {
    # Basic Settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Security
    server_tokens off;
    more_clear_headers Server;
    
    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;
    
    # Buffer settings
    client_body_buffer_size 16k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 8k;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        application/rss+xml
        image/svg+xml
        text/x-component
        text/x-js
        application/x-javascript;
    
    # Brotli Compression (if available)
    brotli on;
    brotli_comp_level 6;
    brotli_types
        text/plain
        text/css
        application/json
        application/javascript
        text/xml
        application/xml
        application/xml+rss
        text/javascript;
    
    # Rate Limiting Zones
    limit_req_zone $binary_remote_addr zone=api:20m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=webhook:10m rate=5r/s;
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:20m;
    
    # Real IP Configuration (if behind load balancer)
    set_real_ip_from 10.0.0.0/8;
    set_real_ip_from **********/12;
    set_real_ip_from ***********/16;
    real_ip_header X-Forwarded-For;
    real_ip_recursive on;
    
    # Custom log format with additional metrics
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    # Performance monitoring log format
    log_format performance '$time_local $remote_addr $status $request_time '
                          '$upstream_response_time "$request" $body_bytes_sent';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;
    
    # Security headers map
    map $sent_http_content_type $csp_policy {
        default "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;";
        ~image/ "";
        ~font/ "";
    }
    
    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:50m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    
    # OCSP stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # Upstream definitions
    upstream api_backend {
        least_conn;
        server api:8000 max_fails=3 fail_timeout=30s weight=1;
        keepalive 32;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }
    
    upstream websocket_backend {
        ip_hash;  # Sticky sessions for WebSocket
        server api:8000 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }
    
    # Health check server for load balancers
    server {
        listen 80 default_server;
        server_name _;
        
        location /nginx-health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        location / {
            return 444;
        }
    }
    
    # Include additional configuration files
    include /etc/nginx/conf.d/*.conf;
}
