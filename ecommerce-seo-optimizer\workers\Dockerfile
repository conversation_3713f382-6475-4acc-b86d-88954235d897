# Use the same base image as API for consistency
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/code

# Set work directory
WORKDIR /code

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for Docker cache optimization
COPY requirements.txt /code/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . /code/

# Create non-root user for security
RUN groupadd -r celeryuser && useradd -r -g celeryuser celeryuser && \
    chown -R celeryuser:celeryuser /code
USER celeryuser

# Health check for Celery worker
HEALTHCHECK --interval=30s --timeout=30s --start-period=10s --retries=3 \
    CMD celery -A workers.celery_app inspect ping || exit 1

# Default command (will be overridden in docker-compose.yml)
CMD ["celery", "-A", "workers.celery_app", "worker", "--loglevel=info"]
