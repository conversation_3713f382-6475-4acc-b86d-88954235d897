"""
Error Handling Utilities for GridSpoke Workers
Provides consistent error handling and retry logic across all tasks.
"""

import logging
import traceback
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

def handle_task_error(exc: Exception, retry_count: int) -> Dict[str, Any]:
    """
    Handle task errors with consistent logging and error information.
    
    Args:
        exc: Exception that occurred
        retry_count: Current retry count
    
    Returns:
        Dict containing error information
    """
    error_info = {
        'error_type': type(exc).__name__,
        'error_message': str(exc),
        'retry_count': retry_count,
        'timestamp': datetime.utcnow().isoformat(),
        'traceback': traceback.format_exc()
    }
    
    # Log error with appropriate level
    if retry_count < 3:
        logger.warning(f"Task error (retry {retry_count}): {error_info['error_message']}")
    else:
        logger.error(f"Task failed after max retries: {error_info['error_message']}")
        logger.error(f"Full traceback: {error_info['traceback']}")
    
    return error_info

def retry_with_backoff(retry_count: int, base_delay: int = 60, max_delay: int = 300) -> int:
    """
    Calculate retry delay with exponential backoff.
    
    Args:
        retry_count: Current retry count
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
    
    Returns:
        Delay in seconds for next retry
    """
    # Exponential backoff: base_delay * (2 ^ retry_count)
    delay = min(base_delay * (2 ** retry_count), max_delay)
    
    logger.info(f"Calculating retry delay: retry {retry_count}, delay {delay} seconds")
    
    return delay

def should_retry_error(exc: Exception) -> bool:
    """
    Determine if an error should trigger a retry.
    
    Args:
        exc: Exception that occurred
    
    Returns:
        True if error should be retried, False otherwise
    """
    # Define retryable error types
    retryable_errors = [
        'ConnectionError',
        'TimeoutError',
        'HTTPError',
        'OpenRouterAPIError',
        'RedisConnectionError',
        'DatabaseConnectionError'
    ]
    
    error_type = type(exc).__name__
    
    # Don't retry validation errors or authentication errors
    non_retryable_errors = [
        'ValidationError',
        'AuthenticationError',
        'PermissionError',
        'ValueError',
        'KeyError'
    ]
    
    if error_type in non_retryable_errors:
        return False
    
    if error_type in retryable_errors:
        return True
    
    # Default to retry for unknown errors
    return True

def log_task_start(task_name: str, task_id: str, args: tuple, kwargs: dict):
    """
    Log task start with consistent format.
    
    Args:
        task_name: Name of the task
        task_id: Task ID
        args: Task arguments
        kwargs: Task keyword arguments
    """
    logger.info(f"Starting task {task_name} [ID: {task_id}]")
    logger.debug(f"Task args: {args}")
    logger.debug(f"Task kwargs: {kwargs}")

def log_task_completion(task_name: str, task_id: str, duration: float, result: Dict[str, Any]):
    """
    Log task completion with consistent format.
    
    Args:
        task_name: Name of the task
        task_id: Task ID
        duration: Task duration in seconds
        result: Task result
    """
    logger.info(f"Completed task {task_name} [ID: {task_id}] in {duration:.2f} seconds")
    
    # Log key metrics if available
    if isinstance(result, dict):
        if 'tokens_used' in result:
            logger.info(f"Tokens used: {result['tokens_used']}")
        if 'cost_usd' in result:
            logger.info(f"Cost: ${result['cost_usd']:.4f}")

def log_task_failure(task_name: str, task_id: str, exc: Exception, duration: float):
    """
    Log task failure with consistent format.
    
    Args:
        task_name: Name of the task
        task_id: Task ID
        exc: Exception that caused failure
        duration: Task duration before failure
    """
    logger.error(f"Failed task {task_name} [ID: {task_id}] after {duration:.2f} seconds")
    logger.error(f"Error: {type(exc).__name__}: {str(exc)}")

def sanitize_error_message(error_message: str) -> str:
    """
    Sanitize error message to remove sensitive information.
    
    Args:
        error_message: Original error message
    
    Returns:
        Sanitized error message
    """
    # Remove common sensitive patterns
    sensitive_patterns = [
        r'api_key=[\w\-]+',
        r'password=[\w\-]+',
        r'token=[\w\-]+',
        r'secret=[\w\-]+',
        r'Authorization: Bearer [\w\-]+',
    ]
    
    sanitized = error_message
    
    for pattern in sensitive_patterns:
        import re
        sanitized = re.sub(pattern, '[REDACTED]', sanitized, flags=re.IGNORECASE)
    
    return sanitized

def create_error_response(error: Exception, task_id: str, additional_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Create standardized error response.
    
    Args:
        error: Exception that occurred
        task_id: Task ID
        additional_context: Additional context information
    
    Returns:
        Standardized error response
    """
    return {
        'status': 'error',
        'error_type': type(error).__name__,
        'error_message': sanitize_error_message(str(error)),
        'task_id': task_id,
        'timestamp': datetime.utcnow().isoformat(),
        'context': additional_context or {}
    }

class TaskExecutionContext:
    """
    Context manager for task execution with automatic error handling and logging.
    """
    
    def __init__(self, task_name: str, task_id: str, args: tuple = (), kwargs: dict = None):
        self.task_name = task_name
        self.task_id = task_id
        self.args = args
        self.kwargs = kwargs or {}
        self.start_time = None
        self.duration = 0
    
    def __enter__(self):
        self.start_time = datetime.utcnow()
        log_task_start(self.task_name, self.task_id, self.args, self.kwargs)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.utcnow()
        self.duration = (end_time - self.start_time).total_seconds()
        
        if exc_type is None:
            # Task completed successfully
            logger.info(f"Task {self.task_name} completed successfully in {self.duration:.2f} seconds")
        else:
            # Task failed
            log_task_failure(self.task_name, self.task_id, exc_val, self.duration)
        
        return False  # Don't suppress exceptions
    
    def log_progress(self, message: str):
        """Log progress message with task context."""
        logger.info(f"[{self.task_name}:{self.task_id}] {message}")
    
    def log_warning(self, message: str):
        """Log warning message with task context."""
        logger.warning(f"[{self.task_name}:{self.task_id}] {message}")
    
    def log_error(self, message: str):
        """Log error message with task context."""
        logger.error(f"[{self.task_name}:{self.task_id}] {message}")

def monitor_task_resources(task_id: str) -> Dict[str, Any]:
    """
    Monitor task resource usage (memory, CPU, etc.).
    
    Args:
        task_id: Task ID
    
    Returns:
        Dict containing resource usage metrics
    """
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        return {
            'task_id': task_id,
            'memory_usage_mb': process.memory_info().rss / 1024 / 1024,
            'cpu_percent': process.cpu_percent(),
            'num_threads': process.num_threads(),
            'timestamp': datetime.utcnow().isoformat()
        }
        
    except ImportError:
        logger.warning("psutil not available for resource monitoring")
        return {'task_id': task_id, 'monitoring': 'unavailable'}
    except Exception as e:
        logger.error(f"Error monitoring task resources: {str(e)}")
        return {'task_id': task_id, 'error': str(e)}
