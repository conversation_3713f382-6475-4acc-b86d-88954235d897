"""
Monitoring endpoints for GridSpoke.
Provides health checks and metrics for system monitoring.
"""
import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import PlainTextResponse
import asyncio
import json
from datetime import datetime

# Import monitoring components
try:
    from api.monitoring.health import (
        health_manager, 
        liveness_probe, 
        readiness_probe, 
        get_health_summary, 
        is_system_healthy, 
        HealthStatus
    )
    from api.monitoring.metrics import metrics_manager, gridspoke_registry
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False
    logging.warning("Monitoring modules not available, using fallback implementations")

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/monitoring", tags=["Monitoring"])

# Fallback implementations if monitoring modules are not available
if not MONITORING_AVAILABLE:
    class HealthStatus:
        HEALTHY = "healthy"
        DEGRADED = "degraded"
        UNHEALTHY = "unhealthy"
    
    class MockHealthResult:
        def __init__(self, status: str = "healthy"):
            self.status = status
            self.timestamp = datetime.utcnow()
            
        def json(self):
            return json.dumps({
                "status": self.status,
                "timestamp": self.timestamp.isoformat(),
                "components": []
            })
    
    async def mock_health_check():
        return MockHealthResult()
    
    async def mock_liveness_probe():
        return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}
    
    async def mock_readiness_probe():
        return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
    
    async def mock_get_health_summary():
        return {
            "overall_status": "healthy",
            "healthy_components": 5,
            "total_components": 5,
            "issues": [],
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def mock_get_metrics():
        return "# HELP gridspoke_up GridSpoke service status\n# TYPE gridspoke_up gauge\ngridspoke_up 1\n"
    
    # Assign mock functions to the expected names
    health_manager = type('MockHealthManager', (), {
        'check_all': mock_health_check,
        'check_component': mock_health_check
    })()
    
    liveness_probe = mock_liveness_probe
    readiness_probe = mock_readiness_probe
    get_health_summary = mock_get_health_summary
    
    class MockMetricsManager:
        def get_metrics(self):
            return mock_get_metrics()
    
    metrics_manager = MockMetricsManager()
    gridspoke_registry = None

# =============================================================================
# HEALTH CHECK ENDPOINTS
# =============================================================================

@router.get("/health", summary="System Health Check")
async def health_check():
    """
    Comprehensive system health check for all GridSpoke components.
    Returns detailed health status for monitoring and debugging.
    """
    try:
        system_health = await health_manager.check_all()
        
        status_code = status.HTTP_200_OK
        if hasattr(system_health, 'status'):
            if system_health.status == HealthStatus.DEGRADED:
                status_code = status.HTTP_206_PARTIAL_CONTENT
            elif system_health.status == HealthStatus.UNHEALTHY:
                status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        
        # Handle both object with json() method and dict
        if hasattr(system_health, 'json'):
            content = system_health.json()
        else:
            content = json.dumps(system_health) if isinstance(system_health, dict) else str(system_health)
        
        return {
            "content": content,
            "status_code": status_code,
            "media_type": "application/json"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )

@router.get("/health/liveness", summary="Liveness Probe")
async def liveness():
    """
    Kubernetes/Docker liveness probe endpoint.
    Returns 200 if the service is alive and running.
    """
    try:
        probe_result = await liveness_probe()
        return probe_result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Liveness probe failed: {str(e)}"
        )

@router.get("/health/readiness", summary="Readiness Probe")
async def readiness():
    """
    Kubernetes/Docker readiness probe endpoint.
    Returns 200 if the service is ready to handle requests.
    """
    try:
        probe_result = await readiness_probe()
        return probe_result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service not ready: {str(e)}"
        )

@router.get("/health/summary", summary="Health Summary")
async def health_summary():
    """
    Simplified health summary for dashboards and monitoring.
    """
    try:
        summary = await get_health_summary()
        return summary
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health summary failed: {str(e)}"
        )

# =============================================================================
# METRICS ENDPOINTS
# =============================================================================

@router.get("/metrics", response_class=PlainTextResponse, summary="Prometheus Metrics")
async def prometheus_metrics():
    """
    Prometheus metrics endpoint in OpenMetrics format.
    Used by Prometheus for scraping GridSpoke business and technical metrics.
    """
    try:
        if hasattr(metrics_manager, 'get_metrics'):
            metrics_data = metrics_manager.get_metrics()
        else:
            metrics_data = "# HELP gridspoke_up GridSpoke service status\n# TYPE gridspoke_up gauge\ngridspoke_up 1\n"
        
        return PlainTextResponse(
            content=metrics_data,
            media_type="text/plain; version=0.0.4; charset=utf-8"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Metrics collection failed: {str(e)}"
        )

# =============================================================================
# SYSTEM STATUS ENDPOINTS
# =============================================================================

@router.get("/status", summary="System Status Overview")
async def system_status():
    """
    Get overall system status for monitoring dashboards.
    """
    try:
        # Get health summary
        health_summary = await get_health_summary()
        
        # Simple status overview
        system_status = {
            "status": health_summary.get("overall_status", "unknown"),
            "timestamp": datetime.utcnow().isoformat(),
            "health": {
                "healthy_components": health_summary.get("healthy_components", 0),
                "total_components": health_summary.get("total_components", 0),
                "issues": health_summary.get("issues", [])
            }
        }
        
        return system_status
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"System status check failed: {str(e)}"
        )