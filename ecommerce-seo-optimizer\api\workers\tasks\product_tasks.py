"""
Celery tasks for product optimization in GridSpoke.
"""
import uuid
import logging
from typing import List, Dict, Any
from celery import Task
from sqlalchemy.orm import Session
from sqlalchemy.future import select

from core.database import engine
from models.product import Product
from models.optimization_job import OptimizationJob, JobStatus
from models.generated_content import GeneratedContent
from crud import crud_job
# from agents.product_optimizer import ProductOptimizer  # TODO: Fix Mirascope dependency

# Import the Celery app
from workers.celery_app import celery_app

# Temporary mock class
class ProductOptimizer:
    def __init__(self):
        pass
    
    async def optimize_product(self, product_data):
        return {
            "title": f"Optimized {product_data.get('name', 'Product')}",
            "description": "Optimized description (AI temporarily disabled)",
            "meta_description": "Optimized meta description",
            "keywords": {"primary": ["keyword1"], "secondary": ["keyword2"]},
            "seo_score": 75
        }

class OptimizationTask(Task):
    """Base class for optimization tasks with database session management."""
    
    def get_db_session(self) -> Session:
        """Get database session."""
        return Session(engine)

@celery_app.task(base=OptimizationTask, bind=True)
def optimize_single_product(self, product_id: str, store_id: str, optimization_options: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Optimize a single product.
    
    Args:
        product_id: UUID of the product to optimize
        store_id: UUID of the store
        optimization_options: Options for optimization
        
    Returns:
        Dict with optimization result
    """
    if optimization_options is None:
        optimization_options = {}
        
    logger.info(f"Starting optimization for product {product_id}")
    
    # Get database session
    session = self.get_db_session()
    
    try:
        # Get the product
        product_uuid = uuid.UUID(product_id)
        stmt = select(Product).filter(Product.id == product_uuid)
        result = session.execute(stmt)
        product = result.scalars().first()
        
        if not product:
            raise ValueError(f"Product {product_id} not found")
        
        # Get the job
        job_stmt = select(OptimizationJob).filter(OptimizationJob.store_id == uuid.UUID(store_id))
        job_result = session.execute(job_stmt)
        job = job_result.scalars().first()
        
        if not job:
            raise ValueError(f"Job for store {store_id} not found")
        
        # Update job status to in progress
        job.status = JobStatus.IN_PROGRESS
        job.started_at = None  # Will be set by the model
        session.commit()
        
        # Perform optimization using AI service
        optimizer = ProductOptimizer()
        stream = optimization_options.get('stream', False)

        if stream:
            async def stream_optimization():
                async for update in optimizer.process(input_data=product_data, stream=True):
                    await websocket_manager.send_task_update(self.request.id, update)
            asyncio.run(stream_optimization())
            return {"status": "streaming_started", "task_id": self.request.id}

        else:
            optimized_content_result = asyncio.run(optimizer.process(
                input_data={
                    "name": product.title,
                    "description": product.description,
                    "category": product.category,
                    "features": product.features if hasattr(product, 'features') else [],
                    "brand": product.brand if hasattr(product, 'brand') else None,
                },
                optimization_type='complete'
            ))

            if not optimized_content_result.success:
                raise Exception(optimized_content_result.error_message)

            optimized_content = optimized_content_result.content
        
        # Update product with optimized content
        product.meta_title = optimized_content.get("meta_title")
        product.meta_description = optimized_content.get("meta_description")
        product.title = optimized_content.get("title")
        product.description = optimized_content.get("description")
        product.keywords = optimized_content.get("keywords", [])
        
        # Update SEO score
        product.update_seo_score()
        
        # Create generated content record
        content_record = GeneratedContent(
            product_id=product.id,
            job_id=job.id,
            content_type="full_optimization",
            content=str(optimized_content),
            ai_model=optimization_options.get("model", "default"),
            is_active=True
        )
        session.add(content_record)
        
        # Update job progress
        job.processed_items = 1
        job.status = JobStatus.COMPLETED
        job.completed_at = None  # Will be set by the model
        
        session.commit()
        
        logger.info(f"Completed optimization for product {product_id}")
        
        return {
            "status": "success",
            "product_id": product_id,
            "job_id": str(job.id),
            "message": "Product optimized successfully"
        }
        
    except Exception as e:
        # Update job status to failed
        try:
            job.status = JobStatus.FAILED
            job.last_error = str(e)
            job.completed_at = None  # Will be set by the model
            session.commit()
        except:
            pass  # If job doesn't exist, we can't update it
            
        logger.error(f"Failed to optimize product {product_id}: {str(e)}")
        session.rollback()
        return {
            "status": "failed",
            "product_id": product_id,
            "error": str(e)
        }
        
    finally:
        session.close()

@celery_app.task(base=OptimizationTask, bind=True)
def optimize_product_batch(self, store_id: str, product_ids: List[str]) -> Dict[str, Any]:
    """
    Optimize a batch of products.
    
    Args:
        store_id: UUID of the store
        product_ids: List of product UUIDs to optimize
        
    Returns:
        Dict with batch optimization result
    """
    logger.info(f"Starting batch optimization for {len(product_ids)} products in store {store_id}")
    
    # Get database session
    session = self.get_db_session()
    
    try:
        # Get the job for this batch
        job_stmt = select(OptimizationJob).filter(OptimizationJob.store_id == uuid.UUID(store_id))
        job_result = session.execute(job_stmt)
        job = job_result.scalars().first()
        
        if not job:
            raise ValueError(f"Job for store {store_id} not found")
        
        # Update job status to in progress
        job.status = JobStatus.IN_PROGRESS
        job.started_at = None  # Will be set by the model
        session.commit()
        
        successful_count = 0
        failed_count = 0
        
        # Process each product
        for product_id in product_ids:
            try:
                # Get the product
                product_uuid = uuid.UUID(product_id)
                stmt = select(Product).filter(Product.id == product_uuid)
                result = session.execute(stmt)
                product = result.scalars().first()
                
                if not product:
                    failed_count += 1
                    continue
                
                # Perform optimization using AI service
                optimized_content = optimize_product_content(
                    product_data={
                        "id": str(product.id),
                        "name": product.title,
                        "description": product.description,
                        "meta_title": product.meta_title,
                        "meta_description": product.meta_description,
                        "keywords": product.keywords,
                        "price": float(product.price) if product.price else None,
                        "category": product.category
                    },
                    optimization_options={}  # Use default options
                )
                
                # Update product with optimized content
                product.meta_title = optimized_content.get("meta_title")
                product.meta_description = optimized_content.get("meta_description")
                product.title = optimized_content.get("title")
                product.description = optimized_content.get("description")
                product.keywords = optimized_content.get("keywords", [])
                
                # Update SEO score
                product.update_seo_score()
                
                # Create generated content record
                content_record = GeneratedContent(
                    product_id=product.id,
                    job_id=job.id,
                    content_type="full_optimization",
                    content=str(optimized_content),
                    ai_model="default",
                    is_active=True
                )
                session.add(content_record)
                
                successful_count += 1
                
                # Update job progress
                job.processed_items = successful_count + failed_count
                session.commit()
                
            except Exception as e:
                failed_count += 1
                logger.error(f"Failed to optimize product {product_id}: {str(e)}")
                session.rollback()
                continue
        
        # Update job status to completed
        if failed_count == 0:
            job.status = JobStatus.COMPLETED
        elif successful_count == 0:
            job.status = JobStatus.FAILED
        else:
            job.status = JobStatus.COMPLETED  # Partial success
            
        job.completed_at = None  # Will be set by the model
        job.processed_items = successful_count
        job.failed_items = failed_count
        
        session.commit()
        
        logger.info(f"Completed batch optimization: {successful_count} successful, {failed_count} failed")
        
        return {
            "status": "success",
            "store_id": store_id,
            "total_products": len(product_ids),
            "successful": successful_count,
            "failed": failed_count,
            "message": f"Batch optimization completed with {successful_count} successful and {failed_count} failed optimizations"
        }
        
    except Exception as e:
        # Update job status to failed
        try:
            if 'job' in locals():
                job.status = JobStatus.FAILED
                job.last_error = str(e)
                job.completed_at = None  # Will be set by the model
                session.commit()
        except:
            pass  # If job doesn't exist, we can't update it
            
        logger.error(f"Failed to optimize batch for store {store_id}: {str(e)}")
        session.rollback()
        return {
            "status": "failed",
            "store_id": store_id,
            "error": str(e)
        }
        
    finally:
        session.close()

# Update the Celery app to include these tasks
celery_app.conf.include = ['workers.tasks.product_tasks']