#!/usr/bin/env python3
"""
Script to hash the demo password for the WordPress user.
"""

import sys

def hash_password(password):
    """Hash a password using bcrypt."""
    try:
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        hashed = pwd_context.hash(password)
        return hashed
    except Exception as e:
        print(f"Error hashing password: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python hash_password.py <password>")
        sys.exit(1)
    
    password = sys.argv[1]
    hashed = hash_password(password)
    
    if hashed:
        print(f"Password: {password}")
        print(f"Hashed: {hashed}")
    else:
        sys.exit(1)