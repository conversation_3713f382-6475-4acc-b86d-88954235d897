version: '3.8'

services:
  wordpress-db:
    image: mysql:8.0
    container_name: gridspoke-wordpress-db
    environment:
      MYSQL_DATABASE: wordpress
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
      MYSQL_ROOT_PASSWORD: somewordpress
    volumes:
      - wordpress-db-data:/var/lib/mysql
    networks:
      - gridspoke-wordpress-network

  wordpress:
    depends_on:
      - wordpress-db
    image: wordpress:6.4-fpm-alpine
    container_name: gridspoke-wordpress
    environment:
      WORDPRESS_DB_HOST: wordpress-db:3306
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress
      WORDPRESS_DB_NAME: wordpress
    volumes:
      - wordpress-data:/var/www/html
      - ./wordpress-plugin/gridspoke-seo-connector:/var/www/html/wp-content/plugins/gridspoke-seo-connector
    networks:
      - gridspoke-wordpress-network

  nginx:
    depends_on:
      - wordpress
    image: nginx:1.25-alpine
    container_name: gridspoke-wordpress-nginx
    ports:
      - "8080:80"
    volumes:
      - wordpress-data:/var/www/html
      - ./wordpress-config/nginx.conf:/etc/nginx/nginx.conf
    networks:
      - gridspoke-wordpress-network

volumes:
  wordpress-db-data:
  wordpress-data:

networks:
  gridspoke-wordpress-network:
    driver: bridge