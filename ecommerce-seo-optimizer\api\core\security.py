"""
Security utilities for authentication and authorization.
"""
import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, TYPE_CHECKING
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from core.config import get_settings
from core.database import get_db_session

if TYPE_CHECKING:
    from models.user import User

settings = get_settings()
logger = structlog.get_logger(__name__)

# Password hashing configuration
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTTPBearer(auto_error=True)

# Token types
TOKEN_TYPE_ACCESS = "access"
TOKEN_TYPE_REFRESH = "refresh"


class SecurityException(HTTPException):
    """Custom security exception."""
    pass


def create_access_token(
    data: Dict[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create JWT access token.
    
    Args:
        data: Token payload data
        expires_delta: Custom expiration time
        
    Returns:
        str: Encoded JWT token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode.update({
        "exp": expire,
        "type": TOKEN_TYPE_ACCESS,
        "iat": datetime.now(timezone.utc),
        "jti": str(uuid.uuid4())  # JWT ID for tracking
    })
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    
    logger.debug("Access token created", user_id=data.get("sub"), expires=expire)
    return encoded_jwt


def create_refresh_token(data: Dict[str, Any]) -> str:
    """
    Create JWT refresh token.
    
    Args:
        data: Token payload data
        
    Returns:
        str: Encoded JWT refresh token
    """
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(
        days=settings.REFRESH_TOKEN_EXPIRE_DAYS
    )
    
    to_encode.update({
        "exp": expire,
        "type": TOKEN_TYPE_REFRESH,
        "iat": datetime.now(timezone.utc),
        "jti": str(uuid.uuid4())
    })
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    
    logger.debug("Refresh token created", user_id=data.get("sub"), expires=expire)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password against hash.
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password from database
        
    Returns:
        bool: True if password matches
    """
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error("Password verification failed", error=str(e))
        return False


def get_password_hash(password: str) -> str:
    """
    Hash password using bcrypt.
    
    Args:
        password: Plain text password
        
    Returns:
        str: Hashed password
    """
    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error("Password hashing failed", error=str(e))
        raise SecurityException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password hashing failed"
        )


async def authenticate_user(db: AsyncSession, email: str, password: str):
    """Authenticate user with email and password."""
    from crud.user import user_crud
    
    user = await user_crud.get_by_email(db, email=email)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user


def decode_token(token: str, token_type: Optional[str] = None) -> Dict[str, Any]:
    """
    Decode and validate JWT token.
    
    Args:
        token: JWT token string
        token_type: Expected token type (access/refresh)
        
    Returns:
        dict: Token payload
        
    Raises:
        SecurityException: If token is invalid
    """
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # Validate token type if specified
        if token_type and payload.get("type") != token_type:
            raise SecurityException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type"
            )
        
        # Check if token is expired
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(timezone.utc):
            raise SecurityException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        
        logger.debug("Token decoded successfully", user_id=payload.get("sub"))
        return payload
        
    except JWTError as e:
        logger.warning("JWT decode error", error=str(e))
        raise SecurityException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"}
        )


async def get_current_user_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> uuid.UUID:
    """
    Extract user ID from JWT token.
    
    Args:
        credentials: HTTP Bearer credentials
        
    Returns:
        uuid.UUID: Current user ID
        
    Raises:
        SecurityException: If token is invalid
    """
    try:
        payload = decode_token(credentials.credentials, TOKEN_TYPE_ACCESS)
        user_id_str = payload.get("sub")
        
        if user_id_str is None:
            raise SecurityException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token missing user ID"
            )
        
        user_id = uuid.UUID(user_id_str)
        logger.debug("User ID extracted from token", user_id=str(user_id))
        return user_id
        
    except ValueError:
        logger.warning("Invalid user ID format in token")
        raise SecurityException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user ID format"
        )


async def verify_refresh_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify refresh token and return payload.
    
    Args:
        token: Refresh JWT token
        
    Returns:
        Dict containing token payload or None if invalid
    """
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        token_type = payload.get("type")
        if token_type != TOKEN_TYPE_REFRESH:
            return None
        
        return payload
        
    except JWTError as e:
        logger.warning("Refresh token verification failed", error=str(e))
        return None


async def get_current_user(
    user_id: uuid.UUID = Depends(get_current_user_id),
    db: AsyncSession = Depends(get_db_session)
) -> "User":  # Forward reference
    """
    Get current authenticated user.
    
    Args:
        user_id: User ID from token
        db: Database session
        
    Returns:
        User: Current user instance
        
    Raises:
        SecurityException: If user not found
    """
    from crud.user import user_crud  # Avoid circular import
    
    user = await user_crud.get(db, user_id)
    if user is None:
        logger.warning("User not found", user_id=str(user_id))
        raise SecurityException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    if not user.is_active:
        logger.warning("Inactive user attempted access", user_id=str(user_id))
        raise SecurityException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user"
        )
    
    logger.debug("Current user retrieved", user_id=str(user_id), email=user.email)
    return user


async def get_current_superuser(
    current_user: "User" = Depends(get_current_user)
) -> "User":
    """
    Get current user and verify superuser status.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User: Current superuser
        
    Raises:
        SecurityException: If user is not superuser
    """
    if not current_user.is_superuser:
        logger.warning("Non-superuser attempted admin access", user_id=str(current_user.id))
        raise SecurityException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return current_user


def create_token_pair(user_id: uuid.UUID) -> Dict[str, str]:
    """
    Create access and refresh token pair.
    
    Args:
        user_id: User ID to include in tokens
        
    Returns:
        dict: Access and refresh tokens
    """
    user_data = {"sub": str(user_id)}
    
    access_token = create_access_token(user_data)
    refresh_token = create_refresh_token(user_data)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }


def verify_webhook_signature(payload: str, signature: str) -> bool:
    """
    Verify webhook signature from WordPress plugin.
    
    Args:
        payload: Request payload
        signature: Signature header
        
    Returns:
        bool: True if signature is valid
    """
    import hmac
    import hashlib
    
    try:
        expected_signature = hmac.new(
            settings.WEBHOOK_SECRET.encode(),
            payload.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Compare signatures securely
        return hmac.compare_digest(signature, expected_signature)
    except Exception as e:
        logger.error("Webhook signature verification failed", error=str(e))
        return False


async def get_current_user_ws(token: str, db: AsyncSession) -> Optional["User"]:
    """
    Get current user from WebSocket token.
    Similar to get_current_user but for WebSocket connections.
    """
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        user_id = payload.get("sub")
        if user_id is None:
            return None
            
        token_type = payload.get("type")
        if token_type != TOKEN_TYPE_ACCESS:
            return None
            
        # Get user from database
        from crud import user_crud
        user = await user_crud.get(db, id=uuid.UUID(user_id))
        return user
        
    except JWTError:
        return None
    except Exception as e:
        logger.error("WebSocket authentication failed", error=str(e))
        return None
