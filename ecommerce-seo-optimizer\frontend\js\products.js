/**
 * GridSpoke SEO Optimizer - Products Management Module
 * Handles product listing, optimization, bulk operations, and search/filter functionality
 */

const Products = {
    // Module state
    state: {
        products: [],
        selectedProducts: new Set(),
        currentFilters: {},
        currentSort: { field: 'created_at', direction: 'desc' },
        currentPage: 1,
        pageSize: 25,
        totalProducts: 0,
        isLoading: false,
        searchTerm: '',
        storeId: null
    },

    // DOM elements
    elements: {
        container: null,
        table: null,
        searchInput: null,
        filterPanel: null,
        bulkActions: null,
        pagination: null
    },

    /**
     * Initialize products module
     */
    init() {
        this.bindElements();
        this.setupEventListeners();
        this.setupWebSocketSubscriptions();
        this.loadProducts();
    },

    /**
     * Bind DOM elements
     */
    bindElements() {
        this.elements.container = document.getElementById('products-container');
    },

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search input debounced handler
        if (this.elements.searchInput) {
            this.elements.searchInput.addEventListener('input', 
                Utils.debounce(this.handleSearch.bind(this), 300)
            );
        }
    },

    /**
     * Setup WebSocket subscriptions for real-time updates
     */
    setupWebSocketSubscriptions() {
        if (WebSocketClient) {
            WebSocketClient.subscribe('products', this.handleWebSocketUpdate.bind(this));
        }
    },

    /**
     * Load products from API
     * @param {Object} options - Load options
     */
    async loadProducts(options = {}) {
        if (this.state.isLoading) return;

        this.state.isLoading = true;
        this.showLoading();

        try {
            const params = {
                page: this.state.currentPage,
                size: this.state.pageSize,
                search: this.state.searchTerm,
                sort_by: this.state.currentSort.field,
                sort_direction: this.state.currentSort.direction,
                ...this.state.currentFilters,
                ...options
            };

            // Resolve current store ID from dashboard state or API
            const storeId = await this.getCurrentStoreId();
            
            const response = await API.getProducts(storeId, params);
            
            this.state.products = response.items || response.data || [];
            this.state.totalProducts = response.total || this.state.products.length;
            
            this.renderProducts();
            this.renderPagination();
            
        } catch (error) {
            console.error('Error loading products:', error);
            const msg = (error && error.message) ? error.message : 'Failed to load products';
            Utils.showToast(msg, 'error');
            this.showError(msg);
        } finally {
            this.state.isLoading = false;
            this.hideLoading();
        }
    },

    /**
     * Render products table
     */
    renderProducts() {
        if (!this.elements.container) return;

        const productsContent = document.getElementById('products-content');
        if (!productsContent) return;

        if (this.state.products.length === 0) {
            productsContent.innerHTML = `
                <div class="text-center py-8">
                    <p class="text-gray-500">No products found</p>
                    <p class="text-sm text-gray-400 mt-2">Sync your WordPress/WooCommerce store to see products here</p>
                </div>
            `;
            return;
        }

        const tableHtml = `
            <div class="overflow-x-auto">
                <table class="table-gridspoke">
                    <thead>
                        <tr>
                            <th class="w-8">
                                <input 
                                    type="checkbox" 
                                    id="select-all"
                                    ${this.state.selectedProducts.size === this.state.products.length && this.state.products.length > 0 ? 'checked' : ''}
                                    class="h-4 w-4 text-gridspoke-primary focus:ring-gridspoke-primary border-gray-300 rounded"
                                >
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="name">
                                Name
                                ${this.getSortIcon('name')}
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="sku">
                                SKU
                                ${this.getSortIcon('sku')}
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="category">
                                Category
                                ${this.getSortIcon('category')}
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="price">
                                Price
                                ${this.getSortIcon('price')}
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="seo_score">
                                SEO Score
                                ${this.getSortIcon('seo_score')}
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="status">
                                Status
                                ${this.getSortIcon('status')}
                            </th>
                            <th class="cursor-pointer hover:bg-gray-100" data-sort="last_optimized">
                                Last Optimized
                                ${this.getSortIcon('last_optimized')}
                            </th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.renderProductRows()}
                    </tbody>
                </table>
            </div>
        `;

        productsContent.innerHTML = tableHtml;
        this.bindTableEvents();
    },

    /**
     * Render product table rows
     */
    renderProductRows() {
        if (this.state.products.length === 0) {
            return `
                <tr>
                    <td colspan="9" class="text-center py-8 text-gray-500">
                        No products found. 
                        <button id="sync-products-empty" class="text-gridspoke-primary hover:text-blue-600 underline">
                            Sync products from your store
                        </button>
                    </td>
                </tr>
            `;
        }

        return this.state.products.map(product => `
            <tr class="hover:bg-gray-50">
                <td>
                    <input 
                        type="checkbox" 
                        class="product-checkbox h-4 w-4 text-gridspoke-primary focus:ring-gridspoke-primary border-gray-300 rounded"
                        value="${product.id}"
                        ${this.state.selectedProducts.has(product.id) ? 'checked' : ''}
                    >
                </td>
                <td>
                    <div class="flex items-center">
                        ${product.image_url ? `<img src="${product.image_url}" alt="${product.name}" class="h-10 w-10 rounded object-cover mr-3">` : ''}
                        <div>
                            <div class="font-medium text-gray-900">${Utils.escapeHtml(Utils.truncate(product.name, 40))}</div>
                            ${product.short_description ? `<div class="text-sm text-gray-500">${Utils.escapeHtml(Utils.truncate(product.short_description, 60))}</div>` : ''}
                        </div>
                    </div>
                </td>
                <td class="text-sm text-gray-900">${product.sku || 'N/A'}</td>
                <td class="text-sm text-gray-900">${product.category || 'Uncategorized'}</td>
                <td class="text-sm text-gray-900">${product.price ? '$' + product.price : 'N/A'}</td>
                <td>
                    <div class="flex items-center">
                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div class="bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 h-2 rounded-full" style="width: ${product.seo_score || 0}%"></div>
                        </div>
                        <span class="text-sm text-gray-900">${product.seo_score || 0}%</span>
                    </div>
                </td>
                <td>
                    <span class="status-badge ${this.getStatusColorClass(product.optimization_status)}">
                        ${product.optimization_status || 'pending'}
                    </span>
                </td>
                <td class="text-sm text-gray-900">${product.last_optimized ? Utils.formatRelativeTime(product.last_optimized) : 'Never'}</td>
                <td>
                    <div class="flex items-center space-x-2">
                        <button class="text-gridspoke-primary hover:text-blue-600 text-sm" onclick="Products.viewProduct('${product.id}')">
                            View
                        </button>
                        <button class="text-gridspoke-primary hover:text-blue-600 text-sm" onclick="Products.optimizeProduct('${product.id}')">
                            Optimize
                        </button>
                        <button class="text-gray-400 hover:text-gray-600 text-sm" onclick="Products.showProductMenu('${product.id}', event)">
                            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                            </svg>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * Render pagination controls
     */
    renderPaginationControls() {
        const totalPages = Math.ceil(this.state.totalProducts / this.state.pageSize);
        
        if (totalPages <= 1) return '';

        const currentPage = this.state.currentPage;
        const maxVisiblePages = 7;
        
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        let paginationHTML = `
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing ${((currentPage - 1) * this.state.pageSize) + 1} to ${Math.min(currentPage * this.state.pageSize, this.state.totalProducts)} of ${this.state.totalProducts} products
                </div>
                <div class="flex items-center space-x-1">
        `;

        // Previous button
        paginationHTML += `
            <button 
                class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}"
                onclick="Products.goToPage(${currentPage - 1})"
                ${currentPage === 1 ? 'disabled' : ''}
            >
                Previous
            </button>
        `;

        // Page numbers
        if (startPage > 1) {
            paginationHTML += `<button class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50" onclick="Products.goToPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span class="px-2 py-2 text-sm text-gray-500">...</span>`;
            }
        }

        for (let page = startPage; page <= endPage; page++) {
            paginationHTML += `
                <button 
                    class="px-3 py-2 text-sm border rounded-md ${page === currentPage ? 'bg-gridspoke-primary text-white border-gridspoke-primary' : 'border-gray-300 hover:bg-gray-50'}"
                    onclick="Products.goToPage(${page})"
                >
                    ${page}
                </button>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span class="px-2 py-2 text-sm text-gray-500">...</span>`;
            }
            paginationHTML += `<button class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50" onclick="Products.goToPage(${totalPages})">${totalPages}</button>`;
        }

        // Next button
        paginationHTML += `
            <button 
                class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}"
                onclick="Products.goToPage(${currentPage + 1})"
                ${currentPage === totalPages ? 'disabled' : ''}
            >
                Next
            </button>
        `;

        paginationHTML += `
                </div>
            </div>
        `;

        return paginationHTML;
    },

    /**
     * Bind table event listeners
     */
    bindTableEvents() {
        // Search input
        const searchInput = document.getElementById('product-search');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.state.searchTerm = e.target.value;
                this.state.currentPage = 1;
                this.loadProducts();
            }, 300));
        }

        // Select all checkbox
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }

        // Individual product checkboxes
        document.querySelectorAll('.product-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.toggleProductSelection(e.target.value, e.target.checked);
            });
        });

        // Sort headers
        document.querySelectorAll('[data-sort]').forEach(header => {
            header.addEventListener('click', (e) => {
                const field = e.currentTarget.getAttribute('data-sort');
                this.handleSort(field);
            });
        });

        // Bulk action buttons
        const bulkOptimizeBtn = document.getElementById('bulk-optimize-btn');
        if (bulkOptimizeBtn) {
            bulkOptimizeBtn.addEventListener('click', () => this.bulkOptimizeProducts());
        }

        const clearSelectionBtn = document.getElementById('clear-selection-btn');
        if (clearSelectionBtn) {
            clearSelectionBtn.addEventListener('click', () => this.clearSelection());
        }

        // Sync products button
        const syncBtn = document.getElementById('sync-products-btn');
        if (syncBtn) {
            syncBtn.addEventListener('click', () => this.syncProducts());
        }
    },

    /**
     * Get sort icon for column header
     */
    getSortIcon(field) {
        if (this.state.currentSort.field !== field) {
            return '<svg class="inline w-3 h-3 ml-1 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path></svg>';
        }
        
        return this.state.currentSort.direction === 'asc' 
            ? '<svg class="inline w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>'
            : '<svg class="inline w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>';
    },

    /**
     * Get status color class
     */
    getStatusColorClass(status) {
        return Utils.getStatusColor(status);
    },

    /**
     * Handle sorting
     */
    handleSort(field) {
        if (this.state.currentSort.field === field) {
            this.state.currentSort.direction = this.state.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.state.currentSort.field = field;
            this.state.currentSort.direction = 'asc';
        }
        
        this.state.currentPage = 1;
        this.loadProducts();
    },

    /**
     * Toggle select all products
     */
    toggleSelectAll(checked) {
        if (checked) {
            this.state.products.forEach(product => {
                this.state.selectedProducts.add(product.id);
            });
        } else {
            this.state.selectedProducts.clear();
        }
        
        this.updateBulkActionsVisibility();
        this.updateCheckboxes();
    },

    /**
     * Toggle individual product selection
     */
    toggleProductSelection(productId, checked) {
        if (checked) {
            this.state.selectedProducts.add(productId);
        } else {
            this.state.selectedProducts.delete(productId);
        }
        
        this.updateBulkActionsVisibility();
        this.updateSelectAllCheckbox();
    },

    /**
     * Clear selection
     */
    clearSelection() {
        this.state.selectedProducts.clear();
        this.updateBulkActionsVisibility();
        this.updateCheckboxes();
    },

    /**
     * Update bulk actions visibility
     */
    updateBulkActionsVisibility() {
        const bulkActions = document.getElementById('bulk-actions');
        if (bulkActions) {
            if (this.state.selectedProducts.size > 0) {
                bulkActions.classList.remove('hidden');
                bulkActions.querySelector('span').textContent = 
                    `${this.state.selectedProducts.size} product${this.state.selectedProducts.size !== 1 ? 's' : ''} selected`;
            } else {
                bulkActions.classList.add('hidden');
            }
        }
    },

    /**
     * Update checkbox states
     */
    updateCheckboxes() {
        document.querySelectorAll('.product-checkbox').forEach(checkbox => {
            checkbox.checked = this.state.selectedProducts.has(checkbox.value);
        });
        this.updateSelectAllCheckbox();
    },

    /**
     * Update select all checkbox state
     */
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = this.state.selectedProducts.size === this.state.products.length && this.state.products.length > 0;
            selectAllCheckbox.indeterminate = this.state.selectedProducts.size > 0 && this.state.selectedProducts.size < this.state.products.length;
        }
    },

    /**
     * Go to specific page
     */
    goToPage(page) {
        if (page < 1 || page > Math.ceil(this.state.totalProducts / this.state.pageSize)) {
            return;
        }
        
        this.state.currentPage = page;
        this.loadProducts();
    },

    /**
     * View product details
     */
    async viewProduct(productId) {
        try {
            const storeId = await this.getCurrentStoreId();
            const product = await API.getProduct(storeId, productId);
            this.showProductModal(product);
        } catch (error) {
            Utils.showToast('Failed to load product details', 'error');
        }
    },

    /**
     * Optimize single product
     */
    async optimizeProduct(productId) {
        try {
            Utils.showToast('Starting product optimization...', 'info');
            
            const storeId = await this.getCurrentStoreId();
            const result = await API.optimizeProduct(storeId, productId);
            
            Utils.showToast('Product optimization started successfully', 'success');
            
            // Refresh products list
            this.loadProducts();
            
        } catch (error) {
            Utils.showToast('Failed to start optimization', 'error');
        }
    },

    /**
     * Bulk optimize selected products
     */
    async bulkOptimizeProducts() {
        if (this.state.selectedProducts.size === 0) {
            Utils.showToast('Please select products to optimize', 'warning');
            return;
        }

        try {
            const productIds = Array.from(this.state.selectedProducts);
            
            Utils.showToast(`Starting optimization for ${productIds.length} products...`, 'info');
            
            const storeId = await this.getCurrentStoreId();
            const result = await API.bulkOptimizeProducts(storeId, productIds);
            
            Utils.showToast('Bulk optimization started successfully', 'success');
            
            // Clear selection and refresh
            this.clearSelection();
            this.loadProducts();
            
        } catch (error) {
            Utils.showToast('Failed to start bulk optimization', 'error');
        }
    },

    /**
     * Sync products from external store
     */
    async syncProducts() {
        try {
            Utils.showToast('Syncing products from store...', 'info');
            
            const storeId = await this.getCurrentStoreId();
            const result = await API.syncProducts(storeId);
            
            Utils.showToast('Product sync completed successfully', 'success');
            
            // Refresh products list
            this.loadProducts();
            
        } catch (error) {
            Utils.showToast('Failed to sync products', 'error');
        }
    },

    /**
     * Handle WebSocket updates
     */
    handleWebSocketUpdate(data) {
        if (data.type === 'product_updated') {
            // Update specific product in the list
            const productIndex = this.state.products.findIndex(p => p.id === data.product_id);
            if (productIndex !== -1) {
                this.state.products[productIndex] = { ...this.state.products[productIndex], ...data.product };
                this.renderProducts();
            }
        } else if (data.type === 'products_synced') {
            // Refresh entire list
            this.loadProducts();
        }
    },

    /**
     * Show loading state
     */
    showLoading() {
        if (this.elements.container) {
            this.elements.container.innerHTML = `
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gridspoke-primary"></div>
                    <p class="mt-2 text-gray-600">Loading products...</p>
                </div>
            `;
        }
    },

    /**
     * Hide loading state
     */
    hideLoading() {
        // Loading state is hidden when products are rendered
    },

    /**
     * Show error state
     */
    showError(message) {
        if (this.elements.container) {
            this.elements.container.innerHTML = `
                <div class="bg-white rounded-lg shadow p-8 text-center">
                    <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Error loading products</h3>
                    <p class="mt-1 text-sm text-gray-500">${message}</p>
                    <button onclick="Products.loadProducts()" class="mt-4 bg-gridspoke-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
                        Try Again
                    </button>
                </div>
            `;
        }
    },

    /**
     * Get current store ID (placeholder - would get from app state)
     */
    async getCurrentStoreId() {
        // Prefer dashboard-selected store
        try {
            const app = window.dashboardApp && window.dashboardApp();
            const selectedId = app && app.settings && app.settings.selectedStoreId;
            if (selectedId) return selectedId;
        } catch (_) { /* ignore */ }

        // Fallback: ask API for current store
        try {
            const current = await API.getCurrentStore();
            if (current && current.id) return current.id;
        } catch (_) { /* ignore */ }

        // Final fallback to known test store (pre-provisioned)
        return 'dbb3d2b0-0be2-41e0-947b-47b809fc5da3';
    },

    /**
     * Handle update from parent component
     */
    handleUpdate(data) {
        this.handleWebSocketUpdate(data);
    }
};

// Export for global access
window.Products = Products;
