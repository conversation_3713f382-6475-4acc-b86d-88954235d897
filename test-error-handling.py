#!/usr/bin/env python3
"""
Test script to verify error handling in the GridSpoke WordPress plugin API client
"""

import requests
import json

# API endpoint
api_endpoint = 'http://localhost:8000/'

def make_request(method, endpoint, data=None):
    """
    Make HTTP request to GridSpoke API
    """
    url = api_endpoint + endpoint.lstrip('/')
    
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'GridSpoke-Test-Script/1.0'
    }
    
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers, timeout=30)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=data, timeout=30)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
            
        return {
            'status_code': response.status_code,
            'body': response.json() if response.content else None,
            'headers': dict(response.headers)
        }
    except requests.exceptions.RequestException as e:
        return {
            'status_code': 0,
            'body': {'error': str(e)},
            'headers': {}
        }

def test_error_handling():
    """
    Test error handling scenarios
    """
    print("[INFO] Testing error handling in API client...")
    
    # Test 1: Invalid endpoint (404)
    print("[TEST 1] Testing 404 error...")
    response = make_request('GET', 'api/v1/nonexistent-endpoint')
    print(f"  Status: {response['status_code']}")
    if response['body']:
        print(f"  Body: {response['body']}")
    
    # Test 2: Invalid authentication (401)
    print("[TEST 2] Testing 401 error...")
    payload = {
        'email': '<EMAIL>',
        'password': 'wrongpassword'
    }
    response = make_request('POST', 'api/v1/auth/login', payload)
    print(f"  Status: {response['status_code']}")
    if response['body']:
        print(f"  Body: {response['body']}")
    
    # Test 3: Rate limiting simulation (if supported)
    print("[TEST 3] Testing multiple rapid requests...")
    for i in range(5):
        response = make_request('GET', 'health')
        print(f"  Request {i+1}: Status {response['status_code']}")
    
    # Test 4: Invalid data format (400)
    print("[TEST 4] Testing 400 error with invalid data...")
    invalid_data = {
        'invalid_field': 'invalid_value'
    }
    response = make_request('POST', 'api/v1/auth/login', invalid_data)
    print(f"  Status: {response['status_code']}")
    if response['body']:
        print(f"  Body: {response['body']}")
    
    print("\n[INFO] Error handling tests completed")

if __name__ == "__main__":
    test_error_handling()