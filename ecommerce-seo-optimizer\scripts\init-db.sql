-- Initialize PostgreSQL database with required extensions and basic schema

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;

-- Create initial indexes for performance
-- This will be expanded by Alembic migrations

-- Set up basic configuration
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET random_page_cost = 1.1;

-- Vector-specific configuration
ALTER SYSTEM SET max_wal_size = '2GB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;

-- Create a simple health check function
CREATE OR REPLACE FUNCTION check_health()
RETURNS json AS $$
BEGIN
    RETURN json_build_object(
        'status', 'healthy',
        'timestamp', now(),
        'extensions', (
            SELECT json_agg(extname) 
            FROM pg_extension 
            WHERE extname IN ('uuid-ossp', 'vector')
        )
    );
END;
$$ LANGUAGE plpgsql;
