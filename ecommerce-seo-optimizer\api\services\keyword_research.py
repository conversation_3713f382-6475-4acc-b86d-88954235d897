"""
Keyword Research Service - Advanced keyword research and analysis
Provides comprehensive keyword research, clustering, and opportunity identification.
"""

import asyncio
import re
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from pydantic import BaseModel, <PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from ..agents.seo_optimizer import SEOOptimizer
from ..core.config import settings


class KeywordData(BaseModel):
    """Core keyword data"""
    keyword: str
    search_volume: Optional[int] = None
    keyword_difficulty: Optional[float] = None  # 0-100
    cpc: Optional[float] = None  # Cost per click
    competition: Optional[str] = None  # "low", "medium", "high"
    trend: Optional[str] = None  # "rising", "stable", "declining"
    seasonality: Optional[Dict[str, float]] = None  # Monthly trends
    intent: Optional[str] = None  # "informational", "commercial", "transactional", "navigational"
    parent_topic: Optional[str] = None


class KeywordCluster(BaseModel):
    """Keyword cluster for topic-based organization"""
    cluster_name: str
    primary_keyword: str
    related_keywords: List[str]
    total_search_volume: int
    avg_difficulty: float
    content_type: str  # "blog_post", "product_page", "category_page", "landing_page"
    search_intent: str
    opportunity_score: float  # 0-100


class KeywordGap(BaseModel):
    """Keyword gap analysis result"""
    keyword: str
    search_volume: Optional[int] = None
    our_position: Optional[int] = None
    competitor_position: Optional[int] = None
    competitor_url: Optional[str] = None
    gap_type: str  # "missing", "underperforming", "opportunity"
    priority: str  # "high", "medium", "low"
    estimated_traffic: Optional[int] = None


class LSIKeyword(BaseModel):
    """Latent Semantic Indexing keyword"""
    keyword: str
    relevance_score: float  # 0-1
    search_volume: Optional[int] = None
    usage_frequency: int  # How often it appears with main keyword


class KeywordOpportunity(BaseModel):
    """Keyword opportunity with actionable insights"""
    keyword: str
    opportunity_type: str  # "low_competition", "high_volume", "trending", "long_tail"
    search_volume: Optional[int] = None
    difficulty: Optional[float] = None
    estimated_traffic: Optional[int] = None
    competition_level: str
    recommended_content_type: str
    target_url: Optional[str] = None
    priority_score: float  # 0-100
    reasoning: str


class KeywordResearchReport(BaseModel):
    """Comprehensive keyword research report"""
    seed_keywords: List[str]
    research_date: datetime = Field(default_factory=datetime.utcnow)
    total_keywords_found: int
    keyword_clusters: List[KeywordCluster]
    top_opportunities: List[KeywordOpportunity]
    keyword_gaps: List[KeywordGap]
    lsi_keywords: List[LSIKeyword]
    content_calendar_suggestions: List[Dict[str, Any]]
    monthly_search_trends: Dict[str, List[float]]
    competitor_keywords: Dict[str, List[str]]  # competitor_domain -> keywords
    recommended_budget: Optional[float] = None  # For PPC


class KeywordResearchService:
    """Main keyword research service"""
    
    def __init__(self):
        self.seo_optimizer = SEOOptimizer()
        
    async def comprehensive_keyword_research(
        self,
        seed_keywords: List[str],
        target_domain: str,
        competitor_domains: List[str] = None,
        location: str = "US",
        language: str = "en",
        include_lsi: bool = True,
        include_trends: bool = True,
        max_keywords: int = 1000
    ) -> KeywordResearchReport:
        """Perform comprehensive keyword research"""
        
        all_keywords = set(seed_keywords)
        
        # 1. Expand keywords using various methods
        expanded_keywords = await self._expand_keywords(seed_keywords, max_keywords)
        all_keywords.update(expanded_keywords)
        
        # 2. Get keyword data for all keywords
        keyword_data = await self._get_keyword_metrics(
            list(all_keywords), location, language
        )
        
        # 3. Analyze competitor keywords
        competitor_keywords = {}
        if competitor_domains:
            competitor_keywords = await self._analyze_competitor_keywords(
                competitor_domains, list(all_keywords)
            )
        
        # 4. Perform gap analysis
        keyword_gaps = await self._perform_gap_analysis(
            list(all_keywords), target_domain, competitor_keywords
        )
        
        # 5. Cluster keywords by topic
        keyword_clusters = await self._cluster_keywords(keyword_data)
        
        # 6. Find opportunities
        opportunities = await self._identify_opportunities(
            keyword_data, keyword_gaps, competitor_keywords
        )
        
        # 7. Get LSI keywords
        lsi_keywords = []
        if include_lsi:
            lsi_keywords = await self._get_lsi_keywords(seed_keywords)
            
        # 8. Analyze trends
        trends = {}
        if include_trends:
            trends = await self._analyze_keyword_trends(list(all_keywords))
            
        # 9. Generate content calendar
        content_calendar = await self._generate_content_calendar(
            keyword_clusters, opportunities
        )
        
        # 10. Calculate recommended PPC budget
        recommended_budget = await self._calculate_ppc_budget(opportunities)
        
        return KeywordResearchReport(
            seed_keywords=seed_keywords,
            total_keywords_found=len(all_keywords),
            keyword_clusters=keyword_clusters,
            top_opportunities=opportunities[:50],  # Top 50 opportunities
            keyword_gaps=keyword_gaps,
            lsi_keywords=lsi_keywords,
            content_calendar_suggestions=content_calendar,
            monthly_search_trends=trends,
            competitor_keywords=competitor_keywords,
            recommended_budget=recommended_budget
        )
        
    async def find_long_tail_keywords(
        self,
        seed_keyword: str,
        min_search_volume: int = 10,
        max_difficulty: float = 40.0,
        limit: int = 100
    ) -> List[KeywordOpportunity]:
        """Find long-tail keyword opportunities"""
        
        # Generate long-tail variations
        long_tail_keywords = await self._generate_long_tail_variations(seed_keyword)
        
        # Get metrics for long-tail keywords
        keyword_data = await self._get_keyword_metrics(long_tail_keywords)
        
        opportunities = []
        for kw_data in keyword_data:
            if (kw_data.search_volume and kw_data.search_volume >= min_search_volume and
                kw_data.keyword_difficulty and kw_data.keyword_difficulty <= max_difficulty):
                
                opportunity = KeywordOpportunity(
                    keyword=kw_data.keyword,
                    opportunity_type="long_tail",
                    search_volume=kw_data.search_volume,
                    difficulty=kw_data.keyword_difficulty,
                    estimated_traffic=int(kw_data.search_volume * 0.3),  # 30% CTR estimate
                    competition_level="low" if kw_data.keyword_difficulty < 20 else "medium",
                    recommended_content_type="blog_post",
                    priority_score=await self._calculate_priority_score(kw_data),
                    reasoning=f"Low competition ({kw_data.keyword_difficulty}) with decent volume ({kw_data.search_volume})"
                )
                opportunities.append(opportunity)
                
        # Sort by priority score
        opportunities.sort(key=lambda x: x.priority_score, reverse=True)
        return opportunities[:limit]
        
    async def analyze_seasonal_keywords(
        self,
        keywords: List[str],
        years_back: int = 2
    ) -> Dict[str, Dict[str, Any]]:
        """Analyze seasonal trends for keywords"""
        
        seasonal_analysis = {}
        
        for keyword in keywords:
            # Get historical trend data
            trend_data = await self._get_historical_trends(keyword, years_back)
            
            # Analyze seasonality
            seasonal_info = await self._analyze_seasonality(trend_data)
            
            seasonal_analysis[keyword] = {
                "peak_months": seasonal_info["peak_months"],
                "low_months": seasonal_info["low_months"],
                "seasonality_score": seasonal_info["score"],  # 0-100
                "year_over_year_growth": seasonal_info["growth"],
                "recommended_timing": seasonal_info["timing"],
                "trend_data": trend_data
            }
            
        return seasonal_analysis
        
    async def keyword_difficulty_analysis(
        self,
        keywords: List[str],
        target_domain: str
    ) -> Dict[str, Dict[str, Any]]:
        """Analyze keyword difficulty and ranking probability"""
        
        analysis = {}
        
        # Get domain authority for target domain
        domain_authority = await self._get_domain_authority(target_domain)
        
        for keyword in keywords:
            # Get SERP analysis
            serp_data = await self._analyze_serp(keyword)
            
            # Calculate custom difficulty score
            difficulty_score = await self._calculate_custom_difficulty(
                keyword, serp_data, domain_authority
            )
            
            # Estimate ranking probability
            ranking_probability = await self._estimate_ranking_probability(
                difficulty_score, domain_authority
            )
            
            # Get required content quality
            content_requirements = await self._analyze_content_requirements(serp_data)
            
            analysis[keyword] = {
                "difficulty_score": difficulty_score,
                "ranking_probability": ranking_probability,
                "top_10_avg_da": serp_data["avg_da"],
                "top_10_avg_content_length": serp_data["avg_content_length"],
                "required_backlinks": serp_data["avg_backlinks"],
                "content_requirements": content_requirements,
                "time_to_rank_estimate": await self._estimate_time_to_rank(difficulty_score),
                "recommended_strategy": await self._get_ranking_strategy(difficulty_score, ranking_probability)
            }
            
        return analysis
        
    async def find_question_keywords(
        self,
        topic: str,
        limit: int = 50
    ) -> List[KeywordData]:
        """Find question-based keywords for FAQ and content"""
        
        # Common question patterns
        question_patterns = [
            "what is {topic}",
            "how to {topic}",
            "why {topic}",
            "when {topic}",
            "where {topic}",
            "how much {topic}",
            "how many {topic}",
            "best {topic}",
            "top {topic}",
            "{topic} vs",
            "{topic} comparison",
            "{topic} reviews",
            "{topic} guide",
            "{topic} tutorial"
        ]
        
        question_keywords = []
        for pattern in question_patterns:
            keywords = await self._generate_question_variations(pattern.format(topic=topic))
            question_keywords.extend(keywords)
            
        # Get metrics for question keywords
        keyword_data = await self._get_keyword_metrics(question_keywords)
        
        # Filter and sort by search volume
        valid_keywords = [kw for kw in keyword_data if kw.search_volume and kw.search_volume > 10]
        valid_keywords.sort(key=lambda x: x.search_volume, reverse=True)
        
        return valid_keywords[:limit]
        
    async def generate_content_keywords(
        self,
        content_type: str,  # "blog", "product", "category", "landing"
        industry: str,
        target_audience: str,
        content_goals: List[str]  # ["awareness", "conversion", "engagement"]
    ) -> Dict[str, List[KeywordData]]:
        """Generate keywords optimized for specific content types"""
        
        content_keywords = {
            "primary_keywords": [],
            "secondary_keywords": [],
            "long_tail_keywords": [],
            "lsi_keywords": []
        }
        
        # Generate base keywords by content type
        if content_type == "blog":
            base_keywords = await self._generate_blog_keywords(industry, target_audience)
        elif content_type == "product":
            base_keywords = await self._generate_product_keywords(industry, content_goals)
        elif content_type == "category":
            base_keywords = await self._generate_category_keywords(industry)
        else:  # landing page
            base_keywords = await self._generate_landing_keywords(industry, content_goals)
            
        # Categorize keywords
        for keyword_data in base_keywords:
            if keyword_data.search_volume and keyword_data.search_volume > 1000:
                content_keywords["primary_keywords"].append(keyword_data)
            elif keyword_data.search_volume and keyword_data.search_volume > 100:
                content_keywords["secondary_keywords"].append(keyword_data)
            else:
                content_keywords["long_tail_keywords"].append(keyword_data)
                
        # Get LSI keywords for primary keywords
        if content_keywords["primary_keywords"]:
            primary_kw_text = [kw.keyword for kw in content_keywords["primary_keywords"]]
            lsi_keywords = await self._get_lsi_keywords(primary_kw_text)
            content_keywords["lsi_keywords"] = lsi_keywords
            
        return content_keywords
        
    async def _expand_keywords(self, seed_keywords: List[str], max_keywords: int) -> List[str]:
        """Expand seed keywords using various methods"""
        
        expanded = set()
        
        # Method 1: Related keywords from SEO APIs
        for seed in seed_keywords:
            related = await self._get_related_keywords(seed)
            expanded.update(related[:50])  # Limit per seed keyword
            
        # Method 2: Autocomplete suggestions
        for seed in seed_keywords:
            suggestions = await self._get_autocomplete_suggestions(seed)
            expanded.update(suggestions)
            
        # Method 3: "People Also Ask" questions
        for seed in seed_keywords:
            paa = await self._get_people_also_ask(seed)
            expanded.update(paa)
            
        # Method 4: Semantic variations
        for seed in seed_keywords:
            variations = await self._generate_semantic_variations(seed)
            expanded.update(variations)
            
        return list(expanded)[:max_keywords]
        
    async def _get_keyword_metrics(
        self,
        keywords: List[str],
        location: str = "US",
        language: str = "en"
    ) -> List[KeywordData]:
        """Get metrics for keywords"""
        
        keyword_data = []
        
        # Process in batches to avoid API limits
        batch_size = 100
        for i in range(0, len(keywords), batch_size):
            batch = keywords[i:i + batch_size]
            
            # Simulate API call to keyword research tool
            batch_data = await self._fetch_keyword_metrics_batch(batch, location, language)
            keyword_data.extend(batch_data)
            
            # Rate limiting
            await asyncio.sleep(1)
            
        return keyword_data
        
    async def _cluster_keywords(self, keyword_data: List[KeywordData]) -> List[KeywordCluster]:
        """Cluster keywords by topic and intent"""
        
        clusters = []
        processed_keywords = set()
        
        for kw_data in sorted(keyword_data, key=lambda x: x.search_volume or 0, reverse=True):
            if kw_data.keyword in processed_keywords:
                continue
                
            # Find similar keywords for clustering
            similar_keywords = await self._find_similar_keywords(
                kw_data.keyword, keyword_data, processed_keywords
            )
            
            if len(similar_keywords) >= 3:  # Minimum cluster size
                cluster_keywords = [kw.keyword for kw in similar_keywords]
                processed_keywords.update(cluster_keywords)
                
                cluster = KeywordCluster(
                    cluster_name=await self._generate_cluster_name(similar_keywords),
                    primary_keyword=kw_data.keyword,
                    related_keywords=cluster_keywords[1:],  # Exclude primary
                    total_search_volume=sum(kw.search_volume or 0 for kw in similar_keywords),
                    avg_difficulty=sum(kw.keyword_difficulty or 0 for kw in similar_keywords) / len(similar_keywords),
                    content_type=await self._determine_content_type(similar_keywords),
                    search_intent=similar_keywords[0].intent or "informational",
                    opportunity_score=await self._calculate_cluster_opportunity(similar_keywords)
                )
                clusters.append(cluster)
                
        return clusters
        
    async def _identify_opportunities(
        self,
        keyword_data: List[KeywordData],
        keyword_gaps: List[KeywordGap],
        competitor_keywords: Dict[str, List[str]]
    ) -> List[KeywordOpportunity]:
        """Identify keyword opportunities"""
        
        opportunities = []
        
        for kw_data in keyword_data:
            # Check for different opportunity types
            opportunity_types = []
            reasoning = []
            
            # Low competition opportunity
            if kw_data.keyword_difficulty and kw_data.keyword_difficulty < 30:
                opportunity_types.append("low_competition")
                reasoning.append(f"Low difficulty score ({kw_data.keyword_difficulty})")
                
            # High volume opportunity
            if kw_data.search_volume and kw_data.search_volume > 1000:
                opportunity_types.append("high_volume")
                reasoning.append(f"High search volume ({kw_data.search_volume})")
                
            # Trending opportunity
            if kw_data.trend == "rising":
                opportunity_types.append("trending")
                reasoning.append("Rising search trend")
                
            # Long tail opportunity
            if len(kw_data.keyword.split()) > 3:
                opportunity_types.append("long_tail")
                reasoning.append("Long-tail keyword with specific intent")
                
            if opportunity_types:
                priority_score = await self._calculate_priority_score(kw_data)
                
                opportunity = KeywordOpportunity(
                    keyword=kw_data.keyword,
                    opportunity_type=", ".join(opportunity_types),
                    search_volume=kw_data.search_volume,
                    difficulty=kw_data.keyword_difficulty,
                    estimated_traffic=int((kw_data.search_volume or 0) * 0.3),
                    competition_level=await self._determine_competition_level(kw_data),
                    recommended_content_type=await self._recommend_content_type(kw_data),
                    priority_score=priority_score,
                    reasoning="; ".join(reasoning)
                )
                opportunities.append(opportunity)
                
        # Sort by priority score
        opportunities.sort(key=lambda x: x.priority_score, reverse=True)
        return opportunities
        
    # Placeholder methods for external API calls and calculations
    async def _fetch_keyword_metrics_batch(
        self,
        keywords: List[str],
        location: str,
        language: str
    ) -> List[KeywordData]:
        """Fetch keyword metrics from API"""
        # Mock implementation
        return [
            KeywordData(
                keyword=kw,
                search_volume=1000,
                keyword_difficulty=45.0,
                cpc=1.25,
                competition="medium",
                trend="stable",
                intent="informational"
            )
            for kw in keywords
        ]
        
    async def _get_related_keywords(self, seed: str) -> List[str]:
        """Get related keywords"""
        # Mock implementation
        return [f"{seed} related {i}" for i in range(10)]
        
    async def _calculate_priority_score(self, kw_data: KeywordData) -> float:
        """Calculate priority score for keyword"""
        score = 50.0  # Base score
        
        if kw_data.search_volume:
            score += min(30, kw_data.search_volume / 1000 * 10)
            
        if kw_data.keyword_difficulty:
            score += (100 - kw_data.keyword_difficulty) / 100 * 20
            
        return min(100, score)
