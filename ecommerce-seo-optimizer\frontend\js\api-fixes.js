// API Fixes for GridSpoke Frontend
// This file contains fixes for API endpoint issues

// Wait for API to be available and apply fixes
function applyAPIFixes() {
    if (typeof window.API === 'undefined') {
        console.warn('API not yet loaded, retrying...');
        setTimeout(applyAPIFixes, 100);
        return;
    }

    // Store the original API methods
    const originalGetProducts = window.API.getProducts;
    const originalGetJobs = window.API.getJobs;
    const originalGetProduct = window.API.getProduct;
    
    // Override getProducts to use correct endpoint
    window.API.getProducts = async function(storeId, filters = {}) {
        console.log('Fixed getProducts called with storeId:', storeId, 'filters:', filters);
        // Use correct endpoint format with store_id parameter
        const params = { store_id: storeId, ...filters };
        return this.get('/products', params);
    };

    // Override getJobs to include required store_id parameter
    window.API.getJobs = async function(filters = {}) {
        console.log('Fixed getJobs called with filters:', filters);
        // Get current store ID
        let storeId = filters.store_id;
        if (!storeId) {
            // Try to get store ID from the stores list
            try {
                const stores = await this.getStores();
                if (stores && stores.length > 0) {
                    storeId = stores[0].id;
                    console.log('Using store ID from stores list:', storeId);
                } else {
                    console.error('No store ID available for jobs request');
                    // Use hardcoded store ID as fallback
                    storeId = 'dbb3d2b0-0be2-41e0-947b-47b809fc5da3';
                    console.warn('Using fallback store ID:', storeId);
                }
            } catch (error) {
                console.error('Error getting stores for jobs request:', error);
                // Use hardcoded store ID as fallback
                storeId = 'dbb3d2b0-0be2-41e0-947b-47b809fc5da3';
                console.warn('Using fallback store ID after error:', storeId);
            }
        }
        
        // Include store_id in the request
        const params = { store_id: storeId, ...filters };
        return this.get('/jobs', params);
    };

    // Override getProduct to use correct endpoint
    window.API.getProduct = async function(storeId, productId) {
        console.log('Fixed getProduct called with storeId:', storeId, 'productId:', productId);
        // Use correct endpoint format with store_id parameter
        const params = { store_id: storeId };
        return this.get(`/products/${productId}`, params);
    };

    // Add method to get the current active store using existing getStores
    window.API.getCurrentStore = async function() {
        try {
            const stores = await this.getStores();
            if (stores && stores.length > 0) {
                // Return the first active store or just the first store
                return stores.find(store => store.is_active) || stores[0];
            }
            return null;
        } catch (error) {
            console.error('Error getting current store:', error);
            return null;
        }
    };

    console.log('API fixes applied successfully');
}

// Fix for products.js getCurrentStoreId method
function fixProductsStoreId() {
    if (window.Products && window.Products.getCurrentStoreId) {
        window.Products.getCurrentStoreId = async function() {
            try {
                const currentStore = await window.API.getCurrentStore();
                if (currentStore) {
                    console.log('Using current store ID:', currentStore.id);
                    return currentStore.id;
                }
            } catch (error) {
                console.error('Error getting current store ID:', error);
            }
            // Fallback to hardcoded value
            console.warn('Falling back to hardcoded store ID');
            return 'dbb3d2b0-0be2-41e0-947b-47b809fc5da3';
        };
    }
}

// Fix for jobs.js to include store_id in requests
function fixJobsStoreId() {
    if (window.Jobs && window.Jobs.loadJobs) {
        const originalLoadJobs = window.Jobs.loadJobs;
        window.Jobs.loadJobs = async function(options = {}) {
            try {
                // Get current store using the fixed API method
                const currentStore = await window.API.getCurrentStore();
                if (currentStore) {
                    // Add store_id to the options
                    options.store_id = currentStore.id;
                    console.log('Added store_id to jobs request:', currentStore.id);
                } else {
                    // Use hardcoded fallback
                    options.store_id = 'dbb3d2b0-0be2-41e0-947b-47b809fc5da3';
                    console.warn('Using fallback store_id for jobs request');
                }
            } catch (error) {
                console.error('Error getting store for jobs request:', error);
                // Use hardcoded fallback
                options.store_id = 'dbb3d2b0-0be2-41e0-947b-47b809fc5da3';
                console.warn('Using fallback store_id after error');
            }
            // Call the original method with enhanced options
            return originalLoadJobs.call(this, options);
        };
    }
}

// Apply all fixes
applyAPIFixes();

// Apply module-specific fixes with delay to ensure modules are loaded
setTimeout(() => {
    fixProductsStoreId();
    fixJobsStoreId();
    console.log('Module-specific fixes applied');
}, 500);

console.log('API fixes loaded successfully');
