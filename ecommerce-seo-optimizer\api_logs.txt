gridspoke-api  | Failed to initialize Redis client: Error 111 connecting to localhost:6379. Connection refused.
gridspoke-api  | WARNING:root:Monitoring modules not available, using fallback implementations
gridspoke-api  | INFO:     Started server process [1]
gridspoke-api  | INFO:     Waiting for application startup.
gridspoke-api  | INFO:     Application startup complete.
gridspoke-api  | INFO:     Uvicorn running on http://0.0.0.0:80 (Press CTRL+C to quit)
gridspoke-api  | INFO:     127.0.0.1:58544 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:43910 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:37778 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46780 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:47696 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:47086 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:38976 - "OPTIONS /api/v1/auth/login HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.database:{"error": "'AsyncAdapt_asyncpg_connection' object has no attribute 'execute'", "event": "Failed to set up database extensions", "logger": "core.database", "level": "warning", "timestamp": "2025-08-15T14:08:05.441062Z"}
gridspoke-api  | WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
gridspoke-api  | Traceback (most recent call last):
gridspoke-api  |   File "/usr/local/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
gridspoke-api  |     version = _bcrypt.__about__.__version__
gridspoke-api  |               ^^^^^^^^^^^^^^^^^
gridspoke-api  | AttributeError: module 'bcrypt' has no attribute '__about__'
gridspoke-api  | INFO:     **********:38976 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:39320 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:39334 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.database:{"error": "'AsyncAdapt_asyncpg_connection' object has no attribute 'execute'", "event": "Failed to set up database extensions", "logger": "core.database", "level": "warning", "timestamp": "2025-08-15T14:08:10.037997Z"}
gridspoke-api  | INFO:     **********:39338 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:39354 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:57054 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:57068 - "GET /api/v1/products/?store_id=dbb3d2b0-0be2-41e0-947b-47b809fc5da3 HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:38708 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56370 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:59138 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56230 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:50164 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:59308 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:43006 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:35566 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:48000 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:36630 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:37006 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:45310 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:52828 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:34604 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:44496 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:50204 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56424 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:44604 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:47748 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:51000 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:34282 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:39368 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:52496 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56194 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:36874 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:44394 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:48944 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:55048 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:34440 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:40610 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:35196 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:59686 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:54844 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:47772 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:49986 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:48656 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:39660 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:38254 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:59848 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:48132 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:58704 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:58816 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:41786 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:53922 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:48692 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:43710 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46856 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56068 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:41072 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:35064 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:48588 - "OPTIONS /api/v1/auth/refresh HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:48590 - "OPTIONS /api/v1/auth/refresh HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:48606 - "OPTIONS /api/v1/auth/refresh HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:48616 - "OPTIONS /api/v1/auth/refresh HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.security:{"error": "Invalid crypto padding", "event": "Refresh token verification failed", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T14:33:09.355550Z"}
gridspoke-api  | WARNING:api.v1.endpoints.auth:{"error": "401: Invalid refresh token", "event": "Token refresh failed", "logger": "api.v1.endpoints.auth", "level": "warning", "timestamp": "2025-08-15T14:33:09.356133Z"}
gridspoke-api  | WARNING:core.security:{"error": "Invalid crypto padding", "event": "Refresh token verification failed", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T14:33:09.358286Z"}
gridspoke-api  | WARNING:api.v1.endpoints.auth:{"error": "401: Invalid refresh token", "event": "Token refresh failed", "logger": "api.v1.endpoints.auth", "level": "warning", "timestamp": "2025-08-15T14:33:09.358617Z"}
gridspoke-api  | WARNING:core.security:{"error": "Invalid crypto padding", "event": "Refresh token verification failed", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T14:33:09.359523Z"}
gridspoke-api  | WARNING:api.v1.endpoints.auth:{"error": "401: Invalid refresh token", "event": "Token refresh failed", "logger": "api.v1.endpoints.auth", "level": "warning", "timestamp": "2025-08-15T14:33:09.359688Z"}
gridspoke-api  | WARNING:core.security:{"error": "Invalid crypto padding", "event": "Refresh token verification failed", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T14:33:09.360490Z"}
gridspoke-api  | WARNING:api.v1.endpoints.auth:{"error": "401: Invalid refresh token", "event": "Token refresh failed", "logger": "api.v1.endpoints.auth", "level": "warning", "timestamp": "2025-08-15T14:33:09.360671Z"}
gridspoke-api  | INFO:     **********:48606 - "POST /api/v1/auth/refresh HTTP/1.1" 401 Unauthorized
gridspoke-api  | INFO:     **********:48630 - "POST /api/v1/auth/refresh HTTP/1.1" 401 Unauthorized
gridspoke-api  | INFO:     **********:48588 - "POST /api/v1/auth/refresh HTTP/1.1" 401 Unauthorized
gridspoke-api  | INFO:     **********:48590 - "POST /api/v1/auth/refresh HTTP/1.1" 401 Unauthorized
gridspoke-api  | INFO:     127.0.0.1:42442 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:34456 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:55282 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:39542 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     Shutting down
gridspoke-api  | INFO:     Waiting for application shutdown.
gridspoke-api  | INFO:     Application shutdown complete.
gridspoke-api  | INFO:     Finished server process [1]
gridspoke-api  | WARNING:root:Monitoring modules not available, using fallback implementations
gridspoke-api  | INFO:     Started server process [1]
gridspoke-api  | INFO:     Waiting for application startup.
gridspoke-api  | INFO:     Application startup complete.
gridspoke-api  | INFO:     Uvicorn running on http://0.0.0.0:80 (Press CTRL+C to quit)
gridspoke-api  | INFO:     127.0.0.1:58308 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:38498 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:58150 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:35142 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:60534 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:54346 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:36238 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:57366 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:39644 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:33836 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:60234 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:54126 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46598 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:54776 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.database:{"error": "'AsyncAdapt_asyncpg_connection' object has no attribute 'execute'", "event": "Failed to set up database extensions", "logger": "core.database", "level": "warning", "timestamp": "2025-08-15T14:42:30.628234Z"}
gridspoke-api  | WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
gridspoke-api  | Traceback (most recent call last):
gridspoke-api  |   File "/usr/local/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
gridspoke-api  |     version = _bcrypt.__about__.__version__
gridspoke-api  |               ^^^^^^^^^^^^^^^^^
gridspoke-api  | AttributeError: module 'bcrypt' has no attribute '__about__'
gridspoke-api  | INFO:     **********:41502 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:34032 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:40036 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:37436 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46056 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:57318 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:43100 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:43290 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:33536 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:32960 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56916 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:47682 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:40266 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:47896 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:52580 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:44926 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:41652 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:57534 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:58934 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:57476 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56212 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:55116 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:48402 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:45432 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46030 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:32832 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:40098 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:60094 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:60398 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:55366 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:33730 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:36112 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:38550 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49808 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49818 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49834 - "GET /api/v1/stores HTTP/1.1" 307 Temporary Redirect
gridspoke-api  | INFO:     **********:49838 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49842 - "POST /api/v1/stores HTTP/1.1" 307 Temporary Redirect
gridspoke-api  | WARNING:main:{"url": "http://localhost:8000/api/v1/stores/", "errors": [{"type": "dict_type", "loc": ["body", "api_credentials"], "msg": "Input should be a valid dictionary", "input": []}], "event": "Validation error", "logger": "main", "level": "warning", "timestamp": "2025-08-15T14:58:35.203742Z"}
gridspoke-api  | INFO:     **********:49858 - "POST /api/v1/stores/ HTTP/1.1" 422 Unprocessable Entity
gridspoke-api  | INFO:     127.0.0.1:33574 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:42006 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:58436 - "OPTIONS /api/v1/auth/login HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:58436 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:43132 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:43142 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.database:{"error": "'AsyncAdapt_asyncpg_connection' object has no attribute 'execute'", "event": "Failed to set up database extensions", "logger": "core.database", "level": "warning", "timestamp": "2025-08-15T14:59:42.869900Z"}
gridspoke-api  | INFO:     **********:43150 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:43154 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:50120 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.security:{"user_id": "963689b7-5159-4965-b37f-4b978e36a95f", "event": "Non-superuser attempted admin access", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T14:59:56.785367Z"}
gridspoke-api  | INFO:     **********:57182 - "PUT /api/v1/users/settings HTTP/1.1" 403 Forbidden
gridspoke-api  | INFO:     **********:57196 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:57198 - "GET /api/v1/products/?store_id=dbb3d2b0-0be2-41e0-947b-47b809fc5da3 HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:49184 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49034 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49042 - "GET /api/v1/stores HTTP/1.1" 307 Temporary Redirect
gridspoke-api  | INFO:     **********:49050 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49066 - "POST /api/v1/stores HTTP/1.1" 307 Temporary Redirect
gridspoke-api  | WARNING:main:{"url": "http://localhost:8000/api/v1/stores/", "errors": [{"type": "dict_type", "loc": ["body", "api_credentials"], "msg": "Input should be a valid dictionary", "input": []}], "event": "Validation error", "logger": "main", "level": "warning", "timestamp": "2025-08-15T15:00:17.359998Z"}
gridspoke-api  | INFO:     **********:49074 - "POST /api/v1/stores/ HTTP/1.1" 422 Unprocessable Entity
gridspoke-api  | INFO:     127.0.0.1:44330 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:37266 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:44638 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49860 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49868 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49874 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:49888 - "POST /api/v1/stores/ HTTP/1.1" 201 Created
gridspoke-api  | INFO:     127.0.0.1:37644 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:34224 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:54908 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     Shutting down
gridspoke-api  | INFO:     Waiting for application shutdown.
gridspoke-api  | INFO:     Application shutdown complete.
gridspoke-api  | INFO:     Finished server process [1]
gridspoke-api  | WARNING:root:Monitoring modules not available, using fallback implementations
gridspoke-api  | INFO:     Started server process [1]
gridspoke-api  | INFO:     Waiting for application startup.
gridspoke-api  | INFO:     Application startup complete.
gridspoke-api  | INFO:     Uvicorn running on http://0.0.0.0:80 (Press CTRL+C to quit)
gridspoke-api  | INFO:     127.0.0.1:60550 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.database:{"error": "'AsyncAdapt_asyncpg_connection' object has no attribute 'execute'", "event": "Failed to set up database extensions", "logger": "core.database", "level": "warning", "timestamp": "2025-08-15T15:03:35.179814Z"}
gridspoke-api  | INFO:     **********:52492 - "POST /api/v1/products/bulk/sync HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:33082 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:40226 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:42582 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.database:{"error": "'AsyncAdapt_asyncpg_connection' object has no attribute 'execute'", "event": "Failed to set up database extensions", "logger": "core.database", "level": "warning", "timestamp": "2025-08-15T15:05:02.072371Z"}
gridspoke-api  | INFO:     **********:38048 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:38050 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:38066 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:38074 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:33966 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56530 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:59818 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:53556 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56600 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:42338 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:41224 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
gridspoke-api  | Traceback (most recent call last):
gridspoke-api  |   File "/usr/local/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
gridspoke-api  |     version = _bcrypt.__about__.__version__
gridspoke-api  |               ^^^^^^^^^^^^^^^^^
gridspoke-api  | AttributeError: module 'bcrypt' has no attribute '__about__'
gridspoke-api  | INFO:     **********:39976 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:37548 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:55810 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46174 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46832 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:51070 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:55492 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:49932 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:45548 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:40884 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:50170 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46360 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:59992 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:40494 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:38994 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:33302 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:60550 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:34158 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:38740 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:35816 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:43520 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:49516 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:48310 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:44900 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:57852 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:47858 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:56732 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:45002 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:43474 - "GET /api/v1/health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:43490 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:43498 - "POST /api/v1/products/bulk/sync HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:60392 - "POST /api/v1/products/bulk/sync HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:43116 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:52990 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:53002 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:53000 - "GET /api/v1/dashboard/stats HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:53018 - "GET /api/v1/stores/ HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:38822 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:45112 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:40260 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:50550 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:51260 - "OPTIONS /api/v1/auth/refresh HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:51270 - "OPTIONS /api/v1/auth/refresh HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.security:{"error": "Invalid crypto padding", "event": "Refresh token verification failed", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T15:24:42.595058Z"}
gridspoke-api  | INFO:     **********:51278 - "POST /api/v1/auth/refresh HTTP/1.1" 401 Unauthorized
gridspoke-api  | WARNING:api.v1.endpoints.auth:{"error": "401: Invalid refresh token", "event": "Token refresh failed", "logger": "api.v1.endpoints.auth", "level": "warning", "timestamp": "2025-08-15T15:24:42.595584Z"}
gridspoke-api  | WARNING:core.security:{"error": "Invalid crypto padding", "event": "Refresh token verification failed", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T15:24:42.596179Z"}
gridspoke-api  | WARNING:api.v1.endpoints.auth:{"error": "401: Invalid refresh token", "event": "Token refresh failed", "logger": "api.v1.endpoints.auth", "level": "warning", "timestamp": "2025-08-15T15:24:42.596572Z"}
gridspoke-api  | INFO:     **********:51260 - "POST /api/v1/auth/refresh HTTP/1.1" 401 Unauthorized
gridspoke-api  | WARNING:core.security:{"error": "Invalid crypto padding", "event": "Refresh token verification failed", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T15:24:42.608616Z"}
gridspoke-api  | WARNING:api.v1.endpoints.auth:{"error": "401: Invalid refresh token", "event": "Token refresh failed", "logger": "api.v1.endpoints.auth", "level": "warning", "timestamp": "2025-08-15T15:24:42.609009Z"}
gridspoke-api  | INFO:     **********:51270 - "POST /api/v1/auth/refresh HTTP/1.1" 401 Unauthorized
gridspoke-api  | WARNING:core.security:{"error": "Invalid crypto padding", "event": "Refresh token verification failed", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T15:24:42.611958Z"}
gridspoke-api  | WARNING:api.v1.endpoints.auth:{"error": "401: Invalid refresh token", "event": "Token refresh failed", "logger": "api.v1.endpoints.auth", "level": "warning", "timestamp": "2025-08-15T15:24:42.612241Z"}
gridspoke-api  | INFO:     **********:51288 - "POST /api/v1/auth/refresh HTTP/1.1" 401 Unauthorized
gridspoke-api  | INFO:     127.0.0.1:59918 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:60146 - "GET /api/v1/stores/ HTTP/1.1" 401 Unauthorized
gridspoke-api  | WARNING:core.security:{"error": "Not enough segments", "event": "JWT decode error", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T15:25:32.087244Z"}
gridspoke-api  | INFO:     127.0.0.1:37466 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | WARNING:core.security:{"error": "Not enough segments", "event": "JWT decode error", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T15:25:33.782037Z"}
gridspoke-api  | INFO:     **********:60156 - "GET /api/v1/stores/ HTTP/1.1" 401 Unauthorized
gridspoke-api  | WARNING:core.security:{"error": "Not enough segments", "event": "JWT decode error", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T15:25:35.866687Z"}
gridspoke-api  | INFO:     **********:43588 - "GET /api/v1/stores/ HTTP/1.1" 401 Unauthorized
gridspoke-api  | WARNING:core.security:{"error": "Not enough segments", "event": "JWT decode error", "logger": "core.security", "level": "warning", "timestamp": "2025-08-15T15:25:36.059608Z"}
gridspoke-api  | INFO:     **********:43594 - "GET /api/v1/stores/ HTTP/1.1" 401 Unauthorized
gridspoke-api  | INFO:     127.0.0.1:59916 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:47892 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46554 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:44180 - "GET /api/v1/health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     **********:44182 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
gridspoke-api  | INFO:     Shutting down
gridspoke-api  | INFO:     Waiting for application shutdown.
gridspoke-api  | INFO:     Application shutdown complete.
gridspoke-api  | INFO:     Finished server process [1]
gridspoke-api  | WARNING:root:Monitoring modules not available, using fallback implementations
gridspoke-api  | INFO:     Started server process [1]
gridspoke-api  | INFO:     Waiting for application startup.
gridspoke-api  | INFO:     Application startup complete.
gridspoke-api  | INFO:     Uvicorn running on http://0.0.0.0:80 (Press CTRL+C to quit)
gridspoke-api  | INFO:     127.0.0.1:58046 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:35418 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:53770 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:36700 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:53366 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:40352 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:37664 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:46788 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:39606 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:42832 - "GET /health HTTP/1.1" 200 OK
gridspoke-api  | INFO:     127.0.0.1:52574 - "GET /health HTTP/1.1" 200 OK
