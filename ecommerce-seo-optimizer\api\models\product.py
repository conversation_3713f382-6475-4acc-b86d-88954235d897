"""
Product model for ecommerce products and SEO optimization.
"""
import uuid
from typing import Optional, List, Dict, Any, TYPE_CHECKING
from enum import Enum
from decimal import Decimal
from sqlalchemy import String, Boolean, ForeignKey, Index, Text, Numeric, Enum as SQLEnum, DateTime as SA_DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from datetime import datetime, timezone
try:
    from pgvector.sqlalchemy import Vector as PGVector  # type: ignore[reportMissingImports]
    def Vector(dim: int):  # type: ignore[misc]
        return PGVector(dim)
except Exception:
    # Local analysis fallback: store embeddings as JSON when pgvector isn't available
    def Vector(dim: int):  # type: ignore[misc]
        return JSONB
from models.base import BaseModel
import structlog

if TYPE_CHECKING:
    from models.store import Store
    from models.generated_content import GeneratedContent

logger = structlog.get_logger(__name__)


class OptimizationStatus(str, Enum):
    """Product optimization status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class Product(BaseModel):
    """Product model representing an ecommerce product."""
    
    __tablename__ = "products"
    
    # Store relationship
    store_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("stores.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # External identifiers
    external_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        comment="Product ID from the ecommerce platform"
    )
    
    sku: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True
    )
    
    # Basic product information
    title: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        index=True
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True
    )
    
    short_description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True
    )
    
    # SEO fields
    meta_title: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True
    )
    
    meta_description: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True
    )
    
    keywords: Mapped[Optional[List[str]]] = mapped_column(
        JSONB,
        nullable=True,
        default=list
    )
    
    # Product categorization
    category: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True
    )
    
    categories: Mapped[Optional[List[str]]] = mapped_column(
        JSONB,
        nullable=True,
        default=list
    )
    
    tags: Mapped[Optional[List[str]]] = mapped_column(
        JSONB,
        nullable=True,
        default=list
    )
    
    # Pricing
    price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 2),
        nullable=True
    )
    
    sale_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 2),
        nullable=True
    )
    
    currency: Mapped[str] = mapped_column(
        String(3),
        default="USD",
        nullable=False
    )
    
    # Media
    image_urls: Mapped[Optional[List[str]]] = mapped_column(
        JSONB,
        nullable=True,
        default=list
    )
    
    gallery_urls: Mapped[Optional[List[str]]] = mapped_column(
        JSONB,
        nullable=True,
        default=list
    )
    
    # Vector embeddings for semantic search
    title_embedding: Mapped[Optional[List[float]]] = mapped_column(
        Vector(1536),
        nullable=True,
        comment="OpenAI embedding of product title"
    )
    
    description_embedding: Mapped[Optional[List[float]]] = mapped_column(
        Vector(1536),
        nullable=True,
        comment="OpenAI embedding of product description"
    )
    
    # Optimization tracking
    optimization_status: Mapped[OptimizationStatus] = mapped_column(
        SQLEnum(OptimizationStatus),
        default=OptimizationStatus.PENDING,
        nullable=False
    )
    
    optimization_score: Mapped[Optional[float]] = mapped_column(
        Numeric(3, 2),
        nullable=True,
        comment="SEO optimization score (0.0 to 1.0)"
    )
    
    last_optimized: Mapped[Optional[datetime]] = mapped_column(
        SA_DateTime(timezone=True),
        nullable=True
    )
    
    optimization_history: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(
        JSONB,
        nullable=True,
        default=list,
        comment="History of optimization attempts"
    )
    
    # Product status
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True
    )
    
    is_published: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False
    )
    
    # Additional metadata
    attributes: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB,
        nullable=True,
        default=dict,
        comment="Additional product attributes from platform"
    )
    
    # Relationships
    store: Mapped["Store"] = relationship(
        "Store",
        back_populates="products"
    )
    
    generated_content: Mapped[List["GeneratedContent"]] = relationship(
        "GeneratedContent",
        back_populates="product",
        cascade="all, delete-orphan"
    )
    
    # Database indexes
    __table_args__ = (
        Index("ix_products_store_external", "store_id", "external_id"),
        Index("ix_products_optimization_status", "optimization_status", "store_id"),
        Index("ix_products_active_published", "is_active", "is_published"),
        Index("ix_products_category_store", "category", "store_id"),
        Index("ix_products_last_optimized", "last_optimized"),
        # Unique constraint for store + external_id
        Index("uq_products_store_external", "store_id", "external_id", unique=True),
        # Vector similarity search indexes
        Index("ix_products_title_embedding", "title_embedding", postgresql_using="ivfflat"),
        Index("ix_products_description_embedding", "description_embedding", postgresql_using="ivfflat"),
    )
    
    def __repr__(self) -> str:
        return f"<Product(id={self.id}, title={self.title[:50] if self.title else 'None'})>"
    
    @property
    def needs_optimization(self) -> bool:
        """Check if product needs SEO optimization."""
        return (
            self.optimization_status in [OptimizationStatus.PENDING, OptimizationStatus.FAILED] or
            not self.meta_title or
            not self.meta_description or
            not self.keywords
        )
    
    @property
    def has_embeddings(self) -> bool:
        """Check if product has vector embeddings."""
        return self.title_embedding is not None or self.description_embedding is not None
    
    def get_display_price(self) -> Optional[Decimal]:
        """Get the price to display (sale price if available, otherwise regular price)."""
        return self.sale_price if self.sale_price else self.price
    
    def add_optimization_record(self, model_used: str, tokens_used: int, success: bool, error: Optional[str] = None) -> None:
        """Add a record to optimization history."""
        if self.optimization_history is None:
            self.optimization_history = []
        
        record = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "model": model_used,
            "tokens_used": tokens_used,
            "success": success,
            "error": error
        }
        
        self.optimization_history.append(record)
        
        # Keep only last 10 records
        if len(self.optimization_history) > 10:
            self.optimization_history = self.optimization_history[-10:]
        
        logger.info(
            "Optimization record added",
            product_id=str(self.id),
            model=model_used,
            tokens=tokens_used,
            success=success
        )
    
    def calculate_seo_score(self) -> float:
        """Calculate SEO optimization score based on available data."""
        score = 0.0
        max_score = 100.0
        
        # Title optimization (25 points)
        if self.meta_title:
            if 50 <= len(self.meta_title) <= 60:
                score += 25
            elif 40 <= len(self.meta_title) <= 70:
                score += 20
            else:
                score += 10
        
        # Meta description optimization (25 points)
        if self.meta_description:
            if 150 <= len(self.meta_description) <= 160:
                score += 25
            elif 140 <= len(self.meta_description) <= 170:
                score += 20
            else:
                score += 10
        
        # Keywords optimization (20 points)
        if self.keywords and len(self.keywords) >= 3:
            score += 20
        elif self.keywords and len(self.keywords) >= 1:
            score += 10
        
        # Description optimization (20 points)
        if self.description and len(self.description) >= 300:
            score += 20
        elif self.description and len(self.description) >= 150:
            score += 10
        
        # Category optimization (10 points)
        if self.category:
            score += 10
        
        return min(score / max_score, 1.0)
    
    def update_seo_score(self) -> None:
        """Update the SEO optimization score."""
        self.optimization_score = self.calculate_seo_score()
        logger.debug(
            "SEO score updated",
            product_id=str(self.id),
            score=self.optimization_score
        )

    # ------------------------------------------------------------------
    # Bridging properties for existing Pydantic schemas expecting `name`
    # while the persisted column is `title`.
    # ------------------------------------------------------------------
    @property
    def name(self) -> Optional[str]:  # type: ignore[override]
        return self.title

    @name.setter
    def name(self, value: Optional[str]) -> None:
        self.title = value
