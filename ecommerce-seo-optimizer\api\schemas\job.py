"""
Optimization job schemas for request/response validation.
"""
import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class JobType(str, Enum):
    """Types of optimization jobs."""
    PRODUCT_OPTIMIZATION = "product_optimization"
    TITLE_GENERATION = "title_generation"
    DESCRIPTION_GENERATION = "description_generation"
    META_OPTIMIZATION = "meta_optimization"
    KEYWORD_RESEARCH = "keyword_research"
    CONTENT_GENERATION = "content_generation"
    BULK_OPTIMIZATION = "bulk_optimization"
    VECTOR_EMBEDDING = "vector_embedding"


class JobStatus(str, Enum):
    """Job execution status."""
    PENDING = "pending"
    QUEUED = "queued"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class JobPriority(int, Enum):
    """Job priority levels."""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    URGENT = 10


class OptimizationJobBase(BaseModel):
    """Base optimization job schema."""
    job_type: JobType
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    priority: JobPriority = JobPriority.NORMAL
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict)


class OptimizationJobCreate(OptimizationJobBase):
    """Schema for job creation."""
    store_id: uuid.UUID
    total_items: int = Field(0, ge=0)
    scheduled_for: Optional[datetime] = None


class OptimizationJobUpdate(BaseModel):
    """Schema for job updates."""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    priority: Optional[JobPriority] = None
    status: Optional[JobStatus] = None
    parameters: Optional[Dict[str, Any]] = None
    scheduled_for: Optional[datetime] = None


class OptimizationJobInDB(OptimizationJobBase):
    """Schema for job in database."""
    id: uuid.UUID
    store_id: uuid.UUID
    status: JobStatus
    total_items: int
    processed_items: int
    failed_items: int
    skipped_items: int
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class OptimizationJob(OptimizationJobInDB):
    """Schema for job response."""
    pass


class OptimizationJobWithProgress(OptimizationJob):
    """Job schema with progress calculations."""
    progress_percentage: float
    success_rate: float
    duration_seconds: Optional[float] = None
    estimated_completion: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class JobCancellationResponse(BaseModel):
    """Response after cancelling a job."""
    job_id: uuid.UUID
    message: str
    status: JobStatus