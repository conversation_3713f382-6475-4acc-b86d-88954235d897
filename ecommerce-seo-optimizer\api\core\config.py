"""
Core configuration settings for the GridSpoke Ecommerce SEO API.
"""
from typing import List, Optional, Any, Dict
from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
import secrets


class Settings(BaseSettings):
    """Application configuration settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )
    
    # API Configuration
    PROJECT_NAME: str = "GridSpoke Ecommerce SEO API"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "AI-powered ecommerce SEO optimization service"
    API_V1_STR: str = "/api/v1"
    
    # Server Configuration
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # Security Configuration
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # Database Configuration
    DATABASE_URL: str = "postgresql+asyncpg://postgres:password@localhost:5432/gridspoke"
    DATABASE_ECHO: bool = False
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 0
    
    # Redis Configuration
    REDIS_URL: str = ""
    REDIS_PASSWORD: Optional[str] = None
    
    # Celery Configuration
    CELERY_BROKER_URL: str = ""
    CELERY_RESULT_BACKEND: str = ""
    
    # OpenRouter/AI Configuration
    OPENROUTER_API_KEY: str = ""
    OPENROUTER_BASE_URL: str = "https://openrouter.ai/api/v1"
    DEFAULT_MODEL: str = "anthropic/claude-3-haiku"
    PREMIUM_MODEL: str = "anthropic/claude-3-opus"
    MAX_TOKENS_PER_REQUEST: int = 4000
    
    # CORS Configuration
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://127.0.0.1:3000"
    ]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1", "*"]

    # Back-compat aliases for older code paths
    # These are not used internally but avoid breaking imports
    ENVIRONMENT: str = "development"
    CORS_ORIGINS: List[str] = ALLOWED_ORIGINS

    # WordPress Integration
    WEBHOOK_SECRET: str = secrets.token_urlsafe(32)
    MAX_PRODUCTS_PER_BATCH: int = 100

    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    BURST_RATE_LIMIT: int = 10
    
    # File Upload Configuration
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = ["image/jpeg", "image/png", "image/webp"]
    
    # Monitoring Configuration
    SENTRY_DSN: Optional[str] = None
    LOG_LEVEL: str = "INFO"
    ENABLE_METRICS: bool = True
    
    # Feature Flags
    ENABLE_BULK_OPTIMIZATION: bool = True
    ENABLE_VECTOR_SEARCH: bool = True
    ENABLE_A_B_TESTING: bool = False
    
    @field_validator("ALLOWED_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Any) -> List[str]:
        """Parse CORS origins from environment variable."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        elif isinstance(v, str):
            return [v]
        raise ValueError(v)
    
    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def validate_database_url(cls, v: str) -> str:
        """Ensure database URL uses asyncpg driver."""
        if v.startswith("postgresql://"):
            return v.replace("postgresql://", "postgresql+asyncpg://", 1)
        return v


# Global settings instance
settings = Settings()


# Environment-specific configurations
class DevelopmentSettings(Settings):
    """Development environment settings."""
    DEBUG: bool = True
    DATABASE_ECHO: bool = True
    LOG_LEVEL: str = "DEBUG"
    
    # Set development defaults if not provided via environment
    def __init__(self, **data):
        super().__init__(**data)
        
        # Set Redis defaults if not provided
        if not self.REDIS_URL:
            self.REDIS_URL = "redis://localhost:6379/0"
            
        # Set Celery defaults if not provided
        if not self.CELERY_BROKER_URL:
            self.CELERY_BROKER_URL = "redis://localhost:6379/1"
        if not self.CELERY_RESULT_BACKEND:
            self.CELERY_RESULT_BACKEND = "redis://localhost:6379/2"


class ProductionSettings(Settings):
    """Production environment settings."""
    DEBUG: bool = False
    DATABASE_ECHO: bool = False
    LOG_LEVEL: str = "WARNING"
    
    # Set production defaults if not provided via environment
    def __init__(self, **data):
        super().__init__(**data)
        
        # Set Redis defaults if not provided
        if not self.REDIS_URL:
            self.REDIS_URL = "redis://localhost:6379/0"
            
        # Set Celery defaults if not provided
        if not self.CELERY_BROKER_URL:
            self.CELERY_BROKER_URL = "redis://localhost:6379/1"
        if not self.CELERY_RESULT_BACKEND:
            self.CELERY_RESULT_BACKEND = "redis://localhost:6379/2"


class TestingSettings(Settings):
    """Testing environment settings."""
    DEBUG: bool = True
    DATABASE_URL: str = "postgresql+asyncpg://postgres:password@localhost:5432/gridspoke_test"
    REDIS_URL: str = "redis://localhost:6379/15"  # Use different Redis DB for tests


def get_settings() -> Settings:
    """Get settings based on environment."""
    import os
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()
