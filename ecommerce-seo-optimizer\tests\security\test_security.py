# GridSpoke Security Testing
"""
Comprehensive security testing for GridSpoke including authentication,
authorization, input validation, and vulnerability protection.
"""

import pytest
import json
import hashlib
import time
from unittest.mock import patch, MagicMock


class TestAuthenticationSecurity:
    """Test authentication security mechanisms"""
    
    def test_jwt_token_validation(self, test_client):
        """Test JWT token validation and security"""
        # Test without token
        response = test_client.get("/api/v1/stores")
        assert response.status_code == 401
        assert "authorization required" in response.json()["detail"].lower()
        
        # Test with malformed token
        response = test_client.get(
            "/api/v1/stores",
            headers={"Authorization": "Bearer malformed.token.here"}
        )
        assert response.status_code == 401
        
        # Test with expired token
        with patch('api.core.security.verify_token') as mock_verify:
            mock_verify.side_effect = Exception("Token expired")
            
            response = test_client.get(
                "/api/v1/stores",
                headers={"Authorization": "Bearer expired_token"}
            )
            assert response.status_code == 401
    
    def test_password_security_requirements(self, test_client):
        """Test password strength requirements"""
        weak_passwords = [
            "123",
            "password",
            "12345678",
            "qwerty",
            "password123"
        ]
        
        for weak_password in weak_passwords:
            user_data = {
                "email": f"test_{weak_password}@example.com",
                "password": weak_password,
                "confirm_password": weak_password
            }
            
            response = test_client.post("/api/v1/auth/register", json=user_data)
            # Should reject weak passwords
            assert response.status_code in [400, 422]
    
    def test_rate_limiting_authentication(self, test_client):
        """Test rate limiting on authentication endpoints"""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrong_password"
        }
        
        # Attempt multiple failed logins rapidly
        failed_attempts = 0
        for i in range(10):
            response = test_client.post("/api/v1/auth/login", json=login_data)
            if response.status_code == 429:  # Rate limited
                break
            elif response.status_code == 401:  # Unauthorized (expected)
                failed_attempts += 1
        
        # Should eventually rate limit after too many attempts
        assert failed_attempts < 10 or response.status_code == 429
    
    def test_session_security(self, test_client, auth_headers):
        """Test session security and token refresh"""
        # Test token refresh endpoint
        refresh_data = {"refresh_token": "test_refresh_token"}
        
        with patch('api.core.security.verify_refresh_token') as mock_verify:
            mock_verify.return_value = {"user_id": "user_123"}
            
            response = test_client.post("/api/v1/auth/refresh", json=refresh_data)
            # Should provide new access token
            assert response.status_code in [200, 401]  # Depends on implementation
    
    def test_account_lockout_protection(self, test_client):
        """Test account lockout after multiple failed attempts"""
        email = "<EMAIL>"
        
        # Create account first
        register_data = {
            "email": email,
            "password": "ValidPassword123!",
            "confirm_password": "ValidPassword123!"
        }
        test_client.post("/api/v1/auth/register", json=register_data)
        
        # Attempt multiple failed logins
        for i in range(15):  # Excessive attempts
            login_data = {
                "email": email,
                "password": f"wrong_password_{i}"
            }
            response = test_client.post("/api/v1/auth/login", json=login_data)
        
        # Final attempt with correct password should be blocked
        correct_login = {
            "email": email,
            "password": "ValidPassword123!"
        }
        response = test_client.post("/api/v1/auth/login", json=correct_login)
        
        # Account should be locked (depending on implementation)
        # Could be 429 (rate limited) or 423 (locked)
        assert response.status_code in [401, 423, 429]


class TestAuthorizationSecurity:
    """Test authorization and role-based access control"""
    
    def test_role_based_access_control(self, test_client):
        """Test different user roles have appropriate access"""
        endpoints_by_role = {
            "admin": [
                "/api/v1/admin/users",
                "/api/v1/admin/system",
                "/api/v1/analytics/all-stores"
            ],
            "user": [
                "/api/v1/stores",
                "/api/v1/products",
                "/api/v1/optimize/product"
            ],
            "readonly": [
                "/api/v1/stores",
                "/api/v1/products"
            ]
        }
        
        for role, allowed_endpoints in endpoints_by_role.items():
            with patch('api.core.security.verify_token') as mock_verify:
                mock_verify.return_value = {"user_id": "user_123", "role": role}
                headers = {"Authorization": f"Bearer {role}_token"}
                
                for endpoint in allowed_endpoints:
                    response = test_client.get(endpoint, headers=headers)
                    # Should not be forbidden (403) for allowed endpoints
                    assert response.status_code != 403
    
    def test_resource_ownership_validation(self, test_client, create_test_store):
        """Test users can only access their own resources"""
        # Create stores for different users
        user1_store = create_test_store(user_id="user_1", name="User 1 Store")
        user2_store = create_test_store(user_id="user_2", name="User 2 Store")
        
        # User 1 trying to access User 2's store
        with patch('api.core.security.verify_token') as mock_verify:
            mock_verify.return_value = {"user_id": "user_1", "role": "user"}
            headers = {"Authorization": "Bearer user1_token"}
            
            # Should be able to access own store
            response = test_client.get(f"/api/v1/stores/{user1_store.id}", headers=headers)
            assert response.status_code != 403
            
            # Should NOT be able to access other user's store
            response = test_client.get(f"/api/v1/stores/{user2_store.id}", headers=headers)
            assert response.status_code == 403
    
    def test_api_key_scoping(self, test_client):
        """Test API key access scoping and limitations"""
        # Test read-only API key
        readonly_headers = {"X-API-Key": "readonly_api_key_123"}
        
        with patch('api.core.security.verify_api_key') as mock_verify:
            mock_verify.return_value = {"scopes": ["read"], "user_id": "api_user"}
            
            # Should allow read operations
            response = test_client.get("/api/v1/stores", headers=readonly_headers)
            assert response.status_code != 403
            
            # Should deny write operations
            store_data = {"name": "Test Store", "url": "https://test.com"}
            response = test_client.post("/api/v1/stores", json=store_data, headers=readonly_headers)
            assert response.status_code == 403


class TestInputValidationSecurity:
    """Test input validation and sanitization"""
    
    def test_sql_injection_protection(self, test_client, auth_headers):
        """Test protection against SQL injection attacks"""
        sql_injection_payloads = [
            "'; DROP TABLE products; --",
            "1' OR '1'='1",
            "'; SELECT * FROM users; --",
            "1; DELETE FROM stores; --"
        ]
        
        for payload in sql_injection_payloads:
            # Test in search parameters
            response = test_client.get(
                f"/api/v1/products?search={payload}",
                headers=auth_headers
            )
            # Should not cause server error (500)
            assert response.status_code != 500
            
            # Test in store creation
            store_data = {
                "name": payload,
                "url": f"https://test-{payload}.com",
                "platform": "woocommerce"
            }
            response = test_client.post("/api/v1/stores", json=store_data, headers=auth_headers)
            # Should either validate and reject (400/422) or sanitize and accept (201)
            assert response.status_code in [201, 400, 422]
    
    def test_xss_protection(self, test_client, auth_headers):
        """Test protection against XSS attacks"""
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//"
        ]
        
        for payload in xss_payloads:
            # Test in product creation
            product_data = {
                "name": f"Product {payload}",
                "description": f"Description with {payload}",
                "category": payload
            }
            
            response = test_client.post("/api/v1/products", json=product_data, headers=auth_headers)
            
            if response.status_code == 201:
                # If accepted, verify payload was sanitized
                created_product = response.json()
                assert "<script>" not in created_product.get("name", "")
                assert "javascript:" not in created_product.get("description", "")
    
    def test_file_upload_security(self, test_client, auth_headers):
        """Test file upload security and validation"""
        # Test various file types and sizes
        test_files = [
            ("test.txt", "text/plain", b"Normal text file"),
            ("malicious.php", "text/php", b"<?php phpinfo(); ?>"),
            ("large_file.txt", "text/plain", b"x" * 10 * 1024 * 1024),  # 10MB
            ("script.js", "application/javascript", b"alert('xss');"),
            ("image.jpg", "image/jpeg", b"\xff\xd8\xff\xe0"),  # JPEG header
        ]
        
        for filename, content_type, content in test_files:
            files = {"file": (filename, content, content_type)}
            
            response = test_client.post(
                "/api/v1/upload/product-image",
                files=files,
                headers=auth_headers
            )
            
            # Should validate file types and sizes appropriately
            if filename.endswith(('.php', '.js')):
                assert response.status_code in [400, 415]  # Bad request or unsupported media type
            elif len(content) > 5 * 1024 * 1024:  # > 5MB
                assert response.status_code in [400, 413]  # Bad request or payload too large
    
    def test_json_payload_limits(self, test_client, auth_headers):
        """Test JSON payload size and structure limits"""
        # Test extremely large JSON payload
        large_description = "x" * 1024 * 1024  # 1MB string
        large_payload = {
            "name": "Test Product",
            "description": large_description,
            "category": "test"
        }
        
        response = test_client.post("/api/v1/products", json=large_payload, headers=auth_headers)
        # Should reject overly large payloads
        assert response.status_code in [400, 413, 422]
        
        # Test deeply nested JSON
        nested_data = {"level1": {"level2": {"level3": {"level4": {"level5": "deep"}}}}}
        response = test_client.post("/api/v1/stores", json=nested_data, headers=auth_headers)
        # Should handle nested data appropriately
        assert response.status_code in [201, 400, 422]


class TestApiSecurityHeaders:
    """Test security headers and CORS configuration"""
    
    def test_security_headers_present(self, test_client):
        """Test presence of security headers"""
        response = test_client.get("/")
        
        expected_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options", 
            "X-XSS-Protection",
            "Strict-Transport-Security",
            "Content-Security-Policy"
        ]
        
        for header in expected_headers:
            # Headers might not all be present depending on configuration
            if header in response.headers:
                assert response.headers[header] is not None
    
    def test_cors_configuration(self, test_client):
        """Test CORS configuration security"""
        # Test preflight request
        response = test_client.options(
            "/api/v1/stores",
            headers={
                "Origin": "https://malicious-site.com",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
        )
        
        # Should not allow arbitrary origins
        cors_origin = response.headers.get("Access-Control-Allow-Origin")
        if cors_origin:
            assert cors_origin != "*" or "localhost" in cors_origin
    
    def test_content_type_validation(self, test_client, auth_headers):
        """Test content type validation"""
        # Test with missing content type
        response = test_client.post(
            "/api/v1/stores",
            data="invalid json data",
            headers=auth_headers
        )
        assert response.status_code in [400, 415, 422]
        
        # Test with wrong content type
        response = test_client.post(
            "/api/v1/stores",
            data="<xml>data</xml>",
            headers={**auth_headers, "Content-Type": "application/xml"}
        )
        assert response.status_code in [400, 415, 422]


class TestWebhookSecurity:
    """Test webhook endpoint security"""
    
    def test_webhook_signature_verification(self, test_client):
        """Test webhook signature verification"""
        webhook_payload = {
            "action": "product_updated",
            "product_id": 123,
            "store_id": "store_123"
        }
        
        # Test without signature
        response = test_client.post("/api/v1/webhooks/wordpress", json=webhook_payload)
        assert response.status_code == 401
        
        # Test with invalid signature
        response = test_client.post(
            "/api/v1/webhooks/wordpress",
            json=webhook_payload,
            headers={"X-WP-Signature": "invalid_signature"}
        )
        assert response.status_code == 401
        
        # Test with valid signature (mocked)
        with patch('api.services.webhook_service.verify_signature') as mock_verify:
            mock_verify.return_value = True
            
            response = test_client.post(
                "/api/v1/webhooks/wordpress",
                json=webhook_payload,
                headers={"X-WP-Signature": "valid_signature"}
            )
            assert response.status_code in [200, 202]
    
    def test_webhook_replay_attack_protection(self, test_client):
        """Test protection against webhook replay attacks"""
        webhook_payload = {
            "action": "product_updated",
            "product_id": 123,
            "timestamp": int(time.time()) - 3600  # 1 hour old
        }
        
        with patch('api.services.webhook_service.verify_signature') as mock_verify:
            mock_verify.return_value = True
            
            response = test_client.post(
                "/api/v1/webhooks/wordpress",
                json=webhook_payload,
                headers={"X-WP-Signature": "valid_signature"}
            )
            
            # Should reject old timestamps
            assert response.status_code in [400, 401]


class TestEncryptionSecurity:
    """Test data encryption and secure storage"""
    
    def test_sensitive_data_encryption(self, test_client, auth_headers):
        """Test that sensitive data is encrypted"""
        # Create store with API key
        store_data = {
            "name": "Encryption Test Store",
            "url": "https://encryption-test.com",
            "platform": "woocommerce",
            "api_key": "sensitive_api_key_12345"
        }
        
        with patch('api.crud.store.create_store') as mock_create:
            # Mock encrypted storage
            encrypted_store = {
                **store_data,
                "api_key": "[ENCRYPTED]"  # Should be encrypted in storage
            }
            mock_create.return_value = encrypted_store
            
            response = test_client.post("/api/v1/stores", json=store_data, headers=auth_headers)
            
            if response.status_code == 201:
                # Verify API key is not returned in plain text
                returned_store = response.json()
                assert returned_store.get("api_key") != "sensitive_api_key_12345"
    
    def test_password_hashing(self, test_client):
        """Test password hashing security"""
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "confirm_password": "TestPassword123!"
        }
        
        with patch('api.crud.user.create_user') as mock_create:
            # Mock password hashing
            hashed_user = {
                **user_data,
                "password": "$2b$12$encrypted_hash_here"  # bcrypt hash format
            }
            mock_create.return_value = hashed_user
            
            response = test_client.post("/api/v1/auth/register", json=user_data)
            
            # Password should be hashed, not stored in plain text
            if mock_create.called:
                call_args = mock_create.call_args[0][0]
                assert call_args.password != "TestPassword123!"
                assert len(call_args.password) > 20  # Hashed passwords are longer


class TestDDoSProtection:
    """Test DDoS and abuse protection"""
    
    def test_rate_limiting_per_endpoint(self, test_client, auth_headers):
        """Test rate limiting on different endpoints"""
        endpoints_to_test = [
            "/api/v1/stores",
            "/api/v1/products", 
            "/api/v1/optimize/product",
            "/api/v1/analytics/overview"
        ]
        
        for endpoint in endpoints_to_test:
            rate_limited = False
            
            # Make rapid requests
            for i in range(50):  # Aggressive testing
                if endpoint == "/api/v1/optimize/product":
                    response = test_client.post(
                        endpoint,
                        json={"product_id": f"prod_{i}", "optimization_type": "title"},
                        headers=auth_headers
                    )
                else:
                    response = test_client.get(endpoint, headers=auth_headers)
                
                if response.status_code == 429:  # Rate limited
                    rate_limited = True
                    break
            
            # Some endpoints should implement rate limiting
            # (This depends on the specific implementation)
            assert rate_limited or endpoint in ["/api/v1/stores", "/api/v1/products"]
    
    def test_request_size_limits(self, test_client, auth_headers):
        """Test request size limits"""
        # Create extremely large request
        large_data = {
            "name": "x" * 1000,
            "description": "y" * 10000,
            "large_field": ["item"] * 10000
        }
        
        response = test_client.post("/api/v1/products", json=large_data, headers=auth_headers)
        
        # Should reject overly large requests
        assert response.status_code in [400, 413, 422]
    
    def test_connection_limits(self, test_client):
        """Test concurrent connection limits"""
        import concurrent.futures
        import requests
        
        def make_request():
            try:
                # Use requests directly for better connection control
                response = requests.get("http://localhost:8000/health", timeout=1)
                return response.status_code
            except:
                return 0
        
        # Test with many concurrent connections
        with concurrent.futures.ThreadPoolExecutor(max_workers=100) as executor:
            futures = [executor.submit(make_request) for _ in range(100)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Some requests might be rejected due to connection limits
        successful_requests = [r for r in results if r == 200]
        
        # At least some should succeed, but not necessarily all
        assert len(successful_requests) > 50


class TestSecurityMonitoring:
    """Test security monitoring and logging"""
    
    def test_failed_login_logging(self, test_client):
        """Test that failed login attempts are logged"""
        with patch('api.monitoring.security.log_failed_login') as mock_log:
            login_data = {
                "email": "<EMAIL>",
                "password": "wrong_password"
            }
            
            response = test_client.post("/api/v1/auth/login", json=login_data)
            
            if response.status_code == 401:
                # Should log failed attempt
                mock_log.assert_called()
    
    def test_suspicious_activity_detection(self, test_client, auth_headers):
        """Test detection of suspicious activity patterns"""
        with patch('api.monitoring.security.detect_suspicious_activity') as mock_detect:
            # Simulate suspicious pattern: rapid API calls from same IP
            for i in range(100):
                test_client.get("/api/v1/stores", headers=auth_headers)
            
            # Should trigger suspicious activity detection
            mock_detect.assert_called()
    
    def test_security_audit_logging(self, test_client, auth_headers):
        """Test security audit logging"""
        with patch('api.monitoring.audit.log_security_event') as mock_audit:
            # Perform security-sensitive operations
            store_data = {"name": "Audit Test", "url": "https://audit.com"}
            test_client.post("/api/v1/stores", json=store_data, headers=auth_headers)
            
            # Should log security events
            mock_audit.assert_called()


class TestThirdPartySecurity:
    """Test security of third-party integrations"""
    
    def test_openrouter_api_key_security(self):
        """Test OpenRouter API key handling"""
        try:
            from api.services.openrouter_client import OpenRouterClient
            
            # Test that API key is not logged or exposed
            with patch('requests.post') as mock_post:
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {"choices": []}
                mock_post.return_value = mock_response
                
                client = OpenRouterClient(api_key="secret_api_key_123")
                client.generate_optimization({"name": "Test"})
                
                # Verify API key is in headers but not logged
                call_args = mock_post.call_args
                headers = call_args[1]["headers"]
                assert "Authorization" in headers
                assert "secret_api_key_123" in headers["Authorization"]
                
        except ImportError:
            pytest.skip("OpenRouterClient not available")
    
    def test_wordpress_webhook_validation(self, test_client):
        """Test WordPress webhook validation security"""
        # Test webhook with invalid signature
        webhook_data = {
            "action": "product_updated",
            "product_id": 123
        }
        
        response = test_client.post(
            "/api/v1/webhooks/wordpress",
            json=webhook_data,
            headers={"X-WP-Signature": "fake_signature"}
        )
        
        assert response.status_code == 401
    
    def test_external_api_ssl_verification(self):
        """Test SSL certificate verification for external APIs"""
        try:
            from api.services.openrouter_client import OpenRouterClient
            
            with patch('requests.post') as mock_post:
                # Test SSL verification is enabled
                client = OpenRouterClient(api_key="test")
                client.generate_optimization({"name": "Test"})
                
                # Verify SSL verification is not disabled
                call_kwargs = mock_post.call_args[1]
                assert call_kwargs.get("verify", True) is True
                
        except ImportError:
            pytest.skip("OpenRouterClient not available")


# Security Test Utilities
def generate_jwt_token(payload, secret="test_secret", algorithm="HS256"):
    """Generate JWT token for testing"""
    import jwt
    return jwt.encode(payload, secret, algorithm=algorithm)


def create_malicious_payload(payload_type="xss"):
    """Create malicious payloads for testing"""
    payloads = {
        "xss": "<script>alert('xss')</script>",
        "sql": "'; DROP TABLE users; --",
        "path_traversal": "../../../etc/passwd",
        "command_injection": "; rm -rf /",
        "xxe": "<?xml version='1.0'?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>"
    }
    return payloads.get(payload_type, "")


# Performance Security Tests
class TestSecurityPerformance:
    """Test security mechanisms don't severely impact performance"""
    
    @pytest.mark.slow
    def test_authentication_performance(self, test_client):
        """Test authentication doesn't severely impact performance"""
        import time
        
        with patch('api.core.security.verify_token') as mock_verify:
            mock_verify.return_value = {"user_id": "user_123", "role": "user"}
            
            start_time = time.time()
            
            # Make 100 authenticated requests
            for i in range(100):
                test_client.get(
                    "/api/v1/stores",
                    headers={"Authorization": "Bearer test_token"}
                )
            
            end_time = time.time()
            
            # Should complete in reasonable time (under 10 seconds)
            assert (end_time - start_time) < 10.0
    
    @pytest.mark.slow  
    def test_rate_limiting_performance(self, test_client, auth_headers):
        """Test rate limiting doesn't severely impact normal usage"""
        import time
        
        start_time = time.time()
        
        # Make normal-rate requests
        for i in range(50):
            test_client.get("/api/v1/products", headers=auth_headers)
            time.sleep(0.1)  # 10 requests per second
        
        end_time = time.time()
        
        # Should complete without rate limiting
        assert (end_time - start_time) < 15.0  # Generous timing
