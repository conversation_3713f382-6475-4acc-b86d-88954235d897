#!/usr/bin/env python3
"""
Script to generate a proper bcrypt hash for the password 'demo12345'.
"""

import bcrypt
import sys

def generate_bcrypt_hash(password):
    """Generate a bcrypt hash for the given password."""
    # Convert password to bytes
    password_bytes = password.encode('utf-8')
    
    # Generate salt and hash
    salt = bcrypt.gensalt(rounds=12)
    hashed = bcrypt.hashpw(password_bytes, salt)
    
    return hashed.decode('utf-8')

if __name__ == "__main__":
    password = "demo12345"
    hashed_password = generate_bcrypt_hash(password)
    
    print(f"Password: {password}")
    print(f"Hashed password: {hashed_password}")
    
    # Verify the hash
    password_bytes = password.encode('utf-8')
    hashed_bytes = hashed_password.encode('utf-8')
    
    if bcrypt.checkpw(password_bytes, hashed_bytes):
        print("[OK] Hash verification successful")
    else:
        print("[ERROR] Hash verification failed")