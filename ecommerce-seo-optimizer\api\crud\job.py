"""
Optimization job CRUD operations.
"""
import uuid
from typing import Optional, List
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from datetime import datetime, timezone, timedelta

from crud.base import CRUDBase
from models.optimization_job import Optimization<PERSON><PERSON>, JobStatus, JobType, JobPriority
from schemas.job import OptimizationJobCreate, OptimizationJobUpdate


class CRUDOptimizationJob(CRUDBase[OptimizationJob, OptimizationJobCreate, OptimizationJobUpdate]):
    """CRUD operations for OptimizationJob model."""
    
    async def get_by_store(
        self, 
        db: AsyncSession, 
        *, 
        store_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[JobStatus] = None
    ) -> List[OptimizationJob]:
        """Get jobs by store ID."""
        query = select(OptimizationJob).where(OptimizationJob.store_id == store_id)
        
        if status:
            query = query.where(OptimizationJob.status == status)
        
        query = query.order_by(OptimizationJob.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_jobs_by_stores(
        self,
        db: AsyncSession,
        *,
        store_ids: List[uuid.UUID],
        skip: int = 0,
        limit: int = 100
    ) -> List[OptimizationJob]:
        """Get jobs for multiple stores."""
        query = select(OptimizationJob).where(OptimizationJob.store_id.in_(store_ids))
        query = query.order_by(OptimizationJob.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_jobs_by_stores(
        self,
        db: AsyncSession,
        *,
        store_ids: List[uuid.UUID],
        skip: int = 0,
        limit: int = 100,
        status: Optional[JobStatus] = None
    ) -> List[OptimizationJob]:
        """Get jobs by multiple store IDs."""
        query = select(OptimizationJob).where(OptimizationJob.store_id.in_(store_ids))
        
        if status:
            query = query.where(OptimizationJob.status == status)
        
        query = query.order_by(OptimizationJob.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_active_jobs(
        self,
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100
    ) -> List[OptimizationJob]:
        """Get all active jobs."""
        query = select(OptimizationJob).where(
            OptimizationJob.status.in_([JobStatus.QUEUED, JobStatus.IN_PROGRESS])
        ).order_by(OptimizationJob.priority.desc(), OptimizationJob.created_at).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_pending_jobs(
        self,
        db: AsyncSession,
        limit: int = 100
    ) -> List[OptimizationJob]:
        """Get jobs ready to be processed."""
        query = select(OptimizationJob).where(
            and_(
                OptimizationJob.status == JobStatus.PENDING,
                or_(
                    OptimizationJob.scheduled_for.is_(None),
                    OptimizationJob.scheduled_for <= datetime.now(timezone.utc)
                )
            )
        ).order_by(OptimizationJob.priority.desc(), OptimizationJob.created_at).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_type(
        self,
        db: AsyncSession,
        *,
        job_type: JobType,
        store_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[OptimizationJob]:
        """Get jobs by type."""
        query = select(OptimizationJob).where(OptimizationJob.job_type == job_type)
        
        if store_id:
            query = query.where(OptimizationJob.store_id == store_id)
        
        query = query.order_by(OptimizationJob.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_with_content(
        self,
        db: AsyncSession,
        *,
        job_id: uuid.UUID
    ) -> Optional[OptimizationJob]:
        """Get job with generated content."""
        query = select(OptimizationJob).options(
            selectinload(OptimizationJob.generated_content)
        ).where(OptimizationJob.id == job_id)
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def start_job(
        self,
        db: AsyncSession,
        *,
        job_id: uuid.UUID
    ) -> Optional[OptimizationJob]:
        """Mark job as started."""
        job = await self.get(db, id=job_id)
        if job and job.status == JobStatus.PENDING:
            job.start()
            db.add(job)
            await db.commit()
            await db.refresh(job)
        return job
    
    async def complete_job(
        self,
        db: AsyncSession,
        *,
        job_id: uuid.UUID
    ) -> Optional[OptimizationJob]:
        """Mark job as completed."""
        job = await self.get(db, id=job_id)
        if job and job.status == JobStatus.IN_PROGRESS:
            job.complete()
            db.add(job)
            await db.commit()
            await db.refresh(job)
        return job
    
    async def fail_job(
        self,
        db: AsyncSession,
        *,
        job_id: uuid.UUID,
        error_message: str
    ) -> Optional[OptimizationJob]:
        """Mark job as failed."""
        job = await self.get(db, id=job_id)
        if job:
            job.fail(error_message)
            db.add(job)
            await db.commit()
            await db.refresh(job)
        return job
    
    async def cancel_job(
        self,
        db: AsyncSession,
        *,
        job_id: uuid.UUID
    ) -> Optional[OptimizationJob]:
        """Cancel a job."""
        job = await self.get(db, id=job_id)
        if job and job.is_active:
            job.status = JobStatus.CANCELLED
            job.completed_at = datetime.now(timezone.utc)
            db.add(job)
            await db.commit()
            await db.refresh(job)
        return job
    
    async def update_progress(
        self,
        db: AsyncSession,
        *,
        job_id: uuid.UUID,
        processed: int = 1,
        failed: int = 0,
        skipped: int = 0
    ) -> Optional[OptimizationJob]:
        """Update job progress."""
        job = await self.get(db, id=job_id)
        if job:
            job.update_progress(processed, failed, skipped)
            db.add(job)
            await db.commit()
            await db.refresh(job)
        return job
    
    async def get_job_stats(
        self,
        db: AsyncSession,
        *,
        store_id: Optional[uuid.UUID] = None
    ) -> dict:
        """Get job statistics."""
        base_query = select(func.count(OptimizationJob.id))
        
        if store_id:
            base_query = base_query.where(OptimizationJob.store_id == store_id)
        
        # Total jobs
        total_result = await db.execute(base_query)
        total_jobs = total_result.scalar()
        
        # Active jobs
        active_query = base_query.where(
            OptimizationJob.status.in_([JobStatus.QUEUED, JobStatus.IN_PROGRESS])
        )
        active_result = await db.execute(active_query)
        active_jobs = active_result.scalar()
        
        # Completed jobs
        completed_query = base_query.where(OptimizationJob.status == JobStatus.COMPLETED)
        completed_result = await db.execute(completed_query)
        completed_jobs = completed_result.scalar()
        
        # Failed jobs
        failed_query = base_query.where(OptimizationJob.status == JobStatus.FAILED)
        failed_result = await db.execute(failed_query)
        failed_jobs = failed_result.scalar()
        
        success_rate = (completed_jobs / total_jobs * 100) if total_jobs > 0 else 0
        
        return {
            "total_jobs": total_jobs,
            "active_jobs": active_jobs,
            "completed_jobs": completed_jobs,
            "failed_jobs": failed_jobs,
            "success_rate": success_rate
        }
    
    async def cleanup_old_jobs(
        self,
        db: AsyncSession,
        *,
        days_old: int = 30
    ) -> int:
        """Clean up old completed jobs."""
        from sqlalchemy import delete
        
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
        
        stmt = delete(OptimizationJob).where(
            and_(
                OptimizationJob.status.in_([JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]),
                OptimizationJob.completed_at < cutoff_date
            )
        )
        
        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount


# Create instance for dependency injection
job_crud = CRUDOptimizationJob(OptimizationJob)
