"""
Test imports for GridSpoke worker tasks.
Verifies that all task modules can be imported without errors.
"""
import sys
import os

# Add the API directory to the Python path
api_dir = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, api_dir)

def test_imports():
    """Test importing all task modules."""
    try:
        # Test product tasks import
        from workers.tasks.product_tasks import optimize_single_product, optimize_product_batch
        print("✓ Product tasks imported successfully")
        
        # Test content tasks import
        from workers.tasks.content_tasks import (
            generate_blog_post, 
            generate_product_faqs, 
            generate_buyers_guide, 
            generate_meta_descriptions
        )
        print("✓ Content tasks imported successfully")
        
        # Test scheduled tasks import
        from workers.tasks.scheduled_tasks import (
            daily_optimization_run, 
            weekly_seo_analysis, 
            check_store_updates, 
            monthly_analytics_report
        )
        print("✓ Scheduled tasks imported successfully")
        
        # Test validation tasks import
        from workers.tasks.validation_tasks import validate_products, calculate_seo_score
        print("✓ Validation tasks imported successfully")
        
        # Test utils imports
        from workers.utils.progress_tracker import ProgressTracker
        print("✓ Progress tracker imported successfully")
        
        from workers.utils.redis_client import get_redis_client
        print("✓ Redis client imported successfully")
        
        print("\nAll imports successful!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)