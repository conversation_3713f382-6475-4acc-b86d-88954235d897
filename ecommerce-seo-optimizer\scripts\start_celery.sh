#!/bin/bash

# GridSpoke Celery Task Queue Startup Script
# This script starts all Celery components for the GridSpoke Ecommerce SEO Optimizer

set -e

echo "🚀 Starting GridSpoke Celery Task Queue System..."

# Check if Redis is running
echo "📡 Checking Redis connection..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis is not running. Starting Redis..."
    redis-server --daemonize yes
    sleep 2
fi

echo "✅ Redis is running"

# Set environment variables if not already set
export CELERY_BROKER_URL=${CELERY_BROKER_URL:-"redis://localhost:6379/0"}
export CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND:-"redis://localhost:6379/0"}
export POSTGRES_URL=${POSTGRES_URL:-"postgresql+asyncpg://gridspoke:password@localhost:5432/gridspoke_db"}

echo "🔧 Environment configured:"
echo "  - Broker: $CELERY_BROKER_URL"
echo "  - Result Backend: $CELERY_RESULT_BACKEND"
echo "  - Database: $POSTGRES_URL"

# Start Celery Worker
echo "🔨 Starting Celery Worker..."
celery -A workers.celery_app worker \
    --loglevel=info \
    --concurrency=4 \
    --queues=urgent,high_priority,default,low_priority \
    --detach \
    --pidfile=/tmp/celery_worker.pid \
    --logfile=/tmp/celery_worker.log

# Start Celery Beat (Scheduler)
echo "⏰ Starting Celery Beat..."
celery -A workers.celery_app beat \
    --loglevel=info \
    --detach \
    --pidfile=/tmp/celery_beat.pid \
    --logfile=/tmp/celery_beat.log \
    --schedule=/tmp/celerybeat-schedule

# Start Flower (Optional monitoring)
if command -v flower &> /dev/null; then
    echo "🌸 Starting Flower monitoring..."
    celery -A workers.celery_app flower \
        --port=5555 \
        --broker=$CELERY_BROKER_URL \
        --detach \
        --pidfile=/tmp/celery_flower.pid \
        --logfile=/tmp/celery_flower.log
    echo "🌸 Flower monitoring available at: http://localhost:5555"
fi

echo ""
echo "✅ GridSpoke Celery Task Queue System started successfully!"
echo ""
echo "📊 System Status:"
echo "  - Worker: Running (PID: $(cat /tmp/celery_worker.pid 2>/dev/null || echo 'Not found'))"
echo "  - Beat: Running (PID: $(cat /tmp/celery_beat.pid 2>/dev/null || echo 'Not found'))"
echo "  - Flower: Running (PID: $(cat /tmp/celery_flower.pid 2>/dev/null || echo 'Not found'))"
echo ""
echo "📁 Log files:"
echo "  - Worker: /tmp/celery_worker.log"
echo "  - Beat: /tmp/celery_beat.log"
echo "  - Flower: /tmp/celery_flower.log"
echo ""
echo "🛑 To stop all services, run: ./scripts/stop_celery.sh"
echo ""

# Monitor services (optional)
if [ "$1" == "--monitor" ]; then
    echo "🔍 Monitoring Celery services (Ctrl+C to exit)..."
    while true; do
        echo "$(date): Checking services..."
        
        # Check worker
        if kill -0 $(cat /tmp/celery_worker.pid 2>/dev/null) 2>/dev/null; then
            echo "  ✅ Worker: Running"
        else
            echo "  ❌ Worker: Stopped"
        fi
        
        # Check beat
        if kill -0 $(cat /tmp/celery_beat.pid 2>/dev/null) 2>/dev/null; then
            echo "  ✅ Beat: Running"
        else
            echo "  ❌ Beat: Stopped"
        fi
        
        # Check flower
        if kill -0 $(cat /tmp/celery_flower.pid 2>/dev/null) 2>/dev/null; then
            echo "  ✅ Flower: Running"
        else
            echo "  ❌ Flower: Stopped"
        fi
        
        sleep 30
    done
fi
