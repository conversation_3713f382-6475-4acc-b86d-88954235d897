<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GridSpoke SEO Optimizer - Dashboard</title>
    
    <!-- Tailwind CSS v3 CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js v3 CDN -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="css/styles.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gridspoke-primary': '#3B82F6',
                        'gridspoke-secondary': '#8B5CF6',
                        'gridspoke-success': '#10B981',
                        'gridspoke-warning': '#F59E0B',
                        'gridspoke-error': '#EF4444',
                        'gridspoke-dark': '#1F2937',
                    }
                }
            }
        }
    </script>
    <script>
        // Initialize Auth before Alpine.js starts
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure Auth module is initialized
            if (typeof Auth !== 'undefined' && Auth.init) {
                Auth.init();
            }
        });
    </script>
</head>
<body class="bg-gray-50" x-data="dashboardApp()" x-init="init()">
    <!-- AI Background Canvas -->
    <canvas id="ai-bg" style="position: fixed; inset: 0; width: 100%; height: 100%; z-index: -1; background: radial-gradient(1000px 600px at 10% 10%, rgba(59,130,246,0.08), transparent), radial-gradient(800px 500px at 90% 20%, rgba(139,92,246,0.10), transparent), radial-gradient(900px 600px at 50% 80%, rgba(20,184,166,0.06), transparent);"></canvas>
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and Navigation -->
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="h-8 w-8 bg-gridspoke-primary rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <span class="ml-2 text-xl font-bold text-gray-900">GridSpoke</span>
                    </div>
                    
                    <!-- Desktop Navigation -->
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <button @click="activeTab = 'dashboard'" 
                                :class="activeTab === 'dashboard' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Dashboard
                        </button>
                        <button @click="switchTab('products')" 
                                :class="activeTab === 'products' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Products
                        </button>
                        <button @click="switchTab('jobs')" 
                                :class="activeTab === 'jobs' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Jobs
                        </button>
                        <button @click="activeTab = 'settings'" 
                                :class="activeTab === 'settings' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Settings
                        </button>
                    </div>
                </div>
                
                <!-- Right side -->
                <div class="flex items-center space-x-4">
                    <!-- Store Switcher -->
                    <div class="hidden md:flex items-center space-x-2" x-show="settings.availableStores && settings.availableStores.length > 0">
                        <span class="text-sm text-gray-600">Store</span>
                        <select x-model="settings.selectedStoreId" @change="updateSelectedStore()"
                                class="text-sm px-2 py-1 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                            <template x-for="store in settings.availableStores" :key="store.id">
                                <option :value="store.id" x-text="store.name"></option>
                            </template>
                        </select>
                    </div>
                    <!-- Notifications -->
                    <button @click="showNotifications = !showNotifications" class="relative p-2 text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5V9.09c0-2.83-2.31-5.14-5.14-5.14S4.72 6.26 4.72 9.09V12l-5 5h5m10 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span x-show="notifications.length > 0" x-text="notifications.length" class="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"></span>
                    </button>
                    
                    <!-- User Menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center space-x-2 text-sm text-gray-700 hover:text-gray-900">
                            <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                                <span x-text="user.name ? user.name.charAt(0).toUpperCase() : 'U'" class="text-gray-600 font-medium"></span>
                            </div>
                            <span x-text="user.name || 'User'"></span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        
                        <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Billing</a>
                            <button @click="logout()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Tab -->
        <div x-show="activeTab === 'dashboard'">
            <!-- Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                                <dd class="text-lg font-medium text-gray-900" x-text="stats.totalProducts"></dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Optimized</dt>
                                <dd class="text-lg font-medium text-gray-900" x-text="stats.optimizedProducts"></dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Active Jobs</dt>
                                <dd class="text-lg font-medium text-gray-900" x-text="stats.activeJobs"></dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">SEO Score</dt>
                                <dd class="text-lg font-medium text-gray-900" x-text="stats.averageSeoScore + '%'"></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Optimization Progress Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Optimization Progress</h3>
                    <div style="height: 300px; position: relative;">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>
                
                <!-- Job Status Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Job Status Distribution</h3>
                    <div style="height: 300px; position: relative;">
                        <canvas id="jobStatusChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
                </div>
                <div class="p-6">
                    <div class="flow-root">
                        <ul class="-mb-8">
                            <template x-for="(activity, index) in recentActivity" :key="index">
                                <li>
                                    <div class="relative pb-8" x-show="index < recentActivity.length - 1">
                                        <span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200"></span>
                                    </div>
                                    <div class="relative flex items-start space-x-3">
                                        <div>
                                            <div class="relative px-1">
                                                <div class="h-8 w-8 bg-gray-100 rounded-full ring-8 ring-white flex items-center justify-center">
                                                    <svg class="h-5 w-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="min-w-0 flex-1">
                                            <div>
                                                <div class="text-sm">
                                                    <span class="font-medium text-gray-900" x-text="activity.message"></span>
                                                </div>
                                                <p class="mt-0.5 text-sm text-gray-500" x-text="formatTime(activity.timestamp)"></p>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Tab -->
        <div x-show="activeTab === 'products'">
            <div id="products-container">
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">Products</h3>
                            <button @click="loadProducts()" class="bg-gridspoke-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
                                Refresh
                            </button>
                        </div>
                    </div>
                    <div id="products-content" class="p-6">
                        <p class="text-gray-500">Loading products...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Jobs Tab -->
        <div x-show="activeTab === 'jobs'">
            <div id="jobs-container">
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">Optimization Jobs</h3>
                            <button @click="loadJobs()" class="bg-gridspoke-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
                                Refresh
                            </button>
                        </div>
                    </div>
                    <div id="jobs-content" class="p-6">
                        <p class="text-gray-500">Loading jobs...</p>
                    </div>
                    <div id="job-progress-container" class="p-6 border-t border-gray-200"></div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div x-show="activeTab === 'settings'">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Settings</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- Store Information -->
                        <div>
                            <h4 class="text-base font-medium text-gray-900 mb-4">Store Information</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div x-show="settings.selectedStore" class="space-y-2">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm font-medium text-gray-700">Selected Store:</span>
                                        <span class="text-sm text-gray-900" x-text="settings.selectedStore?.name || 'No store selected'"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm font-medium text-gray-700">Platform:</span>
                                        <span class="text-sm text-gray-900" x-text="settings.selectedStore?.platform || 'Unknown'"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm font-medium text-gray-700">URL:</span>
                                        <span class="text-sm text-gray-900" x-text="settings.selectedStore?.url || 'Not configured'"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm font-medium text-gray-700">Status:</span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                              :class="settings.selectedStore?.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                              x-text="settings.selectedStore?.is_active ? 'Active' : 'Inactive'"></span>
                                    </div>
                                </div>
                                <div x-show="!settings.selectedStore" class="text-center text-gray-500">
                                    <p class="text-sm">No store connected</p>
                                    <p class="text-xs mt-1">Connect your WordPress/WooCommerce store to get started</p>
                                </div>
                            </div>
                        </div>

                        <!-- Store Selection -->
                        <div x-show="settings.availableStores && settings.availableStores.length > 1">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Store Selection</h4>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Switch Store</label>
                                <select x-model="settings.selectedStoreId" @change="updateSelectedStore()" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                                    <template x-for="store in settings.availableStores" :key="store.id">
                                        <option :value="store.id" x-text="`${store.name} (${store.platform})`"></option>
                                    </template>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">Changing the store will refresh the dashboard data</p>
                            </div>
                        </div>
                        
                        <!-- Optimization Settings -->
                        <div>
                            <h4 class="text-base font-medium text-gray-900 mb-4">Optimization Settings</h4>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Auto-optimize new products</label>
                                        <p class="text-xs text-gray-500">Automatically optimize products when they're added to your store</p>
                                    </div>
                                    <input type="checkbox" x-model="settings.autoOptimize" 
                                           class="h-4 w-4 text-gridspoke-primary focus:ring-gridspoke-primary border-gray-300 rounded">
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Email notifications</label>
                                        <p class="text-xs text-gray-500">Get notified when optimization jobs complete</p>
                                    </div>
                                    <input type="checkbox" x-model="settings.emailNotifications" 
                                           class="h-4 w-4 text-gridspoke-primary focus:ring-gridspoke-primary border-gray-300 rounded">
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Bulk optimization schedule</label>
                                        <p class="text-xs text-gray-500">Run bulk optimizations during off-peak hours (2 AM)</p>
                                    </div>
                                    <input type="checkbox" x-model="settings.scheduledOptimization" 
                                           class="h-4 w-4 text-gridspoke-primary focus:ring-gridspoke-primary border-gray-300 rounded">
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Settings -->
                        <div>
                            <h4 class="text-base font-medium text-gray-900 mb-4">Advanced Settings</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">AI Model Preference</label>
                                    <select x-model="settings.aiModel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                                        <option value="gpt-4">GPT-4 (Premium)</option>
                                        <option value="claude-3">Claude 3 (Premium)</option>
                                        <option value="gpt-3.5">GPT-3.5 (Standard)</option>
                                        <option value="custom">Custom Model</option>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-1">Premium models provide better optimization quality</p>
                                </div>
                                <div x-show="settings.aiModel === 'custom'">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Custom Model Name</label>
                                    <input type="text" x-model="settings.customAiModel" placeholder="e.g., anthropic/claude-3.5-sonnet" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                                    <p class="text-xs text-gray-500 mt-1">Enter a valid OpenRouter model identifier</p>
                                </div>
                                <!-- Vision Model Settings -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Vision Model Preference</label>
                                    <select x-model="settings.visionModel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                                        <option value="gpt-4-vision">GPT-4 Vision (Premium)</option>
                                        <option value="claude-3-vision">Claude 3 Vision (Premium)</option>
                                        <option value="gemini-pro-vision">Gemini Pro Vision (Standard)</option>
                                        <option value="custom">Custom Vision Model</option>
                                    </select>
                                    <p class="text-xs text-gray-500 mt-1">Models specifically designed for image analysis</p>
                                </div>
                                <div x-show="settings.visionModel === 'custom'">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Custom Vision Model Name</label>
                                    <input type="text" x-model="settings.customVisionModel" placeholder="e.g., openai/gpt-4-vision-preview" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                                    <p class="text-xs text-gray-500 mt-1">Enter a valid OpenRouter vision model identifier</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Optimization Frequency</label>
                                    <select x-model="settings.optimizationFrequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="monthly">Monthly</option>
                                        <option value="manual">Manual Only</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">OpenRouter API Key</label>
                                    <input type="password" x-model="settings.openRouterApiKey" placeholder="Enter your OpenRouter API key" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                                    <p class="text-xs text-gray-500 mt-1">Enter your own OpenRouter API key for custom model access</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="pt-4">
                            <button @click="saveSettings()" class="bg-gridspoke-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
                                Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Panel -->
    <div x-show="showNotifications" x-transition class="fixed inset-0 z-50">
        <div class="absolute inset-0 bg-black bg-opacity-25" @click="showNotifications = false"></div>
        <div class="absolute top-16 right-4 w-80 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5">
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Notifications</h3>
            </div>
            <div class="max-h-96 overflow-y-auto">
                <template x-for="notification in notifications" :key="notification.id">
                    <div class="p-4 border-b border-gray-100 hover:bg-gray-50">
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-900" x-text="notification.message"></p>
                            <button @click="removeNotification(notification.id)" class="text-gray-400 hover:text-gray-600">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1" x-text="formatTime(notification.timestamp)"></p>
                    </div>
                </template>
                <div x-show="notifications.length === 0" class="p-4 text-center text-gray-500">
                    No notifications
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts loaded in order before Alpine.js initializes -->
    <script>
        const CB = Date.now() + Math.random().toString(36).substr(2, 9); // Cache Buster
    </script>
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/api.js"></script>
    <script src="js/websocket-v2.js"></script>
    <script src="js/products.js"></script>
    <script src="js/jobs.js"></script>
    <script src="js/dashboard.js"></script>
    
    <!-- Debug script to check if functions are loaded -->
    <script>
        console.log('=== Script Loading Check ===');
        console.log('dashboardApp exists:', typeof window.dashboardApp);
        if (typeof window.dashboardApp === 'undefined') {
            console.error('dashboardApp function not loaded!');
        }
    </script>
    <script>
        // Minimal AI background animation: floating particles parallax
        (function() {
            const c = document.getElementById('ai-bg');
            if (!c) return;
            const dpr = Math.max(window.devicePixelRatio || 1, 1);
            const ctx = c.getContext('2d');
            let W, H, particles = [];
            function resize(){
                W = c.width = Math.floor(window.innerWidth * dpr);
                H = c.height = Math.floor(window.innerHeight * dpr);
            }
            function initParticles(){
                const count = Math.min(80, Math.floor((W*H)/(18000*dpr)));
                particles = Array.from({length: count}, ()=>({
                    x: Math.random()*W,
                    y: Math.random()*H,
                    vx: (Math.random()-0.5)*0.2*dpr,
                    vy: (Math.random()-0.5)*0.2*dpr,
                    r: Math.random()*2.2*dpr + 0.6*dpr,
                    hue: 200 + Math.random()*160,
                    alpha: 0.35 + Math.random()*0.35
                }));
            }
            function step(){
                ctx.clearRect(0,0,W,H);
                for (const p of particles){
                    p.x += p.vx; p.y += p.vy;
                    if (p.x<0||p.x>W) p.vx*=-1;
                    if (p.y<0||p.y>H) p.vy*=-1;
                    ctx.beginPath();
                    ctx.fillStyle = `hsla(${p.hue}, 85%, 60%, ${p.alpha})`;
                    ctx.arc(p.x, p.y, p.r, 0, Math.PI*2);
                    ctx.fill();
                }
                // subtle connecting lines
                ctx.strokeStyle = 'rgba(59,130,246,0.08)';
                for (let i=0;i<particles.length;i++){
                    for (let j=i+1;j<particles.length;j++){
                        const a=particles[i], b=particles[j];
                        const dx=a.x-b.x, dy=a.y-b.y, dist=dx*dx+dy*dy;
                        if (dist < (120*dpr)*(120*dpr)){
                            ctx.globalAlpha = 0.08 * (1 - dist/((120*dpr)*(120*dpr)));
                            ctx.beginPath();
                            ctx.moveTo(a.x,a.y); ctx.lineTo(b.x,b.y); ctx.stroke();
                            ctx.globalAlpha = 1;
                        }
                    }
                }
                requestAnimationFrame(step);
            }
            window.addEventListener('resize', ()=>{ resize(); initParticles(); });
            resize(); initParticles(); step();
        })();
    </script>
    
    <!-- API and WebSocket Fixes -->
    <script src="/js/api-fixes.js"></script>
    <script src="/js/websocket-fixes.js"></script>
</body>
</html>
