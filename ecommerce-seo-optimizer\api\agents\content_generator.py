"""
Content Generator Agent for ecommerce content creation.

This agent specializes in generating blog posts, FAQs, buyer's guides,
and other marketing content for ecommerce websites.
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from mirascope import llm
from pydantic import BaseModel, Field

from .base import BaseAgent, AgentConfig, OptimizationResult
from ..prompts.templates import PromptManager


class ContentRequest(BaseModel):
    """Content generation request structure."""
    
    content_type: str  # blog_post, faq, buyers_guide
    topic: str
    target_keywords: List[str]
    word_count: Optional[int] = None
    target_audience: Optional[str] = None
    tone: Optional[str] = "professional"
    products: Optional[List[Dict[str, Any]]] = None
    category: Optional[str] = None
    include_products: bool = False
    custom_requirements: Optional[Dict[str, Any]] = None


class BlogPostContent(BaseModel):
    """Structure for blog post content."""
    
    title: str
    meta_description: str
    content: str
    internal_links: List[str]
    external_links: List[str]
    target_keywords_used: List[str]
    word_count: int
    seo_score: Optional[float] = None


class FAQContent(BaseModel):
    """Structure for FAQ content."""
    
    faqs: List[Dict[str, Any]]
    total_questions: int
    keywords_covered: List[str]
    voice_search_optimized: bool = True


class BuyersGuideContent(BaseModel):
    """Structure for buyer's guide content."""
    
    title: str
    content: str
    product_comparisons: List[Dict[str, Any]]
    recommendations: List[Dict[str, Any]]
    word_count: int


class ContentGenerator(BaseAgent):
    """
    AI agent specialized in content generation for ecommerce.
    
    Capabilities:
    - Generate SEO-optimized blog posts
    - Create comprehensive FAQs for products and categories
    - Generate buyer's guides with product comparisons
    - Create category descriptions and landing page content
    - Generate content for different stages of buyer journey
    - Optimize content for voice search and featured snippets
    """
    
    def __init__(self, config: Optional[AgentConfig] = None):
        """Initialize the Content Generator agent."""
        super().__init__(config)
        self.prompt_manager = PromptManager()
        self.logger.info("ContentGenerator agent initialized")
    
    async def process(self, input_data: Dict[str, Any], **kwargs) -> OptimizationResult:
        """
        Process content generation request.
        
        Args:
            input_data: Content request data
            **kwargs: Additional parameters
            
        Returns:
            OptimizationResult with generated content
        """
        try:
            # Validate and parse input data
            content_request = ContentRequest(**input_data)
            complexity = kwargs.get('complexity', 'standard')
            
            self.state.current_task = f"Generating {content_request.content_type}: {content_request.topic}"
            self.state.update_progress(0.0)
            
            # Check cache first
            cache_key = self.cache_key("content_generation", {
                "content_type": content_request.content_type,
                "topic": content_request.topic,
                "target_keywords": content_request.target_keywords,
                "word_count": content_request.word_count
            })
            
            cached_result = await self.get_cached_result(cache_key)
            if cached_result:
                self.logger.info(f"Using cached result for content: {content_request.topic}")
                return OptimizationResult(
                    success=True,
                    content=cached_result,
                    model_used="cached",
                    processing_time=0.0
                )
            
            # Generate content based on type
            start_time = time.time()
            
            if content_request.content_type == 'blog_post':
                result = await self._generate_blog_post(content_request, complexity)
            elif content_request.content_type == 'faq':
                result = await self._generate_faq(content_request, complexity)
            elif content_request.content_type == 'buyers_guide':
                result = await self._generate_buyers_guide(content_request, complexity)
            else:
                raise ValueError(f"Unknown content type: {content_request.content_type}")
            
            processing_time = time.time() - start_time
            
            # Cache the result
            await self.cache_result(cache_key, result.model_dump(), ttl=7200)  # 2 hours cache for content
            
            self.state.update_progress(100.0)
            
            return OptimizationResult(
                success=True,
                content=result.model_dump(),
                model_used=self.select_model("content_generation", complexity),
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Content generation failed: {e}")
            self.state.increment_error()
            
            return OptimizationResult(
                success=False,
                error_message=str(e)
            )
    
    async def _generate_blog_post(self, request: ContentRequest, complexity: str) -> BlogPostContent:
        """Generate a comprehensive blog post."""
        self.logger.info(f"Generating blog post: {request.topic}")
        
        # Set default word count if not specified
        word_count = request.word_count or 1500
        
        model = self.select_model("blog_post", complexity)
        
        @llm.call(
            provider="openai",
            model=model,
            api_key=self.config.api_key,
            base_url=self.config.base_url
        )
        def generate_blog_content(prompt: str) -> str:
            return prompt
        
        # Format the prompt
        prompt = self.prompt_manager.format_template(
            "content",
            "blog_post_generation",
            topic=request.topic,
            target_keywords=request.target_keywords,
            word_count=word_count,
            tone=request.tone,
            target_audience=request.target_audience,
            products=request.products or []
        )
        
        self.state.update_progress(30.0)
        
        # Generate content with retry logic
        content_response = await self.call_with_retry(
            lambda: generate_blog_content(prompt)
        )
        
        self.state.update_progress(70.0)
        
        # Parse JSON response
        try:
            content_data = json.loads(content_response)
        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            content_data = {
                "title": request.topic,
                "meta_description": f"Learn about {request.topic}",
                "content": content_response,
                "internal_links": [],
                "external_links": [],
                "target_keywords_used": request.target_keywords
            }
        
        # Calculate word count
        actual_word_count = len(content_data.get("content", "").split())
        
        # Calculate SEO score
        seo_score = self._calculate_blog_seo_score(content_data, request.target_keywords)
        
        self.state.update_progress(90.0)
        
        return BlogPostContent(
            title=content_data.get("title", request.topic),
            meta_description=content_data.get("meta_description", ""),
            content=content_data.get("content", ""),
            internal_links=content_data.get("internal_links", []),
            external_links=content_data.get("external_links", []),
            target_keywords_used=content_data.get("target_keywords_used", []),
            word_count=actual_word_count,
            seo_score=seo_score
        )
    
    async def _generate_faq(self, request: ContentRequest, complexity: str) -> FAQContent:
        """Generate FAQ content for a product or category."""
        self.logger.info(f"Generating FAQ for: {request.topic}")
        
        model = self.select_model("faq", complexity)
        
        @llm.call(
            provider="openai",
            model=model,
            api_key=self.config.api_key,
            base_url=self.config.base_url
        )
        def generate_faq_content(prompt: str) -> str:
            return prompt
        
        # Determine if this is for a specific product or category
        if request.products and len(request.products) == 1:
            # Single product FAQ
            product = request.products[0]
            prompt = self.prompt_manager.format_template(
                "content",
                "faq_generation",
                product_name=product.get("name", request.topic),
                category=product.get("category", request.category or ""),
                common_concerns=request.custom_requirements.get("common_concerns") if request.custom_requirements else None,
                specifications=product.get("specifications"),
                features=product.get("features")
            )
        else:
            # Category or general FAQ
            prompt = self.prompt_manager.format_template(
                "content", 
                "faq_generation",
                product_name=request.topic,
                category=request.category or "",
                common_concerns=request.custom_requirements.get("common_concerns") if request.custom_requirements else None
            )
        
        self.state.update_progress(50.0)
        
        # Generate FAQ content
        faq_response = await self.call_with_retry(
            lambda: generate_faq_content(prompt)
        )
        
        self.state.update_progress(80.0)
        
        # Parse JSON response
        try:
            faq_data = json.loads(faq_response)
            faqs = faq_data.get("faqs", [])
        except json.JSONDecodeError:
            # Fallback with basic FAQ structure
            faqs = [
                {
                    "question": f"What is {request.topic}?",
                    "answer": "This is a comprehensive answer about the topic.",
                    "keywords": request.target_keywords[:3]
                }
            ]
        
        # Extract keywords covered
        keywords_covered = []
        for faq in faqs:
            keywords_covered.extend(faq.get("keywords", []))
        
        return FAQContent(
            faqs=faqs,
            total_questions=len(faqs),
            keywords_covered=list(set(keywords_covered)),
            voice_search_optimized=True
        )
    
    async def _generate_buyers_guide(self, request: ContentRequest, complexity: str) -> BuyersGuideContent:
        """Generate a comprehensive buyer's guide."""
        self.logger.info(f"Generating buyer's guide for: {request.topic}")
        
        model = self.select_model("buyers_guide", complexity)
        
        @llm.call(
            provider="openai",
            model=model,
            api_key=self.config.api_key,
            base_url=self.config.base_url
        )
        def generate_guide_content(prompt: str) -> str:
            return prompt
        
        # Format the prompt with product information
        products_info = []
        if request.products:
            for product in request.products:
                products_info.append(f"- {product.get('name', '')}: {product.get('description', '')}")
        
        prompt = self.prompt_manager.format_template(
            "content",
            "buyers_guide_generation",
            category=request.category or request.topic,
            products=products_info,
            budget_ranges=request.custom_requirements.get("budget_ranges") if request.custom_requirements else None,
            use_cases=request.custom_requirements.get("use_cases") if request.custom_requirements else None,
            comparison_factors=request.custom_requirements.get("comparison_factors") if request.custom_requirements else None
        )
        
        self.state.update_progress(40.0)
        
        # Generate guide content
        guide_response = await self.call_with_retry(
            lambda: generate_guide_content(prompt)
        )
        
        self.state.update_progress(70.0)
        
        # Extract product comparisons and recommendations
        product_comparisons = []
        recommendations = []
        
        if request.products:
            for product in request.products:
                product_comparisons.append({
                    "name": product.get("name", ""),
                    "pros": [],  # Would be extracted from content
                    "cons": [],  # Would be extracted from content
                    "best_for": ""  # Would be extracted from content
                })
                
                recommendations.append({
                    "product": product.get("name", ""),
                    "use_case": "General use",
                    "reasoning": "Recommended based on features and value"
                })
        
        # Calculate word count
        word_count = len(guide_response.split())
        
        return BuyersGuideContent(
            title=f"Complete {request.topic} Buyer's Guide",
            content=guide_response,
            product_comparisons=product_comparisons,
            recommendations=recommendations,
            word_count=word_count
        )
    
    def _calculate_blog_seo_score(self, content_data: Dict[str, Any], target_keywords: List[str]) -> float:
        """Calculate SEO score for blog post content."""
        score = 0.0
        
        title = content_data.get("title", "")
        meta_description = content_data.get("meta_description", "")
        content = content_data.get("content", "")
        
        # Title optimization (20 points)
        if title:
            if len(title) <= 60:
                score += 10
            if any(keyword.lower() in title.lower() for keyword in target_keywords):
                score += 10
        
        # Meta description optimization (20 points)
        if meta_description:
            if len(meta_description) <= 155:
                score += 10
            if any(keyword.lower() in meta_description.lower() for keyword in target_keywords):
                score += 10
        
        # Content length (20 points)
        word_count = len(content.split())
        if 1200 <= word_count <= 2000:
            score += 20
        elif 800 <= word_count < 1200 or 2000 < word_count <= 2500:
            score += 15
        else:
            score += 5
        
        # Keyword usage (20 points)
        content_lower = content.lower()
        keywords_found = sum(1 for keyword in target_keywords if keyword.lower() in content_lower)
        keyword_density = keywords_found / len(target_keywords) if target_keywords else 0
        score += keyword_density * 20
        
        # Structure (20 points)
        if "<h2>" in content or "## " in content:
            score += 10
        if "<h3>" in content or "### " in content:
            score += 5
        if "<ul>" in content or "<ol>" in content or "- " in content:
            score += 5
        
        return min(score, 100.0)
    
    async def generate_category_content(
        self,
        category: str,
        products: List[Dict[str, Any]],
        content_types: List[str] = None
    ) -> Dict[str, OptimizationResult]:
        """
        Generate multiple types of content for a product category.
        
        Args:
            category: Product category name
            products: List of products in the category
            content_types: Types of content to generate
            
        Returns:
            Dictionary of content type to OptimizationResult
        """
        if content_types is None:
            content_types = ["blog_post", "faq", "buyers_guide"]
        
        self.logger.info(f"Generating category content for: {category}")
        
        results = {}
        total_types = len(content_types)
        
        for i, content_type in enumerate(content_types):
            try:
                # Update progress
                progress = (i / total_types) * 100
                self.state.update_progress(progress)
                
                # Prepare content request
                request_data = {
                    "content_type": content_type,
                    "topic": f"{category} Products",
                    "target_keywords": [category.lower(), f"best {category.lower()}", f"{category.lower()} guide"],
                    "category": category,
                    "products": products,
                    "include_products": True
                }
                
                # Generate content
                result = await self.process(request_data)
                results[content_type] = result
                
                # Small delay between requests
                await asyncio.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Failed to generate {content_type} for {category}: {e}")
                results[content_type] = OptimizationResult(
                    success=False,
                    error_message=str(e)
                )
        
        self.state.update_progress(100.0)
        return results
    
    async def generate_seasonal_content(
        self,
        season: str,
        categories: List[str],
        content_focus: str = "buying_guide"
    ) -> List[OptimizationResult]:
        """
        Generate seasonal content for multiple categories.
        
        Args:
            season: Season name (e.g., "Holiday", "Back to School")
            categories: List of relevant categories
            content_focus: Type of seasonal content
            
        Returns:
            List of OptimizationResult objects
        """
        self.logger.info(f"Generating {season} content for {len(categories)} categories")
        
        results = []
        
        for category in categories:
            try:
                request_data = {
                    "content_type": "blog_post",
                    "topic": f"Best {category} for {season} {datetime.now().year}",
                    "target_keywords": [
                        f"{season.lower()} {category.lower()}",
                        f"best {category.lower()} {season.lower()}",
                        f"{category.lower()} deals"
                    ],
                    "word_count": 1200,
                    "custom_requirements": {
                        "seasonal_focus": season,
                        "buying_urgency": True
                    }
                }
                
                result = await self.process(request_data)
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Failed to generate seasonal content for {category}: {e}")
                results.append(OptimizationResult(
                    success=False,
                    error_message=str(e)
                ))
        
        return results
