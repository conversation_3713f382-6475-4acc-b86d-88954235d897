<?php
/**
 * SureCart Integration for GridSpoke SEO Connector
 * 
 * Handles all SureCart-specific functionality including
 * product sync, bulk actions, and content updates.
 */

if (!defined('ABSPATH')) {
    exit;
}

class GridSpoke_SureCart_Integration {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * API client instance
     */
    private $api_client;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->api_client = GridSpoke_API_Client::get_instance();
        $this->init_hooks();
    }
    
    /**
     * Initialize SureCart hooks
     */
    private function init_hooks() {
        // SureCart product sync hooks
        add_action('surecart/product_created', array($this, 'sync_single_product'));
        add_action('surecart/product_updated', array($this, 'sync_single_product'));
        
        // Admin hooks for SureCart products
        add_action('admin_init', array($this, 'init_admin_hooks'));
        
        // AJAX handlers
        add_action('wp_ajax_gridspoke_optimize_surecart_product', array($this, 'ajax_optimize_product'));
        add_action('wp_ajax_gridspoke_sync_surecart_product', array($this, 'ajax_sync_product'));
        
        // Add meta box to SureCart product edit page
        add_action('add_meta_boxes', array($this, 'add_optimization_meta_box'));
    }
    
    /**
     * Initialize admin hooks after checking SureCart is available
     */
    public function init_admin_hooks() {
        if (!$this->is_surecart_available()) {
            return;
        }
        
        // Add bulk actions to SureCart product list
        add_filter('bulk_actions-edit-sc_product', array($this, 'add_bulk_actions'));
        add_filter('handle_bulk_actions-edit-sc_product', array($this, 'handle_bulk_actions'), 10, 3);
        
        // Add optimization column to product list
        add_filter('manage_edit-sc_product_columns', array($this, 'add_optimization_column'));
        add_action('manage_sc_product_posts_custom_column', array($this, 'display_optimization_column'), 10, 2);
        
        // Admin notices
        add_action('admin_notices', array($this, 'bulk_action_notices'));
        
        // Enqueue admin scripts
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Check if SureCart is available and properly loaded
     */
    private function is_surecart_available() {
        return defined('SURECART_PLUGIN_FILE') && function_exists('surecart');
    }
    
    /**
     * Sync single product when created or updated
     */
    public function sync_single_product($product_data) {
        $settings = GridSpoke_SEO_Connector::get_settings();
        
        // Only auto-sync if enabled
        if (empty($settings['auto_sync'])) {
            return;
        }
        
        if (isset($product_data['id'])) {
            $formatted_data = $this->get_product_data($product_data['id']);
            if ($formatted_data) {
                $this->api_client->sync_products(array($formatted_data), 'surecart');
            }
        }
    }
    
    /**
     * Get formatted product data for API
     */
    public function get_product_data($product_id) {
        if (!$this->is_surecart_available()) {
            return false;
        }
        
        // Get SureCart product
        $product = \SureCart\Models\Product::find($product_id);
        
        if (!$product || is_wp_error($product)) {
            return false;
        }
        
        // Get WordPress post data if linked
        $wp_post_id = null;
        $wp_post_data = array();
        
        // SureCart products might have linked WordPress posts
        if (!empty($product->wp_post_id)) {
            $wp_post_id = $product->wp_post_id;
            $wp_post = get_post($wp_post_id);
            
            if ($wp_post) {
                $wp_post_data = array(
                    'title' => $wp_post->post_title,
                    'content' => $wp_post->post_content,
                    'excerpt' => $wp_post->post_excerpt,
                    'slug' => $wp_post->post_name,
                    'status' => $wp_post->post_status
                );
            }
        }
        
        // Format pricing data
        $pricing = array();
        if (!empty($product->prices)) {
            foreach ($product->prices as $price) {
                $pricing[] = array(
                    'id' => $price->id ?? '',
                    'amount' => $price->amount ?? 0,
                    'currency' => $price->currency ?? 'USD',
                    'recurring' => !empty($price->recurring),
                    'interval' => $price->recurring->interval ?? null,
                    'interval_count' => $price->recurring->interval_count ?? null
                );
            }
        }
        
        // Get product images
        $images = array();
        if (!empty($product->image_url)) {
            $images[] = array(
                'url' => $product->image_url,
                'alt' => $product->image_alt ?? ''
            );
        }
        
        if (!empty($product->gallery)) {
            foreach ($product->gallery as $gallery_item) {
                $images[] = array(
                    'url' => $gallery_item->url ?? '',
                    'alt' => $gallery_item->alt ?? ''
                );
            }
        }
        
        // Get product features/benefits
        $features = array();
        if (!empty($product->statement_descriptors)) {
            foreach ($product->statement_descriptors as $descriptor) {
                $features[] = $descriptor;
            }
        }
        
        return array(
            'id' => $product->id,
            'wp_post_id' => $wp_post_id,
            'name' => $product->name ?? '',
            'slug' => $wp_post_data['slug'] ?? '',
            'status' => $product->status ?? 'draft',
            'description' => $product->description ?? '',
            'short_description' => $wp_post_data['excerpt'] ?? '',
            'statement_descriptor' => $product->statement_descriptor ?? '',
            'pricing' => $pricing,
            'images' => $images,
            'features' => $features,
            'is_shippable' => !empty($product->is_shippable),
            'is_taxable' => !empty($product->is_taxable),
            'tax_code' => $product->tax_code ?? '',
            'permalink' => !empty($wp_post_id) ? get_permalink($wp_post_id) : '',
            'date_created' => $product->created_at ?? '',
            'date_modified' => $product->updated_at ?? '',
            'meta' => array(
                'product_type' => 'surecart',
                'wp_post_data' => $wp_post_data
            )
        );
    }
    
    /**
     * Sync multiple products
     */
    public function sync_products($product_ids = null) {
        if (!$this->is_surecart_available()) {
            return false;
        }
        
        if ($product_ids === null) {
            // Get all products from SureCart
            $products = \SureCart\Models\Product::where(array(
                'status' => 'published',
                'per_page' => 100 // Adjust as needed
            ));
            
            $product_ids = array();
            if ($products && !is_wp_error($products)) {
                foreach ($products->data as $product) {
                    $product_ids[] = $product->id;
                }
            }
        }
        
        $product_data = array();
        foreach ($product_ids as $product_id) {
            $data = $this->get_product_data($product_id);
            if ($data) {
                $product_data[] = $data;
            }
        }
        
        if (!empty($product_data)) {
            return $this->api_client->sync_products($product_data, 'surecart');
        }
        
        return false;
    }
    
    /**
     * Add bulk actions to product list
     */
    public function add_bulk_actions($actions) {
        $actions['gridspoke_optimize'] = __('Optimize with GridSpoke', 'gridspoke-seo');
        $actions['gridspoke_sync'] = __('Sync to GridSpoke', 'gridspoke-seo');
        return $actions;
    }
    
    /**
     * Handle bulk actions
     */
    public function handle_bulk_actions($redirect_url, $action, $post_ids) {
        if ($action === 'gridspoke_optimize') {
            // Convert WordPress post IDs to SureCart product IDs
            $surecart_ids = $this->convert_wp_ids_to_surecart_ids($post_ids);
            
            if (!empty($surecart_ids)) {
                $result = $this->api_client->request_optimization($surecart_ids);
                
                if ($result) {
                    $redirect_url = add_query_arg('gridspoke_optimized', count($surecart_ids), $redirect_url);
                    
                    // Store optimization ID for tracking
                    if (isset($result['optimization_id'])) {
                        set_transient('gridspoke_optimization_' . $result['optimization_id'], $surecart_ids, DAY_IN_SECONDS);
                    }
                } else {
                    $redirect_url = add_query_arg('gridspoke_error', 'optimization_failed', $redirect_url);
                }
            }
        } elseif ($action === 'gridspoke_sync') {
            $surecart_ids = $this->convert_wp_ids_to_surecart_ids($post_ids);
            
            if (!empty($surecart_ids)) {
                $result = $this->sync_products($surecart_ids);
                
                if ($result) {
                    $redirect_url = add_query_arg('gridspoke_synced', count($surecart_ids), $redirect_url);
                } else {
                    $redirect_url = add_query_arg('gridspoke_error', 'sync_failed', $redirect_url);
                }
            }
        }
        
        return $redirect_url;
    }
    
    /**
     * Convert WordPress post IDs to SureCart product IDs
     */
    private function convert_wp_ids_to_surecart_ids($wp_post_ids) {
        $surecart_ids = array();
        
        foreach ($wp_post_ids as $wp_post_id) {
            // Get SureCart product ID from post meta
            $surecart_id = get_post_meta($wp_post_id, '_surecart_product_id', true);
            
            if ($surecart_id) {
                $surecart_ids[] = $surecart_id;
            }
        }
        
        return $surecart_ids;
    }
    
    /**
     * Display bulk action notices
     */
    public function bulk_action_notices() {
        if (!empty($_REQUEST['gridspoke_optimized'])) {
            $count = intval($_REQUEST['gridspoke_optimized']);
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>' . sprintf(
                _n(
                    'Successfully requested optimization for %d SureCart product.',
                    'Successfully requested optimization for %d SureCart products.',
                    $count,
                    'gridspoke-seo'
                ),
                $count
            ) . '</p>';
            echo '</div>';
        }
        
        if (!empty($_REQUEST['gridspoke_synced'])) {
            $count = intval($_REQUEST['gridspoke_synced']);
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>' . sprintf(
                _n(
                    'Successfully synced %d SureCart product to GridSpoke.',
                    'Successfully synced %d SureCart products to GridSpoke.',
                    $count,
                    'gridspoke-seo'
                ),
                $count
            ) . '</p>';
            echo '</div>';
        }
        
        if (!empty($_REQUEST['gridspoke_error'])) {
            $error = sanitize_text_field($_REQUEST['gridspoke_error']);
            $messages = array(
                'optimization_failed' => __('Failed to request SureCart product optimization. Please check your API settings.', 'gridspoke-seo'),
                'sync_failed' => __('Failed to sync SureCart products to GridSpoke. Please check your API settings.', 'gridspoke-seo')
            );
            
            echo '<div class="notice notice-error is-dismissible">';
            echo '<p>' . ($messages[$error] ?? __('An unknown error occurred.', 'gridspoke-seo')) . '</p>';
            echo '</div>';
        }
    }
    
    /**
     * Add optimization meta box to SureCart product edit page
     */
    public function add_optimization_meta_box() {
        if (!$this->is_surecart_available()) {
            return;
        }
        
        add_meta_box(
            'gridspoke-surecart-optimization',
            __('GridSpoke SEO Optimization', 'gridspoke-seo'),
            array($this, 'render_optimization_meta_box'),
            'sc_product',
            'side',
            'default'
        );
    }
    
    /**
     * Render optimization meta box
     */
    public function render_optimization_meta_box($post) {
        $surecart_id = get_post_meta($post->ID, '_surecart_product_id', true);
        $optimization_status = $this->get_optimization_status($surecart_id);
        
        echo '<div id="gridspoke-surecart-optimization-status">';
        
        if ($optimization_status) {
            $status_class = $optimization_status['status'] === 'completed' ? 'success' : 'warning';
            echo '<p class="gridspoke-status ' . esc_attr($status_class) . '">';
            echo '<strong>' . __('Status:', 'gridspoke-seo') . '</strong> ' . esc_html(ucfirst($optimization_status['status']));
            echo '</p>';
            
            if (!empty($optimization_status['fields_optimized'])) {
                echo '<p><strong>' . __('Optimized Fields:', 'gridspoke-seo') . '</strong><br>';
                echo esc_html(implode(', ', $optimization_status['fields_optimized']));
                echo '</p>';
            }
            
            if (!empty($optimization_status['updated_at'])) {
                echo '<p><strong>' . __('Last Updated:', 'gridspoke-seo') . '</strong><br>';
                echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($optimization_status['updated_at'])));
                echo '</p>';
            }
        } else {
            echo '<p>' . __('No optimization data available.', 'gridspoke-seo') . '</p>';
        }
        
        echo '</div>';
        
        // Action buttons
        if ($surecart_id) {
            echo '<div class="gridspoke-actions">';
            echo '<button type="button" class="button button-secondary" id="gridspoke-sync-surecart-product" data-product-id="' . esc_attr($surecart_id) . '">';
            echo __('Sync to GridSpoke', 'gridspoke-seo');
            echo '</button>';
            
            echo '<button type="button" class="button button-primary" id="gridspoke-optimize-surecart-product" data-product-id="' . esc_attr($surecart_id) . '">';
            echo __('Request Optimization', 'gridspoke-seo');
            echo '</button>';
            echo '</div>';
        } else {
            echo '<p><em>' . __('SureCart product ID not found.', 'gridspoke-seo') . '</em></p>';
        }
        
        wp_nonce_field('gridspoke_surecart_actions', 'gridspoke_surecart_nonce');
    }
    
    /**
     * Add optimization column to product list
     */
    public function add_optimization_column($columns) {
        $new_columns = array();
        
        foreach ($columns as $key => $column) {
            $new_columns[$key] = $column;
            
            // Add our column after the title column
            if ($key === 'title') {
                $new_columns['gridspoke_status'] = __('GridSpoke Status', 'gridspoke-seo');
            }
        }
        
        return $new_columns;
    }
    
    /**
     * Display optimization column content
     */
    public function display_optimization_column($column, $post_id) {
        if ($column === 'gridspoke_status') {
            $surecart_id = get_post_meta($post_id, '_surecart_product_id', true);
            
            if ($surecart_id) {
                $status = $this->get_optimization_status($surecart_id);
                
                if ($status) {
                    $status_text = ucfirst($status['status']);
                    $status_class = $status['status'] === 'completed' ? 'completed' : 'pending';
                    
                    echo '<span class="gridspoke-status-badge ' . esc_attr($status_class) . '">';
                    echo esc_html($status_text);
                    echo '</span>';
                    
                    if ($status['status'] === 'completed' && !empty($status['fields_optimized'])) {
                        echo '<br><small>' . esc_html(implode(', ', $status['fields_optimized'])) . '</small>';
                    }
                } else {
                    echo '<span class="gridspoke-status-badge not-optimized">' . __('Not Optimized', 'gridspoke-seo') . '</span>';
                }
            } else {
                echo '<span class="gridspoke-status-badge no-id">' . __('No SureCart ID', 'gridspoke-seo') . '</span>';
            }
        }
    }
    
    /**
     * Get optimization status for a SureCart product
     */
    private function get_optimization_status($surecart_id) {
        if (!$surecart_id) {
            return false;
        }
        
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        
        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE product_id = %s AND product_type = 'surecart' ORDER BY created_at DESC LIMIT 1",
                $surecart_id
            ),
            ARRAY_A
        );
        
        if ($result) {
            $result['fields_optimized'] = json_decode($result['fields_optimized'], true);
        }
        
        return $result;
    }
    
    /**
     * AJAX handler for single product optimization
     */
    public function ajax_optimize_product() {
        check_ajax_referer('gridspoke_surecart_actions', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'gridspoke-seo'));
        }
        
        $product_id = sanitize_text_field($_POST['product_id']);
        $result = $this->api_client->request_optimization(array($product_id));
        
        if ($result) {
            wp_send_json_success(array(
                'message' => __('Optimization requested successfully', 'gridspoke-seo'),
                'optimization_id' => $result['optimization_id'] ?? null
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to request optimization', 'gridspoke-seo')
            ));
        }
    }
    
    /**
     * AJAX handler for single product sync
     */
    public function ajax_sync_product() {
        check_ajax_referer('gridspoke_surecart_actions', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_die(__('Insufficient permissions', 'gridspoke-seo'));
        }
        
        $product_id = sanitize_text_field($_POST['product_id']);
        $result = $this->sync_products(array($product_id));
        
        if ($result) {
            wp_send_json_success(array(
                'message' => __('Product synced successfully', 'gridspoke-seo')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to sync product', 'gridspoke-seo')
            ));
        }
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if (in_array($hook, array('post.php', 'post-new.php', 'edit.php'))) {
            $screen = get_current_screen();
            
            if ($screen && $screen->post_type === 'sc_product') {
                wp_enqueue_script(
                    'gridspoke-surecart-admin',
                    GRIDSPOKE_SEO_PLUGIN_URL . 'admin/assets/js/surecart-admin.js',
                    array('jquery'),
                    GRIDSPOKE_SEO_VERSION,
                    true
                );
                
                wp_localize_script('gridspoke-surecart-admin', 'gridspoke_surecart_ajax', array(
                    'ajax_url' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('gridspoke_surecart_actions'),
                    'messages' => array(
                        'optimizing' => __('Requesting optimization...', 'gridspoke-seo'),
                        'syncing' => __('Syncing product...', 'gridspoke-seo'),
                        'error' => __('An error occurred. Please try again.', 'gridspoke-seo')
                    )
                ));
                
                wp_enqueue_style(
                    'gridspoke-surecart-admin',
                    GRIDSPOKE_SEO_PLUGIN_URL . 'admin/assets/css/admin.css',
                    array(),
                    GRIDSPOKE_SEO_VERSION
                );
            }
        }
    }
    
    /**
     * Update SureCart product with optimized content
     */
    public function update_product_content($product_id, $optimized_data) {
        if (!$this->is_surecart_available()) {
            return false;
        }
        
        $product = \SureCart\Models\Product::find($product_id);
        
        if (!$product || is_wp_error($product)) {
            return false;
        }
        
        $update_data = array();
        
        // Update product name
        if (!empty($optimized_data['name'])) {
            $update_data['name'] = $optimized_data['name'];
        }
        
        // Update description
        if (!empty($optimized_data['description'])) {
            $update_data['description'] = $optimized_data['description'];
        }
        
        // Update image alt text
        if (!empty($optimized_data['image_alt'])) {
            $update_data['image_alt'] = $optimized_data['image_alt'];
        }
        
        if (!empty($update_data)) {
            $updated_product = $product->update($update_data);
            
            if (!is_wp_error($updated_product)) {
                // Also update linked WordPress post if exists
                if (!empty($product->wp_post_id)) {
                    $post_update_data = array(
                        'ID' => $product->wp_post_id
                    );
                    
                    if (!empty($optimized_data['name'])) {
                        $post_update_data['post_title'] = $optimized_data['name'];
                    }
                    
                    if (!empty($optimized_data['description'])) {
                        $post_update_data['post_content'] = $optimized_data['description'];
                    }
                    
                    if (!empty($optimized_data['short_description'])) {
                        $post_update_data['post_excerpt'] = $optimized_data['short_description'];
                    }
                    
                    wp_update_post($post_update_data);
                    
                    // Update meta description if provided
                    if (!empty($optimized_data['meta_description'])) {
                        update_post_meta($product->wp_post_id, '_yoast_wpseo_metadesc', $optimized_data['meta_description']);
                    }
                }
                
                GridSpoke_Logger::log(sprintf('Updated SureCart product %s with optimized content', $product_id), 'info');
                
                return true;
            }
        }
        
        return false;
    }
}
