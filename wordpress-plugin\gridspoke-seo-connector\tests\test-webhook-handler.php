<?php
/**
 * Tests for GridSpoke_Webhook_Handler class
 */

class Test_GridSpoke_Webhook_Handler extends GridSpoke_Test_Case {
    
    /**
     * Webhook handler instance
     */
    private $webhook_handler;
    
    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();
        $this->webhook_handler = GridSpoke_Webhook_Handler::get_instance();
    }
    
    /**
     * Test singleton instance
     */
    public function test_singleton_instance() {
        $instance1 = GridSpoke_Webhook_Handler::get_instance();
        $instance2 = GridSpoke_Webhook_Handler::get_instance();
        
        $this->assertSame($instance1, $instance2);
        $this->assertInstanceOf('GridSpoke_Webhook_Handler', $instance1);
    }
    
    /**
     * Test webhook secret generation
     */
    public function test_webhook_secret_generation() {
        // Initially no secret should exist
        $settings = $this->get_plugin_settings();
        $this->assertEmpty($settings['webhook_secret']);
        
        // Generate secret
        $secret = $this->webhook_handler->generate_webhook_secret();
        
        $this->assertNotEmpty($secret);
        $this->assertEquals(64, strlen($secret)); // Should be 64 characters
        
        // Verify secret was saved
        $updated_settings = $this->get_plugin_settings();
        $this->assertEquals($secret, $updated_settings['webhook_secret']);
    }
    
    /**
     * Test webhook signature verification
     */
    public function test_webhook_signature_verification() {
        // Set up webhook secret
        $secret = 'test_webhook_secret_123';
        $this->update_plugin_settings(array('webhook_secret' => $secret));
        
        $payload = json_encode(array('test' => 'data'));
        $signature = hash_hmac('sha256', $payload, $secret);
        
        // Test valid signature
        $this->assertTrue($this->webhook_handler->verify_webhook_signature($payload, $signature));
        
        // Test invalid signature
        $this->assertFalse($this->webhook_handler->verify_webhook_signature($payload, 'invalid_signature'));
        
        // Test empty signature
        $this->assertFalse($this->webhook_handler->verify_webhook_signature($payload, ''));
    }
    
    /**
     * Test optimization completion webhook processing
     */
    public function test_optimization_completion_webhook() {
        // Create test product
        $product_id = $this->create_test_woo_product();
        
        // Store GridSpoke UUID mapping
        update_post_meta($product_id, '_gridspoke_product_uuid', 'test_uuid_123');
        
        // Prepare webhook data
        $webhook_data = $this->test_data->get_sample_webhook_data();
        $webhook_data['external_id'] = (string) $product_id;
        $webhook_data['product_id'] = 'test_uuid_123';
        
        // Process webhook
        $result = $this->webhook_handler->process_webhook($webhook_data);
        
        $this->assertTrue($result);
        
        // Verify product was updated
        $product = wc_get_product($product_id);
        $this->assertEquals($webhook_data['optimized_content']['title'], $product->get_name());
        $this->assertEquals($webhook_data['optimized_content']['description'], $product->get_description());
        
        // Verify optimization record was created
        global $wpdb;
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        $record = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE product_id = %s", $product_id),
            ARRAY_A
        );
        
        $this->assertNotNull($record);
        $this->assertEquals('completed', $record['status']);
    }
    
    /**
     * Test optimization failure webhook processing
     */
    public function test_optimization_failure_webhook() {
        $product_id = $this->create_test_woo_product();
        update_post_meta($product_id, '_gridspoke_product_uuid', 'test_uuid_123');
        
        $webhook_data = $this->test_data->get_sample_error_webhook_data();
        $webhook_data['external_id'] = (string) $product_id;
        $webhook_data['product_id'] = 'test_uuid_123';
        
        $result = $this->webhook_handler->process_webhook($webhook_data);
        
        $this->assertTrue($result);
        
        // Verify error was logged
        $this->assertLogEntryExists('error', '/optimization failed/i');
        
        // Verify optimization record shows failure
        global $wpdb;
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        $record = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE product_id = %s", $product_id),
            ARRAY_A
        );
        
        $this->assertNotNull($record);
        $this->assertEquals('failed', $record['status']);
    }
    
    /**
     * Test webhook with invalid signature
     */
    public function test_webhook_invalid_signature() {
        $this->update_plugin_settings(array('webhook_secret' => 'test_secret'));
        
        $webhook_data = $this->test_data->get_sample_webhook_data();
        
        // Simulate REST API request with invalid signature
        $request = new WP_REST_Request('POST', '/gridspoke-seo/v1/webhook');
        $request->set_body(json_encode($webhook_data));
        $request->set_header('X-GridSpoke-Signature', 'invalid_signature');
        
        $response = $this->webhook_handler->handle_webhook_request($request);
        
        $this->assertInstanceOf('WP_Error', $response);
        $this->assertEquals('invalid_signature', $response->get_error_code());
    }
    
    /**
     * Test webhook with valid signature
     */
    public function test_webhook_valid_signature() {
        $secret = 'test_webhook_secret_123';
        $this->update_plugin_settings(array('webhook_secret' => $secret));
        
        $webhook_data = $this->test_data->get_sample_webhook_data();
        $payload = json_encode($webhook_data);
        $signature = hash_hmac('sha256', $payload, $secret);
        
        // Create test product
        $product_id = $this->create_test_woo_product();
        $webhook_data['external_id'] = (string) $product_id;
        update_post_meta($product_id, '_gridspoke_product_uuid', $webhook_data['product_id']);
        
        // Simulate REST API request
        $request = new WP_REST_Request('POST', '/gridspoke-seo/v1/webhook');
        $request->set_body(json_encode($webhook_data));
        $request->set_header('X-GridSpoke-Signature', $signature);
        
        $response = $this->webhook_handler->handle_webhook_request($request);
        
        $this->assertInstanceOf('WP_REST_Response', $response);
        $this->assertEquals(200, $response->get_status());
    }
    
    /**
     * Test webhook stats retrieval
     */
    public function test_webhook_stats() {
        // Create some test optimization records
        global $wpdb;
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        
        $test_records = array(
            array('status' => 'completed', 'created_at' => current_time('mysql')),
            array('status' => 'completed', 'created_at' => current_time('mysql')),
            array('status' => 'pending', 'created_at' => current_time('mysql')),
            array('status' => 'failed', 'created_at' => current_time('mysql'))
        );
        
        foreach ($test_records as $record) {
            $record['product_id'] = '123';
            $record['product_type'] = 'woocommerce';
            $record['fields_optimized'] = json_encode(array('title'));
            $record['optimization_data'] = json_encode(array());
            $record['updated_at'] = current_time('mysql');
            
            $wpdb->insert($table_name, $record);
        }
        
        $stats = $this->webhook_handler->get_webhook_stats();
        
        $this->assertEquals(2, $stats['completed_optimizations']);
        $this->assertEquals(1, $stats['pending_optimizations']);
        $this->assertEquals(1, $stats['failed_optimizations']);
        $this->assertEquals(4, $stats['total_optimizations']);
    }
    
    /**
     * Test webhook endpoint registration
     */
    public function test_webhook_endpoint_registration() {
        // Check that REST route is registered
        $routes = rest_get_server()->get_routes();
        
        $this->assertArrayHasKey('/gridspoke-seo/v1/webhook', $routes);
        
        $route = $routes['/gridspoke-seo/v1/webhook'];
        $this->assertNotEmpty($route);
        
        // Check that POST method is supported
        $methods = array();
        foreach ($route as $handler) {
            $methods = array_merge($methods, $handler['methods']);
        }
        
        $this->assertContains('POST', $methods);
    }
    
    /**
     * Test product lookup by external ID
     */
    public function test_product_lookup_by_external_id() {
        $product_id = $this->create_test_woo_product();
        $gridspoke_uuid = 'test_uuid_' . uniqid();
        
        // Store UUID mapping
        update_post_meta($product_id, '_gridspoke_product_uuid', $gridspoke_uuid);
        
        // Use reflection to test private method
        $reflection = new ReflectionClass($this->webhook_handler);
        $method = $reflection->getMethod('find_product_by_external_id');
        $method->setAccessible(true);
        
        $found_product = $method->invoke($this->webhook_handler, (string) $product_id, 'woocommerce');
        
        $this->assertNotNull($found_product);
        $this->assertEquals($product_id, $found_product->get_id());
    }
}
