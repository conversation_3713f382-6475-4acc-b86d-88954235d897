import asyncio
from core.database import get_session
from crud.user import user_crud
from core.security import get_password_hash

async def reset_password():
    async for db in get_session():
        user = await user_crud.get_by_email(db, email="<EMAIL>")
        if user:
            print(f"Current hashed password: {user.hashed_password}")
            # Update with properly hashed password
            new_hash = get_password_hash("demo12345")
            print(f"New hash: {new_hash}")
            user.hashed_password = new_hash
            db.add(user)
            await db.commit()
            await db.refresh(user)
            print("Password updated successfully")
        else:
            print("User not found")
        break

if __name__ == "__main__":
    asyncio.run(reset_password())
