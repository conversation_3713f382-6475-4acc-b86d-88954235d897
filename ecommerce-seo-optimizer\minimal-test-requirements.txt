# Minimal Test Requirements - Using SQLite instead of PostgreSQL for easier testing
# This allows us to test without compilation issues

# Core FastAPI
fastapi==0.115.0
uvicorn==0.30.6

# Database (SQLite for testing)
sqlalchemy==2.0.34
alembic==1.13.2

# Redis (for caching/message queue)
redis==5.0.8

# Data Validation
pydantic==2.8.2
pydantic-settings==2.4.0

# HTTP Client
httpx==0.27.2

# Security
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.9

# Utilities
python-dotenv==1.0.1
email-validator==2.2.0

# Testing Core
pytest==8.3.2
pytest-asyncio==0.24.0
pytest-mock==3.11.1
pytest-cov==4.0.0
requests==2.31.0

# Mock AI responses (since we can't install mirascope easily)
responses==0.23.3
aioresponses==0.7.4
