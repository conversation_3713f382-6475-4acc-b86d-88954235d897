"""
API version 1 router aggregation.
"""
from fastapi import APIRouter
from api.v1.endpoints import auth, users, stores, products, jobs, dashboard, analytics
from api.v1.endpoints import tasks, monitoring, webhooks

api_router = APIRouter()

# Add v1 health endpoint for WordPress plugin compatibility
@api_router.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint for v1 API compatibility."""
    return {"status": "healthy", "service": "gridspoke-api", "version": "v1"}

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users.router, prefix="/users", tags=["Users"])
api_router.include_router(stores.router, prefix="/stores", tags=["Stores"])
api_router.include_router(products.router, prefix="/products", tags=["Products"])
api_router.include_router(jobs.router, prefix="/jobs", tags=["Optimization Jobs"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["Dashboard"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["Analytics"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["Tasks"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["Monitoring"])
api_router.include_router(webhooks.router, prefix="/webhooks", tags=["Webhooks"])
