"""Celery application initialization for GridSpoke.
Provides a minimal Celery app so worker/beat containers start successfully.
Extend with task discovery and configuration as deeper task modules are implemented.
"""
from celery import Celery
import os

redis_url = os.getenv("REDIS_URL", "redis://redis:6379/0")
celery_app = Celery(
    "gridspoke",
    broker=redis_url,
    backend=redis_url,
    include=[
        'workers.tasks.product_tasks',
        'workers.tasks.content_tasks',
        'workers.tasks.scheduled_tasks',
        'workers.tasks.validation_tasks'
    ]
)

celery_app.conf.update(
    task_routes={
        'workers.tasks.product_tasks.*': {'queue': 'products'},
        'workers.tasks.content_tasks.*': {'queue': 'content'},
        'workers.tasks.scheduled_tasks.*': {'queue': 'scheduled'},
        'workers.tasks.validation_tasks.*': {'queue': 'validation'},
    },
    task_default_queue="default",
    result_expires=3600,
)

@celery_app.task
def ping():  # simple sanity task
    return "pong"
