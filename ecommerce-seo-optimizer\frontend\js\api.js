/**
 * GridSpoke SEO Optimizer - API Client Module
 * Handles all API requests with authentication, error handling, and request/response interceptors
 */

const API = {
    // Configuration
    config: {
        baseUrl: '/api/v1',
        timeout: 30000, // 30 seconds
        retryAttempts: 3,
        retryDelay: 1000 // 1 second
    },

    /**
     * Initialize API client
     */
    init() {
        this.setupInterceptors();
    },

    /**
     * Setup request/response interceptors
     */
    setupInterceptors() {
        // Will be used for global error handling and auth headers
    },

    /**\n     * Make HTTP request with automatic auth header and error handling\n     * @param {string} endpoint - API endpoint\n     * @param {Object} options - Request options\n     * @returns {Promise} Request promise\n     */\n    async request(endpoint, options = {}) {\n        const url = `${this.config.baseUrl}${endpoint}`;\n        \n        // Default options\n        const defaultOptions = {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json',\n            },\n            timeout: this.config.timeout,\n            ...options\n        };\n\n        // Add auth header if token exists\n        if (window.authToken) {\n            defaultOptions.headers['Authorization'] = `Bearer ${window.authToken}`;\n            console.log('Adding auth header to request to:', url);\n        } else {\n            console.warn('No auth token available for request to:', url);\n        }\n\n        // Handle request body\n        if (defaultOptions.body && typeof defaultOptions.body === 'object') {\n            defaultOptions.body = JSON.stringify(defaultOptions.body);\n        }\n\n        try {\n            const response = await this.fetchWithTimeout(url, defaultOptions);\n            return await this.handleResponse(response);\n        } catch (error) {\n            throw await this.handleError(error);\n        }\n    },

    /**
     * Fetch with timeout support
     * @param {string} url - Request URL
     * @param {Object} options - Request options
     * @returns {Promise} Fetch promise
     */
    async fetchWithTimeout(url, options) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), options.timeout || this.config.timeout);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    },

    /**
     * Handle API response
     * @param {Response} response - Fetch response
     * @returns {Promise} Processed response
     */
    async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        
        let data;
        if (contentType && contentType.includes('application/json')) {
            data = await response.json();
        } else {
            data = await response.text();
        }

        if (!response.ok) {
            throw {
                status: response.status,
                statusText: response.statusText,
                message: data.detail || data.message || 'Request failed',
                data
            };
        }

        return data;
    },

    /**
     * Handle API errors
     * @param {Error} error - Request error
     * @returns {Error} Processed error
     */
    async handleError(error) {
        console.error('API Error:', error);

        // Handle network errors
        if (error.name === 'AbortError') {
            return new Error('Request timeout');
        }

        if (!navigator.onLine) {
            return new Error('No internet connection');
        }

        // Handle HTTP errors
        if (error.status) {
            switch (error.status) {
                case 401:
                    // Unauthorized - clear auth and redirect
                    if (Auth && Auth.clearAuth) {
                        Auth.clearAuth();
                        window.location.href = 'login.html';
                    }
                    return new Error('Session expired. Please log in again.');
                
                case 403:
                    return new Error('Access denied');
                
                case 404:
                    return new Error('Resource not found');
                
                case 429:
                    return new Error('Too many requests. Please try again later.');
                
                case 500:
                    return new Error('Server error. Please try again later.');
                
                default:
                    return new Error(error.message || 'Unknown error occurred');
            }
        }

        return error;
    },

    // HTTP Methods

    /**
     * GET request
     * @param {string} endpoint - API endpoint
     * @param {Object} params - Query parameters
     * @returns {Promise} Request promise
     */
    async get(endpoint, params = {}) {
        const queryString = Utils.objectToQueryString(params);
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        return this.request(url, {
            method: 'GET'
        });
    },

    /**
     * POST request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @returns {Promise} Request promise
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: data
        });
    },

    /**
     * PUT request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @returns {Promise} Request promise
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: data
        });
    },

    /**
     * PATCH request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @returns {Promise} Request promise
     */
    async patch(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PATCH',
            body: data
        });
    },

    /**
     * DELETE request
     * @param {string} endpoint - API endpoint
     * @returns {Promise} Request promise
     */
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    },

    /**
     * Upload file
     * @param {string} endpoint - API endpoint
     * @param {FormData} formData - Form data with file
     * @param {Function} onProgress - Progress callback
     * @returns {Promise} Upload promise
     */
    async upload(endpoint, formData, onProgress = null) {
        const url = `${this.config.baseUrl}${endpoint}`;

        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            // Add auth header
            if (window.authToken) {
                xhr.setRequestHeader('Authorization', `Bearer ${window.authToken}`);
            }

            // Track upload progress
            if (onProgress) {
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const percentComplete = (event.loaded / event.total) * 100;
                        onProgress(percentComplete);
                    }
                });
            }

            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject(new Error(`Upload failed: ${xhr.statusText}`));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new Error('Upload failed'));
            });

            xhr.open('POST', url);
            xhr.send(formData);
        });
    },

    // Store Management

    /**
     * Get stores
     * @param {Object} filters - Filter parameters
     * @returns {Promise} Stores list
     */
    async getStores(filters = {}) {
        return this.get('/stores', filters);
    },

    /**
     * Get store by ID
     * @param {string} storeId - Store ID
     * @returns {Promise} Store data
     */
    async getStore(storeId) {
        return this.get(`/stores/${storeId}`);
    },

    /**
     * Create store
     * @param {Object} storeData - Store data
     * @returns {Promise} Created store
     */
    async createStore(storeData) {
        return this.post('/stores', storeData);
    },

    /**
     * Update store
     * @param {string} storeId - Store ID
     * @param {Object} storeData - Updated store data
     * @returns {Promise} Updated store
     */
    async updateStore(storeId, storeData) {
        return this.put(`/stores/${storeId}`, storeData);
    },

    /**
     * Delete store
     * @param {string} storeId - Store ID
     * @returns {Promise} Deletion result
     */
    async deleteStore(storeId) {
        return this.delete(`/stores/${storeId}`);
    },

    // Product Management

    /**
     * Get products
     * @param {string} storeId - Store ID
     * @param {Object} filters - Filter parameters
     * @returns {Promise} Products list
     */
    async getProducts(storeId, filters = {}) {
        // Backend expects top-level products endpoint with store_id query
        const params = { store_id: storeId, ...filters };
        return this.get('/products', params);
    },

    /**
     * Get product by ID
     * @param {string} storeId - Store ID
     * @param {string} productId - Product ID
     * @returns {Promise} Product data
     */
    async getProduct(storeId, productId) {
        // Use top-level product by ID with store_id query when provided
        const params = storeId ? { store_id: storeId } : {};
        return this.get(`/products/${productId}`, params);
    },

    /**
     * Create product
     * @param {string} storeId - Store ID
     * @param {Object} productData - Product data
     * @returns {Promise} Created product
     */
    async createProduct(storeId, productData) {
        return this.post(`/stores/${storeId}/products`, productData);
    },

    /**
     * Update product
     * @param {string} storeId - Store ID
     * @param {string} productId - Product ID
     * @param {Object} productData - Updated product data
     * @returns {Promise} Updated product
     */
    async updateProduct(storeId, productId, productData) {
        return this.put(`/stores/${storeId}/products/${productId}`, productData);
    },

    /**
     * Delete product
     * @param {string} storeId - Store ID
     * @param {string} productId - Product ID
     * @returns {Promise} Deletion result
     */
    async deleteProduct(storeId, productId) {
        return this.delete(`/stores/${storeId}/products/${productId}`);
    },

    /**
     * Sync products from external store
     * @param {string} storeId - Store ID
     * @returns {Promise} Sync result
     */
    async syncProducts(storeId) {
        return this.post(`/stores/${storeId}/sync`);
    },

    // Optimization Jobs

    /**
     * Get optimization jobs
     * @param {Object} filters - Filter parameters
     * @returns {Promise} Jobs list
     */
    async getJobs(filters = {}) {
        // Ensure store_id is provided
        if (!filters.store_id) {
            console.warn('No store_id provided for getJobs, this may cause errors');
        }
        return this.get('/jobs', filters);
    },

    /**
     * Get job by ID
     * @param {string} jobId - Job ID
     * @returns {Promise} Job data
     */
    async getJob(jobId) {
        return this.get(`/jobs/${jobId}`);
    },

    /**
     * Create optimization job
     * @param {Object} jobData - Job data
     * @returns {Promise} Created job
     */
    async createJob(jobData) {
        return this.post('/jobs', jobData);
    },

    /**
     * Cancel job
     * @param {string} jobId - Job ID
     * @returns {Promise} Cancellation result
     */
    async cancelJob(jobId) {
        return this.post(`/jobs/${jobId}/cancel`);
    },

    /**
     * Retry failed job
     * @param {string} jobId - Job ID
     * @returns {Promise} Retry result
     */
    async retryJob(jobId) {
        return this.post(`/jobs/${jobId}/retry`);
    },

    /**
     * Get job logs
     * @param {string} jobId - Job ID
     * @returns {Promise} Job logs
     */
    async getJobLogs(jobId) {
        return this.get(`/jobs/${jobId}/logs`);
    },

    // Optimization Operations

    /**
     * Optimize single product
     * @param {string} storeId - Store ID
     * @param {string} productId - Product ID
     * @param {Object} options - Optimization options
     * @returns {Promise} Optimization result
     */
    async optimizeProduct(storeId, productId, options = {}) {
        return this.post(`/stores/${storeId}/products/${productId}/optimize`, options);
    },

    /**
     * Bulk optimize products
     * @param {string} storeId - Store ID
     * @param {Array} productIds - Array of product IDs
     * @param {Object} options - Optimization options
     * @returns {Promise} Bulk optimization result
     */
    async bulkOptimizeProducts(storeId, productIds, options = {}) {
        return this.post(`/stores/${storeId}/products/bulk-optimize`, {
            product_ids: productIds,
            ...options
        });
    },

    /**
     * Get optimization history
     * @param {string} storeId - Store ID
     * @param {string} productId - Product ID
     * @returns {Promise} Optimization history
     */
    async getOptimizationHistory(storeId, productId) {
        return this.get(`/stores/${storeId}/products/${productId}/history`);
    },

    /**
     * Helper: Get current store (first active or first available)
     * @returns {Promise<Object|null>} store object or null
     */
    async getCurrentStore() {
        try {
            const stores = await this.getStores();
            if (Array.isArray(stores) && stores.length > 0) {
                return stores.find(s => s.is_active) || stores[0];
            }
            return null;
        } catch (e) {
            console.error('API.getCurrentStore error:', e);
            return null;
        }
    },

    // Analytics and Statistics

    /**
     * Get dashboard statistics
     * @param {Object} filters - Filter parameters
     * @returns {Promise} Dashboard stats
     */
    async getStats(filters = {}) {
        return this.get('/stats', filters);
    },

    /**
     * Get optimization analytics
     * @param {Object} filters - Filter parameters
     * @returns {Promise} Analytics data
     */
    async getAnalytics(filters = {}) {
        return this.get('/analytics', filters);
    },

    /**
     * Get recent activity
     * @param {Object} filters - Filter parameters
     * @returns {Promise} Recent activity
     */
    async getRecentActivity(filters = {}) {
        return this.get('/activity', filters);
    },

    // Settings and Configuration

    /**
     * Get user settings
     * @returns {Promise} User settings
     */
    async getSettings() {
        return this.get('/settings');
    },

    /**
     * Update user settings
     * @param {Object} settings - Settings data
     * @returns {Promise} Updated settings
     */
    async updateSettings(settings) {
        return this.put('/settings', settings);
    },

    /**
     * Test API connection
     * @returns {Promise} Connection test result
     */
    async testConnection() {
        return this.get('/health');
    },

    // Webhooks

    /**
     * Get webhooks
     * @param {string} storeId - Store ID
     * @returns {Promise} Webhooks list
     */
    async getWebhooks(storeId) {
        return this.get(`/stores/${storeId}/webhooks`);
    },

    /**
     * Create webhook
     * @param {string} storeId - Store ID
     * @param {Object} webhookData - Webhook data
     * @returns {Promise} Created webhook
     */
    async createWebhook(storeId, webhookData) {
        return this.post(`/stores/${storeId}/webhooks`, webhookData);
    },

    /**
     * Update webhook
     * @param {string} storeId - Store ID
     * @param {string} webhookId - Webhook ID
     * @param {Object} webhookData - Updated webhook data
     * @returns {Promise} Updated webhook
     */
    async updateWebhook(storeId, webhookId, webhookData) {
        return this.put(`/stores/${storeId}/webhooks/${webhookId}`, webhookData);
    },

    /**
     * Delete webhook
     * @param {string} storeId - Store ID
     * @param {string} webhookId - Webhook ID
     * @returns {Promise} Deletion result
     */
    async deleteWebhook(storeId, webhookId) {
        return this.delete(`/stores/${storeId}/webhooks/${webhookId}`);
    },

    /**
     * Get current user's store (first available)
     * @returns {Promise} Store data
     */
    async getCurrentStore() {
        try {
            const stores = await this.getStores();
            if (stores && stores.length > 0) {
                // Prefer WordPress stores if available
                const wordpressStore = stores.find(store => 
                    store.name.toLowerCase().includes('wordpress') || 
                    store.platform === 'wordpress'
                );
                return wordpressStore || stores[0];
            }
            return null;
        } catch (error) {
            console.error('API.getCurrentStore error:', error);
            throw error;
        }
    }
};

// Initialize API client
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        API.init();
    });
}

// Export for module use (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = API;
}
