# Docker Compose configuration for Celery workers and task processing

version: '3.8'

services:
  # Redis service for Celery broker and result backend
  redis:
    image: redis:7-alpine
    container_name: gridspoke_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Celery worker for task processing
  celery-worker:
    build:
      context: ./workers
      dockerfile: Dockerfile
    container_name: gridspoke_celery_worker
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - POSTGRES_URL=postgresql+asyncpg://gridspoke:password@db:5432/gridspoke_db
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./workers:/app
      - ./api:/api  # Mount API directory for imports
    command: celery -A celery_app worker --loglevel=info --concurrency=4 --queues=urgent,high_priority,default,low_priority
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Beat for scheduled tasks
  celery-beat:
    build:
      context: ./workers
      dockerfile: Dockerfile
    container_name: gridspoke_celery_beat
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - POSTGRES_URL=postgresql+asyncpg://gridspoke:password@db:5432/gridspoke_db
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./workers:/app
      - ./api:/api  # Mount API directory for imports
      - celery_beat_data:/app/celerybeat-schedule
    command: celery -A celery_app beat --loglevel=info --schedule=/app/celerybeat-schedule --pidfile=/tmp/celerybeat.pid
    healthcheck:
      test: ["CMD", "test", "-f", "/tmp/celerybeat.pid"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Flower for Celery monitoring (optional)
  celery-flower:
    build:
      context: ./workers
      dockerfile: Dockerfile
    container_name: gridspoke_celery_flower
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    ports:
      - "5555:5555"
    volumes:
      - ./workers:/app
    command: celery -A celery_app flower --port=5555 --broker=redis://redis:6379/0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555/api/workers"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
    driver: local
  celery_beat_data:
    driver: local

networks:
  default:
    name: gridspoke_network
    external: true
