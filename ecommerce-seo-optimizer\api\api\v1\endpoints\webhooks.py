"""
Webhooks endpoints for GridSpoke.
Handles incoming webhooks from WordPress plugin when optimization tasks are completed.
"""
import json
import hmac
import hashlib
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, Header, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from core.database import get_db_session
from core.security import verify_webhook_signature
from core.config import get_settings
import structlog

logger = structlog.get_logger(__name__)
router = APIRouter(prefix="/webhooks", tags=["Webhooks"])
settings = get_settings()


@router.post("/wordpress")
async def handle_wordpress_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    x_gridspoke_signature: str = Header(None)
) -> Any:
    """
    Handle incoming webhook from WordPress plugin.
    """
    # Get raw body
    body = await request.body()
    body_str = body.decode('utf-8')
    
    # Verify webhook signature
    if not verify_webhook_signature(body_str, x_gridspoke_signature):
        logger.warning("Webhook signature verification failed")
        raise HTTPException(status_code=401, detail="Invalid webhook signature")
    
    try:
        # Parse JSON payload
        payload = await request.json()
    except json.JSONDecodeError:
        logger.warning("Webhook received with invalid JSON payload")
        raise HTTPException(status_code=400, detail="Invalid JSON payload")
    
    if not payload:
        logger.warning("Webhook received with empty payload")
        raise HTTPException(status_code=400, detail="Empty payload")
    
    # Log webhook reception
    event_type = payload.get('event', 'unknown')
    logger.info("Webhook received", event=event_type)
    
    # Route to appropriate handler based on event type
    if event_type == 'optimization.completed':
        return await handle_optimization_completed(payload, db)
    elif event_type == 'optimization.failed':
        return await handle_optimization_failed(payload, db)
    elif event_type == 'sync.completed':
        return await handle_sync_completed(payload, db)
    elif event_type == 'sync.failed':
        return await handle_sync_failed(payload, db)
    else:
        logger.warning("Unknown webhook event", event=event_type)
        raise HTTPException(status_code=400, detail="Unknown event type")


async def handle_optimization_completed(payload: dict, db: AsyncSession) -> Any:
    """
    Handle optimization completed webhook.
    """
    optimization_id = payload.get('optimization_id', '')
    products = payload.get('products', [])
    
    if not optimization_id or not products:
        raise HTTPException(status_code=400, detail="Missing required data")
    
    logger.info("Optimization completed", 
                optimization_id=optimization_id, 
                product_count=len(products))
    
    # In a real implementation, we would update the database with the optimized content
    # For now, we'll just log the event
    
    return {
        "success": True,
        "message": f"Processed optimization completed for {len(products)} products"
    }


async def handle_optimization_failed(payload: dict, db: AsyncSession) -> Any:
    """
    Handle optimization failed webhook.
    """
    optimization_id = payload.get('optimization_id', '')
    error_message = payload.get('error_message', 'Unknown error')
    
    if not optimization_id:
        raise HTTPException(status_code=400, detail="Missing optimization ID")
    
    logger.error("Optimization failed", 
                 optimization_id=optimization_id, 
                 error=error_message)
    
    # In a real implementation, we would update the database with the error status
    # For now, we'll just log the event
    
    return {
        "success": True,
        "message": "Failure notification processed"
    }


async def handle_sync_completed(payload: dict, db: AsyncSession) -> Any:
    """
    Handle sync completed webhook.
    """
    sync_id = payload.get('sync_id', '')
    synced_count = payload.get('synced_count', 0)
    
    logger.info("Sync completed", 
                sync_id=sync_id, 
                synced_count=synced_count)
    
    # In a real implementation, we would update the database with the sync status
    # For now, we'll just log the event
    
    return {
        "success": True,
        "message": "Sync completion notification processed"
    }


async def handle_sync_failed(payload: dict, db: AsyncSession) -> Any:
    """
    Handle sync failed webhook.
    """
    sync_id = payload.get('sync_id', '')
    error_message = payload.get('error_message', 'Unknown error')
    
    if not sync_id:
        raise HTTPException(status_code=400, detail="Missing sync ID")
    
    logger.error("Sync failed", 
                 sync_id=sync_id, 
                 error=error_message)
    
    # In a real implementation, we would update the database with the error status
    # For now, we'll just log the event
    
    return {
        "success": True,
        "message": "Sync failure notification processed"
    }


@router.post("/register")
async def register_webhook(
    webhook_data: dict,
    db: AsyncSession = Depends(get_db_session)
) -> Any:
    """
    Register webhook with backend.
    """
    webhook_url = webhook_data.get('webhook_url')
    webhook_secret = webhook_data.get('webhook_secret')
    events = webhook_data.get('events', [])
    
    if not webhook_url or not webhook_secret:
        raise HTTPException(status_code=400, detail="Missing required webhook data")
    
    # In a real implementation, we would store the webhook registration in the database
    # For now, we'll just log the registration
    
    logger.info("Webhook registered", 
                url=webhook_url, 
                events=events)
    
    return {
        "success": True,
        "message": "Webhook registered successfully"
    }