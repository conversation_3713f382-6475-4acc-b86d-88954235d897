# GridSpoke Alertmanager Configuration
# Phase 8: Alert routing and notification management

global:
  # Default SMTP configuration
  smtp_smarthost: '${SMTP_HOST}:${SMTP_PORT}'
  smtp_from: '${SMTP_FROM_ADDRESS}'
  smtp_auth_username: '${SMTP_USER}'
  smtp_auth_password: '${SMTP_PASSWORD}'
  smtp_require_tls: true
  
  # Default notification template
  slack_api_url: '${SLACK_WEBHOOK_URL}'

# Templates for custom messages
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# =============================================================================
# ROUTING CONFIGURATION
# =============================================================================

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 12h
  receiver: 'gridspoke-default'
  
  routes:
    # Critical business alerts - immediate notification
    - match:
        severity: critical
      receiver: 'gridspoke-critical'
      group_wait: 0s
      group_interval: 5m
      repeat_interval: 30m
      routes:
        # AI optimization critical issues
        - match:
            component: ai-optimization
          receiver: 'gridspoke-ai-critical'
        # Database critical issues
        - match:
            component: database
          receiver: 'gridspoke-database-critical'
        # System critical issues
        - match:
            component: system
          receiver: 'gridspoke-system-critical'
    
    # Warning level alerts
    - match:
        severity: warning
      receiver: 'gridspoke-warning'
      group_wait: 2m
      group_interval: 10m
      repeat_interval: 4h
      routes:
        # Performance warnings
        - match:
            component: performance
          receiver: 'gridspoke-performance-warning'
        # Cost-related warnings
        - match:
            component: ai-cost
          receiver: 'gridspoke-cost-warning'
        # WordPress integration warnings
        - match:
            component: wordpress-sync
          receiver: 'gridspoke-wordpress-warning'
    
    # Informational alerts
    - match:
        severity: info
      receiver: 'gridspoke-info'
      group_wait: 5m
      group_interval: 30m
      repeat_interval: 24h
    
    # External dependency alerts
    - match:
        component: external-api
      receiver: 'gridspoke-external-deps'
      group_wait: 1m
      group_interval: 5m
      repeat_interval: 2h
    
    # Business metrics alerts
    - match:
        component: business-metrics
      receiver: 'gridspoke-business'
      group_wait: 10m
      group_interval: 30m
      repeat_interval: 12h

# =============================================================================
# INHIBITION RULES
# =============================================================================

inhibit_rules:
  # Inhibit minor alerts when critical alerts are firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service', 'component']
  
  # Inhibit API alerts when the service is down
  - source_match:
      alertname: 'GridSpokeAPIDown'
    target_match_re:
      alertname: 'GridSpoke.*API.*'
    equal: ['service']
  
  # Inhibit optimization alerts when Celery workers are down
  - source_match:
      alertname: 'GridSpokeCeleryWorkersDown'
    target_match:
      component: 'ai-optimization'
    equal: ['service']

# =============================================================================
# RECEIVERS CONFIGURATION
# =============================================================================

receivers:
  
  # Default receiver
  - name: 'gridspoke-default'
    email_configs:
      - to: '${ALERT_EMAIL_DEFAULT}'
        subject: 'GridSpoke Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Component: {{ .Labels.component }}
          {{ end }}
        headers:
          X-GridSpoke-Alert: 'default'
  
  # Critical alerts - multiple channels
  - name: 'gridspoke-critical'
    email_configs:
      - to: '${ALERT_EMAIL_CRITICAL}'
        subject: '🚨 CRITICAL: GridSpoke {{ .GroupLabels.alertname }}'
        body: |
          🚨 CRITICAL ALERT 🚨
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          
          Service: {{ .Labels.service }}
          Component: {{ .Labels.component }}
          Severity: {{ .Labels.severity }}
          
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          
          {{ if .Annotations.runbook_url }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
          {{ end }}
        headers:
          X-GridSpoke-Priority: 'critical'
          X-GridSpoke-Alert: 'critical'
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_CRITICAL}'
        channel: '#gridspoke-alerts'
        title: '🚨 CRITICAL: GridSpoke Alert'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          
          *Description:* {{ .Annotations.description }}
          *Service:* {{ .Labels.service }}
          *Component:* {{ .Labels.component }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
        color: 'danger'
        send_resolved: true
    
    # PagerDuty for critical alerts (if configured)
    pagerduty_configs:
      - routing_key: '${PAGERDUTY_ROUTING_KEY}'
        description: 'GridSpoke Critical Alert: {{ .GroupLabels.alertname }}'
        details:
          service: '{{ .GroupLabels.service }}'
          component: '{{ .GroupLabels.component }}'
          severity: '{{ .GroupLabels.severity }}'
        
  # AI Optimization critical
  - name: 'gridspoke-ai-critical'
    email_configs:
      - to: '${ALERT_EMAIL_AI_TEAM}'
        subject: '🤖 AI CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          🤖 AI OPTIMIZATION CRITICAL ALERT
          
          {{ range .Alerts }}
          Issue: {{ .Annotations.summary }}
          Details: {{ .Annotations.description }}
          
          This affects the core AI optimization functionality.
          Immediate attention required.
          
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_AI_TEAM}'
        channel: '#ai-optimization'
        title: '🤖 AI Optimization Critical'
        color: 'danger'
  
  # Database critical
  - name: 'gridspoke-database-critical'
    email_configs:
      - to: '${ALERT_EMAIL_DB_TEAM}'
        subject: '💾 DATABASE CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          💾 DATABASE CRITICAL ALERT
          
          {{ range .Alerts }}
          Issue: {{ .Annotations.summary }}
          Details: {{ .Annotations.description }}
          
          Database issues affect all GridSpoke functionality.
          Immediate attention required.
          
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
  
  # System critical
  - name: 'gridspoke-system-critical'
    email_configs:
      - to: '${ALERT_EMAIL_DEVOPS}'
        subject: '⚙️ SYSTEM CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          ⚙️ SYSTEM CRITICAL ALERT
          
          {{ range .Alerts }}
          Issue: {{ .Annotations.summary }}
          Details: {{ .Annotations.description }}
          
          System-level issue affecting GridSpoke infrastructure.
          
          Time: {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
          {{ end }}
  
  # Warning level alerts
  - name: 'gridspoke-warning'
    email_configs:
      - to: '${ALERT_EMAIL_WARNING}'
        subject: '⚠️ Warning: GridSpoke {{ .GroupLabels.alertname }}'
        body: |
          ⚠️ GridSpoke Warning Alert
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Component: {{ .Labels.component }}
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_WARNING}'
        channel: '#gridspoke-monitoring'
        title: '⚠️ GridSpoke Warning'
        color: 'warning'
  
  # Performance warnings
  - name: 'gridspoke-performance-warning'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_PERFORMANCE}'
        channel: '#performance'
        title: '🐌 Performance Warning'
        text: |
          {{ range .Alerts }}
          *Performance Issue:* {{ .Annotations.summary }}
          *Details:* {{ .Annotations.description }}
          *Component:* {{ .Labels.component }}
          {{ end }}
        color: 'warning'
  
  # Cost warnings
  - name: 'gridspoke-cost-warning'
    email_configs:
      - to: '${ALERT_EMAIL_FINANCE}'
        subject: '💰 Cost Alert: {{ .GroupLabels.alertname }}'
        body: |
          💰 GridSpoke Cost Warning
          
          {{ range .Alerts }}
          Cost Issue: {{ .Annotations.summary }}
          Details: {{ .Annotations.description }}
          
          Please review AI usage and optimization costs.
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_FINANCE}'
        channel: '#finance-alerts'
        title: '💰 Cost Warning'
        color: 'warning'
  
  # WordPress integration warnings
  - name: 'gridspoke-wordpress-warning'
    email_configs:
      - to: '${ALERT_EMAIL_WORDPRESS_TEAM}'
        subject: '🔌 WordPress Integration: {{ .GroupLabels.alertname }}'
        body: |
          🔌 WordPress Integration Warning
          
          {{ range .Alerts }}
          Issue: {{ .Annotations.summary }}
          Details: {{ .Annotations.description }}
          
          This affects WordPress store integrations.
          {{ end }}
  
  # Informational alerts
  - name: 'gridspoke-info'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_INFO}'
        channel: '#gridspoke-info'
        title: 'ℹ️ GridSpoke Info'
        color: 'good'
        send_resolved: false
  
  # External dependencies
  - name: 'gridspoke-external-deps'
    email_configs:
      - to: '${ALERT_EMAIL_EXTERNAL}'
        subject: '🌐 External Service: {{ .GroupLabels.alertname }}'
        body: |
          🌐 External Service Alert
          
          {{ range .Alerts }}
          Service: {{ .Annotations.summary }}
          Details: {{ .Annotations.description }}
          
          An external service that GridSpoke depends on is having issues.
          {{ end }}
  
  # Business metrics
  - name: 'gridspoke-business'
    email_configs:
      - to: '${ALERT_EMAIL_BUSINESS}'
        subject: '📊 Business Metrics: {{ .GroupLabels.alertname }}'
        body: |
          📊 GridSpoke Business Metrics Alert
          
          {{ range .Alerts }}
          Metric: {{ .Annotations.summary }}
          Details: {{ .Annotations.description }}
          
          This relates to GridSpoke business performance indicators.
          {{ end }}
    
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_BUSINESS}'
        channel: '#business-metrics'
        title: '📊 Business Metrics'
        color: '#36a64f'

# =============================================================================
# TIME INTERVALS
# =============================================================================

time_intervals:
  
  # Business hours (adjust timezone as needed)
  - name: business-hours
    time_intervals:
      - times:
          - start_time: '09:00'
            end_time: '17:00'
        weekdays: ['monday:friday']
        location: 'UTC'
  
  # Weekend
  - name: weekend
    time_intervals:
      - weekdays: ['saturday', 'sunday']
  
  # After hours
  - name: after-hours
    time_intervals:
      - times:
          - start_time: '17:00'
            end_time: '09:00'
        weekdays: ['monday:friday']
      - weekdays: ['saturday', 'sunday']

# =============================================================================
# MUTE TIME INTERVALS (Optional)
# =============================================================================

mute_time_intervals:
  
  # Maintenance windows
  - name: maintenance-window
    time_intervals:
      - times:
          - start_time: '02:00'
            end_time: '04:00'
        weekdays: ['sunday']
        location: 'UTC'

# =============================================================================
# CONFIGURATION VALIDATION
# =============================================================================

# Validate configuration on startup
config_validation:
  enabled: true
