"""
Schemas package initialization.
"""
from .auth import Token, TokenRefresh, TokenPayload
from .user import User, UserCreate, UserUpdate, UserProfile
from .store import Store, StoreCreate, StoreUpdate, StoreWithStats, StoreSyncRequest
from .product import Product, ProductCreate, ProductUpdate, ProductWithSEO, ProductOptimizationRequest
from .job import OptimizationJob, OptimizationJobCreate, OptimizationJobUpdate, OptimizationJobWithProgress
from .content import GeneratedContent, GeneratedContentCreate, GeneratedContentUpdate, GeneratedContentWithMetrics

__all__ = [
    # Auth
    "Token", "TokenRefresh", "TokenPayload",
    # User
    "User", "UserCreate", "UserUpdate", "UserProfile",
    # Store
    "Store", "StoreCreate", "StoreUpdate", "StoreWithStats", "StoreSyncRequest",
    # Product
    "Product", "ProductCreate", "ProductUpdate", "ProductWithSEO", "ProductOptimizationRequest",
    # Job
    "OptimizationJob", "OptimizationJobCreate", "OptimizationJobUpdate", "OptimizationJobWithProgress",
    # Content
    "GeneratedContent", "GeneratedContentCreate", "GeneratedContentUpdate", "GeneratedContentWithMetrics"
]
