"""
CRUD operations for OptimizationJob model.
"""
import uuid
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from models.optimization_job import OptimizationJob, JobStatus
from schemas.job import OptimizationJob<PERSON><PERSON>, OptimizationJobUpdate


async def create_job(db: AsyncSession, job: OptimizationJobCreate) -> OptimizationJob:
    """Create a new optimization job."""
    db_job = OptimizationJob(**job.dict())
    db.add(db_job)
    await db.commit()
    await db.refresh(db_job)
    return db_job


async def get_job(db: AsyncSession, job_id: uuid.UUID) -> Optional[OptimizationJob]:
    """Get a job by its ID."""
    result = await db.execute(select(OptimizationJob).filter(OptimizationJob.id == job_id))
    return result.scalars().first()


async def get_jobs_by_store(db: AsyncSession, store_id: uuid.UUID, skip: int = 0, limit: int = 100) -> List[OptimizationJob]:
    """Get all jobs for a given store."""
    result = await db.execute(
        select(OptimizationJob)
        .filter(OptimizationJob.store_id == store_id)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()


async def update_job(db: AsyncSession, job: OptimizationJob, update_data: OptimizationJobUpdate) -> OptimizationJob:
    """Update a job."""
    update_data_dict = update_data.dict(exclude_unset=True)
    for key, value in update_data_dict.items():
        setattr(job, key, value)
    await db.commit()
    await db.refresh(job)
    return job


async def delete_job(db: AsyncSession, job_id: uuid.UUID) -> None:
    """Delete a job."""
    job = await get_job(db, job_id)
    if job:
        await db.delete(job)
        await db.commit()

async def cancel_job(db: AsyncSession, job_id: uuid.UUID) -> Optional[OptimizationJob]:
    """Cancel a job."""
    job = await get_job(db, job_id)
    if job and job.status not in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
        job.status = JobStatus.CANCELLED
        await db.commit()
        await db.refresh(job)
        return job
    return None
