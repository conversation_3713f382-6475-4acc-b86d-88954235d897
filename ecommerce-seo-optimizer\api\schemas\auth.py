"""
Authentication schemas for request/response validation.
"""
from typing import Optional
from pydantic import BaseModel
from schemas.user import User  # Added to include user info in token responses


class Token(BaseModel):
    """JWT token response schema (extended with user info for frontend convenience)."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    user: Optional[User] = None  # Optional to preserve backward compatibility

    model_config = {
        "from_attributes": True
    }


class TokenRefresh(BaseModel):
    """Token refresh request schema."""
    refresh_token: str


class TokenPayload(BaseModel):
    """JWT token payload schema."""
    sub: Optional[str] = None
    email: Optional[str] = None
    exp: Optional[int] = None
