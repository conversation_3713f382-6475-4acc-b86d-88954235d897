<?php
/**
 * Test data helper for GridSpoke SEO Connector tests
 */

class GridSpoke_Test_Data {
    
    /**
     * Get sample WooCommerce product data
     */
    public function get_sample_woo_product_data($count = 1) {
        $products = array();
        
        for ($i = 1; $i <= $count; $i++) {
            $products[] = array(
                'id' => $i,
                'name' => "Test Product $i",
                'slug' => "test-product-$i",
                'description' => "This is a detailed description for test product $i. It includes features and benefits.",
                'short_description' => "Short description for test product $i",
                'sku' => "TEST-SKU-$i",
                'price' => 19.99 + $i,
                'regular_price' => 24.99 + $i,
                'sale_price' => 19.99 + $i,
                'status' => 'publish',
                'categories' => array(
                    array('id' => 1, 'name' => 'Test Category'),
                    array('id' => 2, 'name' => 'Electronics')
                ),
                'tags' => array(
                    array('id' => 1, 'name' => 'featured'),
                    array('id' => 2, 'name' => 'bestseller')
                ),
                'images' => array(
                    array(
                        'id' => $i * 10,
                        'url' => "https://example.com/image-$i.jpg",
                        'alt' => "Test product $i image"
                    )
                ),
                'attributes' => array(
                    'color' => 'Blue',
                    'size' => 'Medium',
                    'material' => 'Cotton'
                ),
                'meta_data' => array(
                    '_yoast_wpseo_title' => "SEO Title for Product $i",
                    '_yoast_wpseo_metadesc' => "SEO meta description for product $i"
                ),
                'date_created' => date('c', time() - ($i * 3600)),
                'date_modified' => date('c', time() - ($i * 1800))
            );
        }
        
        return $count === 1 ? $products[0] : $products;
    }
    
    /**
     * Get sample SureCart product data
     */
    public function get_sample_surecart_product_data($count = 1) {
        $products = array();
        
        for ($i = 1; $i <= $count; $i++) {
            $products[] = array(
                'id' => "sc_product_$i",
                'name' => "SureCart Product $i",
                'description' => "This is a detailed description for SureCart product $i.",
                'statement_descriptor' => "SURECART PROD $i",
                'status' => 'published',
                'is_shippable' => true,
                'is_taxable' => true,
                'tax_code' => 'txcd_1234567890',
                'image_url' => "https://example.com/surecart-image-$i.jpg",
                'image_alt' => "SureCart product $i image",
                'gallery' => array(
                    array(
                        'url' => "https://example.com/gallery-$i-1.jpg",
                        'alt' => "Gallery image 1 for product $i"
                    )
                ),
                'prices' => array(
                    array(
                        'id' => "price_$i",
                        'amount' => (29.99 + $i) * 100, // SureCart uses cents
                        'currency' => 'USD',
                        'recurring' => false
                    )
                ),
                'statement_descriptors' => array(
                    "Feature 1 for product $i",
                    "Feature 2 for product $i"
                ),
                'wp_post_id' => 100 + $i,
                'created_at' => date('c', time() - ($i * 3600)),
                'updated_at' => date('c', time() - ($i * 1800))
            );
        }
        
        return $count === 1 ? $products[0] : $products;
    }
    
    /**
     * Get sample optimization response
     */
    public function get_sample_optimization_response() {
        return array(
            'job_id' => 'job_' . uniqid(),
            'status' => 'pending',
            'message' => 'Optimization queued successfully',
            'estimated_completion' => date('c', time() + 3600),
            'optimization_types' => array('title', 'description', 'meta_description')
        );
    }
    
    /**
     * Get sample optimization completion webhook data
     */
    public function get_sample_webhook_data() {
        return array(
            'event' => 'optimization.completed',
            'job_id' => 'job_' . uniqid(),
            'product_id' => 'uuid_' . uniqid(),
            'external_id' => '123',
            'status' => 'completed',
            'optimized_content' => array(
                'title' => 'Optimized Product Title - Enhanced for SEO',
                'description' => 'This is an optimized product description that includes relevant keywords and compelling copy to improve search rankings and conversion rates.',
                'meta_description' => 'Optimized meta description for better search engine visibility and click-through rates.',
                'image_alt' => 'Optimized alt text for product image'
            ),
            'optimization_types' => array('title', 'description', 'meta_description', 'image_alt'),
            'ai_model' => 'claude-3-sonnet',
            'processing_time' => 45.2,
            'timestamp' => date('c')
        );
    }
    
    /**
     * Get sample error webhook data
     */
    public function get_sample_error_webhook_data() {
        return array(
            'event' => 'optimization.failed',
            'job_id' => 'job_' . uniqid(),
            'product_id' => 'uuid_' . uniqid(),
            'external_id' => '123',
            'status' => 'failed',
            'error' => array(
                'code' => 'OPTIMIZATION_FAILED',
                'message' => 'Failed to generate optimized content',
                'details' => 'AI model returned invalid response'
            ),
            'timestamp' => date('c')
        );
    }
    
    /**
     * Get sample plugin settings
     */
    public function get_sample_settings() {
        return array(
            'api_endpoint' => 'http://localhost:8000/api/v1',
            'email' => '<EMAIL>',
            'password' => 'test_password',
            'auto_sync' => true,
            'sync_interval' => 'daily',
            'webhook_secret' => 'test_webhook_secret_' . uniqid(),
            'log_level' => 'info',
            'admin_notifications' => true,
            'optimization_fields' => array('title', 'description', 'meta_description'),
            'ai_model_preference' => 'claude-3-sonnet',
            'optimization_priority' => 'normal',
            'language' => 'en',
            'batch_size' => 50,
            'rate_limit_delay' => 1,
            'cleanup_logs_days' => 30
        );
    }
    
    /**
     * Get sample log entries
     */
    public function get_sample_log_entries($count = 5) {
        $logs = array();
        $levels = array('debug', 'info', 'warning', 'error');
        
        for ($i = 1; $i <= $count; $i++) {
            $level = $levels[array_rand($levels)];
            
            $logs[] = array(
                'level' => $level,
                'message' => "Test log message $i for level $level",
                'context' => json_encode(array(
                    'product_id' => $i,
                    'action' => 'test_action',
                    'user_id' => 1
                )),
                'user_id' => 1,
                'ip_address' => '127.0.0.1',
                'timestamp' => date('Y-m-d H:i:s', time() - ($i * 3600))
            );
        }
        
        return $logs;
    }
    
    /**
     * Create test log entries in database
     */
    public function create_test_logs($count = 5) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        $logs = $this->get_sample_log_entries($count);
        
        foreach ($logs as $log) {
            $wpdb->insert($table_name, $log);
        }
        
        return $logs;
    }
    
    /**
     * Get sample optimization stats
     */
    public function get_sample_optimization_stats() {
        return array(
            'completed_optimizations' => 25,
            'pending_optimizations' => 3,
            'failed_optimizations' => 2,
            'total_optimizations' => 30,
            'last_optimization' => date('c', time() - 3600),
            'success_rate' => 83.33
        );
    }
}
