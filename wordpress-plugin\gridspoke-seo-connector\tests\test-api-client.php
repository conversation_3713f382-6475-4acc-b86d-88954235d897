<?php
/**
 * Tests for GridSpoke_API_Client class
 */

class Test_GridSpoke_API_Client extends GridSpoke_Test_Case {
    
    /**
     * Test API client singleton
     */
    public function test_singleton_instance() {
        $instance1 = GridSpoke_API_Client::get_instance();
        $instance2 = GridSpoke_API_Client::get_instance();
        
        $this->assertSame($instance1, $instance2);
        $this->assertInstanceOf('GridSpoke_API_Client', $instance1);
    }
    
    /**
     * Test authentication with valid credentials
     */
    public function test_authentication_success() {
        $this->api_mock->mock_auth_success();
        
        $api_client = GridSpoke_API_Client::get_instance();
        $result = $api_client->authenticate('<EMAIL>', 'password123');
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Authentication successful', $result['message']);
        $this->assertNotEmpty($result['user']);
        
        // Check that tokens were stored
        $this->assertNotEmpty(get_transient('gridspoke_access_token'));
        $this->assertNotEmpty(get_option('gridspoke_refresh_token'));
    }
    
    /**
     * Test authentication with invalid credentials
     */
    public function test_authentication_failure() {
        $this->api_mock->mock_auth_failure();
        
        $api_client = GridSpoke_API_Client::get_instance();
        $result = $api_client->authenticate('<EMAIL>', 'wrong_password');
        
        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid credentials', $result['message']);
        
        // Check that no tokens were stored
        $this->assertEmpty(get_transient('gridspoke_access_token'));
        $this->assertEmpty(get_option('gridspoke_refresh_token'));
    }
    
    /**
     * Test connection test with health endpoint
     */
    public function test_connection_test() {
        $api_client = GridSpoke_API_Client::get_instance();
        $result = $api_client->test_connection();
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('connection successful', strtolower($result['message']));
        
        // Verify health endpoint was called
        $request = $this->api_mock->find_request('health', 'GET');
        $this->assertNotNull($request);
    }
    
    /**
     * Test product sync with valid data
     */
    public function test_product_sync_success() {
        // Set up authentication
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_store_creation();
        $this->api_mock->mock_sync_success(2, 0);
        
        // Set up settings
        $this->update_plugin_settings(array(
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        // Authenticate first
        $api_client = GridSpoke_API_Client::get_instance();
        $auth_result = $api_client->authenticate('<EMAIL>', 'password123');
        $this->assertTrue($auth_result['success']);
        
        // Test sync
        $products = $this->test_data->get_sample_woo_product_data(2);
        $result = $api_client->sync_products($products);
        
        $this->assertNotFalse($result);
        $this->assertEquals(2, $result['created']);
        $this->assertEquals(0, $result['updated']);
        
        // Verify sync endpoint was called
        $request = $this->api_mock->find_request('products/bulk/sync', 'POST');
        $this->assertNotNull($request);
        $this->assertCount(2, $request['data']);
    }
    
    /**
     * Test product optimization request
     */
    public function test_optimization_request() {
        // Set up authentication and mocks
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_optimization_success();
        
        $this->update_plugin_settings(array(
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        // Authenticate first
        $api_client = GridSpoke_API_Client::get_instance();
        $auth_result = $api_client->authenticate('<EMAIL>', 'password123');
        $this->assertTrue($auth_result['success']);
        
        // Create test product and store UUID mapping
        $product_id = $this->create_test_woo_product();
        update_post_meta($product_id, '_gridspoke_product_uuid', 'test_uuid_123');
        
        // Test optimization request
        $result = $api_client->request_optimization(array($product_id), array(
            'optimization_types' => array('title', 'description')
        ));
        
        $this->assertNotFalse($result);
        $this->assertNotEmpty($result['job_id']);
        
        // Verify optimization endpoint was called
        $request = $this->api_mock->find_request('products/test_uuid_123/optimize', 'POST');
        $this->assertNotNull($request);
    }
    
    /**
     * Test sync without authentication
     */
    public function test_sync_without_authentication() {
        $api_client = GridSpoke_API_Client::get_instance();
        $products = $this->test_data->get_sample_woo_product_data(1);
        
        $result = $api_client->sync_products($products);
        
        $this->assertFalse($result);
        
        // Check that error was logged
        $this->assertLogEntryExists('error', '/not authenticated/i');
    }
    
    /**
     * Test token refresh
     */
    public function test_token_refresh() {
        // Set up refresh token
        update_option('gridspoke_refresh_token', 'test_refresh_token');
        
        $this->api_mock->mock_response('auth/refresh', array(
            'status_code' => 200,
            'body' => json_encode(array(
                'access_token' => 'new_access_token',
                'refresh_token' => 'new_refresh_token'
            ))
        ));
        
        $api_client = GridSpoke_API_Client::get_instance();
        $result = $api_client->refresh_access_token();
        
        $this->assertTrue($result);
        $this->assertEquals('new_access_token', get_transient('gridspoke_access_token'));
        $this->assertEquals('new_refresh_token', get_option('gridspoke_refresh_token'));
    }
    
    /**
     * Test store creation
     */
    public function test_store_creation() {
        // Set up authentication
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_response('stores', array(
            'status_code' => 200,
            'body' => json_encode(array()) // No existing stores
        ), 'GET');
        
        $this->api_mock->mock_response('stores', array(
            'status_code' => 201,
            'body' => json_encode(array(
                'id' => 'new_store_id',
                'name' => get_bloginfo('name'),
                'domain' => parse_url(home_url(), PHP_URL_HOST),
                'platform' => 'woocommerce'
            ))
        ), 'POST');
        
        $this->update_plugin_settings(array(
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        // Authenticate and sync (which should create store)
        $api_client = GridSpoke_API_Client::get_instance();
        $auth_result = $api_client->authenticate('<EMAIL>', 'password123');
        $this->assertTrue($auth_result['success']);
        
        // Mock successful sync after store creation
        $this->api_mock->mock_sync_success(1, 0);
        
        $products = $this->test_data->get_sample_woo_product_data(1);
        $result = $api_client->sync_products($products);
        
        $this->assertNotFalse($result);
        
        // Verify store was created and stored
        $this->assertEquals('new_store_id', get_option('gridspoke_store_id'));
        
        // Verify store creation request was made
        $store_request = $this->api_mock->find_request('stores', 'POST');
        $this->assertNotNull($store_request);
        $this->assertEquals(get_bloginfo('name'), $store_request['data']['name']);
    }
    
    /**
     * Test API error handling
     */
    public function test_api_error_handling() {
        $this->api_mock->mock_response('health', array(
            'status_code' => 500,
            'body' => json_encode(array('detail' => 'Internal server error'))
        ));
        
        $api_client = GridSpoke_API_Client::get_instance();
        $result = $api_client->test_connection();
        
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('500', $result['message']);
    }
    
    /**
     * Test webhook URL generation
     */
    public function test_webhook_url_generation() {
        $api_client = GridSpoke_API_Client::get_instance();
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($api_client);
        $method = $reflection->getMethod('get_webhook_url');
        $method->setAccessible(true);
        
        $webhook_url = $method->invoke($api_client);
        
        $this->assertStringContainsString('gridspoke-seo/v1/webhook', $webhook_url);
        $this->assertStringStartsWith(rest_url(), $webhook_url);
    }
}
