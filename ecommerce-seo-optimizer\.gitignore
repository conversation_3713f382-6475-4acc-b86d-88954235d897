# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Database
*.db
*.sqlite
*.sqlite3

# Build outputs
build/
dist/
*.egg-info/
.pytest_cache/
.coverage
htmlcov/

# Temporary files
*.tmp
*.temp
.cache/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Docker volumes (local development)
postgres_data/
redis_data/

# Backup files
*.bak
*.backup

# Jupyter Notebook
.ipynb_checkpoints

# Package manager
package-lock.json
yarn.lock

# Next.js
.next/
out/

# Nuxt.js
.nuxt
dist

# Gatsby
.cache/
public

# Serverless
.serverless/

# FuseBox
.fusebox/

# DynamoDB Local
.dynamodb/

# TernJS
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Alembic
alembic/versions/*.pyc

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json
