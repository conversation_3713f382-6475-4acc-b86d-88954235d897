"""
A/B Testing Service - Framework for testing SEO optimizations
Enables testing different titles, descriptions, and content variations.
"""

import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from statistics import mean, stdev

from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_

from ..models.product import Product
from ..schemas.product import ProductResponse
from ..agents.seo_optimizer import SEOOptimizer
from ..core.config import settings


class TestStatus(str, Enum):
    """A/B test status"""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TestMetric(str, Enum):
    """Available test metrics"""
    CLICK_THROUGH_RATE = "ctr"
    CONVERSION_RATE = "conversion_rate"
    BOUNCE_RATE = "bounce_rate"
    TIME_ON_PAGE = "time_on_page"
    ORGANIC_TRAFFIC = "organic_traffic"
    SEARCH_IMPRESSIONS = "search_impressions"
    AVERAGE_POSITION = "average_position"


class TestVariation(BaseModel):
    """A/B test variation"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    title: Optional[str] = None
    meta_description: Optional[str] = None
    description: Optional[str] = None
    alt_text: Optional[str] = None
    traffic_allocation: float = Field(..., ge=0, le=1)  # Percentage of traffic
    is_control: bool = False


class TestConfiguration(BaseModel):
    """A/B test configuration"""
    test_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    product_ids: List[str]
    variations: List[TestVariation]
    primary_metric: TestMetric
    secondary_metrics: List[TestMetric] = []
    minimum_sample_size: int = 100
    minimum_duration_days: int = 7
    maximum_duration_days: int = 30
    confidence_level: float = 0.95
    status: TestStatus = TestStatus.DRAFT
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None


class TestResult(BaseModel):
    """Individual test result data point"""
    variation_id: str
    metric: TestMetric
    value: float
    timestamp: datetime
    product_id: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None


class VariationPerformance(BaseModel):
    """Performance data for a test variation"""
    variation_id: str
    variation_name: str
    sample_size: int
    metrics: Dict[TestMetric, float]
    confidence_intervals: Dict[TestMetric, Dict[str, float]]
    is_significant: Dict[TestMetric, bool]
    lift: Dict[TestMetric, float]  # Percentage improvement over control


class TestAnalysis(BaseModel):
    """Complete A/B test analysis"""
    test_id: str
    status: TestStatus
    duration_days: int
    total_sample_size: int
    variations: List[VariationPerformance]
    winner: Optional[str] = None
    recommendation: str
    statistical_significance: Dict[TestMetric, bool]
    confidence_level: float


class ABTestingService:
    """Main A/B testing service"""
    
    def __init__(self):
        self.seo_optimizer = SEOOptimizer()
        
    async def create_title_test(
        self,
        db: AsyncSession,
        product_ids: List[str],
        test_name: str,
        num_variations: int = 3,
        primary_metric: TestMetric = TestMetric.CLICK_THROUGH_RATE
    ) -> TestConfiguration:
        """Create A/B test for product titles"""
        
        # Get products
        products = await self._get_products(db, product_ids)
        
        variations = []
        control_variation = TestVariation(
            name="Control (Original)",
            title=products[0].title if products else "Original Title",
            traffic_allocation=0.25,
            is_control=True
        )
        variations.append(control_variation)
        
        # Generate title variations using AI
        for i in range(num_variations):
            variation_title = await self._generate_title_variation(
                products[0] if products else None, i + 1
            )
            
            variation = TestVariation(
                name=f"Variation {i + 1}",
                title=variation_title,
                traffic_allocation=0.75 / num_variations
            )
            variations.append(variation)
            
        return TestConfiguration(
            name=test_name,
            description=f"A/B testing {num_variations + 1} title variations",
            product_ids=product_ids,
            variations=variations,
            primary_metric=primary_metric,
            secondary_metrics=[TestMetric.SEARCH_IMPRESSIONS, TestMetric.AVERAGE_POSITION]
        )
        
    async def create_meta_description_test(
        self,
        db: AsyncSession,
        product_ids: List[str],
        test_name: str,
        num_variations: int = 3,
        primary_metric: TestMetric = TestMetric.CLICK_THROUGH_RATE
    ) -> TestConfiguration:
        """Create A/B test for meta descriptions"""
        
        products = await self._get_products(db, product_ids)
        
        variations = []
        control_variation = TestVariation(
            name="Control (Original)",
            meta_description=products[0].meta_description if products else "Original meta description",
            traffic_allocation=0.25,
            is_control=True
        )
        variations.append(control_variation)
        
        # Generate meta description variations
        for i in range(num_variations):
            variation_meta = await self._generate_meta_description_variation(
                products[0] if products else None, i + 1
            )
            
            variation = TestVariation(
                name=f"Variation {i + 1}",
                meta_description=variation_meta,
                traffic_allocation=0.75 / num_variations
            )
            variations.append(variation)
            
        return TestConfiguration(
            name=test_name,
            description=f"A/B testing {num_variations + 1} meta description variations",
            product_ids=product_ids,
            variations=variations,
            primary_metric=primary_metric,
            secondary_metrics=[TestMetric.BOUNCE_RATE, TestMetric.TIME_ON_PAGE]
        )
        
    async def create_content_test(
        self,
        db: AsyncSession,
        product_ids: List[str],
        test_name: str,
        test_elements: List[str],  # ["title", "meta_description", "description"]
        primary_metric: TestMetric = TestMetric.CONVERSION_RATE
    ) -> TestConfiguration:
        """Create comprehensive content A/B test"""
        
        products = await self._get_products(db, product_ids)
        base_product = products[0] if products else None
        
        variations = []
        
        # Control variation
        control = TestVariation(
            name="Control (Original)",
            title=base_product.title if base_product else "Original Title",
            meta_description=base_product.meta_description if base_product else "Original meta",
            description=base_product.description if base_product else "Original description",
            traffic_allocation=0.5,
            is_control=True
        )
        variations.append(control)
        
        # AI-optimized variation
        optimized_content = await self._generate_optimized_content_variation(base_product, test_elements)
        
        optimized = TestVariation(
            name="AI Optimized",
            title=optimized_content.get("title"),
            meta_description=optimized_content.get("meta_description"),
            description=optimized_content.get("description"),
            traffic_allocation=0.5
        )
        variations.append(optimized)
        
        return TestConfiguration(
            name=test_name,
            description=f"A/B testing optimized content for {', '.join(test_elements)}",
            product_ids=product_ids,
            variations=variations,
            primary_metric=primary_metric,
            secondary_metrics=[
                TestMetric.CLICK_THROUGH_RATE,
                TestMetric.BOUNCE_RATE,
                TestMetric.ORGANIC_TRAFFIC
            ]
        )
        
    async def start_test(
        self,
        db: AsyncSession,
        test_config: TestConfiguration
    ) -> TestConfiguration:
        """Start an A/B test"""
        
        # Validate test configuration
        validation_result = await self._validate_test_config(test_config)
        if not validation_result["valid"]:
            raise ValueError(f"Invalid test configuration: {validation_result['errors']}")
            
        # Update test status
        test_config.status = TestStatus.ACTIVE
        test_config.started_at = datetime.utcnow()
        
        # Apply variations to products
        await self._apply_test_variations(db, test_config)
        
        # Store test configuration
        await self._store_test_config(db, test_config)
        
        return test_config
        
    async def stop_test(
        self,
        db: AsyncSession,
        test_id: str,
        reason: str = "Manual stop"
    ) -> TestConfiguration:
        """Stop an active A/B test"""
        
        test_config = await self._get_test_config(db, test_id)
        if not test_config:
            raise ValueError(f"Test {test_id} not found")
            
        test_config.status = TestStatus.COMPLETED
        test_config.ended_at = datetime.utcnow()
        
        # Restore original content
        await self._restore_original_content(db, test_config)
        
        # Update test configuration
        await self._update_test_config(db, test_config)
        
        return test_config
        
    async def record_test_result(
        self,
        db: AsyncSession,
        test_id: str,
        variation_id: str,
        metric: TestMetric,
        value: float,
        product_id: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> TestResult:
        """Record a test result data point"""
        
        result = TestResult(
            variation_id=variation_id,
            metric=metric,
            value=value,
            timestamp=datetime.utcnow(),
            product_id=product_id,
            user_id=user_id,
            session_id=session_id
        )
        
        # Store result
        await self._store_test_result(db, test_id, result)
        
        return result
        
    async def analyze_test_results(
        self,
        db: AsyncSession,
        test_id: str
    ) -> TestAnalysis:
        """Analyze A/B test results and determine winner"""
        
        test_config = await self._get_test_config(db, test_id)
        if not test_config:
            raise ValueError(f"Test {test_id} not found")
            
        # Get all test results
        results = await self._get_test_results(db, test_id)
        
        # Calculate performance for each variation
        variation_performances = []
        control_variation = next((v for v in test_config.variations if v.is_control), None)
        
        for variation in test_config.variations:
            performance = await self._calculate_variation_performance(
                variation, results, control_variation
            )
            variation_performances.append(performance)
            
        # Determine statistical significance
        significance = await self._calculate_statistical_significance(
            test_config, variation_performances
        )
        
        # Determine winner
        winner = await self._determine_winner(
            test_config, variation_performances, significance
        )
        
        # Generate recommendation
        recommendation = await self._generate_test_recommendation(
            test_config, variation_performances, winner, significance
        )
        
        duration_days = (
            (test_config.ended_at or datetime.utcnow()) - test_config.started_at
        ).days if test_config.started_at else 0
        
        return TestAnalysis(
            test_id=test_id,
            status=test_config.status,
            duration_days=duration_days,
            total_sample_size=sum(vp.sample_size for vp in variation_performances),
            variations=variation_performances,
            winner=winner,
            recommendation=recommendation,
            statistical_significance=significance,
            confidence_level=test_config.confidence_level
        )
        
    async def get_active_tests(self, db: AsyncSession) -> List[TestConfiguration]:
        """Get all active A/B tests"""
        return await self._get_tests_by_status(db, TestStatus.ACTIVE)
        
    async def get_test_recommendations(
        self,
        db: AsyncSession,
        product_id: str
    ) -> List[Dict[str, Any]]:
        """Get A/B test recommendations for a product"""
        
        product = await self._get_product(db, product_id)
        if not product:
            return []
            
        recommendations = []
        
        # Analyze current content for test opportunities
        analysis = await self._analyze_test_opportunities(product)
        
        # Title optimization test
        if analysis["title_score"] < 80:
            recommendations.append({
                "type": "title_test",
                "priority": "high",
                "description": "Test optimized product titles for better CTR",
                "expected_improvement": "15-25% CTR improvement",
                "duration": "14 days"
            })
            
        # Meta description test
        if analysis["meta_score"] < 80:
            recommendations.append({
                "type": "meta_description_test",
                "priority": "medium", 
                "description": "Test compelling meta descriptions",
                "expected_improvement": "10-20% CTR improvement",
                "duration": "14 days"
            })
            
        # Content optimization test
        if analysis["content_score"] < 70:
            recommendations.append({
                "type": "content_test",
                "priority": "medium",
                "description": "Test AI-optimized product descriptions",
                "expected_improvement": "5-15% conversion improvement",
                "duration": "21 days"
            })
            
        return recommendations
        
    async def _generate_title_variation(
        self,
        product: Optional[Product],
        variation_number: int
    ) -> str:
        """Generate title variation using AI"""
        
        if not product:
            return f"Optimized Title Variation {variation_number}"
            
        try:
            prompt = f"""
            Create an SEO-optimized title variation for this product:
            Current Title: {product.title}
            Category: {product.category or 'Product'}
            Description: {(product.description or '')[:200]}
            
            Requirements:
            - 50-60 characters maximum
            - Include buying intent keywords
            - Different approach than original (Variation {variation_number})
            - Focus on benefits or features
            - Natural and compelling
            """
            
            result = await self.seo_optimizer.call_async(prompt=prompt)
            return result[:60]  # Ensure max length
            
        except Exception:
            return f"Optimized {product.title or 'Product'} - V{variation_number}"[:60]
            
    async def _generate_meta_description_variation(
        self,
        product: Optional[Product],
        variation_number: int
    ) -> str:
        """Generate meta description variation using AI"""
        
        if not product:
            return f"Optimized meta description variation {variation_number}"
            
        try:
            prompt = f"""
            Create an SEO-optimized meta description variation for this product:
            Product: {product.title}
            Current Meta: {product.meta_description or 'None'}
            Category: {product.category or 'Product'}
            
            Requirements:
            - 150-160 characters maximum
            - Include call-to-action
            - Different tone/approach (Variation {variation_number})
            - Compelling and click-worthy
            - Include benefits or unique features
            """
            
            result = await self.seo_optimizer.call_async(prompt=prompt)
            return result[:160]  # Ensure max length
            
        except Exception:
            return f"Discover the best {product.title or 'product'} - Shop now for great deals! Variation {variation_number}"[:160]
            
    async def _generate_optimized_content_variation(
        self,
        product: Optional[Product],
        test_elements: List[str]
    ) -> Dict[str, str]:
        """Generate optimized content variation for multiple elements"""
        
        content = {}
        
        if "title" in test_elements:
            content["title"] = await self._generate_title_variation(product, 1)
            
        if "meta_description" in test_elements:
            content["meta_description"] = await self._generate_meta_description_variation(product, 1)
            
        if "description" in test_elements and product:
            try:
                prompt = f"""
                Optimize this product description for better SEO and conversion:
                Current Description: {product.description or 'No description'}
                Title: {product.title}
                Category: {product.category or 'Product'}
                
                Create an improved version that:
                - Uses natural keyword placement
                - Highlights benefits and features
                - Includes social proof elements
                - Has clear structure with bullet points
                - Encourages purchase action
                """
                
                result = await self.seo_optimizer.call_async(prompt=prompt)
                content["description"] = result
                
            except Exception:
                content["description"] = product.description
                
        return content
        
    async def _validate_test_config(self, config: TestConfiguration) -> Dict[str, Any]:
        """Validate A/B test configuration"""
        errors = []
        
        # Check traffic allocation
        total_allocation = sum(v.traffic_allocation for v in config.variations)
        if abs(total_allocation - 1.0) > 0.01:
            errors.append(f"Traffic allocation must sum to 1.0, got {total_allocation}")
            
        # Check for control variation
        control_count = sum(1 for v in config.variations if v.is_control)
        if control_count != 1:
            errors.append(f"Must have exactly one control variation, got {control_count}")
            
        # Check minimum variations
        if len(config.variations) < 2:
            errors.append("Must have at least 2 variations")
            
        # Check product IDs
        if not config.product_ids:
            errors.append("Must specify at least one product ID")
            
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
        
    async def _calculate_variation_performance(
        self,
        variation: TestVariation,
        all_results: List[TestResult],
        control_variation: Optional[TestVariation]
    ) -> VariationPerformance:
        """Calculate performance metrics for a variation"""
        
        # Filter results for this variation
        variation_results = [r for r in all_results if r.variation_id == variation.id]
        
        # Group by metric
        metrics = {}
        confidence_intervals = {}
        is_significant = {}
        lift = {}
        
        for metric in TestMetric:
            metric_results = [r.value for r in variation_results if r.metric == metric]
            
            if metric_results:
                metrics[metric] = mean(metric_results)
                
                # Calculate confidence interval (simplified)
                if len(metric_results) > 1:
                    std = stdev(metric_results)
                    margin = 1.96 * (std / (len(metric_results) ** 0.5))  # 95% CI
                    confidence_intervals[metric] = {
                        "lower": metrics[metric] - margin,
                        "upper": metrics[metric] + margin
                    }
                else:
                    confidence_intervals[metric] = {
                        "lower": metrics[metric],
                        "upper": metrics[metric]
                    }
                    
                # Calculate statistical significance vs control (simplified)
                is_significant[metric] = len(metric_results) >= 30  # Simplified check
                
                # Calculate lift vs control
                if control_variation:
                    control_results = [r.value for r in all_results 
                                     if r.variation_id == control_variation.id and r.metric == metric]
                    if control_results:
                        control_mean = mean(control_results)
                        lift[metric] = ((metrics[metric] - control_mean) / control_mean * 100) if control_mean > 0 else 0
                    else:
                        lift[metric] = 0
                else:
                    lift[metric] = 0
            else:
                metrics[metric] = 0
                confidence_intervals[metric] = {"lower": 0, "upper": 0}
                is_significant[metric] = False
                lift[metric] = 0
                
        return VariationPerformance(
            variation_id=variation.id,
            variation_name=variation.name,
            sample_size=len(variation_results),
            metrics=metrics,
            confidence_intervals=confidence_intervals,
            is_significant=is_significant,
            lift=lift
        )
        
    async def _calculate_statistical_significance(
        self,
        config: TestConfiguration,
        performances: List[VariationPerformance]
    ) -> Dict[TestMetric, bool]:
        """Calculate statistical significance for test results"""
        
        significance = {}
        
        # Simplified significance calculation
        for metric in [config.primary_metric] + config.secondary_metrics:
            # Check if we have enough sample size
            total_samples = sum(p.sample_size for p in performances)
            significance[metric] = (
                total_samples >= config.minimum_sample_size and
                any(p.is_significant.get(metric, False) for p in performances)
            )
            
        return significance
        
    async def _determine_winner(
        self,
        config: TestConfiguration,
        performances: List[VariationPerformance],
        significance: Dict[TestMetric, bool]
    ) -> Optional[str]:
        """Determine winning variation"""
        
        primary_metric = config.primary_metric
        
        # Only declare winner if statistically significant
        if not significance.get(primary_metric, False):
            return None
            
        # Find variation with best performance on primary metric
        best_performance = max(
            performances,
            key=lambda p: p.metrics.get(primary_metric, 0)
        )
        
        # Only declare winner if it's not the control and has positive lift
        if (not best_performance.variation_name.startswith("Control") and
            best_performance.lift.get(primary_metric, 0) > 0):
            return best_performance.variation_id
            
        return None
        
    async def _generate_test_recommendation(
        self,
        config: TestConfiguration,
        performances: List[VariationPerformance],
        winner: Optional[str],
        significance: Dict[TestMetric, bool]
    ) -> str:
        """Generate test recommendation"""
        
        if winner:
            winner_perf = next(p for p in performances if p.variation_id == winner)
            primary_lift = winner_perf.lift.get(config.primary_metric, 0)
            return f"Implement winning variation '{winner_perf.variation_name}' with {primary_lift:.1f}% improvement in {config.primary_metric.value}"
        elif significance.get(config.primary_metric, False):
            return "No clear winner found. Control variation performs best - keep current content"
        else:
            return "Test needs more time/data to reach statistical significance"
            
    # Placeholder methods for database operations
    async def _get_products(self, db: AsyncSession, product_ids: List[str]) -> List[Product]:
        """Get products from database"""
        # Implementation would query actual database
        return []
        
    async def _get_product(self, db: AsyncSession, product_id: str) -> Optional[Product]:
        """Get single product from database"""
        # Implementation would query actual database
        return None
        
    async def _apply_test_variations(self, db: AsyncSession, config: TestConfiguration):
        """Apply test variations to products"""
        # Implementation would update product content
        pass
        
    async def _store_test_config(self, db: AsyncSession, config: TestConfiguration):
        """Store test configuration in database"""
        # Implementation would save to database
        pass
        
    async def _get_test_config(self, db: AsyncSession, test_id: str) -> Optional[TestConfiguration]:
        """Get test configuration from database"""
        # Implementation would query database
        return None
        
    async def _update_test_config(self, db: AsyncSession, config: TestConfiguration):
        """Update test configuration in database"""
        # Implementation would update database
        pass
        
    async def _restore_original_content(self, db: AsyncSession, config: TestConfiguration):
        """Restore original product content"""
        # Implementation would restore original content
        pass
        
    async def _store_test_result(self, db: AsyncSession, test_id: str, result: TestResult):
        """Store test result in database"""
        # Implementation would save result to database
        pass
        
    async def _get_test_results(self, db: AsyncSession, test_id: str) -> List[TestResult]:
        """Get test results from database"""
        # Implementation would query results
        return []
        
    async def _get_tests_by_status(self, db: AsyncSession, status: TestStatus) -> List[TestConfiguration]:
        """Get tests by status"""
        # Implementation would query database
        return []
        
    async def _analyze_test_opportunities(self, product: Product) -> Dict[str, float]:
        """Analyze product for A/B test opportunities"""
        # Simplified scoring - real implementation would analyze content quality
        return {
            "title_score": 75.0,
            "meta_score": 70.0,
            "content_score": 65.0
        }
