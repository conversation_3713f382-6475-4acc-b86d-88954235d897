# GridSpoke Advanced SEO Tasks
# Phase 7: Advanced SEO processing tasks for comprehensive optimization

from typing import Dict, Any, List, Optional, Union
import asyncio
import logging
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

# Runtime imports - these will be available when deployed with proper dependencies
try:
    from celery import Celery
    from workers.celery_app import celery_app
    from workers.utils.progress_tracker import track_progress
    from workers.utils.error_handler import handle_task_error
    from api.agents.product_optimizer import ProductOptimizer
    from api.agents.content_generator import ContentGenerator
    from api.services.ai_service import AIService
    from api.core.database import get_db
    from api.models.product import Product
    from api.models.optimization_job import OptimizationJob
    from api.models.generated_content import GeneratedContent
    from sqlalchemy import select, update
    from sqlalchemy.orm import selectinload
    
    @celery_app.task(bind=True, name="generate_structured_data")
    def generate_structured_data(self, product_id: str, schema_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Generate structured data (schema markup) for a product.
        
        Args:
            product_id: Product identifier
            schema_types: Types of schema to generate (Product, Review, BreadcrumbList, etc.)
        
        Returns:
            Dict containing generated structured data
        """
        
        if schema_types is None:
            schema_types = ["Product", "Review", "BreadcrumbList"]
        
        try:
            # Initialize progress tracking
            track_progress(self.request.id, 0, 5, "Initializing structured data generation")
            
            # Get product data with relationships
            db = next(get_db())
            
            try:
                product = db.query(Product).filter(Product.id == product_id).first()
                
                if not product:
                    raise ValueError(f"Product {product_id} not found")
                
                track_progress(self.request.id, 1, 5, "Product data retrieved")
                
                # Generate schema markup
                ai_service = AIService()
                schema_data = {}
                
                # Generate Product schema
                if "Product" in schema_types:
                    track_progress(self.request.id, 2, 5, "Generating Product schema")
                    schema_data["Product"] = _generate_product_schema(product, ai_service)
                
                # Generate Review schema if reviews exist (when available)
                if "Review" in schema_types:
                    track_progress(self.request.id, 3, 5, "Generating Review schema")
                    schema_data["AggregateRating"] = _generate_review_schema(product)
                
                # Generate BreadcrumbList schema
                if "BreadcrumbList" in schema_types:
                    track_progress(self.request.id, 4, 5, "Generating BreadcrumbList schema")
                    schema_data["BreadcrumbList"] = _generate_breadcrumb_schema(product)
                
                # Store generated content
                content_record = GeneratedContent(
                    product_id=product_id,
                    content_type="structured_data",
                    content=json.dumps(schema_data),
                    ai_model="gpt-4",
                    tokens_used=500,  # Estimate
                    created_at=datetime.utcnow()
                )
                db.add(content_record)
                db.commit()
                
                track_progress(self.request.id, 5, 5, "Structured data generation completed")
                
                return {
                    'status': 'success',
                    'product_id': product_id,
                    'schema_types': schema_types,
                    'structured_data': schema_data,
                    'task_id': self.request.id
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error generating structured data for product {product_id}: {str(e)}")
            handle_task_error(self, e, f"structured_data:{product_id}")
            raise

    def _generate_product_schema(product: 'Product', ai_service: 'AIService') -> Dict[str, Any]:
        """Generate Product schema markup"""
        
        # Basic product schema using correct Product model attributes
        schema = {
            "@context": "https://schema.org",
            "@type": "Product",
            "name": product.title or "Product",
            "description": product.description or product.short_description or "Quality product",
            "sku": product.sku or str(product.id)
        }
        
        # Add brand info
        if hasattr(product, 'store') and product.store:
            schema["brand"] = {
                "@type": "Brand",
                "name": getattr(product.store, 'name', 'Store')
            }
        
        # Add images if available (using image_urls from model)
        if hasattr(product, 'image_urls') and product.image_urls:
            schema["image"] = product.image_urls
        
        # Add offers
        if product.price:
            schema["offers"] = {
                "@type": "Offer",
                "url": f"/products/{product.id}",
                "priceCurrency": getattr(product, 'currency', 'USD'),
                "price": str(product.price),
                "availability": "https://schema.org/InStock"  # Default to in stock
            }
        
        return schema

    def _generate_review_schema(product: 'Product') -> Dict[str, Any]:
        """Generate AggregateRating schema - placeholder when reviews not available"""
        
        # Return basic schema when reviews relationship not available
        return {
            "@type": "AggregateRating",
            "ratingValue": 4.5,  # Default rating
            "reviewCount": 1,
            "name": product.title or "Product"
        }

    def _generate_breadcrumb_schema(product: 'Product') -> Dict[str, Any]:
        """Generate BreadcrumbList schema"""
        
        items = [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "/"
            }
        ]
        
        # Add category if available
        if product.category:
            items.append({
                "@type": "ListItem", 
                "position": 2,
                "name": product.category,
                "item": f"/category/{product.category.lower().replace(' ', '-')}"
            })
        
        # Add product
        items.append({
            "@type": "ListItem",
            "position": len(items) + 1,
            "name": product.title or "Product",
            "item": f"/products/{product.id}"
        })
        
        return {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": items
        }

    @celery_app.task(bind=True, name="optimize_open_graph_tags")
    def optimize_open_graph_tags(self, product_id: str, target_platforms: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Generate optimized Open Graph tags for social media sharing.
        
        Args:
            product_id: Product identifier
            target_platforms: Social platforms to optimize for (facebook, twitter, linkedin)
        
        Returns:
            Dict containing optimized Open Graph tags
        """
        
        if target_platforms is None:
            target_platforms = ["facebook", "twitter", "linkedin"]
        
        try:
            track_progress(self.request.id, 0, 4, "Initializing Open Graph optimization")
            
            db = next(get_db())
            
            try:
                product = db.query(Product).filter(Product.id == product_id).first()
                
                if not product:
                    raise ValueError(f"Product {product_id} not found")
                
                track_progress(self.request.id, 1, 4, "Product data retrieved")
                
                ai_service = AIService()
                og_tags = {}
                
                # Generate platform-specific tags
                if "facebook" in target_platforms:
                    track_progress(self.request.id, 2, 4, "Generating Facebook Open Graph tags")
                    og_tags["facebook"] = _generate_facebook_og_tags(product, ai_service)
                
                if "twitter" in target_platforms:
                    track_progress(self.request.id, 3, 4, "Generating Twitter Card tags")
                    og_tags["twitter"] = _generate_twitter_card_tags(product, ai_service)
                
                if "linkedin" in target_platforms:
                    og_tags["linkedin"] = _generate_linkedin_og_tags(product, ai_service)
                
                # Store generated content
                content_record = GeneratedContent(
                    product_id=product_id,
                    content_type="open_graph_tags",
                    content=json.dumps(og_tags),
                    ai_model="gpt-4",
                    tokens_used=300,
                    created_at=datetime.utcnow()
                )
                db.add(content_record)
                db.commit()
                
                track_progress(self.request.id, 4, 4, "Open Graph optimization completed")
                
                return {
                    'status': 'success',
                    'product_id': product_id,
                    'target_platforms': target_platforms,
                    'og_tags': og_tags,
                    'task_id': self.request.id
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error optimizing Open Graph tags for product {product_id}: {str(e)}")
            handle_task_error(self, e, f"open_graph:{product_id}")
            raise

    def _generate_facebook_og_tags(product: 'Product', ai_service: 'AIService') -> Dict[str, Any]:
        """Generate Facebook-optimized Open Graph tags"""
        
        # Use AI service for description generation
        try:
            fb_description = ai_service.generate_content({
                "content_type": "social_media",
                "product_title": product.title,
                "max_length": 125
            }).get("content", product.description or f"Discover {product.title}")
        except Exception:
            fb_description = product.description or f"Discover {product.title or 'this product'}"
        
        tags = {
            "og:title": (product.title or "Product")[:60],  # Facebook truncates at ~60 chars
            "og:description": fb_description[:125],
            "og:type": "product",
            "og:url": f"/products/{product.id}",
            "product:price:amount": str(product.price) if product.price else "0",
            "product:price:currency": getattr(product, 'currency', 'USD')
        }
        
        # Add image if available
        if hasattr(product, 'image_urls') and product.image_urls:
            tags["og:image"] = product.image_urls[0]
            tags["og:image:alt"] = product.title or "Product image"
        
        return tags

    def _generate_twitter_card_tags(product: 'Product', ai_service: 'AIService') -> Dict[str, Any]:
        """Generate Twitter Card tags"""
        
        try:
            twitter_description = ai_service.generate_content({
                "content_type": "social_media",
                "product_title": product.title,
                "max_length": 160
            }).get("content", product.description or f"Check out {product.title}")
        except Exception:
            twitter_description = product.description or f"Check out {product.title or 'this product'}"
        
        tags = {
            "twitter:card": "summary_large_image",
            "twitter:title": (product.title or "Product")[:70],  # Twitter truncates at ~70 chars
            "twitter:description": twitter_description[:160],
            "twitter:url": f"/products/{product.id}"
        }
        
        # Add image if available
        if hasattr(product, 'image_urls') and product.image_urls:
            tags["twitter:image"] = product.image_urls[0]
            tags["twitter:image:alt"] = product.title or "Product image"
        
        return tags

    def _generate_linkedin_og_tags(product: 'Product', ai_service: 'AIService') -> Dict[str, Any]:
        """Generate LinkedIn-optimized Open Graph tags"""
        
        try:
            linkedin_description = ai_service.generate_content({
                "content_type": "social_media",
                "product_title": product.title,
                "max_length": 150
            }).get("content", product.description or f"Professional quality {product.title}")
        except Exception:
            linkedin_description = product.description or f"Professional quality {product.title or 'product'}"
        
        tags = {
            "og:title": product.title or "Product",
            "og:description": linkedin_description[:150],
            "og:type": "product",
            "og:url": f"/products/{product.id}"
        }
        
        # Add image if available
        if hasattr(product, 'image_urls') and product.image_urls:
            tags["og:image"] = product.image_urls[0]
        
        return tags

    @celery_app.task(bind=True, name="optimize_canonical_urls")
    def optimize_canonical_urls(self, store_id: str, batch_size: int = 100) -> Dict[str, Any]:
        """
        Optimize canonical URLs to prevent duplicate content issues.
        
        Args:
            store_id: Store identifier
            batch_size: Number of products to process in one batch
        
        Returns:
            Dict containing optimization results
        """
        
        try:
            track_progress(self.request.id, 0, 4, "Initializing canonical URL optimization")
            
            db = next(get_db())
            
            try:
                # Get products for the store
                products = db.query(Product).filter(
                    Product.store_id == store_id
                ).limit(batch_size).all()
                
                if not products:
                    return {
                        'status': 'success',
                        'message': 'No products found for canonical URL optimization',
                        'store_id': store_id
                    }
                
                track_progress(self.request.id, 1, 4, f"Processing {len(products)} products")
                
                canonical_urls = {}
                duplicate_groups = []
                
                # Detect potential duplicates by title similarity
                title_groups = {}
                for product in products:
                    if not product.title:
                        continue
                        
                    # Create a normalized title for comparison
                    clean_title = product.title.lower().strip()
                    clean_title = ''.join(c for c in clean_title if c.isalnum() or c.isspace())
                    clean_title = ' '.join(clean_title.split())
                    
                    if clean_title not in title_groups:
                        title_groups[clean_title] = []
                    title_groups[clean_title].append(product)
                
                track_progress(self.request.id, 2, 4, "Analyzing duplicate content")
                
                # Process each group
                for clean_title, group in title_groups.items():
                    if len(group) > 1:
                        # Multiple products with similar titles - potential duplicates
                        primary_product = group[0]  # Use first as canonical
                        canonical_url = f"/products/{primary_product.id}"
                        
                        duplicate_group = {
                            "canonical_url": canonical_url,
                            "canonical_product_id": str(primary_product.id),
                            "duplicate_products": []
                        }
                        
                        for product in group:
                            product_url = f"/products/{product.id}"
                            canonical_urls[str(product.id)] = canonical_url
                            
                            duplicate_group["duplicate_products"].append({
                                "product_id": str(product.id),
                                "title": product.title,
                                "url": product_url,
                                "is_canonical": product.id == primary_product.id
                            })
                        
                        duplicate_groups.append(duplicate_group)
                    else:
                        # Single product - canonical URL is itself
                        product = group[0]
                        canonical_urls[str(product.id)] = f"/products/{product.id}"
                
                track_progress(self.request.id, 3, 4, "Storing canonical URL mappings")
                
                # Store canonical URL mappings
                content_record = GeneratedContent(
                    store_id=store_id,
                    content_type="canonical_urls",
                    content=json.dumps({
                        "canonical_mappings": canonical_urls,
                        "duplicate_groups": duplicate_groups
                    }),
                    ai_model="canonical_optimizer",
                    created_at=datetime.utcnow()
                )
                db.add(content_record)
                db.commit()
                
                track_progress(self.request.id, 4, 4, "Canonical URL optimization completed")
                
                return {
                    'status': 'success',
                    'store_id': store_id,
                    'products_processed': len(products),
                    'canonical_mappings': canonical_urls,
                    'duplicate_groups_found': len(duplicate_groups),
                    'duplicate_groups': duplicate_groups,
                    'task_id': self.request.id
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error optimizing canonical URLs for store {store_id}: {str(e)}")
            handle_task_error(self, e, f"canonical_urls:{store_id}")
            raise

    @celery_app.task(bind=True, name="generate_xml_sitemap")
    def generate_xml_sitemap(self, store_id: str, include_images: bool = True) -> Dict[str, Any]:
        """
        Generate XML sitemap for all products in a store.
        
        Args:
            store_id: Store identifier
            include_images: Whether to include image tags in sitemap
        
        Returns:
            Dict containing generated sitemap
        """
        
        try:
            import xml.etree.ElementTree as ET
            from xml.dom import minidom
            
            track_progress(self.request.id, 0, 4, "Initializing XML sitemap generation")
            
            db = next(get_db())
            
            try:
                # Get all active products for the store
                products = db.query(Product).filter(
                    Product.store_id == store_id,
                    Product.is_active == True,
                    Product.is_published == True
                ).all()
                
                if not products:
                    return {
                        'status': 'success',
                        'message': 'No active products found for sitemap generation',
                        'store_id': store_id
                    }
                
                track_progress(self.request.id, 1, 4, f"Generating sitemap for {len(products)} products")
                
                # Create XML sitemap
                urlset = ET.Element("urlset")
                urlset.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
                if include_images:
                    urlset.set("xmlns:image", "http://www.google.com/schemas/sitemap-image/1.1")
                
                for product in products:
                    url = ET.SubElement(urlset, "url")
                    
                    # Product URL
                    loc = ET.SubElement(url, "loc")
                    loc.text = f"/products/{product.id}"
                    
                    # Last modified date
                    lastmod = ET.SubElement(url, "lastmod")
                    lastmod.text = (product.updated_at or product.created_at or datetime.utcnow()).strftime("%Y-%m-%d")
                    
                    # Change frequency
                    changefreq = ET.SubElement(url, "changefreq")
                    changefreq.text = "weekly"
                    
                    # Priority
                    priority = ET.SubElement(url, "priority")
                    priority.text = "0.8"
                    
                    # Add image tags if enabled and images exist
                    if include_images and hasattr(product, 'image_urls') and product.image_urls:
                        for image_url in product.image_urls[:5]:  # Limit to 5 images per product
                            image = ET.SubElement(url, "image:image")
                            image_loc = ET.SubElement(image, "image:loc")
                            image_loc.text = image_url
                            
                            image_title = ET.SubElement(image, "image:title")
                            image_title.text = f"{product.title or 'Product'} - Image"
                            
                            if product.description:
                                image_caption = ET.SubElement(image, "image:caption")
                                image_caption.text = product.description[:150]
                
                track_progress(self.request.id, 2, 4, "Formatting XML sitemap")
                
                # Format XML
                rough_string = ET.tostring(urlset, 'utf-8')
                reparsed = minidom.parseString(rough_string)
                sitemap_xml = reparsed.toprettyxml(indent="  ")
                
                track_progress(self.request.id, 3, 4, "Storing sitemap data")
                
                # Store sitemap
                content_record = GeneratedContent(
                    store_id=store_id,
                    content_type="xml_sitemap",
                    content=sitemap_xml,
                    ai_model="sitemap_generator",
                    created_at=datetime.utcnow()
                )
                db.add(content_record)
                db.commit()
                
                track_progress(self.request.id, 4, 4, "XML sitemap generation completed")
                
                return {
                    'status': 'success',
                    'store_id': store_id,
                    'products_included': len(products),
                    'sitemap_xml': sitemap_xml,
                    'include_images': include_images,
                    'task_id': self.request.id
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error generating XML sitemap for store {store_id}: {str(e)}")
            handle_task_error(self, e, f"xml_sitemap:{store_id}")
            raise

except ImportError:
    # Define placeholder functions when imports fail (development environment)
    logger.warning("SEO tasks imports failed - functions will be placeholders until deployment")
    
    def generate_structured_data(product_id: str, schema_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Placeholder for structured data generation"""
        return {"status": "placeholder", "message": "Function will be available when deployed"}
    
    def optimize_open_graph_tags(product_id: str, target_platforms: Optional[List[str]] = None) -> Dict[str, Any]:
        """Placeholder for Open Graph optimization"""
        return {"status": "placeholder", "message": "Function will be available when deployed"}
    
    def optimize_canonical_urls(store_id: str, batch_size: int = 100) -> Dict[str, Any]:
        """Placeholder for canonical URL optimization"""
        return {"status": "placeholder", "message": "Function will be available when deployed"}
    
    def generate_xml_sitemap(store_id: str, include_images: bool = True) -> Dict[str, Any]:
        """Placeholder for XML sitemap generation"""
        return {"status": "placeholder", "message": "Function will be available when deployed"}
