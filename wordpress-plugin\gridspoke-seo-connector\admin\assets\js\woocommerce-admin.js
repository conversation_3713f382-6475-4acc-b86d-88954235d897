/**
 * GridSpoke SEO WooCommerce Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        initializeProductPageIntegration();
        initializeBulkActions();
        initializeProductListIntegration();
    });
    
    /**
     * Initialize product page integration
     */
    function initializeProductPageIntegration() {
        // Add GridSpoke SEO meta box functionality
        if ($('#gridspoke-seo-metabox').length > 0) {
            initializeMetaBox();
        }
        
        // Add optimization buttons to product edit page
        if ($('.post-type-product').length > 0) {
            addProductOptimizationButtons();
        }
    }
    
    /**
     * Initialize meta box functionality
     */
    function initializeMetaBox() {
        // Optimize single product
        $('#gridspoke-optimize-product').on('click', function() {
            var $button = $(this);
            var $status = $('#gridspoke-optimization-status');
            var productId = $('#post_ID').val();
            var fields = [];
            
            $('input[name="gridspoke_optimization_fields[]"]:checked').each(function() {
                fields.push($(this).val());
            });
            
            if (fields.length === 0) {
                showMetaBoxNotice(gridspoke_woo_admin.select_fields_error, 'error');
                return;
            }
            
            $button.prop('disabled', true).text(gridspoke_woo_admin.optimizing_text);
            $status.html('<span class="gridspoke-status-loading">' + gridspoke_woo_admin.optimizing_text + '</span>');
            
            $.ajax({
                url: gridspoke_woo_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_optimize_single_product',
                    nonce: gridspoke_woo_admin.nonce,
                    product_id: productId,
                    fields: fields
                },
                success: function(response) {
                    if (response.success) {
                        $status.html('<span class="gridspoke-status-success">' + response.data.message + '</span>');
                        
                        if (response.data.job_id) {
                            $status.append('<br><small>' + gridspoke_woo_admin.job_id_text + ' ' + response.data.job_id + '</small>');
                            
                            // Start polling for completion
                            pollOptimizationStatus(response.data.job_id, productId);
                        }
                    } else {
                        $status.html('<span class="gridspoke-status-error">' + response.data.message + '</span>');
                    }
                },
                error: function() {
                    $status.html('<span class="gridspoke-status-error">' + gridspoke_woo_admin.optimization_error + '</span>');
                },
                complete: function() {
                    $button.prop('disabled', false).text(gridspoke_woo_admin.optimize_text);
                }
            });
        });
        
        // Sync single product
        $('#gridspoke-sync-product').on('click', function() {
            var $button = $(this);
            var $status = $('#gridspoke-sync-status');
            var productId = $('#post_ID').val();
            
            $button.prop('disabled', true).text(gridspoke_woo_admin.syncing_text);
            $status.html('<span class="gridspoke-status-loading">' + gridspoke_woo_admin.syncing_text + '</span>');
            
            $.ajax({
                url: gridspoke_woo_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_sync_single_product',
                    nonce: gridspoke_woo_admin.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        $status.html('<span class="gridspoke-status-success">' + response.data.message + '</span>');
                        
                        if (response.data.gridspoke_id) {
                            $status.append('<br><small>' + gridspoke_woo_admin.gridspoke_id_text + ' ' + response.data.gridspoke_id + '</small>');
                        }
                    } else {
                        $status.html('<span class="gridspoke-status-error">' + response.data.message + '</span>');
                    }
                },
                error: function() {
                    $status.html('<span class="gridspoke-status-error">' + gridspoke_woo_admin.sync_error + '</span>');
                },
                complete: function() {
                    $button.prop('disabled', false).text(gridspoke_woo_admin.sync_text);
                }
            });
        });
    }
    
    /**
     * Add optimization buttons to product edit page
     */
    function addProductOptimizationButtons() {
        // Add quick optimization button to publish meta box
        var $publishBox = $('#submitdiv');
        if ($publishBox.length > 0) {
            var optimizeButton = '<div id="gridspoke-quick-actions" class="misc-pub-section">' +
                '<label>' + gridspoke_woo_admin.quick_optimize_label + '</label>' +
                '<button type="button" id="gridspoke-quick-optimize" class="button button-secondary">' +
                gridspoke_woo_admin.quick_optimize_text + '</button>' +
                '</div>';
            
            $publishBox.find('#minor-publishing-actions').after(optimizeButton);
        }
        
        // Quick optimize handler
        $(document).on('click', '#gridspoke-quick-optimize', function() {
            var $button = $(this);
            var productId = $('#post_ID').val();
            
            $button.prop('disabled', true).text(gridspoke_woo_admin.optimizing_text);
            
            $.ajax({
                url: gridspoke_woo_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_quick_optimize_product',
                    nonce: gridspoke_woo_admin.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        showMetaBoxNotice(response.data.message, 'success');
                    } else {
                        showMetaBoxNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showMetaBoxNotice(gridspoke_woo_admin.optimization_error, 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text(gridspoke_woo_admin.quick_optimize_text);
                }
            });
        });
    }
    
    /**
     * Initialize bulk actions
     */
    function initializeBulkActions() {
        // Add bulk optimization option to WooCommerce product list
        if ($('#bulk-action-selector-top').length > 0) {
            $('#bulk-action-selector-top, #bulk-action-selector-bottom').append(
                '<option value="gridspoke_optimize">' + gridspoke_woo_admin.bulk_optimize_text + '</option>'
            );
        }
        
        // Handle bulk optimization
        $('#doaction, #doaction2').on('click', function(e) {
            var action = $(this).prev('select').val();
            
            if (action === 'gridspoke_optimize') {
                e.preventDefault();
                
                var selectedProducts = [];
                $('input[name="post[]"]:checked').each(function() {
                    selectedProducts.push($(this).val());
                });
                
                if (selectedProducts.length === 0) {
                    alert(gridspoke_woo_admin.select_products_error);
                    return;
                }
                
                if (!confirm(gridspoke_woo_admin.confirm_bulk_optimize_products.replace('%d', selectedProducts.length))) {
                    return;
                }
                
                performBulkOptimization(selectedProducts);
            }
        });
    }
    
    /**
     * Initialize product list integration
     */
    function initializeProductListIntegration() {
        // Add GridSpoke status column indicators
        $('.gridspoke-optimization-status').each(function() {
            var $indicator = $(this);
            var status = $indicator.data('status');
            
            switch (status) {
                case 'optimized':
                    $indicator.addClass('gridspoke-status-success').attr('title', gridspoke_woo_admin.status_optimized);
                    break;
                case 'pending':
                    $indicator.addClass('gridspoke-status-pending').attr('title', gridspoke_woo_admin.status_pending);
                    break;
                case 'error':
                    $indicator.addClass('gridspoke-status-error').attr('title', gridspoke_woo_admin.status_error);
                    break;
                default:
                    $indicator.addClass('gridspoke-status-none').attr('title', gridspoke_woo_admin.status_none);
            }
        });
    }
    
    /**
     * Perform bulk optimization
     */
    function performBulkOptimization(productIds) {
        var $notice = $('<div class="notice notice-info"><p>' + gridspoke_woo_admin.bulk_optimizing_text + '</p></div>');
        $('.wrap h1').after($notice);
        
        $.ajax({
            url: gridspoke_woo_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_bulk_optimize_products',
                nonce: gridspoke_woo_admin.nonce,
                product_ids: productIds
            },
            success: function(response) {
                $notice.remove();
                
                if (response.success) {
                    showNotice(response.data.message, 'success');
                    
                    // Refresh page to show updated statuses
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showNotice(response.data.message, 'error');
                }
            },
            error: function() {
                $notice.remove();
                showNotice(gridspoke_woo_admin.bulk_optimization_error, 'error');
            }
        });
    }
    
    /**
     * Poll optimization status
     */
    function pollOptimizationStatus(jobId, productId) {
        var pollCount = 0;
        var maxPolls = 60; // Poll for up to 5 minutes
        
        var pollInterval = setInterval(function() {
            pollCount++;
            
            if (pollCount > maxPolls) {
                clearInterval(pollInterval);
                return;
            }
            
            $.ajax({
                url: gridspoke_woo_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_check_optimization_status',
                    nonce: gridspoke_woo_admin.nonce,
                    job_id: jobId,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success && response.data.status === 'completed') {
                        clearInterval(pollInterval);
                        $('#gridspoke-optimization-status').html('<span class="gridspoke-status-success">' + gridspoke_woo_admin.optimization_completed + '</span>');
                        
                        // Refresh meta box to show updated content
                        refreshMetaBox();
                    } else if (response.success && response.data.status === 'failed') {
                        clearInterval(pollInterval);
                        $('#gridspoke-optimization-status').html('<span class="gridspoke-status-error">' + gridspoke_woo_admin.optimization_failed + '</span>');
                    }
                }
            });
        }, 5000); // Poll every 5 seconds
    }
    
    /**
     * Refresh meta box content
     */
    function refreshMetaBox() {
        var productId = $('#post_ID').val();
        
        $.ajax({
            url: gridspoke_woo_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_refresh_metabox',
                nonce: gridspoke_woo_admin.nonce,
                product_id: productId
            },
            success: function(response) {
                if (response.success && response.data.html) {
                    $('#gridspoke-seo-metabox .inside').html(response.data.html);
                }
            }
        });
    }
    
    /**
     * Show notice in meta box
     */
    function showMetaBoxNotice(message, type) {
        var $notice = $('<div class="gridspoke-metabox-notice gridspoke-notice-' + type + '">' + message + '</div>');
        $('#gridspoke-seo-metabox .inside').prepend($notice);
        
        setTimeout(function() {
            $notice.fadeOut();
        }, 5000);
    }
    
    /**
     * Show admin notice
     */
    function showNotice(message, type) {
        var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.wrap h1').after($notice);
        
        setTimeout(function() {
            $notice.fadeOut();
        }, 5000);
    }
    
})(jQuery);
