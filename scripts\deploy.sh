#!/bin/bash

# GridSpoke Production Deployment Script
# Usage: ./scripts/deploy.sh [version]

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
VERSION="${1:-latest}"
DOCKER_REGISTRY="your-registry.com"
PROJECT_NAME="gridspoke"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "Docker Compose is not installed. Please install it and try again."
        exit 1
    fi
    
    # Check if .env.prod exists
    if [[ ! -f "$PROJECT_DIR/.env.prod" ]]; then
        log_error ".env.prod file not found. Please create it from .env.prod.example"
        exit 1
    fi
    
    # Check if SSL certificates exist (if not first deployment)
    if [[ ! -f "$PROJECT_DIR/certbot/conf/live/$(grep DOMAIN_NAME .env.prod | cut -d'=' -f2)/fullchain.pem" ]] && [[ "$1" != "--first-time" ]]; then
        log_warning "SSL certificates not found. Run with --first-time flag for initial deployment."
    fi
    
    log_success "Prerequisites check passed"
}

# Function to backup database
backup_database() {
    log_info "Creating database backup..."
    
    # Create backup directory if it doesn't exist
    mkdir -p "$PROJECT_DIR/backups/$(date +%Y-%m-%d)"
    
    # Get database credentials from .env.prod
    source "$PROJECT_DIR/.env.prod"
    
    # Create database backup
    docker-compose -f docker-compose.prod.yml exec -T db pg_dump \
        -U "$POSTGRES_USER" \
        -d "$POSTGRES_DB" \
        --no-password \
        --clean \
        --if-exists \
        > "$PROJECT_DIR/backups/$(date +%Y-%m-%d)/gridspoke_backup_$(date +%H%M%S).sql"
    
    # Compress backup
    gzip "$PROJECT_DIR/backups/$(date +%Y-%m-%d)/gridspoke_backup_$(date +%H%M%S).sql"
    
    log_success "Database backup created"
}

# Function to setup SSL certificates (first-time deployment)
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    source "$PROJECT_DIR/.env.prod"
    
    # Create necessary directories
    mkdir -p "$PROJECT_DIR/certbot/conf"
    mkdir -p "$PROJECT_DIR/certbot/www"
    
    # Start nginx with temporary config for ACME challenge
    log_info "Starting temporary nginx for SSL certificate acquisition..."
    
    # Create temporary nginx config for ACME challenge
    cat > "$PROJECT_DIR/nginx/nginx.temp.conf" << EOF
events {
    worker_connections 1024;
}

http {
    server {
        listen 80;
        server_name $DOMAIN_NAME www.$DOMAIN_NAME;
        
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }
        
        location / {
            return 200 'OK';
            add_header Content-Type text/plain;
        }
    }
}
EOF
    
    # Start temporary nginx
    docker run -d --name nginx-temp \
        -p 80:80 \
        -v "$PROJECT_DIR/certbot/www:/var/www/certbot:ro" \
        -v "$PROJECT_DIR/nginx/nginx.temp.conf:/etc/nginx/nginx.conf:ro" \
        nginx:alpine
    
    # Wait for nginx to start
    sleep 5
    
    # Get SSL certificate
    docker run --rm \
        -v "$PROJECT_DIR/certbot/conf:/etc/letsencrypt" \
        -v "$PROJECT_DIR/certbot/www:/var/www/certbot" \
        certbot/certbot \
        certonly --webroot \
        --webroot-path=/var/www/certbot \
        --email "$SSL_EMAIL" \
        --agree-tos \
        --no-eff-email \
        -d "$DOMAIN_NAME" \
        -d "www.$DOMAIN_NAME"
    
    # Stop temporary nginx
    docker stop nginx-temp && docker rm nginx-temp
    
    # Clean up temporary config
    rm "$PROJECT_DIR/nginx/nginx.temp.conf"
    
    log_success "SSL certificates obtained"
}

# Function to pull latest images
pull_images() {
    log_info "Pulling latest Docker images..."
    
    docker-compose -f docker-compose.prod.yml pull
    
    log_success "Images pulled successfully"
}

# Function to build custom images
build_images() {
    log_info "Building custom images..."
    
    # Build images with version tag
    docker-compose -f docker-compose.prod.yml build \
        --build-arg VERSION="$VERSION" \
        --no-cache
    
    log_success "Images built successfully"
}

# Function to run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Wait for database to be ready
    docker-compose -f docker-compose.prod.yml exec -T db pg_isready -U "$POSTGRES_USER"
    
    # Run migrations
    docker-compose -f docker-compose.prod.yml exec -T api alembic upgrade head
    
    log_success "Database migrations completed"
}

# Function to deploy application
deploy_application() {
    log_info "Deploying application..."
    
    # Stop existing containers (if any)
    docker-compose -f docker-compose.prod.yml down --remove-orphans
    
    # Start services
    docker-compose -f docker-compose.prod.yml up -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose -f docker-compose.prod.yml ps | grep -q "unhealthy"; then
            log_info "Services still starting... (attempt $attempt/$max_attempts)"
            sleep 10
            ((attempt++))
        else
            break
        fi
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "Services failed to start within expected time"
        docker-compose -f docker-compose.prod.yml logs
        exit 1
    fi
    
    log_success "Application deployed successfully"
}

# Function to run health checks
run_health_checks() {
    log_info "Running health checks..."
    
    source "$PROJECT_DIR/.env.prod"
    
    # Check API health
    local api_health=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN_NAME/health" || echo "000")
    if [[ "$api_health" == "200" ]]; then
        log_success "API health check passed"
    else
        log_error "API health check failed (HTTP $api_health)"
        exit 1
    fi
    
    # Check database connection
    if docker-compose -f docker-compose.prod.yml exec -T db pg_isready -U "$POSTGRES_USER" >/dev/null 2>&1; then
        log_success "Database health check passed"
    else
        log_error "Database health check failed"
        exit 1
    fi
    
    # Check Redis connection
    if docker-compose -f docker-compose.prod.yml exec -T redis redis-cli ping >/dev/null 2>&1; then
        log_success "Redis health check passed"
    else
        log_error "Redis health check failed"
        exit 1
    fi
    
    # Check Celery workers
    local celery_status=$(docker-compose -f docker-compose.prod.yml exec -T celery-worker celery -A tasks.celery_app inspect ping 2>/dev/null | grep -c "pong" || echo "0")
    if [[ "$celery_status" -gt "0" ]]; then
        log_success "Celery workers health check passed"
    else
        log_error "Celery workers health check failed"
        exit 1
    fi
    
    log_success "All health checks passed"
}

# Function to setup monitoring
setup_monitoring() {
    log_info "Setting up monitoring..."
    
    # Ensure monitoring directories exist
    mkdir -p "$PROJECT_DIR/monitoring/prometheus"
    mkdir -p "$PROJECT_DIR/monitoring/grafana/dashboards"
    mkdir -p "$PROJECT_DIR/monitoring/grafana/datasources"
    
    # Start monitoring services if not already running
    docker-compose -f docker-compose.prod.yml up -d prometheus grafana
    
    log_success "Monitoring setup completed"
}

# Function to clean up old containers and images
cleanup() {
    log_info "Cleaning up old containers and images..."
    
    # Remove unused containers
    docker container prune -f
    
    # Remove unused images (keep last 3 versions)
    docker image prune -f
    
    # Remove old backups (keep last 7 days)
    find "$PROJECT_DIR/backups" -name "*.sql.gz" -mtime +7 -delete 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Function to display deployment summary
show_summary() {
    log_success "Deployment completed successfully!"
    
    source "$PROJECT_DIR/.env.prod"
    
    echo ""
    echo "🚀 GridSpoke is now running at:"
    echo "   Main site: https://$DOMAIN_NAME"
    echo "   API docs:  https://$DOMAIN_NAME/docs"
    echo ""
    echo "📊 Monitoring dashboards:"
    echo "   Grafana:   https://admin.$DOMAIN_NAME/grafana/"
    echo "   Flower:    https://admin.$DOMAIN_NAME/flower/"
    echo ""
    echo "🔧 Useful commands:"
    echo "   View logs:     docker-compose -f docker-compose.prod.yml logs -f"
    echo "   Check status:  docker-compose -f docker-compose.prod.yml ps"
    echo "   Stop services: docker-compose -f docker-compose.prod.yml down"
    echo ""
}

# Main deployment function
main() {
    cd "$PROJECT_DIR"
    
    log_info "Starting GridSpoke deployment (version: $VERSION)"
    
    # Parse command line arguments
    FIRST_TIME_DEPLOYMENT=false
    SKIP_BACKUP=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --first-time)
                FIRST_TIME_DEPLOYMENT=true
                shift
                ;;
            --skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            --help)
                echo "Usage: $0 [version] [options]"
                echo ""
                echo "Options:"
                echo "  --first-time   First-time deployment (sets up SSL certificates)"
                echo "  --skip-backup  Skip database backup"
                echo "  --help         Show this help message"
                exit 0
                ;;
            *)
                VERSION="$1"
                shift
                ;;
        esac
    done
    
    # Run deployment steps
    check_prerequisites "$([[ $FIRST_TIME_DEPLOYMENT == true ]] && echo "--first-time" || echo "")"
    
    if [[ $FIRST_TIME_DEPLOYMENT == true ]]; then
        setup_ssl
    fi
    
    if [[ $SKIP_BACKUP == false ]]; then
        backup_database
    fi
    
    pull_images
    build_images
    deploy_application
    run_migrations
    run_health_checks
    setup_monitoring
    cleanup
    show_summary
    
    log_success "Deployment completed successfully!"
}

# Trap errors
trap 'log_error "Deployment failed! Check the logs above for details."' ERR

# Run main function
main "$@"
