"""
Technical SEO Service - XML Sitemaps, Robots.txt, and Technical SEO
Handles technical SEO automation including sitemap generation, robots.txt optimization,
canonical URL management, and internal linking suggestions.
"""

import asyncio
import re
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime, timedelta
from urllib.parse import urlparse, urljoin, urlunparse
from xml.etree.ElementTree import Element, SubElement, tostring
import xml.etree.ElementTree as ET
from dataclasses import dataclass

from pydantic import BaseModel, Field, HttpUrl
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from ..agents.seo_optimizer import SEOOptimizer
from ..core.config import settings


class SitemapURL(BaseModel):
    """Individual URL entry in sitemap"""
    loc: str  # URL location
    lastmod: Optional[datetime] = None  # Last modification date
    changefreq: Optional[str] = None  # Change frequency
    priority: Optional[float] = None  # Priority (0.0-1.0)
    image_urls: List[str] = []  # Associated images
    alternate_urls: Dict[str, str] = {}  # Alternate language versions


class SitemapIndex(BaseModel):
    """Sitemap index entry"""
    loc: str  # Sitemap location
    lastmod: Optional[datetime] = None


class RobotsRule(BaseModel):
    """Robots.txt rule"""
    user_agent: str
    directive: str  # "Allow", "Disallow"
    path: str
    comment: Optional[str] = None


class RobotsConfiguration(BaseModel):
    """Complete robots.txt configuration"""
    rules: List[RobotsRule]
    crawl_delay: Optional[int] = None
    sitemaps: List[str] = []
    host: Optional[str] = None
    additional_directives: List[str] = []


class InternalLink(BaseModel):
    """Internal link structure"""
    source_url: str
    target_url: str
    anchor_text: str
    link_type: str  # "navigation", "content", "footer", "sidebar"
    context: str  # Surrounding text context
    is_dofollow: bool = True
    relevance_score: float  # 0-1


class LinkingSuggestion(BaseModel):
    """Internal linking suggestion"""
    source_page: str
    target_page: str
    suggested_anchor_text: str
    reason: str
    estimated_impact: str  # "high", "medium", "low"
    implementation_difficulty: str  # "easy", "medium", "hard"
    priority_score: float  # 0-100


class TechnicalSEOIssue(BaseModel):
    """Technical SEO issue found"""
    issue_type: str
    severity: str  # "critical", "warning", "info"
    affected_urls: List[str]
    description: str
    recommendation: str
    impact: str


class TechnicalSEOAudit(BaseModel):
    """Complete technical SEO audit result"""
    domain: str
    audit_date: datetime = Field(default_factory=datetime.utcnow)
    crawled_pages: int
    issues_found: List[TechnicalSEOIssue]
    sitemap_analysis: Dict[str, Any]
    robots_analysis: Dict[str, Any]
    internal_linking_analysis: Dict[str, Any]
    page_speed_analysis: Dict[str, Any]
    mobile_friendliness: Dict[str, Any]
    canonical_issues: List[str]
    overall_health_score: float  # 0-100


class TechnicalSEOService:
    """Main technical SEO service"""
    
    def __init__(self):
        self.seo_optimizer = SEOOptimizer()
        
    async def generate_xml_sitemap(
        self,
        db: AsyncSession,
        domain: str,
        include_images: bool = True,
        include_alternates: bool = True,
        max_urls_per_sitemap: int = 50000
    ) -> Dict[str, str]:
        """Generate XML sitemaps for the domain"""
        
        # 1. Get all URLs from database
        all_urls = await self._get_all_urls(db, domain)
        
        # 2. Categorize URLs
        categorized_urls = await self._categorize_urls(all_urls)
        
        # 3. Generate individual sitemaps
        sitemaps = {}
        
        for category, urls in categorized_urls.items():
            if len(urls) > max_urls_per_sitemap:
                # Split into multiple sitemaps
                for i, chunk in enumerate(self._chunk_list(urls, max_urls_per_sitemap)):
                    sitemap_name = f"{category}-{i+1}.xml"
                    sitemap_content = await self._generate_sitemap_xml(
                        chunk, include_images, include_alternates
                    )
                    sitemaps[sitemap_name] = sitemap_content
            else:
                sitemap_name = f"{category}.xml"
                sitemap_content = await self._generate_sitemap_xml(
                    urls, include_images, include_alternates
                )
                sitemaps[sitemap_name] = sitemap_content
                
        # 4. Generate sitemap index
        if len(sitemaps) > 1:
            sitemap_index = await self._generate_sitemap_index(sitemaps.keys(), domain)
            sitemaps["sitemap.xml"] = sitemap_index
            
        return sitemaps
        
    async def optimize_robots_txt(
        self,
        domain: str,
        business_type: str = "ecommerce",
        crawl_budget_optimization: bool = True
    ) -> RobotsConfiguration:
        """Generate optimized robots.txt configuration"""
        
        rules = []
        
        # 1. Basic rules for all user agents
        basic_rules = await self._get_basic_robots_rules(business_type)
        rules.extend(basic_rules)
        
        # 2. Search engine specific rules
        search_engine_rules = await self._get_search_engine_specific_rules()
        rules.extend(search_engine_rules)
        
        # 3. Crawl budget optimization rules
        if crawl_budget_optimization:
            optimization_rules = await self._get_crawl_budget_rules(domain)
            rules.extend(optimization_rules)
            
        # 4. Security rules
        security_rules = await self._get_security_robots_rules()
        rules.extend(security_rules)
        
        # 5. Get sitemaps
        sitemaps = await self._get_sitemap_urls(domain)
        
        # 6. Determine crawl delay
        crawl_delay = await self._calculate_optimal_crawl_delay(domain)
        
        return RobotsConfiguration(
            rules=rules,
            crawl_delay=crawl_delay,
            sitemaps=sitemaps,
            host=domain,
            additional_directives=await self._get_additional_directives(domain)
        )
        
    async def analyze_internal_linking(
        self,
        db: AsyncSession,
        domain: str,
        max_pages: int = 1000
    ) -> Dict[str, Any]:
        """Analyze internal linking structure"""
        
        # 1. Crawl internal links
        internal_links = await self._crawl_internal_links(domain, max_pages)
        
        # 2. Analyze link distribution
        link_distribution = await self._analyze_link_distribution(internal_links)
        
        # 3. Find orphaned pages
        orphaned_pages = await self._find_orphaned_pages(db, internal_links)
        
        # 4. Analyze anchor text diversity
        anchor_analysis = await self._analyze_anchor_text(internal_links)
        
        # 5. Calculate page authority distribution
        page_authority = await self._calculate_internal_page_authority(internal_links)
        
        # 6. Find linking opportunities
        linking_suggestions = await self._generate_linking_suggestions(
            db, internal_links, page_authority
        )
        
        return {
            "total_internal_links": len(internal_links),
            "unique_pages_linked": len(set(link.target_url for link in internal_links)),
            "orphaned_pages": orphaned_pages,
            "link_distribution": link_distribution,
            "anchor_text_analysis": anchor_analysis,
            "page_authority_scores": page_authority,
            "linking_suggestions": linking_suggestions[:20],  # Top 20 suggestions
            "issues": await self._find_internal_linking_issues(internal_links)
        }
        
    async def generate_linking_suggestions(
        self,
        db: AsyncSession,
        source_content: str,
        source_url: str,
        target_keywords: List[str] = None
    ) -> List[LinkingSuggestion]:
        """Generate internal linking suggestions for specific content"""
        
        suggestions = []
        
        # 1. Find relevant pages to link to
        relevant_pages = await self._find_relevant_pages(
            db, source_content, target_keywords
        )
        
        # 2. Analyze content for linking opportunities
        linking_opportunities = await self._analyze_content_for_links(
            source_content, relevant_pages
        )
        
        # 3. Generate suggestions
        for opportunity in linking_opportunities:
            suggestion = LinkingSuggestion(
                source_page=source_url,
                target_page=opportunity["target_url"],
                suggested_anchor_text=opportunity["anchor_text"],
                reason=opportunity["reason"],
                estimated_impact=opportunity["impact"],
                implementation_difficulty=opportunity["difficulty"],
                priority_score=opportunity["priority_score"]
            )
            suggestions.append(suggestion)
            
        # Sort by priority score
        suggestions.sort(key=lambda x: x.priority_score, reverse=True)
        return suggestions
        
    async def audit_technical_seo(
        self,
        domain: str,
        max_pages: int = 500,
        check_page_speed: bool = True,
        check_mobile: bool = True
    ) -> TechnicalSEOAudit:
        """Perform comprehensive technical SEO audit"""
        
        issues = []
        
        # 1. Crawl website
        crawled_pages = await self._crawl_website(domain, max_pages)
        
        # 2. Check for duplicate content
        duplicate_issues = await self._check_duplicate_content(crawled_pages)
        issues.extend(duplicate_issues)
        
        # 3. Check for broken links
        broken_link_issues = await self._check_broken_links(crawled_pages)
        issues.extend(broken_link_issues)
        
        # 4. Check canonical implementation
        canonical_issues = await self._check_canonical_implementation(crawled_pages)
        issues.extend(canonical_issues)
        
        # 5. Check meta tags
        meta_issues = await self._check_meta_tags(crawled_pages)
        issues.extend(meta_issues)
        
        # 6. Check heading structure
        heading_issues = await self._check_heading_structure(crawled_pages)
        issues.extend(heading_issues)
        
        # 7. Check image optimization
        image_issues = await self._check_image_optimization(crawled_pages)
        issues.extend(image_issues)
        
        # 8. Analyze sitemaps
        sitemap_analysis = await self._analyze_existing_sitemaps(domain)
        
        # 9. Analyze robots.txt
        robots_analysis = await self._analyze_existing_robots(domain)
        
        # 10. Analyze internal linking
        linking_analysis = await self.analyze_internal_linking(None, domain, max_pages // 2)
        
        # 11. Page speed analysis (if enabled)
        page_speed_analysis = {}
        if check_page_speed:
            page_speed_analysis = await self._analyze_page_speed(crawled_pages[:10])  # Sample
            
        # 12. Mobile friendliness (if enabled)
        mobile_analysis = {}
        if check_mobile:
            mobile_analysis = await self._analyze_mobile_friendliness(crawled_pages[:10])
            
        # 13. Calculate overall health score
        health_score = await self._calculate_technical_health_score(issues, crawled_pages)
        
        return TechnicalSEOAudit(
            domain=domain,
            crawled_pages=len(crawled_pages),
            issues_found=issues,
            sitemap_analysis=sitemap_analysis,
            robots_analysis=robots_analysis,
            internal_linking_analysis=linking_analysis,
            page_speed_analysis=page_speed_analysis,
            mobile_friendliness=mobile_analysis,
            canonical_issues=[issue.description for issue in canonical_issues],
            overall_health_score=health_score
        )
        
    async def manage_canonical_urls(
        self,
        db: AsyncSession,
        domain: str
    ) -> Dict[str, Any]:
        """Manage canonical URL implementation"""
        
        # 1. Find pages that need canonical tags
        pages_needing_canonical = await self._find_pages_needing_canonical(db, domain)
        
        # 2. Detect canonical conflicts
        canonical_conflicts = await self._detect_canonical_conflicts(db, domain)
        
        # 3. Generate canonical recommendations
        canonical_recommendations = []
        
        for page_data in pages_needing_canonical:
            recommendation = await self._generate_canonical_recommendation(page_data)
            canonical_recommendations.append(recommendation)
            
        # 4. Find self-referencing canonical issues
        self_ref_issues = await self._find_self_referencing_issues(db, domain)
        
        return {
            "pages_needing_canonical": len(pages_needing_canonical),
            "canonical_conflicts": canonical_conflicts,
            "canonical_recommendations": canonical_recommendations,
            "self_referencing_issues": self_ref_issues,
            "implementation_guide": await self._create_canonical_implementation_guide()
        }
        
    # Private helper methods
    async def _get_all_urls(self, db: AsyncSession, domain: str) -> List[SitemapURL]:
        """Get all URLs for sitemap generation"""
        
        # Mock implementation - would query actual database
        urls = []
        
        # Product pages
        for i in range(100):
            urls.append(SitemapURL(
                loc=f"https://{domain}/product/{i}",
                lastmod=datetime.utcnow() - timedelta(days=i % 30),
                changefreq="weekly",
                priority=0.8,
                image_urls=[f"https://{domain}/images/product-{i}.jpg"]
            ))
            
        # Category pages
        for i in range(20):
            urls.append(SitemapURL(
                loc=f"https://{domain}/category/{i}",
                lastmod=datetime.utcnow() - timedelta(days=i % 7),
                changefreq="daily",
                priority=0.9
            ))
            
        # Blog posts
        for i in range(50):
            urls.append(SitemapURL(
                loc=f"https://{domain}/blog/post-{i}",
                lastmod=datetime.utcnow() - timedelta(days=i),
                changefreq="monthly",
                priority=0.6
            ))
            
        return urls
        
    async def _categorize_urls(self, urls: List[SitemapURL]) -> Dict[str, List[SitemapURL]]:
        """Categorize URLs for separate sitemaps"""
        
        categories = {
            "products": [],
            "categories": [],
            "blog": [],
            "pages": []
        }
        
        for url in urls:
            if "/product/" in url.loc:
                categories["products"].append(url)
            elif "/category/" in url.loc:
                categories["categories"].append(url)
            elif "/blog/" in url.loc:
                categories["blog"].append(url)
            else:
                categories["pages"].append(url)
                
        return categories
        
    async def _generate_sitemap_xml(
        self,
        urls: List[SitemapURL],
        include_images: bool,
        include_alternates: bool
    ) -> str:
        """Generate XML sitemap content"""
        
        # Create root element
        urlset = Element("urlset")
        urlset.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
        
        if include_images:
            urlset.set("xmlns:image", "http://www.google.com/schemas/sitemap-image/1.1")
            
        if include_alternates:
            urlset.set("xmlns:xhtml", "http://www.w3.org/1999/xhtml")
            
        for url_data in urls:
            url_elem = SubElement(urlset, "url")
            
            # Location
            loc_elem = SubElement(url_elem, "loc")
            loc_elem.text = url_data.loc
            
            # Last modified
            if url_data.lastmod:
                lastmod_elem = SubElement(url_elem, "lastmod")
                lastmod_elem.text = url_data.lastmod.strftime("%Y-%m-%d")
                
            # Change frequency
            if url_data.changefreq:
                changefreq_elem = SubElement(url_elem, "changefreq")
                changefreq_elem.text = url_data.changefreq
                
            # Priority
            if url_data.priority:
                priority_elem = SubElement(url_elem, "priority")
                priority_elem.text = str(url_data.priority)
                
            # Images
            if include_images and url_data.image_urls:
                for image_url in url_data.image_urls:
                    image_elem = SubElement(url_elem, "image:image")
                    image_loc = SubElement(image_elem, "image:loc")
                    image_loc.text = image_url
                    
            # Alternate URLs
            if include_alternates and url_data.alternate_urls:
                for hreflang, alt_url in url_data.alternate_urls.items():
                    link_elem = SubElement(url_elem, "xhtml:link")
                    link_elem.set("rel", "alternate")
                    link_elem.set("hreflang", hreflang)
                    link_elem.set("href", alt_url)
                    
        # Convert to string
        xml_str = tostring(urlset, encoding="unicode")
        return f'<?xml version="1.0" encoding="UTF-8"?>\n{xml_str}'
        
    async def _generate_sitemap_index(self, sitemap_names: List[str], domain: str) -> str:
        """Generate sitemap index XML"""
        
        sitemapindex = Element("sitemapindex")
        sitemapindex.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
        
        for sitemap_name in sitemap_names:
            if sitemap_name != "sitemap.xml":  # Don't include self
                sitemap_elem = SubElement(sitemapindex, "sitemap")
                loc_elem = SubElement(sitemap_elem, "loc")
                loc_elem.text = f"https://{domain}/{sitemap_name}"
                
                lastmod_elem = SubElement(sitemap_elem, "lastmod")
                lastmod_elem.text = datetime.utcnow().strftime("%Y-%m-%d")
                
        xml_str = tostring(sitemapindex, encoding="unicode")
        return f'<?xml version="1.0" encoding="UTF-8"?>\n{xml_str}'
        
    async def _get_basic_robots_rules(self, business_type: str) -> List[RobotsRule]:
        """Get basic robots.txt rules"""
        
        rules = [
            # Allow all bots by default
            RobotsRule(
                user_agent="*",
                directive="Allow",
                path="/",
                comment="Allow all crawling by default"
            ),
            
            # Block admin areas
            RobotsRule(
                user_agent="*",
                directive="Disallow",
                path="/admin/",
                comment="Block admin areas"
            ),
            
            # Block search pages
            RobotsRule(
                user_agent="*",
                directive="Disallow",
                path="/search",
                comment="Block search result pages"
            ),
            
            # Block cart and checkout for ecommerce
            RobotsRule(
                user_agent="*",
                directive="Disallow",
                path="/cart",
                comment="Block shopping cart"
            ),
            
            RobotsRule(
                user_agent="*",
                directive="Disallow",
                path="/checkout",
                comment="Block checkout process"
            )
        ]
        
        if business_type == "ecommerce":
            # Additional ecommerce-specific rules
            ecommerce_rules = [
                RobotsRule(
                    user_agent="*",
                    directive="Disallow",
                    path="/account/",
                    comment="Block user accounts"
                ),
                RobotsRule(
                    user_agent="*",
                    directive="Disallow",
                    path="/*?sort=",
                    comment="Block sorting parameters"
                ),
                RobotsRule(
                    user_agent="*",
                    directive="Disallow",
                    path="/*?filter=",
                    comment="Block filter parameters"
                )
            ]
            rules.extend(ecommerce_rules)
            
        return rules
        
    async def _get_search_engine_specific_rules(self) -> List[RobotsRule]:
        """Get search engine specific rules"""
        
        return [
            # Allow Google to crawl CSS and JS for rendering
            RobotsRule(
                user_agent="Googlebot",
                directive="Allow",
                path="/css/",
                comment="Allow Googlebot to access CSS"
            ),
            RobotsRule(
                user_agent="Googlebot",
                directive="Allow",
                path="/js/",
                comment="Allow Googlebot to access JavaScript"
            ),
            
            # Block aggressive crawlers
            RobotsRule(
                user_agent="AhrefsBot",
                directive="Disallow",
                path="/",
                comment="Block Ahrefs bot"
            ),
            RobotsRule(
                user_agent="SemrushBot",
                directive="Disallow",
                path="/",
                comment="Block Semrush bot"
            )
        ]
        
    async def _crawl_internal_links(self, domain: str, max_pages: int) -> List[InternalLink]:
        """Crawl and extract internal links"""
        
        # Mock implementation
        links = []
        
        for i in range(min(max_pages, 100)):
            links.append(InternalLink(
                source_url=f"https://{domain}/page-{i}",
                target_url=f"https://{domain}/page-{i+1}",
                anchor_text=f"Link to page {i+1}",
                link_type="content",
                context=f"This is contextual text around the link to page {i+1}",
                relevance_score=0.8
            ))
            
        return links
        
    async def _generate_linking_suggestions(
        self,
        db: AsyncSession,
        internal_links: List[InternalLink],
        page_authority: Dict[str, float]
    ) -> List[LinkingSuggestion]:
        """Generate internal linking suggestions"""
        
        suggestions = []
        
        # Find pages with high authority that could link to lower authority pages
        high_authority_pages = {url: score for url, score in page_authority.items() if score > 0.7}
        low_authority_pages = {url: score for url, score in page_authority.items() if score < 0.3}
        
        for high_auth_url, high_score in list(high_authority_pages.items())[:10]:
            for low_auth_url, low_score in list(low_authority_pages.items())[:5]:
                # Check if link doesn't already exist
                existing_link = any(
                    link.source_url == high_auth_url and link.target_url == low_auth_url
                    for link in internal_links
                )
                
                if not existing_link:
                    suggestions.append(LinkingSuggestion(
                        source_page=high_auth_url,
                        target_page=low_auth_url,
                        suggested_anchor_text=await self._generate_anchor_text(low_auth_url),
                        reason=f"High authority page ({high_score:.2f}) linking to low authority page ({low_score:.2f})",
                        estimated_impact="high",
                        implementation_difficulty="easy",
                        priority_score=85.0
                    ))
                    
        return suggestions
        
    async def _chunk_list(self, lst: List, chunk_size: int) -> List[List]:
        """Split list into chunks"""
        for i in range(0, len(lst), chunk_size):
            yield lst[i:i + chunk_size]
            
    async def _generate_anchor_text(self, url: str) -> str:
        """Generate appropriate anchor text for URL"""
        # Extract meaningful part from URL
        path = urlparse(url).path
        if "/product/" in path:
            return "View Product Details"
        elif "/category/" in path:
            return "Browse Category"
        elif "/blog/" in path:
            return "Read More"
        else:
            return "Learn More"
