<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GridSpoke SEO Optimizer - Dashboard</title>
    
    <!-- Tailwind CSS v3 CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js v3 CDN -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="css/styles.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gridspoke-primary': '#3B82F6',
                        'gridspoke-secondary': '#8B5CF6',
                        'gridspoke-success': '#10B981',
                        'gridspoke-warning': '#F59E0B',
                        'gridspoke-error': '#EF4444',
                        'gridspoke-dark': '#1F2937',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Loading Authentication & Scripts -->
    <script src="js/auth.js?v=4"></script>
    <script src="js/websocket-v2.js?v=4"></script>
    <script src="js/dashboard-v3.js?v=4"></script>
    
    <!-- Main Dashboard Container -->
    <div x-data="dashboardApp()" x-init="init()">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <div class="h-8 w-8 bg-gridspoke-primary rounded-lg flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-xl font-bold text-gray-900">GridSpoke</span>
                        </div>
                        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                            <button @click="activeTab = 'dashboard'" 
                                    :class="activeTab === 'dashboard' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Dashboard
                            </button>
                            <button @click="activeTab = 'products'" 
                                    :class="activeTab === 'products' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Products
                            </button>
                            <button @click="activeTab = 'jobs'" 
                                    :class="activeTab === 'jobs' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Jobs
                            </button>
                            <button @click="activeTab = 'settings'" 
                                    :class="activeTab === 'settings' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Settings
                            </button>
                        </div>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:items-center">
                        <!-- Notifications -->
                        <button class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gridspoke-primary"
                                @click="showNotifications = !showNotifications">
                            <span class="sr-only">View notifications</span>
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5-5 5h5zm0 0v-4a6 6 0 00-12 0v4"></path>
                            </svg>
                        </button>
                        
                        <!-- Profile dropdown -->
                        <div class="ml-3 relative" x-data="{ open: false }">
                            <button @click="open = !open" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gridspoke-primary">
                                <span class="sr-only">Open user menu</span>
                                <div class="h-8 w-8 rounded-full bg-gridspoke-primary flex items-center justify-center text-white font-medium text-sm"
                                     x-text="user.name ? user.name.charAt(0).toUpperCase() : 'U'"></div>
                                <span class="ml-2 text-gray-700 text-sm font-medium" x-text="user.name || 'User'"></span>
                                <svg class="ml-1 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="py-10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Dashboard Tab -->
                <div x-show="activeTab === 'dashboard'">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                        <!-- Total Products -->
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                                            <dd class="text-lg font-medium text-gray-900" x-text="stats.totalProducts"></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Optimized Products -->
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Optimized</dt>
                                            <dd class="text-lg font-medium text-gray-900" x-text="stats.optimizedProducts"></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Active Jobs -->
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Active Jobs</dt>
                                            <dd class="text-lg font-medium text-gray-900" x-text="stats.activeJobs"></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SEO Score -->
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">SEO Score</dt>
                                            <dd class="text-lg font-medium text-gray-900" x-text="stats.averageSeoScore + '%'"></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <!-- Optimization Progress Chart -->
                        <div class="bg-white shadow rounded-lg p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Optimization Progress</h3>
                            <canvas id="progressChart" width="400" height="200"></canvas>
                        </div>

                        <!-- Job Status Distribution -->
                        <div class="bg-white shadow rounded-lg p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Job Status Distribution</h3>
                            <canvas id="statusChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
                            <div class="flow-root">
                                <ul class="-mb-8">
                                    <template x-for="(activity, index) in recentActivity" :key="index">
                                        <li>
                                            <div class="relative pb-8" :class="index < recentActivity.length - 1 ? 'pb-8' : ''">
                                                <div x-show="index < recentActivity.length - 1" class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                                                <div class="relative flex space-x-3">
                                                    <div>
                                                        <span class="h-8 w-8 rounded-full bg-gray-400 flex items-center justify-center ring-8 ring-white">
                                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </span>
                                                    </div>
                                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                        <div>
                                                            <p class="text-sm text-gray-500" x-text="activity.description"></p>
                                                        </div>
                                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                            <span x-text="activity.time"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </template>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Tab -->
                <div x-show="activeTab === 'products'">
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Products</h2>
                        <p class="text-gray-600">Product management coming soon...</p>
                    </div>
                </div>

                <!-- Jobs Tab -->
                <div x-show="activeTab === 'jobs'">
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Optimization Jobs</h2>
                        <p class="text-gray-600">Job management coming soon...</p>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div x-show="activeTab === 'settings'">
                    <div class="bg-white shadow rounded-lg p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Settings</h2>
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">API Base URL</label>
                                <input type="text" x-model="settings.apiBaseUrl" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">WebSocket URL</label>
                                <input type="text" x-model="settings.websocketUrl" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" x-model="settings.autoOptimize" class="h-4 w-4 text-gridspoke-primary">
                                <label class="ml-2 block text-sm text-gray-900">Auto-optimize new products</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" x-model="settings.emailNotifications" class="h-4 w-4 text-gridspoke-primary">
                                <label class="ml-2 block text-sm text-gray-900">Email notifications</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications Panel -->
        <div x-show="showNotifications" class="fixed inset-0 z-50 overflow-hidden" style="display: none;">
            <div class="absolute inset-0 bg-black bg-opacity-50" @click="showNotifications = false"></div>
            <div class="absolute right-0 top-0 h-full w-96 bg-white shadow-xl">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Notifications</h3>
                    <div x-show="notifications.length === 0" class="text-gray-500">No notifications</div>
                    <template x-for="notification in notifications" :key="notification.id">
                        <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                            <p class="text-sm text-gray-900" x-text="notification.message"></p>
                            <p class="text-xs text-gray-500 mt-1" x-text="notification.time"></p>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <!-- Initialize Application -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard loading...');
            
            // Check authentication
            if (typeof Auth !== 'undefined' && Auth.isAuthenticated && !Auth.isAuthenticated()) {
                console.log('User not authenticated, redirecting to login...');
                window.location.href = '/login.html';
                return;
            }
            
            console.log('Dashboard ready!');
        });
    </script>
</body>
</html>
