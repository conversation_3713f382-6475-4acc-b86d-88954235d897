// Dashboard App - Alpine.js Compatible
window.dashboardApp = function() {
    return {
        activeTab: 'dashboard',
        user: {},
        stats: {
            totalProducts: 0,
            optimizedProducts: 0,
            activeJobs: 0,
            averageSeoScore: 0
        },
        recentActivity: [],
        notifications: [],
        showNotifications: false,
        settings: {
            apiBaseUrl: '/api/v1',
            websocketUrl: 'ws://localhost:8000/api/v1/ws',
            autoOptimize: true,
            emailNotifications: true,
            scheduledOptimization: true,
            aiModel: 'gpt-4',
            customAiModel: '',
            visionModel: 'gpt-4-vision',
            customVisionModel: '',
            optimizationFrequency: 'weekly',
            openRouterApiKey: '',
            selectedStore: null,
            selectedStoreId: null,
            availableStores: []
        },

        async init() {
            console.log('Dashboard initializing...');
            
            // Wait for Auth to be ready
            if (typeof Auth === 'undefined') {
                console.log('Auth not ready, waiting...');
                setTimeout(() => this.init(), 100);
                return;
            }

            await Auth.init();
            
            if (!Auth.isAuthenticated()) {
                console.log('User not authenticated, redirecting to login');
                window.location.href = '/login.html';
                return;
            }

            this.user = Auth.getUser();
            console.log('User loaded:', this.user);

            // Load user settings
            await this.loadUserSettings();

            // Load dashboard data with error handling
            try {
                console.log('Starting loadStats...');
                await this.loadStats();
                console.log('loadStats completed, starting loadRecentActivity...');
                await this.loadRecentActivity();
                console.log('loadRecentActivity completed, starting loadStores...');
                await this.loadStores();
                console.log('loadStores completed, starting loadCharts...');
                await this.loadCharts();
                console.log('All data loading completed');
            } catch (error) {
                console.error('Error during data loading:', error);
            }
            
            // Connect WebSocket
            this.connectWebSocket();

            // Wire quick actions if present
            setTimeout(() => {
                const syncBtn = document.getElementById('qa-sync');
                const optBtn = document.getElementById('qa-optimize');
                if (syncBtn) syncBtn.addEventListener('click', () => this.loadProducts());
                if (optBtn) optBtn.addEventListener('click', () => this.addNotification('Bulk optimize coming soon'));
                if (typeof Products !== 'undefined' && document.getElementById('products-container')) {
                    Products.init();
                }
                if (typeof Jobs !== 'undefined' && document.getElementById('jobs-container')) {
                    Jobs.init();
                }
            }, 0);
            
            console.log('Dashboard initialized successfully');
        },

        switchTab(tab) {
            this.activeTab = tab;
            
            // Load data when switching to specific tabs
            if (tab === 'products') {
                this.loadProducts();
            } else if (tab === 'jobs') {
                this.loadJobs();
            }
        },

        async loadStats() {
            try {
                console.log('Loading dashboard stats...');
                const response = await fetch('/api/v1/dashboard/stats', {
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`
                    }
                });
                
                if (response.ok) {
                    this.stats = await response.json();
                    console.log('Stats loaded:', this.stats);
                } else {
                    console.error('Failed to load stats:', response.status);
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        },

        async loadRecentActivity() {
            try {
                console.log('Loading recent activity...');
                const storeId = this.settings.selectedStoreId || '';
                const qs = storeId ? `?store_id=${encodeURIComponent(storeId)}&limit=20` : '?limit=20';
                console.log('Fetching activity from:', `/api/v1/dashboard/activity${qs}`);
                const res = await fetch(`/api/v1/dashboard/activity${qs}`, {
                    headers: { 'Authorization': `Bearer ${Auth.getToken()}` }
                });
                if (res.ok) {
                    const data = await res.json();
                    console.log('Activity data received:', data);
                    // Normalize into expected shape
                    this.recentActivity = (data || []).map(a => ({
                        message: a.message || 'Activity',
                        timestamp: a.timestamp ? new Date(a.timestamp) : new Date()
                    }));
                    console.log('Activity loaded successfully, count:', this.recentActivity.length);
                } else {
                    console.error('Failed to load activity:', res.status);
                    // Clear activity data on failure to avoid showing stale mock data
                    this.recentActivity = [];
                }
            } catch (e) {
                console.error('Error loading activity:', e);
                // Clear activity data on error to avoid showing stale mock data  
                this.recentActivity = [];
            }
        },

        async loadStores() {
            try {
                console.log('Loading user stores...');
                const response = await fetch('/api/v1/stores/', {
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`
                    }
                });
                
                if (response.ok) {
                    this.settings.availableStores = await response.json();
                    console.log('Stores loaded:', this.settings.availableStores);
                    
                    // Find and set the selected store (prioritize WordPress stores)
                    if (this.settings.availableStores.length > 0) {
                        let selectedStore = this.settings.availableStores.find(store => 
                            store.name.toLowerCase().includes('wordpress') || 
                            store.platform === 'wordpress'
                        );
                        
                        if (!selectedStore) {
                            selectedStore = this.settings.availableStores[0];
                        }
                        
                        this.settings.selectedStore = selectedStore;
                        this.settings.selectedStoreId = selectedStore.id;
                        console.log('Selected store:', selectedStore);
                    }
                    // Refresh charts after stores are loaded
                    await this.loadCharts();
                } else {
                    console.error('Failed to load stores:', response.status);
                }
            } catch (error) {
                console.error('Error loading stores:', error);
            }
        },

        async loadUserSettings() {
            try {
                console.log('Loading user settings...');
                const response = await fetch('/api/v1/users/me/settings', {
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`
                    }
                });
                
                if (response.ok) {
                    const settings = await response.json();
                    console.log('User settings loaded:', settings);
                    
                    // Update settings with saved values
                    if (settings) {
                        this.settings.autoOptimize = settings.auto_optimize !== undefined ? settings.auto_optimize : this.settings.autoOptimize;
                        this.settings.emailNotifications = settings.email_notifications !== undefined ? settings.email_notifications : this.settings.emailNotifications;
                        this.settings.scheduledOptimization = settings.scheduled_optimization !== undefined ? settings.scheduled_optimization : this.settings.scheduledOptimization;
                        this.settings.aiModel = settings.ai_model || this.settings.aiModel;
                        this.settings.customAiModel = settings.custom_ai_model || this.settings.customAiModel;
                        this.settings.visionModel = settings.vision_model || this.settings.visionModel;
                        this.settings.customVisionModel = settings.custom_vision_model || this.settings.customVisionModel;
                        this.settings.optimizationFrequency = settings.optimization_frequency || this.settings.optimizationFrequency;
                        this.settings.openRouterApiKey = settings.openrouter_api_key || this.settings.openRouterApiKey;
                    }
                } else {
                    console.error('Failed to load user settings:', response.status);
                }
            } catch (error) {
                console.error('Error loading user settings:', error);
            }
        },

        updateSelectedStore() {
            const selectedStore = this.settings.availableStores.find(
                store => store.id === this.settings.selectedStoreId
            );
            
            if (selectedStore) {
                this.settings.selectedStore = selectedStore;
                console.log('Store switched to:', selectedStore);
                
                // Refresh dashboard data for the new store
                this.loadStats();
                this.loadRecentActivity();
                this.loadCharts();
                
                // If on products tab, reload products
                if (this.activeTab === 'products') {
                    this.loadProducts();
                }
                
                this.addNotification(`Switched to store: ${selectedStore.name}`);
            }
        },

        connectWebSocket() {
            try {
                const token = Auth.getToken();
                const wsUrl = `ws://localhost:8000/api/v1/ws?token=${token}`;
                console.log('Connecting to WebSocket:', wsUrl);
                
                if (typeof WebSocketManager !== 'undefined') {
                    const ws = new WebSocketManager(wsUrl);
                    
                    ws.on('open', () => {
                        console.log('WebSocket connection established');
                        this.addNotification('Connected to real-time updates');
                    });
                    
                    ws.on('stats_update', (data) => {
                        console.log('Stats update received:', data);
                        this.stats = { ...this.stats, ...data };
                        // opportunistically refresh charts too
                        this.loadCharts();
                    });
                    
                    ws.on('task_update', (data) => {
                        console.log('Task update received:', data);
                        const progressContainer = document.getElementById('job-progress-container');
                        if (progressContainer) {
                            progressContainer.innerHTML = `
                                <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md">
                                    <p class="font-bold">${data.message}</p>
                                    <p>${data.progress}% complete</p>
                                </div>
                            `;
                        }
                    });
                    
                    ws.on('error', (error) => {
                        console.error('WebSocket error:', error);
                    });
                    
                    ws.on('close', () => {
                        console.log('WebSocket connection closed');
                    });
                } else {
                    console.warn('WebSocketManager not available');
                }
            } catch (error) {
                console.error('Error connecting WebSocket:', error);
            }
        },

        async loadCharts() {
            try {
                const storeId = this.settings.selectedStoreId || '';
                const qs = storeId ? `?store_id=${encodeURIComponent(storeId)}` : '';

                // Progress series
                const progRes = await fetch(`/api/v1/dashboard/progress${qs ? qs + '&days=30' : '?days=30'}`, {
                    headers: { 'Authorization': `Bearer ${Auth.getToken()}` }
                });
                if (progRes.ok) {
                    const series = await progRes.json();
                    this.renderProgressChart(series.labels || [], series.data || []);
                }

                // Job status distribution
                const distRes = await fetch(`/api/v1/dashboard/job-status${qs}`, {
                    headers: { 'Authorization': `Bearer ${Auth.getToken()}` }
                });
                if (distRes.ok) {
                    const dist = await distRes.json();
                    this.renderJobStatusChart(dist || {});
                }
            } catch (e) {
                console.error('Error loading charts:', e);
            }
        },

        renderProgressChart(labels, data) {
            const ctx = document.getElementById('progressChart');
            if (!ctx) return;
            if (this._progressChart) this._progressChart.destroy();
            this._progressChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels,
                    datasets: [{
                        label: 'Products Optimized',
                        data,
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.15)',
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false }
            });
        },

        renderJobStatusChart(dist) {
            const ctx = document.getElementById('jobStatusChart') || document.getElementById('statusChart');
            if (!ctx) return;
            if (this._statusChart) this._statusChart.destroy();
            const labels = Object.keys(dist);
            const data = Object.values(dist);
            const colors = ['#F59E0B', '#10B981', '#3B82F6', '#EF4444', '#6B7280', '#8B5CF6', '#14B8A6'];
            this._statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels,
                    datasets: [{
                        data,
                        backgroundColor: labels.map((_, i) => colors[i % colors.length])
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false }
            });
        },

        addNotification(message) {
            this.notifications.unshift({
                id: Date.now(),
                message: message,
                timestamp: new Date()
            });
            
            // Keep only last 10 notifications
            if (this.notifications.length > 10) {
                this.notifications = this.notifications.slice(0, 10);
            }
        },

        removeNotification(id) {
            this.notifications = this.notifications.filter(n => n.id !== id);
        },

        formatTime(timestamp) {
            const now = new Date();
            const diff = now - timestamp;
            const minutes = Math.floor(diff / 60000);
            
            if (minutes < 1) return 'Just now';
            if (minutes < 60) return `${minutes}m ago`;
            
            const hours = Math.floor(minutes / 60);
            if (hours < 24) return `${hours}h ago`;
            
            const days = Math.floor(hours / 24);
            return `${days}d ago`;
        },

    // removed old loadProducts (unused)

    // removed duplicate renderProducts (use single definition below)

    // removed duplicate optimizeProduct (use single definition below)

        async loadJobs() {
            console.log('Loading jobs...');
            // Jobs loading will be implemented in jobs.js
        },

        async saveSettings() {
            console.log('Saving settings:', this.settings);
            this.addNotification('Settings saved successfully');
        },

        switchTab(tab) {
            this.activeTab = tab;
            
            // Load data when switching to specific tabs
            if (tab === 'products') {
                this.loadProducts();
            } else if (tab === 'jobs') {
                this.loadJobs();
            }
        },

    async loadProducts() {
            console.log('Loading products...');
            try {
                // First, get the user's stores to find the WordPress store
                const storesResponse = await fetch('/api/v1/stores/', {
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!storesResponse.ok) {
                    throw new Error(`Failed to fetch stores: ${storesResponse.status}`);
                }
                
                const storesData = await storesResponse.json();
                console.log('Stores loaded:', storesData);
                
                // Find WordPress store (prioritize it) or use the first store
                let targetStore = storesData.find(store => 
                    store.name.toLowerCase().includes('wordpress') || 
                    store.platform === 'wordpress'
                );
                
                if (!targetStore && storesData.length > 0) {
                    targetStore = storesData[0]; // Fallback to first store
                }
                
                if (!targetStore) {
                    this.renderProducts([]);
                    return;
                }
                
                console.log('Using store:', targetStore);
                
                // Now fetch products for the selected store
                const response = await fetch(`/api/v1/products/?store_id=${targetStore.id}`, {
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const products = await response.json();
                console.log('Products loaded:', products);
                this.renderProducts(products || []);
            } catch (error) {
                console.error('Error loading products:', error);
                document.getElementById('products-content').innerHTML = 
                    '<div class="text-red-600">Error loading products. Please try again.</div>';
            }
        },

        renderProducts(products) {
            const container = document.getElementById('products-content');
            if (!container) return;

            if (products.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-gray-500">No products found</p>
                        <p class="text-sm text-gray-400 mt-2">Sync your WordPress/WooCommerce store to see products here</p>
                    </div>
                `;
                return;
            }

            const productsHtml = products.map(product => `
                <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">${product.title || product.name || 'Unnamed Product'}</h4>
                            <p class="text-sm text-gray-500 mt-1">${product.sku || 'No SKU'}</p>
                            <p class="text-sm text-gray-600 mt-2">${(product.description || '').toString().substring(0, 100)}...</p>
                            <div class="flex items-center mt-3 space-x-4">
                                <span class="text-sm font-medium text-green-600">$${product.price || '0.00'}</span>
                                <span class="text-xs px-2 py-1 rounded-full ${product.status === 'optimized' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                    ${product.status === 'optimized' ? 'Optimized' : 'Pending'}
                                </span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <button @click="optimizeProduct('${product.id}')" 
                                    class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                Optimize
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = productsHtml;
        },

        async optimizeProduct(productId) {
            console.log('Optimizing product:', productId);
            try {
                const response = await fetch(`/api/v1/products/${productId}/optimize`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    this.addNotification('Product optimization started!');
                    this.loadProducts(); // Refresh products
                } else {
                    this.addNotification('Failed to start optimization', 'error');
                }
            } catch (error) {
                console.error('Error optimizing product:', error);
                this.addNotification('Error starting optimization', 'error');
            }
        },

        renderProducts(products) {
            const container = document.getElementById('products-content');
            if (!container) return;

            if (products.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-gray-500">No products found</p>
                        <p class="text-sm text-gray-400 mt-2">Sync your WordPress/WooCommerce store to see products here</p>
                    </div>
                `;
                return;
            }

            const productsHtml = products.map(product => `
                <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">${product.name || 'Unnamed Product'}</h4>
                            <p class="text-sm text-gray-500 mt-1">${product.sku || 'No SKU'}</p>
                            <p class="text-sm text-gray-600 mt-2">${product.description ? product.description.substring(0, 100) + '...' : 'No description'}</p>
                            <div class="flex items-center mt-3 space-x-4">
                                <span class="text-sm font-medium text-green-600">$${product.price || '0.00'}</span>
                                <span class="text-xs px-2 py-1 rounded-full ${product.status === 'optimized' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                    ${product.status === 'optimized' ? 'Optimized' : 'Pending'}
                                </span>
                                <span class="text-xs text-gray-500">Store: ${product.store_name || 'Unknown'}</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <button onclick="optimizeProduct('${product.id}')" 
                                    class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                Optimize
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = productsHtml;
        },

        async optimizeProduct(productId) {
            console.log('Optimizing product:', productId);
            try {
                const response = await fetch(`/api/v1/products/${productId}/optimize`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    this.addNotification('Product optimization started!');
                    this.loadProducts(); // Refresh products
                } else {
                    this.addNotification('Failed to start optimization', 'error');
                }
            } catch (error) {
                console.error('Error optimizing product:', error);
                this.addNotification('Error starting optimization', 'error');
            }
        },

        logout() {
            Auth.logout();
        },

        async saveSettings() {
            try {
                console.log('Saving settings...');
                
                // Validate custom AI model if selected
                if (this.settings.aiModel === 'custom' && this.settings.customAiModel) {
                    if (!this.validateCustomAiModel(this.settings.customAiModel)) {
                        this.addNotification('Invalid custom AI model name. Please use format: provider/model-name', 'error');
                        return;
                    }
                }
                
                // Validate custom vision model if selected
                if (this.settings.visionModel === 'custom' && this.settings.customVisionModel) {
                    if (!this.validateCustomAiModel(this.settings.customVisionModel)) {
                        this.addNotification('Invalid custom vision model name. Please use format: provider/model-name', 'error');
                        return;
                    }
                }
                
                // Prepare settings data to save
                const settingsData = {
                    auto_optimize: this.settings.autoOptimize,
                    email_notifications: this.settings.emailNotifications,
                    scheduled_optimization: this.settings.scheduledOptimization,
                    ai_model: this.settings.aiModel,
                    custom_ai_model: this.settings.customAiModel,
                    vision_model: this.settings.visionModel,
                    custom_vision_model: this.settings.customVisionModel,
                    optimization_frequency: this.settings.optimizationFrequency,
                    openrouter_api_key: this.settings.openRouterApiKey
                };
                
                const response = await fetch('/api/v1/users/me/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${Auth.getToken()}`
                    },
                    body: JSON.stringify(settingsData)
                });

                if (response.ok) {
                    this.addNotification('Settings saved successfully', 'success');
                } else {
                    throw new Error('Failed to save settings');
                }
            } catch (error) {
                console.error('Error saving settings:', error);
                this.addNotification('Failed to save settings', 'error');
            }
        },

        validateCustomAiModel(modelName) {
            // Safety check: Ensure the model name follows OpenRouter format (provider/model-name)
            // and doesn't contain potentially dangerous characters
            if (!modelName || typeof modelName !== 'string') {
                return false;
            }
            
            // Check length
            if (modelName.length > 100) {
                return false;
            }
            
            // Check for valid format: provider/model-name
            const modelRegex = /^[a-zA-Z0-9\-_]+\/[a-zA-Z0-9\-_.]+$/;
            if (!modelRegex.test(modelName)) {
                return false;
            }
            
            // Check for potentially dangerous patterns
            const dangerousPatterns = [
                '..', '<', '>', '"', "'", '`', '$', ';', '&', '|', 
                '(', ')', '[', ']', '{', '}', '\n', '\r', '\t'
            ];
            
            for (const pattern of dangerousPatterns) {
                if (modelName.includes(pattern)) {
                    return false;
                }
            }
            
            // Check that it's not trying to access local files or system resources
            const forbiddenPrefixes = ['file://', 'http://', 'https://', '/', '\\'];
            for (const prefix of forbiddenPrefixes) {
                if (modelName.toLowerCase().startsWith(prefix)) {
                    return false;
                }
            }
            
            return true;
        }
    };
};

// Global init function for Alpine.js
window.init = function() {
    console.log('Global init called');
    // This will be called by Alpine.js x-init
};