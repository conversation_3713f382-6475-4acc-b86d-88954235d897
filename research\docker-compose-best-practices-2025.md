# Docker Compose Best Practices for FastAPI + PostgreSQL + Redis + Celery + Nginx (2025)

## Research Summary
Comprehensive analysis of modern Docker Compose patterns for microservices architecture, based on official documentation and industry best practices as of August 2025.

## Key Findings

### 1. Modern Compose File Format
- **No version field required** - Latest Compose specification automatically used
- **Service dependencies** with health checks using `depends_on` with conditions
- **Named volumes** for data persistence
- **Custom networks** for service isolation
- **Environment variable management** via .env files

### 2. FastAPI Docker Optimization (2025)
- **Base Image**: Python 3.11+ for performance and security
- **Docker Cache Strategy**: Copy requirements.txt first, install deps, then copy code
- **Command Format**: Use exec form: `CMD ["fastapi", "run", "app/main.py", "--port", "80"]`
- **Proxy Headers**: Use `--proxy-headers` flag when behind reverse proxy
- **Single Process**: One process per container for orchestration compatibility

### 3. PostgreSQL + pgvector Setup
- **Version**: PostgreSQL 16 with pgvector extension
- **Image**: `pgvector/pgvector:pg16` for vector embeddings
- **Async Support**: SQLAlchemy 2.0 with asyncpg driver
- **Schema Design**: UUID primary keys, JSONB for flexible fields, vector columns for embeddings

### 4. Redis Configuration
- **Version**: Redis 7 Alpine for minimal footprint
- **Dual Role**: Message broker AND result backend for Celery
- **Persistence**: Named volume for data durability
- **Connection**: `redis://redis:6379/0` format

### 5. Celery Modern Patterns
- **Worker Configuration**: Separate container with same build context
- **Beat Scheduler**: Dedicated container for scheduled tasks
- **Task Organization**: Modular task files in workers/ directory
- **Result Backend**: Redis for task result storage and progress tracking

### 6. Nginx Reverse Proxy
- **Configuration**: Upstream blocks for API load balancing
- **Static Files**: Serve frontend from volume mount
- **Headers**: Proper proxy headers for FastAPI
- **SSL Ready**: Configuration for HTTPS termination

## Architecture Recommendations

### Service Dependencies
```yaml
# Proper dependency chain with health checks
api:
  depends_on:
    db:
      condition: service_healthy
    redis:
      condition: service_started
```

### Health Checks
```yaml
# PostgreSQL health check
healthcheck:
  test: ["CMD-SHELL", "pg_isready -U user -d dbname"]
  interval: 30s
  timeout: 10s
  retries: 5
```

### Network Isolation
```yaml
# Custom network for service communication
networks:
  app-network:
    driver: bridge
```

### Volume Management
```yaml
# Named volumes for persistence
volumes:
  postgres_data:
  redis_data:
```

## Security Best Practices

1. **Environment Variables**: All secrets via .env files
2. **Network Isolation**: Custom Docker networks
3. **Resource Limits**: Memory and CPU constraints
4. **User Permissions**: Non-root containers where possible
5. **Image Security**: Official base images with minimal attack surface

## Performance Optimizations

1. **Multi-stage Builds**: Separate build and runtime stages
2. **Layer Caching**: Optimize Dockerfile layer ordering
3. **Health Checks**: Prevent traffic to unhealthy services
4. **Resource Allocation**: Proper CPU/memory limits
5. **Connection Pooling**: Database connection optimization

## Development Workflow

```bash
# Standard development commands
docker-compose up -d                    # Start all services
docker-compose logs -f api              # View specific service logs
docker-compose exec api bash            # Access container shell
docker-compose down --volumes           # Clean shutdown with data removal
```

## Production Considerations

1. **Service Monitoring**: Health check endpoints
2. **Graceful Shutdown**: Proper signal handling
3. **Data Backup**: Volume backup strategies
4. **Scaling**: Horizontal scaling patterns
5. **Secrets Management**: External secret stores

## Sources
- Docker Compose Specification (Latest)
- FastAPI Docker Documentation
- tiangolo/full-stack-fastapi-template
- Celery Documentation
- PostgreSQL + pgvector Official Images
- Nginx Reverse Proxy Patterns
