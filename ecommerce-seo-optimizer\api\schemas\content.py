"""
Generated content schemas for request/response validation.
"""
import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, <PERSON>, ConfigDict
from enum import Enum


class ContentType(str, Enum):
    """Types of generated content."""
    TITLE = "title"
    DESCRIPTION = "description"
    META_TITLE = "meta_title"
    META_DESCRIPTION = "meta_description"
    KEYWORDS = "keywords"
    ALT_TEXT = "alt_text"
    PRODUCT_FEATURES = "product_features"
    CATEGORY_DESCRIPTION = "category_description"
    FAQ = "faq"
    BLOG_POST = "blog_post"
    SOCIAL_MEDIA = "social_media"
    AD_COPY = "ad_copy"


class ContentStatus(str, Enum):
    """Status of generated content."""
    GENERATED = "generated"
    APPROVED = "approved"
    REJECTED = "rejected"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    NEEDS_REVIEW = "needs_review"


class AIModel(str, Enum):
    """AI models used for content generation."""
    GPT_4 = "gpt-4"
    GPT_4_TURBO = "gpt-4-turbo"
    CLAUDE_3_OPUS = "claude-3-opus"
    CLAUDE_3_SONNET = "claude-3-sonnet"
    CLAUDE_3_HAIKU = "claude-3-haiku"
    GEMINI_PRO = "gemini-pro"
    MISTRAL_LARGE = "mistral-large"


class GeneratedContentBase(BaseModel):
    """Base generated content schema."""
    content_type: ContentType
    title: str = Field(..., min_length=1, max_length=255)
    content: str = Field(..., min_length=1)
    ai_model: AIModel
    language_code: str = Field("en", max_length=10)


class GeneratedContentCreate(GeneratedContentBase):
    """Schema for content creation."""
    product_id: uuid.UUID
    job_id: Optional[uuid.UUID] = None
    original_content: Optional[str] = None
    generation_parameters: Optional[Dict[str, Any]] = Field(default_factory=dict)
    confidence_score: Optional[float] = Field(None, ge=0, le=1)


class GeneratedContentUpdate(BaseModel):
    """Schema for content updates."""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    content: Optional[str] = Field(None, min_length=1)
    status: Optional[ContentStatus] = None
    reviewer_notes: Optional[str] = None
    approval_score: Optional[int] = Field(None, ge=1, le=10)


class GeneratedContentInDB(GeneratedContentBase):
    """Schema for content in database."""
    id: uuid.UUID
    product_id: uuid.UUID
    job_id: Optional[uuid.UUID] = None
    status: ContentStatus
    created_at: datetime
    updated_at: datetime
    published_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class GeneratedContent(GeneratedContentInDB):
    """Schema for content response."""
    pass


class GeneratedContentWithMetrics(GeneratedContent):
    """Content schema with performance metrics."""
    seo_score: Optional[float] = None
    word_count: int = 0
    character_count: int = 0
    click_through_rate: Optional[float] = None
    conversion_rate: Optional[float] = None
    quality_score: Optional[float] = None
    
    class Config:
        from_attributes = True


class ContentApprovalRequest(BaseModel):
    """Schema for content approval."""
    reviewer_notes: Optional[str] = Field(None, max_length=1000)
    approval_score: int = Field(..., ge=1, le=10)
    feedback_tags: Optional[List[str]] = Field(default_factory=list)


class ContentRejectionRequest(BaseModel):
    """Schema for content rejection."""
    reviewer_notes: str = Field(..., min_length=1, max_length=1000)
    feedback_tags: Optional[List[str]] = Field(default_factory=list)


class ContentAnalyticsUpdate(BaseModel):
    """Schema for updating content analytics."""
    click_through_rate: Optional[float] = Field(None, ge=0, le=1)
    conversion_rate: Optional[float] = Field(None, ge=0, le=1)
    engagement_score: Optional[float] = Field(None, ge=0, le=100)


class ContentGenerationRequest(BaseModel):
    """Schema for content generation request."""
    content_types: List[ContentType] = Field(..., min_length=1)
    target_keywords: Optional[List[str]] = Field(None, max_length=10)
    ai_model: AIModel = AIModel.CLAUDE_3_SONNET
    custom_instructions: Optional[str] = Field(None, max_length=1000)
    language_code: str = Field("en", max_length=10)
