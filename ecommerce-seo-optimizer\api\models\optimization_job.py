"""
Optimization job model for tracking bulk SEO optimization tasks.
"""
import uuid
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any
from enum import Enum
from sqlalchemy import String, Boolean, ForeignKey, Index, DateTime, Integer, Enum as SQLEnum, Numeric
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
from models.base import BaseModel
import structlog

logger = structlog.get_logger(__name__)


class JobType(str, Enum):
    """Types of optimization jobs."""
    PRODUCT_OPTIMIZATION = "product_optimization"
    TITLE_GENERATION = "title_generation"
    DESCRIPTION_GENERATION = "description_generation"
    META_OPTIMIZATION = "meta_optimization"
    KEYWORD_RESEARCH = "keyword_research"
    CONTENT_GENERATION = "content_generation"
    BULK_OPTIMIZATION = "bulk_optimization"
    VECTOR_EMBEDDING = "vector_embedding"


class JobStatus(str, Enum):
    """Job execution status."""
    PENDING = "pending"
    QUEUED = "queued"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class JobPriority(int, Enum):
    """Job priority levels."""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    URGENT = 10


class OptimizationJob(BaseModel):
    """Model for tracking SEO optimization jobs."""
    
    __tablename__ = "optimization_jobs"
    
    # Store relationship
    store_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("stores.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Job identification
    job_type: Mapped[JobType] = mapped_column(
        SQLEnum(JobType),
        nullable=False,
        index=True
    )
    
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="Human-readable job title"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        String(1000),
        nullable=True,
        comment="Job description and context"
    )
    
    # Job status and priority
    status: Mapped[JobStatus] = mapped_column(
        SQLEnum(JobStatus),
        default=JobStatus.PENDING,
        nullable=False,
        index=True
    )
    
    priority: Mapped[JobPriority] = mapped_column(
        SQLEnum(JobPriority),
        default=JobPriority.NORMAL,
        nullable=False,
        index=True
    )
    
    # Progress tracking
    total_items: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="Total number of items to process"
    )
    
    processed_items: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="Number of items successfully processed"
    )
    
    failed_items: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="Number of items that failed processing"
    )
    
    skipped_items: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="Number of items skipped"
    )
    
    # Job configuration
    parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB,
        nullable=True,
        default=dict,
        comment="Job-specific parameters and settings"
    )
    
    # Results and output
    results: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB,
        nullable=True,
        default=dict,
        comment="Job results and statistics"
    )
    
    # Error tracking
    error_log: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(
        JSONB,
        nullable=True,
        default=list,
        comment="Detailed error log for failed items"
    )
    
    last_error: Mapped[Optional[str]] = mapped_column(
        String(1000),
        nullable=True,
        comment="Last error message"
    )
    
    # Timing information
    started_at: Mapped[Optional[DateTime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        index=True
    )
    
    completed_at: Mapped[Optional[DateTime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        index=True
    )
    
    estimated_completion: Mapped[Optional[DateTime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="Estimated completion time"
    )
    
    # Resource usage
    tokens_used: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="Total AI tokens consumed"
    )
    
    api_calls_made: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="Total API calls made"
    )
    
    estimated_cost: Mapped[Optional[float]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="Estimated cost in USD"
    )
    
    # Scheduling
    scheduled_for: Mapped[Optional[DateTime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        comment="When to execute this job"
    )
    
    is_recurring: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="Whether this is a recurring job"
    )
    
    recurring_schedule: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="Cron expression for recurring jobs"
    )
    
    # Relationships
    store: Mapped["Store"] = relationship(
        "Store",
        back_populates="optimization_jobs"
    )
    
    generated_content: Mapped[List["GeneratedContent"]] = relationship(
        "GeneratedContent",
        back_populates="job",
        cascade="all, delete-orphan"
    )
    
    # Database indexes
    __table_args__ = (
        Index("ix_jobs_store_status", "store_id", "status"),
        Index("ix_jobs_type_status", "job_type", "status"),
        Index("ix_jobs_priority_created", "priority", "created_at"),
        Index("ix_jobs_scheduled", "scheduled_for", "is_recurring"),
        Index("ix_jobs_timing", "started_at", "completed_at"),
    )
    
    def __repr__(self) -> str:
        return f"<OptimizationJob(id={self.id}, type={self.job_type}, status={self.status})>"
    
    @property
    def progress_percentage(self) -> float:
        """Calculate job progress as percentage."""
        if self.total_items == 0:
            return 0.0
        return (self.processed_items / self.total_items) * 100
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate of processed items."""
        if self.processed_items == 0:
            return 0.0
        successful = self.processed_items - self.failed_items
        return (successful / self.processed_items) * 100
    
    @property
    def is_active(self) -> bool:
        """Check if job is currently active."""
        return self.status in [JobStatus.QUEUED, JobStatus.IN_PROGRESS]
    
    @property
    def is_finished(self) -> bool:
        """Check if job has finished (success or failure)."""
        return self.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Calculate job duration in seconds."""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.now(timezone.utc)
        return (end_time - self.started_at).total_seconds()
    
    def add_error(self, error_message: str, item_id: Optional[str] = None, context: Optional[Dict[str, Any]] = None) -> None:
        """Add an error to the error log."""
        if self.error_log is None:
            self.error_log = []
        
        error_record = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message": error_message,
            "item_id": item_id,
            "context": context or {}
        }
        
        self.error_log.append(error_record)
        self.last_error = error_message
        
        # Keep only last 100 errors
        if len(self.error_log) > 100:
            self.error_log = self.error_log[-100:]
        
        logger.error(
            "Job error recorded",
            job_id=str(self.id),
            error=error_message,
            item_id=item_id
        )
    
    def update_progress(self, processed: int = 1, failed: int = 0, skipped: int = 0) -> None:
        """Update job progress counters."""
        self.processed_items += processed
        self.failed_items += failed
        self.skipped_items += skipped
        
        logger.debug(
            "Job progress updated",
            job_id=str(self.id),
            progress=self.progress_percentage,
            processed=self.processed_items,
            failed=self.failed_items
        )
    
    def start(self) -> None:
        """Mark job as started."""
        self.status = JobStatus.IN_PROGRESS
        self.started_at = datetime.now(timezone.utc)
        
        logger.info(
            "Job started",
            job_id=str(self.id),
            type=self.job_type,
            total_items=self.total_items
        )
    
    def complete(self) -> None:
        """Mark job as completed."""
        self.status = JobStatus.COMPLETED
        self.completed_at = datetime.now(timezone.utc)
        
        logger.info(
            "Job completed",
            job_id=str(self.id),
            duration=self.duration_seconds,
            success_rate=self.success_rate
        )
    
    def fail(self, error_message: str) -> None:
        """Mark job as failed."""
        self.status = JobStatus.FAILED
        self.completed_at = datetime.now(timezone.utc)
        self.last_error = error_message
        
        logger.error(
            "Job failed",
            job_id=str(self.id),
            error=error_message,
            duration=self.duration_seconds
        )
    
    def estimate_completion_time(self, items_per_minute: float = 10.0) -> Optional[DateTime]:
        """Estimate when the job will complete based on current progress."""
        if self.total_items == 0 or self.processed_items == 0:
            return None
        
        remaining_items = self.total_items - self.processed_items
        if remaining_items <= 0:
            return datetime.now(timezone.utc)
        
        minutes_remaining = remaining_items / items_per_minute
        estimated_completion = datetime.now(timezone.utc) + timedelta(minutes=minutes_remaining)
        
        self.estimated_completion = estimated_completion
        return estimated_completion
