#!/usr/bin/env python3
"""
Test script to verify WordPress plugin connection to GridSpoke API
"""

import requests
import json

# API endpoint
api_endpoint = 'http://localhost:8000/'

# Test user credentials
email = '<EMAIL>'
password = 'test12345'

def make_request(method, endpoint, data=None, access_token=None):
    """
    Make HTTP request to GridSpoke API
    """
    url = api_endpoint + endpoint.lstrip('/')
    
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'GridSpoke-Test-Script/1.0'
    }
    
    # Add authentication header if token is provided
    if access_token:
        headers['Authorization'] = 'Bearer ' + access_token
    
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers, timeout=30)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=data, timeout=30)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
            
        return {
            'status_code': response.status_code,
            'body': response.json() if response.content else None
        }
    except requests.exceptions.RequestException as e:
        return {
            'status_code': 0,
            'body': {'error': str(e)}
        }

def authenticate(email, password):
    """
    Authenticate with email/password and get JWT tokens
    """
    payload = {
        'email': email,
        'password': password
    }
    
    response = make_request('POST', 'api/v1/auth/login', payload)
    
    if response['status_code'] == 200 and 'access_token' in response['body']:
        return {
            'success': True,
            'access_token': response['body']['access_token'],
            'refresh_token': response['body']['refresh_token'],
            'user': response['body']['user']
        }
    else:
        return {
            'success': False,
            'message': response['body'].get('detail', 'Authentication failed') if response['body'] else 'Authentication failed'
        }

def test_authenticated_connection(access_token):
    """
    Test authenticated connection
    """
    response = make_request('GET', 'api/v1/auth/me', None, access_token)
    
    return {
        'success': response['status_code'] == 200,
        'message': 'Authentication successful' if response['status_code'] == 200 else f'Authentication failed with status: {response["status_code"]}'
    }

if __name__ == "__main__":
    print("Testing basic API connection...")
    response = make_request('GET', 'health')
    if response['status_code'] == 200:
        print("[OK] Basic connection successful")
    else:
        print(f"[ERROR] Basic connection failed with status: {response['status_code']}")
        exit(1)

    print("\nTesting authentication...")
    auth_result = authenticate(email, password)
    if auth_result['success']:
        print("[OK] Authentication successful")
        print(f"Access token: {auth_result['access_token'][:20]}...")
    else:
        print(f"[ERROR] Authentication failed: {auth_result['message']}")
        exit(1)

    print("\nTesting authenticated connection...")
    auth_test = test_authenticated_connection(auth_result['access_token'])
    if auth_test['success']:
        print("[OK] Authenticated connection successful")
        print(f"User: {auth_result['user']['email']}")
    else:
        print(f"[ERROR] Authenticated connection failed: {auth_test['message']}")
        exit(1)

    print("\nAll tests passed! WordPress plugin should be able to connect to the GridSpoke API.")