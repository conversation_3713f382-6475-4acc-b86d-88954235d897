events {
    worker_connections 1024;
}

http {
    upstream php {
        server wordpress:9000;
    }

    server {
        listen 80;
        server_name localhost;
        root /var/www/html;
        index index.php;

        client_max_body_size 64M;

        location / {
            try_files $uri $uri/ /index.php$is_args$args;
        }

        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass php;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }

        location ~ /\.ht {
            deny all;
        }
    }
}