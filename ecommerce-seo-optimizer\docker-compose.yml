services:
  api:
    build: ./api
    container_name: gridspoke-api
    ports:
      - "8000:80"
    environment:
      - DATABASE_URL=postgresql+asyncpg://gridspoke_user:${POSTGRES_PASSWORD}@db:5432/gridspoke_db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=development
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - ./api:/code
    networks:
      - gridspoke-network
    restart: unless-stopped

  db:
    image: pgvector/pgvector:pg16
    container_name: gridspoke-db
    environment:
      - POSTGRES_DB=gridspoke_db
      - POSTGRES_USER=gridspoke_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gridspoke_user -d gridspoke_db"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - gridspoke-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: gridspoke-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - gridspoke-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  celery-worker:
    build: ./api
    container_name: gridspoke-worker
    command: celery -A workers.celery_app worker --loglevel=info --concurrency=4
    environment:
      - PYTHONPATH=/code
      - DATABASE_URL=postgresql+asyncpg://gridspoke_user:${POSTGRES_PASSWORD}@db:5432/gridspoke_db
      - REDIS_URL=redis://redis:6379/0
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=development
    depends_on:
      - db
      - redis
      - api
    volumes:
      - ./api:/code
    networks:
      - gridspoke-network
    restart: unless-stopped

  celery-beat:
    build: ./api
    container_name: gridspoke-scheduler
    command: celery -A workers.celery_app beat --loglevel=info
    environment:
      - PYTHONPATH=/code
      - DATABASE_URL=postgresql+asyncpg://gridspoke_user:${POSTGRES_PASSWORD}@db:5432/gridspoke_db
      - REDIS_URL=redis://redis:6379/0
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=development
    depends_on:
      - db
      - redis
      - api
    volumes:
      - ./api:/code
    networks:
      - gridspoke-network
    restart: unless-stopped

  nginx:
    build: ./nginx
    container_name: gridspoke-proxy
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - api
      - frontend
    volumes:
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - gridspoke-network
    restart: unless-stopped

  frontend:
    build: ./frontend
    container_name: gridspoke-frontend
    ports:
      - "3000:80"
    volumes:
      - ./frontend:/app
      - frontend_dist:/app/dist
    networks:
      - gridspoke-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  frontend_dist:
    driver: local
  nginx_logs:
    driver: local

networks:
  gridspoke-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
