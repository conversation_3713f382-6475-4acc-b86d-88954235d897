<?php
/**
 * WooCommerce Integration for GridSpoke SEO Connector
 * 
 * Handles all WooCommerce-specific functionality including
 * product sync, bulk actions, and content updates.
 */

if (!defined('ABSPATH')) {
    exit;
}

class GridSpoke_WooCommerce_Integration {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * API client instance
     */
    private $api_client;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->api_client = GridSpoke_API_Client::get_instance();
        $this->init_hooks();
    }
    
    /**
     * Initialize WooCommerce hooks
     */
    private function init_hooks() {
        // Product sync hooks
        add_action('woocommerce_new_product', array($this, 'sync_single_product'));
        add_action('woocommerce_update_product', array($this, 'sync_single_product'));
        
        // Bulk actions
        add_filter('bulk_actions-edit-product', array($this, 'add_bulk_actions'));
        add_filter('handle_bulk_actions-edit-product', array($this, 'handle_bulk_actions'), 10, 3);
        
        // Admin notices for bulk actions
        add_action('admin_notices', array($this, 'bulk_action_notices'));
        
        // Product meta box for optimization status
        add_action('add_meta_boxes', array($this, 'add_optimization_meta_box'));
        
        // Product list column
        add_filter('manage_edit-product_columns', array($this, 'add_optimization_column'));
        add_action('manage_product_posts_custom_column', array($this, 'display_optimization_column'), 10, 2);
        
        // AJAX handlers
        add_action('wp_ajax_gridspoke_optimize_product', array($this, 'ajax_optimize_product'));
        add_action('wp_ajax_gridspoke_sync_product', array($this, 'ajax_sync_product'));
        
        // Enqueue admin scripts
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Sync single product when created or updated
     */
    public function sync_single_product($product_id, $force = false) {
        $settings = GridSpoke_SEO_Connector::get_settings();

        // Only auto-sync if enabled (unless forced)
        if (!$force && empty($settings['auto_sync'])) {
            return false;
        }

        $product_data = $this->get_product_data($product_id);
        if (!$product_data) {
            return false;
        }

        $result = $this->api_client->sync_products(array($product_data), 'woocommerce');

        if ($result && !empty($result['created']) || !empty($result['updated'])) {
            // Store the GridSpoke product UUID if available
            if (!empty($result['product_mappings'])) {
                foreach ($result['product_mappings'] as $mapping) {
                    if ($mapping['external_id'] == $product_id) {
                        $this->api_client->store_product_uuid($product_id, $mapping['gridspoke_id']);
                        break;
                    }
                }
            }

            return array(
                'success' => true,
                'gridspoke_id' => get_post_meta($product_id, '_gridspoke_product_uuid', true)
            );
        }

        return false;
    }
    
    /**
     * Get formatted product data for API
     */
    public function get_product_data($product_id) {
        $product = wc_get_product($product_id);
        
        if (!$product) {
            return false;
        }
        
        // Get categories
        $categories = array();
        $terms = get_the_terms($product_id, 'product_cat');
        if ($terms && !is_wp_error($terms)) {
            foreach ($terms as $term) {
                $categories[] = $term->name;
            }
        }
        
        // Get tags
        $tags = array();
        $tag_terms = get_the_terms($product_id, 'product_tag');
        if ($tag_terms && !is_wp_error($tag_terms)) {
            foreach ($tag_terms as $term) {
                $tags[] = $term->name;
            }
        }
        
        // Get attributes
        $attributes = array();
        $product_attributes = $product->get_attributes();
        foreach ($product_attributes as $attribute) {
            if ($attribute->get_variation()) {
                continue; // Skip variation attributes
            }
            
            $attribute_name = wc_attribute_label($attribute->get_name());
            $attribute_values = wc_get_product_terms($product_id, $attribute->get_name(), array('fields' => 'names'));
            
            if (!empty($attribute_values)) {
                $attributes[$attribute_name] = $attribute_values;
            }
        }
        
        // Get images
        $images = array();
        $image_ids = array_merge(
            array($product->get_image_id()),
            $product->get_gallery_image_ids()
        );
        
        foreach (array_filter($image_ids) as $image_id) {
            $image_url = wp_get_attachment_image_url($image_id, 'full');
            $image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
            
            if ($image_url) {
                $images[] = array(
                    'id' => $image_id,
                    'url' => $image_url,
                    'alt' => $image_alt
                );
            }
        }
        
        return array(
            'id' => $product_id,
            'sku' => $product->get_sku(),
            'name' => $product->get_name(),
            'slug' => $product->get_slug(),
            'status' => $product->get_status(),
            'type' => $product->get_type(),
            'description' => $product->get_description(),
            'short_description' => $product->get_short_description(),
            'price' => $product->get_price(),
            'regular_price' => $product->get_regular_price(),
            'sale_price' => $product->get_sale_price(),
            'categories' => $categories,
            'tags' => $tags,
            'attributes' => $attributes,
            'images' => $images,
            'weight' => $product->get_weight(),
            'dimensions' => array(
                'length' => $product->get_length(),
                'width' => $product->get_width(),
                'height' => $product->get_height()
            ),
            'stock_status' => $product->get_stock_status(),
            'manage_stock' => $product->get_manage_stock(),
            'stock_quantity' => $product->get_stock_quantity(),
            'featured' => $product->is_featured(),
            'permalink' => get_permalink($product_id),
            'date_created' => $product->get_date_created()->date('c'),
            'date_modified' => $product->get_date_modified()->date('c'),
            'meta' => array(
                'focus_keyword' => get_post_meta($product_id, '_yoast_wpseo_focuskw', true),
                'meta_description' => get_post_meta($product_id, '_yoast_wpseo_metadesc', true),
                'meta_title' => get_post_meta($product_id, '_yoast_wpseo_title', true)
            )
        );
    }
    
    /**
     * Sync multiple products
     */
    public function sync_products($product_ids = null) {
        if ($product_ids === null) {
            // Get all published products
            $products = wc_get_products(array(
                'status' => 'publish',
                'limit' => -1,
                'return' => 'ids'
            ));
        } else {
            $products = $product_ids;
        }
        
        $product_data = array();
        foreach ($products as $product_id) {
            $data = $this->get_product_data($product_id);
            if ($data) {
                $product_data[] = $data;
            }
        }
        
        if (!empty($product_data)) {
            return $this->api_client->sync_products($product_data, 'woocommerce');
        }
        
        return false;
    }
    
    /**
     * Add bulk actions to product list
     */
    public function add_bulk_actions($actions) {
        $actions['gridspoke_optimize'] = __('Optimize with GridSpoke', 'gridspoke-seo');
        $actions['gridspoke_sync'] = __('Sync to GridSpoke', 'gridspoke-seo');
        return $actions;
    }
    
    /**
     * Handle bulk actions
     */
    public function handle_bulk_actions($redirect_url, $action, $post_ids) {
        if ($action === 'gridspoke_optimize') {
            $result = $this->api_client->request_optimization($post_ids);
            
            if ($result) {
                $redirect_url = add_query_arg('gridspoke_optimized', count($post_ids), $redirect_url);
                
                // Store optimization ID for tracking
                if (isset($result['optimization_id'])) {
                    set_transient('gridspoke_optimization_' . $result['optimization_id'], $post_ids, DAY_IN_SECONDS);
                }
            } else {
                $redirect_url = add_query_arg('gridspoke_error', 'optimization_failed', $redirect_url);
            }
        } elseif ($action === 'gridspoke_sync') {
            $result = $this->sync_products($post_ids);
            
            if ($result) {
                $redirect_url = add_query_arg('gridspoke_synced', count($post_ids), $redirect_url);
            } else {
                $redirect_url = add_query_arg('gridspoke_error', 'sync_failed', $redirect_url);
            }
        }
        
        return $redirect_url;
    }
    
    /**
     * Display bulk action notices
     */
    public function bulk_action_notices() {
        if (!empty($_REQUEST['gridspoke_optimized'])) {
            $count = intval($_REQUEST['gridspoke_optimized']);
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>' . sprintf(
                _n(
                    'Successfully requested optimization for %d product.',
                    'Successfully requested optimization for %d products.',
                    $count,
                    'gridspoke-seo'
                ),
                $count
            ) . '</p>';
            echo '</div>';
        }
        
        if (!empty($_REQUEST['gridspoke_synced'])) {
            $count = intval($_REQUEST['gridspoke_synced']);
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>' . sprintf(
                _n(
                    'Successfully synced %d product to GridSpoke.',
                    'Successfully synced %d products to GridSpoke.',
                    $count,
                    'gridspoke-seo'
                ),
                $count
            ) . '</p>';
            echo '</div>';
        }
        
        if (!empty($_REQUEST['gridspoke_error'])) {
            $error = sanitize_text_field($_REQUEST['gridspoke_error']);
            $messages = array(
                'optimization_failed' => __('Failed to request product optimization. Please check your API settings.', 'gridspoke-seo'),
                'sync_failed' => __('Failed to sync products to GridSpoke. Please check your API settings.', 'gridspoke-seo')
            );
            
            echo '<div class="notice notice-error is-dismissible">';
            echo '<p>' . ($messages[$error] ?? __('An unknown error occurred.', 'gridspoke-seo')) . '</p>';
            echo '</div>';
        }
    }
    
    /**
     * Add optimization meta box to product edit page
     */
    public function add_optimization_meta_box() {
        add_meta_box(
            'gridspoke-optimization',
            __('GridSpoke SEO Optimization', 'gridspoke-seo'),
            array($this, 'render_optimization_meta_box'),
            'product',
            'side',
            'default'
        );
    }
    
    /**
     * Render optimization meta box
     */
    public function render_optimization_meta_box($post) {
        $optimization_status = $this->get_optimization_status($post->ID);
        
        echo '<div id="gridspoke-optimization-status">';
        
        if ($optimization_status) {
            $status_class = $optimization_status['status'] === 'completed' ? 'success' : 'warning';
            echo '<p class="gridspoke-status ' . esc_attr($status_class) . '">';
            echo '<strong>' . __('Status:', 'gridspoke-seo') . '</strong> ' . esc_html(ucfirst($optimization_status['status']));
            echo '</p>';
            
            if (!empty($optimization_status['fields_optimized'])) {
                echo '<p><strong>' . __('Optimized Fields:', 'gridspoke-seo') . '</strong><br>';
                echo esc_html(implode(', ', $optimization_status['fields_optimized']));
                echo '</p>';
            }
            
            if (!empty($optimization_status['updated_at'])) {
                echo '<p><strong>' . __('Last Updated:', 'gridspoke-seo') . '</strong><br>';
                echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($optimization_status['updated_at'])));
                echo '</p>';
            }
        } else {
            echo '<p>' . __('No optimization data available.', 'gridspoke-seo') . '</p>';
        }
        
        echo '</div>';
        
        // Action buttons
        echo '<div class="gridspoke-actions">';
        echo '<button type="button" class="button button-secondary" id="gridspoke-sync-product" data-product-id="' . esc_attr($post->ID) . '">';
        echo __('Sync to GridSpoke', 'gridspoke-seo');
        echo '</button>';
        
        echo '<button type="button" class="button button-primary" id="gridspoke-optimize-product" data-product-id="' . esc_attr($post->ID) . '">';
        echo __('Request Optimization', 'gridspoke-seo');
        echo '</button>';
        echo '</div>';
        
        wp_nonce_field('gridspoke_product_actions', 'gridspoke_nonce');
    }
    
    /**
     * Add optimization column to product list
     */
    public function add_optimization_column($columns) {
        $new_columns = array();
        
        foreach ($columns as $key => $column) {
            $new_columns[$key] = $column;
            
            // Add our column after the title column
            if ($key === 'name') {
                $new_columns['gridspoke_status'] = __('GridSpoke Status', 'gridspoke-seo');
            }
        }
        
        return $new_columns;
    }
    
    /**
     * Display optimization column content
     */
    public function display_optimization_column($column, $post_id) {
        if ($column === 'gridspoke_status') {
            $status = $this->get_optimization_status($post_id);
            
            if ($status) {
                $status_text = ucfirst($status['status']);
                $status_class = $status['status'] === 'completed' ? 'completed' : 'pending';
                
                echo '<span class="gridspoke-status-badge ' . esc_attr($status_class) . '">';
                echo esc_html($status_text);
                echo '</span>';
                
                if ($status['status'] === 'completed' && !empty($status['fields_optimized'])) {
                    echo '<br><small>' . esc_html(implode(', ', $status['fields_optimized'])) . '</small>';
                }
            } else {
                echo '<span class="gridspoke-status-badge not-optimized">' . __('Not Optimized', 'gridspoke-seo') . '</span>';
            }
        }
    }
    
    /**
     * Get optimization status for a product
     */
    private function get_optimization_status($product_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        
        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE product_id = %d AND product_type = 'woocommerce' ORDER BY created_at DESC LIMIT 1",
                $product_id
            ),
            ARRAY_A
        );
        
        if ($result) {
            $result['fields_optimized'] = json_decode($result['fields_optimized'], true);
        }
        
        return $result;
    }
    
    /**
     * AJAX handler for single product optimization
     */
    public function ajax_optimize_product() {
        check_ajax_referer('gridspoke_product_actions', 'nonce');
        
        if (!current_user_can('edit_products')) {
            wp_die(__('Insufficient permissions', 'gridspoke-seo'));
        }
        
        $product_id = intval($_POST['product_id']);
        $result = $this->api_client->request_optimization(array($product_id));
        
        if ($result) {
            wp_send_json_success(array(
                'message' => __('Optimization requested successfully', 'gridspoke-seo'),
                'optimization_id' => $result['optimization_id'] ?? null
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to request optimization', 'gridspoke-seo')
            ));
        }
    }
    
    /**
     * AJAX handler for single product sync
     */
    public function ajax_sync_product() {
        check_ajax_referer('gridspoke_product_actions', 'nonce');
        
        if (!current_user_can('edit_products')) {
            wp_die(__('Insufficient permissions', 'gridspoke-seo'));
        }
        
        $product_id = intval($_POST['product_id']);
        $result = $this->sync_products(array($product_id));
        
        if ($result) {
            wp_send_json_success(array(
                'message' => __('Product synced successfully', 'gridspoke-seo')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to sync product', 'gridspoke-seo')
            ));
        }
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if (in_array($hook, array('post.php', 'post-new.php', 'edit.php'))) {
            $screen = get_current_screen();
            
            if ($screen && $screen->post_type === 'product') {
                wp_enqueue_script(
                    'gridspoke-woo-admin',
                    GRIDSPOKE_SEO_PLUGIN_URL . 'admin/assets/woocommerce-admin.js',
                    array('jquery'),
                    GRIDSPOKE_SEO_VERSION,
                    true
                );
                
                wp_localize_script('gridspoke-woo-admin', 'gridspoke_ajax', array(
                    'ajax_url' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('gridspoke_product_actions'),
                    'messages' => array(
                        'optimizing' => __('Requesting optimization...', 'gridspoke-seo'),
                        'syncing' => __('Syncing product...', 'gridspoke-seo'),
                        'error' => __('An error occurred. Please try again.', 'gridspoke-seo')
                    )
                ));
                
                wp_enqueue_style(
                    'gridspoke-woo-admin',
                    GRIDSPOKE_SEO_PLUGIN_URL . 'admin/assets/admin.css',
                    array(),
                    GRIDSPOKE_SEO_VERSION
                );
            }
        }
    }
    
    /**
     * Update product with optimized content
     */
    public function update_product_content($product_id, $optimized_data) {
        $product = wc_get_product($product_id);
        
        if (!$product) {
            return false;
        }
        
        $updated = false;
        
        // Update title
        if (!empty($optimized_data['title'])) {
            $product->set_name($optimized_data['title']);
            $updated = true;
        }
        
        // Update description
        if (!empty($optimized_data['description'])) {
            $product->set_description($optimized_data['description']);
            $updated = true;
        }
        
        // Update short description
        if (!empty($optimized_data['short_description'])) {
            $product->set_short_description($optimized_data['short_description']);
            $updated = true;
        }
        
        // Update meta description
        if (!empty($optimized_data['meta_description'])) {
            update_post_meta($product_id, '_yoast_wpseo_metadesc', $optimized_data['meta_description']);
            $updated = true;
        }
        
        // Update image alt texts
        if (!empty($optimized_data['image_alt_texts'])) {
            foreach ($optimized_data['image_alt_texts'] as $image_id => $alt_text) {
                update_post_meta($image_id, '_wp_attachment_image_alt', $alt_text);
            }
            $updated = true;
        }
        
        if ($updated) {
            $product->save();
            
            // Log the update
            GridSpoke_Logger::log(sprintf('Updated product %d with optimized content', $product_id), 'info');
            
            return true;
        }
        
        return false;
    }
}
