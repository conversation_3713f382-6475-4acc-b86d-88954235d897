<?php
/**
 * Tests for GridSpoke_WooCommerce_Integration class
 */

class Test_GridSpoke_WooCommerce_Integration extends GridSpoke_Test_Case {
    
    /**
     * WooCommerce integration instance
     */
    private $woo_integration;
    
    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();
        
        // Skip tests if WooCommerce is not available
        if (!class_exists('WooCommerce')) {
            $this->markTestSkipped('WooCommerce is not available');
        }
        
        $this->woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
    }
    
    /**
     * Test singleton instance
     */
    public function test_singleton_instance() {
        $instance1 = GridSpoke_WooCommerce_Integration::get_instance();
        $instance2 = GridSpoke_WooCommerce_Integration::get_instance();
        
        $this->assertSame($instance1, $instance2);
        $this->assertInstanceOf('GridSpoke_WooCommerce_Integration', $instance1);
    }
    
    /**
     * Test product data formatting
     */
    public function test_get_product_data() {
        $product_id = $this->create_test_woo_product(array(
            'name' => 'Test Product for Data Formatting',
            'description' => 'Detailed product description',
            'short_description' => 'Short description',
            'sku' => 'TEST-DATA-SKU',
            'price' => '29.99'
        ));
        
        $product_data = $this->woo_integration->get_product_data($product_id);
        
        $this->assertNotFalse($product_data);
        $this->assertEquals('Test Product for Data Formatting', $product_data['name']);
        $this->assertEquals('Detailed product description', $product_data['description']);
        $this->assertEquals('Short description', $product_data['short_description']);
        $this->assertEquals('TEST-DATA-SKU', $product_data['sku']);
        $this->assertEquals('29.99', $product_data['price']);
        $this->assertEquals($product_id, $product_data['id']);
        
        // Check required fields are present
        $this->assertArrayHasKey('categories', $product_data);
        $this->assertArrayHasKey('tags', $product_data);
        $this->assertArrayHasKey('images', $product_data);
        $this->assertArrayHasKey('permalink', $product_data);
    }
    
    /**
     * Test single product sync with auto-sync enabled
     */
    public function test_single_product_sync_auto() {
        // Enable auto-sync
        $this->update_plugin_settings(array(
            'auto_sync' => true,
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        // Set up API mocks
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_store_creation();
        $this->api_mock->mock_sync_success(1, 0);
        
        // Create product (should trigger auto-sync)
        $product_id = $this->create_test_woo_product();
        
        // Manually call sync since we're not in a real WordPress environment
        $result = $this->woo_integration->sync_single_product($product_id, true);
        
        $this->assertNotFalse($result);
        $this->assertTrue($result['success']);
        
        // Verify sync request was made
        $request = $this->api_mock->find_request('products/bulk/sync', 'POST');
        $this->assertNotNull($request);
    }
    
    /**
     * Test single product sync with auto-sync disabled
     */
    public function test_single_product_sync_disabled() {
        // Disable auto-sync
        $this->update_plugin_settings(array('auto_sync' => false));
        
        $product_id = $this->create_test_woo_product();
        $result = $this->woo_integration->sync_single_product($product_id);
        
        $this->assertFalse($result);
        
        // Verify no sync request was made
        $request = $this->api_mock->find_request('products/bulk/sync', 'POST');
        $this->assertNull($request);
    }
    
    /**
     * Test forced single product sync
     */
    public function test_forced_single_product_sync() {
        // Disable auto-sync but force sync
        $this->update_plugin_settings(array(
            'auto_sync' => false,
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        // Set up API mocks
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_store_creation();
        $this->api_mock->mock_sync_success(1, 0);
        
        $product_id = $this->create_test_woo_product();
        $result = $this->woo_integration->sync_single_product($product_id, true); // Force sync
        
        $this->assertNotFalse($result);
        $this->assertTrue($result['success']);
        
        // Verify sync request was made despite auto-sync being disabled
        $request = $this->api_mock->find_request('products/bulk/sync', 'POST');
        $this->assertNotNull($request);
    }
    
    /**
     * Test bulk product sync
     */
    public function test_bulk_product_sync() {
        // Set up authentication and mocks
        $this->update_plugin_settings(array(
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_store_creation();
        $this->api_mock->mock_sync_success(3, 0);
        
        // Create multiple test products
        $product_ids = array();
        for ($i = 1; $i <= 3; $i++) {
            $product_ids[] = $this->create_test_woo_product(array(
                'name' => "Bulk Test Product $i",
                'sku' => "BULK-TEST-$i"
            ));
        }
        
        $result = $this->woo_integration->sync_products($product_ids);
        
        $this->assertNotFalse($result);
        $this->assertEquals(3, $result['created']);
        
        // Verify bulk sync request was made
        $request = $this->api_mock->find_request('products/bulk/sync', 'POST');
        $this->assertNotNull($request);
        $this->assertCount(3, $request['data']);
    }
    
    /**
     * Test product content update after optimization
     */
    public function test_product_content_update() {
        $product_id = $this->create_test_woo_product(array(
            'name' => 'Original Product Name',
            'description' => 'Original description'
        ));
        
        $optimized_data = array(
            'name' => 'Optimized Product Name - SEO Enhanced',
            'description' => 'Optimized product description with better keywords and compelling copy.',
            'short_description' => 'Optimized short description',
            'meta_description' => 'Optimized meta description for better search visibility'
        );
        
        $result = $this->woo_integration->update_product_content($product_id, $optimized_data);
        
        $this->assertTrue($result);
        
        // Verify product was updated
        $product = wc_get_product($product_id);
        $this->assertEquals('Optimized Product Name - SEO Enhanced', $product->get_name());
        $this->assertEquals('Optimized product description with better keywords and compelling copy.', $product->get_description());
        $this->assertEquals('Optimized short description', $product->get_short_description());
        
        // Verify meta description was updated
        $meta_desc = get_post_meta($product_id, '_yoast_wpseo_metadesc', true);
        $this->assertEquals('Optimized meta description for better search visibility', $meta_desc);
    }
    
    /**
     * Test bulk actions registration
     */
    public function test_bulk_actions_registration() {
        $actions = array('edit' => 'Edit');
        $result = $this->woo_integration->add_bulk_actions($actions);
        
        $this->assertArrayHasKey('gridspoke_optimize', $result);
        $this->assertArrayHasKey('gridspoke_sync', $result);
        $this->assertEquals('Optimize with GridSpoke', $result['gridspoke_optimize']);
        $this->assertEquals('Sync to GridSpoke', $result['gridspoke_sync']);
    }
    
    /**
     * Test optimization status retrieval
     */
    public function test_get_optimization_status() {
        $product_id = $this->create_test_woo_product();
        
        // Initially should return false (no optimization data)
        $status = $this->woo_integration->get_optimization_status($product_id);
        $this->assertFalse($status);
        
        // Create optimization record
        global $wpdb;
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        
        $wpdb->insert($table_name, array(
            'product_id' => $product_id,
            'product_type' => 'woocommerce',
            'status' => 'completed',
            'fields_optimized' => json_encode(array('title', 'description')),
            'optimization_data' => json_encode(array('job_id' => 'test_job')),
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ));
        
        // Now should return optimization data
        $status = $this->woo_integration->get_optimization_status($product_id);
        $this->assertNotFalse($status);
        $this->assertEquals('completed', $status['status']);
        $this->assertContains('title', $status['fields_optimized']);
        $this->assertContains('description', $status['fields_optimized']);
    }
    
    /**
     * Test product column display
     */
    public function test_optimization_column() {
        $columns = array('title' => 'Title', 'price' => 'Price');
        $result = $this->woo_integration->add_optimization_column($columns);
        
        $this->assertArrayHasKey('gridspoke_status', $result);
        $this->assertEquals('GridSpoke Status', $result['gridspoke_status']);
        
        // Test that column is added after title
        $keys = array_keys($result);
        $title_index = array_search('title', $keys);
        $status_index = array_search('gridspoke_status', $keys);
        
        $this->assertEquals($title_index + 1, $status_index);
    }
}
