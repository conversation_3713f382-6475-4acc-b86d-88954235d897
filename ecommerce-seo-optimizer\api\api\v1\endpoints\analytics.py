"""
Analytics endpoints.
"""
from typing import Any
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, select

from core.database import get_db_session
from core.security import get_current_user
from models.user import User as UserModel
from models.product import Product
from models.store import Store
from models.optimization_job import OptimizationJob

router = APIRouter()


@router.get("/stats")
async def get_stats(
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get basic statistics."""
    total_products = await db.scalar(select(func.count(Product.id)))
    total_stores = await db.scalar(select(func.count(Store.id)))
    total_jobs = await db.scalar(select(func.count(OptimizationJob.id)))

    return {
        "total_products": total_products,
        "total_stores": total_stores,
        "total_jobs": total_jobs,
    }


from datetime import datetime, timedelta
from models.optimization_job import JobStatus

@router.get("/analytics")
async def get_analytics(
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get analytics data."""
    # Time series of optimizations per day for the last 30 days
    optimizations_per_day = await db.execute(
        select(func.date(OptimizationJob.created_at), func.count(OptimizationJob.id))
        .where(OptimizationJob.created_at >= datetime.utcnow() - timedelta(days=30))
        .group_by(func.date(OptimizationJob.created_at))
        .order_by(func.date(OptimizationJob.created_at))
    )
    optimizations_per_day = optimizations_per_day.all()

    # Distribution of optimization job statuses
    status_distribution = await db.execute(
        select(OptimizationJob.status, func.count(OptimizationJob.id))
        .group_by(OptimizationJob.status)
    )
    status_distribution = status_distribution.all()

    return {
        "optimizations_per_day": [
            {"day": day.isoformat(), "count": count} for day, count in optimizations_per_day
        ],
        "status_distribution": [
            {"status": status.value, "count": count} for status, count in status_distribution
        ],
    }
