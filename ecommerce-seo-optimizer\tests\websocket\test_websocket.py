# GridSpoke WebSocket Testing
"""
Comprehensive WebSocket testing for real-time optimization progress updates.
Tests connection lifecycle, message handling, and real-time notifications.
"""

import pytest
import asyncio
import json
from unittest.mock import MagicMock, AsyncMock, patch
from datetime import datetime


class TestWebSocketConnection:
    """Test WebSocket connection management"""
    
    @pytest.mark.asyncio
    async def test_websocket_connection_lifecycle(self):
        """Test WebSocket connection establishment and teardown"""
        try:
            from api.websocket.manager import WebSocketManager
            
            manager = WebSocketManager()
            
            # Mock WebSocket
            mock_websocket = MagicMock()
            mock_websocket.accept = AsyncMock()
            mock_websocket.send_text = AsyncMock()
            mock_websocket.receive_text = AsyncMock()
            mock_websocket.close = AsyncMock()
            
            user_id = "test_user_123"
            
            # Test connection
            await manager.connect(mock_websocket, user_id)
            assert user_id in manager.active_connections
            mock_websocket.accept.assert_called_once()
            
            # Test disconnection
            manager.disconnect(mock_websocket)
            assert user_id not in manager.active_connections
            
        except ImportError:
            pytest.skip("WebSocket manager not available")
    
    @pytest.mark.asyncio
    async def test_websocket_authentication(self):
        """Test WebSocket authentication with JWT tokens"""
        try:
            from api.websocket.auth import authenticate_websocket
            
            # Test valid token
            with patch('api.core.security.verify_token') as mock_verify:
                mock_verify.return_value = {"user_id": "user_123", "role": "user"}
                
                user_info = await authenticate_websocket("valid_jwt_token")
                assert user_info["user_id"] == "user_123"
                mock_verify.assert_called_once_with("valid_jwt_token")
            
            # Test invalid token
            with patch('api.core.security.verify_token') as mock_verify:
                mock_verify.side_effect = Exception("Invalid token")
                
                with pytest.raises(Exception):
                    await authenticate_websocket("invalid_token")
                    
        except ImportError:
            pytest.skip("WebSocket authentication not available")
    
    @pytest.mark.asyncio
    async def test_websocket_message_handling(self):
        """Test WebSocket message parsing and handling"""
        try:
            from api.websocket.handlers import handle_websocket_message
            
            mock_websocket = MagicMock()
            mock_websocket.send_text = AsyncMock()
            
            # Test ping message
            ping_message = {"type": "ping", "timestamp": datetime.utcnow().isoformat()}
            
            with patch('api.websocket.handlers.send_pong') as mock_pong:
                await handle_websocket_message(mock_websocket, json.dumps(ping_message))
                mock_pong.assert_called_once()
            
            # Test subscribe to job updates
            subscribe_message = {
                "type": "subscribe",
                "channel": "job_updates",
                "job_id": "job_123"
            }
            
            with patch('api.websocket.handlers.subscribe_to_job') as mock_subscribe:
                await handle_websocket_message(mock_websocket, json.dumps(subscribe_message))
                mock_subscribe.assert_called_once_with(mock_websocket, "job_123")
            
            # Test invalid message
            with pytest.raises(Exception):
                await handle_websocket_message(mock_websocket, "invalid_json")
                
        except ImportError:
            pytest.skip("WebSocket handlers not available")


class TestOptimizationProgressWebSocket:
    """Test real-time optimization progress updates via WebSocket"""
    
    @pytest.mark.asyncio
    async def test_job_progress_updates(self):
        """Test real-time job progress WebSocket updates"""
        try:
            from api.websocket.manager import WebSocketManager
            from workers.utils.progress_tracker import send_progress_update
            
            manager = WebSocketManager()
            
            # Setup mock WebSocket connection
            mock_websocket = MagicMock()
            mock_websocket.send_text = AsyncMock()
            user_id = "user_123"
            
            await manager.connect(mock_websocket, user_id)
            
            # Simulate job progress update
            progress_data = {
                "job_id": "job_456",
                "status": "processing",
                "progress": 50,
                "total_items": 100,
                "processed_items": 50,
                "current_item": "Product ABC",
                "estimated_completion": "2024-01-15T10:30:00Z"
            }
            
            await send_progress_update(user_id, progress_data)
            
            # Verify WebSocket message was sent
            mock_websocket.send_text.assert_called()
            sent_message = mock_websocket.send_text.call_args[0][0]
            sent_data = json.loads(sent_message)
            
            assert sent_data["type"] == "job_progress"
            assert sent_data["data"]["job_id"] == "job_456"
            assert sent_data["data"]["progress"] == 50
            
        except ImportError:
            pytest.skip("WebSocket progress tracking not available")
    
    @pytest.mark.asyncio
    async def test_bulk_optimization_real_time_updates(self):
        """Test bulk optimization with real-time WebSocket updates"""
        try:
            from workers.tasks.product_tasks import optimize_products_bulk
            from api.websocket.manager import WebSocketManager
            
            # Setup WebSocket manager
            manager = WebSocketManager()
            mock_websocket = MagicMock()
            mock_websocket.send_text = AsyncMock()
            user_id = "user_123"
            
            await manager.connect(mock_websocket, user_id)
            
            # Mock bulk optimization with progress updates
            with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
                with patch('workers.utils.progress_tracker.send_websocket_update') as mock_ws_update:
                    mock_optimize.return_value = {
                        "title": "Optimized Product",
                        "seo_score": 85
                    }
                    
                    product_ids = ["prod_1", "prod_2", "prod_3"]
                    
                    # Execute bulk optimization (mocked)
                    result = await asyncio.create_task(
                        asyncio.to_thread(
                            optimize_products_bulk.apply,
                            args=["store_123", product_ids, "full"]
                        )
                    )
                    
                    # Verify multiple progress updates were sent
                    assert mock_ws_update.call_count >= len(product_ids)
                    
                    # Verify update structure
                    for call in mock_ws_update.call_args_list:
                        update_data = call[0][1]  # Second argument is the data
                        assert "progress" in update_data
                        assert "status" in update_data
                        
        except ImportError:
            pytest.skip("Bulk optimization WebSocket updates not available")
    
    @pytest.mark.asyncio
    async def test_optimization_completion_notification(self):
        """Test WebSocket notification when optimization completes"""
        try:
            from api.websocket.notifications import send_completion_notification
            
            mock_websocket = MagicMock()
            mock_websocket.send_text = AsyncMock()
            
            completion_data = {
                "job_id": "job_789",
                "status": "completed",
                "total_items": 50,
                "successful_items": 48,
                "failed_items": 2,
                "completion_time": datetime.utcnow().isoformat(),
                "summary": {
                    "avg_seo_score": 87.5,
                    "total_cost": 12.34,
                    "processing_time": 300  # seconds
                }
            }
            
            await send_completion_notification("user_123", completion_data)
            
            # In real implementation, this would send via WebSocket manager
            # For testing, we verify the notification structure
            assert completion_data["status"] == "completed"
            assert completion_data["successful_items"] > 0
            assert "summary" in completion_data
            
        except ImportError:
            pytest.skip("WebSocket notifications not available")


class TestWebSocketErrorHandling:
    """Test WebSocket error handling and recovery"""
    
    @pytest.mark.asyncio
    async def test_websocket_connection_failure(self):
        """Test handling of WebSocket connection failures"""
        try:
            from api.websocket.manager import WebSocketManager
            
            manager = WebSocketManager()
            
            # Mock WebSocket that fails to connect
            mock_websocket = MagicMock()
            mock_websocket.accept = AsyncMock(side_effect=Exception("Connection failed"))
            
            # Should handle connection failure gracefully
            with pytest.raises(Exception):
                await manager.connect(mock_websocket, "user_123")
            
            # User should not be in active connections
            assert "user_123" not in manager.active_connections
            
        except ImportError:
            pytest.skip("WebSocket manager not available")
    
    @pytest.mark.asyncio
    async def test_websocket_message_send_failure(self):
        """Test handling when sending WebSocket message fails"""
        try:
            from api.websocket.manager import WebSocketManager
            
            manager = WebSocketManager()
            
            # Mock WebSocket that fails to send
            mock_websocket = MagicMock()
            mock_websocket.accept = AsyncMock()
            mock_websocket.send_text = AsyncMock(side_effect=Exception("Send failed"))
            
            user_id = "user_456"
            await manager.connect(mock_websocket, user_id)
            
            # Should handle send failure gracefully
            with pytest.raises(Exception):
                await manager.send_personal_message("Test message", user_id)
            
            # Connection might be removed after send failure
            # This depends on implementation details
            
        except ImportError:
            pytest.skip("WebSocket manager not available")
    
    @pytest.mark.asyncio
    async def test_websocket_disconnect_cleanup(self):
        """Test proper cleanup when WebSocket disconnects"""
        try:
            from api.websocket.manager import WebSocketManager
            
            manager = WebSocketManager()
            
            mock_websocket = MagicMock()
            mock_websocket.accept = AsyncMock()
            user_id = "user_789"
            
            # Connect
            await manager.connect(mock_websocket, user_id)
            assert user_id in manager.active_connections
            
            # Simulate sudden disconnection
            manager.disconnect(mock_websocket)
            assert user_id not in manager.active_connections
            
            # Verify no orphaned connections
            assert len(manager.active_connections) == 0
            
        except ImportError:
            pytest.skip("WebSocket manager not available")


class TestWebSocketBroadcasting:
    """Test WebSocket broadcasting and multi-user scenarios"""
    
    @pytest.mark.asyncio
    async def test_broadcast_to_all_users(self):
        """Test broadcasting messages to all connected users"""
        try:
            from api.websocket.manager import WebSocketManager
            
            manager = WebSocketManager()
            
            # Setup multiple mock WebSocket connections
            users = []
            for i in range(3):
                mock_websocket = MagicMock()
                mock_websocket.accept = AsyncMock()
                mock_websocket.send_text = AsyncMock()
                user_id = f"user_{i}"
                
                await manager.connect(mock_websocket, user_id)
                users.append((user_id, mock_websocket))
            
            # Broadcast message
            broadcast_message = {
                "type": "system_announcement",
                "message": "Scheduled maintenance in 30 minutes"
            }
            
            await manager.broadcast(json.dumps(broadcast_message))
            
            # Verify all users received the message
            for user_id, mock_websocket in users:
                mock_websocket.send_text.assert_called_with(json.dumps(broadcast_message))
            
        except ImportError:
            pytest.skip("WebSocket manager not available")
    
    @pytest.mark.asyncio
    async def test_user_specific_messaging(self):
        """Test sending messages to specific users only"""
        try:
            from api.websocket.manager import WebSocketManager
            
            manager = WebSocketManager()
            
            # Setup multiple users
            target_user = "target_user"
            other_user = "other_user"
            
            target_ws = MagicMock()
            target_ws.accept = AsyncMock()
            target_ws.send_text = AsyncMock()
            
            other_ws = MagicMock()
            other_ws.accept = AsyncMock()
            other_ws.send_text = AsyncMock()
            
            await manager.connect(target_ws, target_user)
            await manager.connect(other_ws, other_user)
            
            # Send message to target user only
            personal_message = "Your optimization job is complete!"
            await manager.send_personal_message(personal_message, target_user)
            
            # Verify only target user received message
            target_ws.send_text.assert_called_with(personal_message)
            other_ws.send_text.assert_not_called()
            
        except ImportError:
            pytest.skip("WebSocket manager not available")


class TestWebSocketIntegrationWithTasks:
    """Test WebSocket integration with Celery tasks"""
    
    @pytest.mark.asyncio
    async def test_task_progress_websocket_integration(self):
        """Test Celery task sending progress via WebSocket"""
        try:
            from workers.tasks.product_tasks import optimize_single_product
            from api.websocket.manager import WebSocketManager
            
            # Setup WebSocket connection
            manager = WebSocketManager()
            mock_websocket = MagicMock()
            mock_websocket.accept = AsyncMock()
            mock_websocket.send_text = AsyncMock()
            user_id = "task_user"
            
            await manager.connect(mock_websocket, user_id)
            
            # Mock task execution with WebSocket updates
            with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
                with patch('workers.utils.websocket_notifier.notify_progress') as mock_notify:
                    mock_optimize.return_value = {
                        "title": "Optimized Title",
                        "seo_score": 90
                    }
                    
                    # Execute task
                    result = await asyncio.create_task(
                        asyncio.to_thread(
                            optimize_single_product.apply,
                            args=["product_123", "store_456", "full"]
                        )
                    )
                    
                    # Verify WebSocket notification was triggered
                    mock_notify.assert_called()
                    
        except ImportError:
            pytest.skip("Task WebSocket integration not available")
    
    @pytest.mark.asyncio
    async def test_task_error_websocket_notification(self):
        """Test WebSocket notification when task fails"""
        try:
            from workers.tasks.product_tasks import optimize_single_product
            
            with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
                with patch('workers.utils.websocket_notifier.notify_error') as mock_notify_error:
                    # Simulate AI service failure
                    mock_optimize.side_effect = Exception("OpenRouter API Error")
                    
                    # Execute task (should fail)
                    with pytest.raises(Exception):
                        await asyncio.create_task(
                            asyncio.to_thread(
                                optimize_single_product.apply,
                                args=["product_123", "store_456", "full"]
                            )
                        )
                    
                    # Verify error notification was sent
                    mock_notify_error.assert_called()
                    
        except ImportError:
            pytest.skip("Task error WebSocket integration not available")


class TestWebSocketPerformance:
    """Test WebSocket performance under load"""
    
    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_multiple_concurrent_connections(self):
        """Test handling multiple concurrent WebSocket connections"""
        try:
            from api.websocket.manager import WebSocketManager
            
            manager = WebSocketManager()
            connections = []
            
            # Create 100 concurrent connections
            for i in range(100):
                mock_websocket = MagicMock()
                mock_websocket.accept = AsyncMock()
                mock_websocket.send_text = AsyncMock()
                user_id = f"perf_user_{i}"
                
                await manager.connect(mock_websocket, user_id)
                connections.append((user_id, mock_websocket))
            
            # Verify all connections are tracked
            assert len(manager.active_connections) == 100
            
            # Test broadcasting to all connections
            start_time = asyncio.get_event_loop().time()
            await manager.broadcast("Performance test message")
            end_time = asyncio.get_event_loop().time()
            
            # Should complete broadcast quickly (under 1 second)
            assert (end_time - start_time) < 1.0
            
            # Verify all connections received the message
            for user_id, mock_websocket in connections:
                mock_websocket.send_text.assert_called()
            
            # Cleanup
            for user_id, mock_websocket in connections:
                manager.disconnect(mock_websocket)
            
            assert len(manager.active_connections) == 0
            
        except ImportError:
            pytest.skip("WebSocket manager not available")
    
    @pytest.mark.asyncio
    async def test_high_frequency_messages(self):
        """Test handling high-frequency WebSocket messages"""
        try:
            from api.websocket.manager import WebSocketManager
            
            manager = WebSocketManager()
            
            mock_websocket = MagicMock()
            mock_websocket.accept = AsyncMock()
            mock_websocket.send_text = AsyncMock()
            user_id = "high_freq_user"
            
            await manager.connect(mock_websocket, user_id)
            
            # Send 1000 messages rapidly
            start_time = asyncio.get_event_loop().time()
            
            for i in range(1000):
                await manager.send_personal_message(f"Message {i}", user_id)
            
            end_time = asyncio.get_event_loop().time()
            
            # Should handle high frequency well (under 5 seconds)
            assert (end_time - start_time) < 5.0
            
            # Verify all messages were sent
            assert mock_websocket.send_text.call_count == 1000
            
        except ImportError:
            pytest.skip("WebSocket manager not available")


# Test Fixtures for WebSocket Testing
@pytest.fixture
async def websocket_manager():
    """Fixture providing WebSocket manager instance"""
    try:
        from api.websocket.manager import WebSocketManager
        return WebSocketManager()
    except ImportError:
        pytest.skip("WebSocket manager not available")


@pytest.fixture
async def mock_websocket():
    """Fixture providing mock WebSocket connection"""
    mock_websocket = MagicMock()
    mock_websocket.accept = AsyncMock()
    mock_websocket.send_text = AsyncMock()
    mock_websocket.receive_text = AsyncMock()
    mock_websocket.close = AsyncMock()
    return mock_websocket


@pytest.fixture
async def connected_websocket_user(websocket_manager, mock_websocket):
    """Fixture providing connected WebSocket user"""
    user_id = "test_ws_user"
    await websocket_manager.connect(mock_websocket, user_id)
    return user_id, mock_websocket


# Integration Test Example
@pytest.mark.asyncio
async def test_full_websocket_optimization_workflow(websocket_manager, mock_websocket):
    """
    Complete integration test: WebSocket connection → optimization job → 
    real-time progress → completion notification
    """
    try:
        user_id = "integration_user"
        await websocket_manager.connect(mock_websocket, user_id)
        
        # Start optimization job
        with patch('workers.tasks.product_tasks.optimize_products_bulk.delay') as mock_task:
            with patch('workers.utils.progress_tracker.send_websocket_update') as mock_progress:
                mock_task.return_value = MagicMock(id="task_123")
                
                # Simulate job creation and progress updates
                job_data = {
                    "job_id": "job_integration",
                    "status": "processing",
                    "progress": 0
                }
                
                # Send initial status
                await websocket_manager.send_personal_message(
                    json.dumps({"type": "job_started", "data": job_data}), 
                    user_id
                )
                
                # Simulate progress updates
                for progress in [25, 50, 75, 100]:
                    job_data["progress"] = progress
                    job_data["status"] = "completed" if progress == 100 else "processing"
                    
                    await websocket_manager.send_personal_message(
                        json.dumps({"type": "job_progress", "data": job_data}), 
                        user_id
                    )
                
                # Verify messages were sent
                assert mock_websocket.send_text.call_count >= 5  # Initial + 4 progress updates
                
    except ImportError:
        pytest.skip("WebSocket integration test not available")
