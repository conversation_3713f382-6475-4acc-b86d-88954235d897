import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from core.database import get_db_session
from crud.user import user_crud

async def delete_test_user():
    db: AsyncSession = await anext(get_db_session())
    try:
        user = await user_crud.get_by_email(db, email="<EMAIL>")
        if user:
            await user_crud.remove(db, id=user.id)
            print(f"User {user.email} deleted successfully.")
        else:
            print("User not found.")
    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(delete_test_user())
