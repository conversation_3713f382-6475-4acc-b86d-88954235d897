# GridSpoke User Guide

## Getting Started with GridSpoke

GridSpoke is an AI-powered SEO optimization service that automatically improves your ecommerce product listings for better search engine visibility and higher conversion rates.

## Table of Contents

1. [Account Setup](#account-setup)
2. [Dashboard Overview](#dashboard-overview)
3. [Connecting Your Store](#connecting-your-store)
4. [Product Optimization](#product-optimization)
5. [Monitoring Progress](#monitoring-progress)
6. [Analytics & Reports](#analytics--reports)
7. [Settings & Configuration](#settings--configuration)
8. [Troubleshooting](#troubleshooting)

## Account Setup

### Creating Your Account

1. **Visit the Registration Page**
   - Navigate to `https://your-gridspoke-domain.com/register`
   - Fill in your business information:
     - Email address (used for login)
     - Secure password
     - Full name
     - Company name
     - Store URL (optional)

2. **Email Verification**
   - Check your email for a verification link
   - Click the link to activate your account
   - You'll be redirected to the dashboard

3. **Initial Setup**
   - Complete your profile information
   - Set up billing preferences
   - Choose your optimization preferences

### Account Security

- **Two-Factor Authentication**: Enable 2FA in account settings for enhanced security
- **API Keys**: Generate API keys for programmatic access
- **Password Policy**: Use strong passwords with at least 12 characters

## Dashboard Overview

### Main Dashboard

The GridSpoke dashboard provides a comprehensive view of your SEO optimization activities:

**Key Metrics Display**:
- Total products in your store
- Products optimized this month
- Average SEO score improvement
- Current optimization queue status
- Recent optimization results

**Quick Actions**:
- Start new optimization job
- View recent activity
- Access analytics reports
- Manage store connections

### Navigation Menu

- **Dashboard**: Main overview and metrics
- **Stores**: Manage store connections
- **Products**: Browse and manage product listings
- **Jobs**: View optimization job history and status
- **Analytics**: SEO performance reports and insights
- **Settings**: Account and optimization preferences

## Connecting Your Store

GridSpoke supports multiple ecommerce platforms. Here's how to connect your store:

### WooCommerce Integration

1. **Generate API Keys in WooCommerce**:
   - Go to WooCommerce → Settings → Advanced → REST API
   - Click "Add Key"
   - Set permissions to "Read/Write"
   - Copy the Consumer Key and Consumer Secret

2. **Add Store in GridSpoke**:
   - Navigate to Stores → Add New Store
   - Select "WooCommerce" as platform
   - Enter your store information:
     - Store name (for identification)
     - Store URL (e.g., `https://yourstore.com`)
     - Consumer Key (from WooCommerce)
     - Consumer Secret (from WooCommerce)
   - Click "Test Connection" to verify
   - Save the store configuration

3. **Initial Product Sync**:
   - GridSpoke will automatically sync your products
   - This may take a few minutes depending on product count
   - You'll receive a notification when sync is complete

### SureCart Integration

1. **Generate API Credentials in SureCart**:
   - Go to Settings → Integrations → API Keys
   - Create a new API key with product access
   - Copy the API key and secret

2. **Connect to GridSpoke**:
   - Follow similar steps as WooCommerce
   - Select "SureCart" as platform
   - Enter your SureCart credentials

### Shopify Integration

1. **Install GridSpoke App** (if available):
   - Visit Shopify App Store
   - Search for "GridSpoke"
   - Install and authorize the app

2. **Manual Integration**:
   - Create private app in Shopify admin
   - Generate API credentials
   - Connect through GridSpoke dashboard

## Product Optimization

### Understanding SEO Optimization

GridSpoke uses advanced AI to improve various aspects of your product listings:

**Optimization Types**:
- **Product Titles**: SEO-friendly titles with target keywords
- **Meta Descriptions**: Compelling descriptions for search results
- **Product Descriptions**: Detailed, keyword-rich content
- **Image Alt Text**: Descriptive alt text for better accessibility
- **Schema Markup**: Structured data for rich snippets
- **Keyword Optimization**: Strategic keyword placement

### Starting an Optimization Job

1. **Select Products**:
   - Navigate to Products section
   - Use filters to select products:
     - By category (e.g., Electronics, Clothing)
     - By optimization status (Pending, Optimized, Failed)
     - By SEO score range
     - By last optimization date
   - Select specific products or choose "Select All"

2. **Configure Optimization Settings**:
   - **Optimization Type**: Choose what to optimize
     - Titles only
     - Descriptions only
     - Full optimization (recommended)
     - Custom selection
   - **AI Model**: Select AI model (GPT-4, Claude, etc.)
   - **Target Keywords**: Specify keywords to focus on
   - **Brand Guidelines**: Upload brand voice guidelines
   - **Custom Instructions**: Add specific requirements

3. **Review and Start**:
   - Review selected products and settings
   - Check estimated cost and processing time
   - Click "Start Optimization"
   - Monitor progress in real-time

### Optimization Settings

**AI Model Selection**:
- **GPT-4**: Best quality, higher cost
- **GPT-3.5**: Good balance of quality and cost
- **Claude**: Excellent for creative content
- **Custom Models**: Enterprise-specific models

**Keyword Strategy**:
- **Automatic**: AI selects optimal keywords
- **Manual**: Specify target keywords
- **Competitor-Based**: Analyze competitor keywords
- **Trending**: Use trending search terms

**Content Guidelines**:
- Set brand voice and tone
- Specify prohibited words or phrases
- Define target audience
- Set content length preferences

## Monitoring Progress

### Real-Time Progress Tracking

GridSpoke provides real-time updates on optimization jobs:

**Job Dashboard**:
- Overall progress percentage
- Products processed vs. total
- Current product being optimized
- Estimated completion time
- Live cost tracking

**Progress Notifications**:
- Browser notifications for job completion
- Email updates for major milestones
- SMS alerts for urgent issues (optional)

### Job History

View detailed history of all optimization jobs:

**Job Details**:
- Start and completion times
- Products processed successfully
- Failed optimizations with reasons
- Cost breakdown
- Before/after comparisons
- SEO score improvements

**Filtering and Search**:
- Filter by date range
- Search by store or product name
- Sort by success rate or improvement score

## Analytics & Reports

### SEO Performance Dashboard

**Overview Metrics**:
- Average SEO score across all products
- Monthly optimization trends
- Cost per optimization
- ROI calculations
- Traffic impact estimates

**Category Analysis**:
- Performance by product category
- Best and worst performing categories
- Optimization recommendations
- Keyword performance by category

### Detailed Reports

**Monthly SEO Report**:
- Comprehensive monthly performance summary
- Before/after comparisons
- Keyword ranking improvements
- Traffic and conversion impact
- Cost analysis and ROI

**Product Performance Report**:
- Individual product SEO scores
- Optimization history
- Keyword performance
- Competitor comparison
- Improvement recommendations

**Store Health Audit**:
- Overall store SEO health score
- Critical issues identification
- Priority improvement recommendations
- Implementation timeline
- Cost estimates for fixes

### Custom Reports

Create custom reports for specific needs:

**Report Builder**:
- Select metrics and dimensions
- Choose date ranges
- Apply filters (category, brand, etc.)
- Schedule automated delivery
- Export in multiple formats (PDF, CSV, Excel)

**Automated Insights**:
- AI-generated insights and recommendations
- Anomaly detection
- Trend identification
- Performance predictions

## Settings & Configuration

### Account Settings

**Profile Information**:
- Update contact details
- Change password
- Manage notification preferences
- Configure timezone settings

**Billing & Usage**:
- View current plan and usage
- Update payment methods
- Download invoices
- Set usage alerts and limits

### Optimization Preferences

**Default Settings**:
- Set default AI model preferences
- Configure automatic optimization schedules
- Set quality thresholds
- Define approval workflows

**Brand Guidelines**:
- Upload brand style guides
- Define brand voice and tone
- Set content restrictions
- Configure approval processes

**Integration Settings**:
- Manage store connections
- Configure webhook endpoints
- Set sync frequencies
- Configure backup settings

### Team Management

**User Roles** (Enterprise):
- Admin: Full access to all features
- Manager: Can manage optimizations and view reports
- Editor: Can run optimizations with approval
- Viewer: Read-only access to reports

**Permissions**:
- Store access permissions
- Budget and spending limits
- Approval requirements
- Notification settings

## Best Practices

### Optimization Strategy

1. **Start with High-Impact Products**:
   - Focus on best-selling products first
   - Prioritize products with low SEO scores
   - Optimize seasonal products before peak times

2. **Use Keyword Research**:
   - Research relevant keywords before optimization
   - Include long-tail keywords for better targeting
   - Monitor keyword performance over time

3. **Maintain Brand Consistency**:
   - Set clear brand guidelines
   - Review optimized content regularly
   - Maintain consistent tone across products

4. **Monitor Performance**:
   - Track SEO scores and rankings
   - Monitor traffic and conversion changes
   - Adjust strategy based on results

### Cost Optimization

1. **Batch Optimizations**:
   - Group similar products together
   - Schedule optimizations during off-peak hours
   - Use bulk operations for efficiency

2. **Smart Model Selection**:
   - Use GPT-3.5 for simple optimizations
   - Reserve GPT-4 for complex products
   - Test different models for best ROI

3. **Set Budgets and Alerts**:
   - Configure monthly spending limits
   - Set up usage alerts
   - Review costs regularly

## Troubleshooting

### Common Issues

**Store Connection Problems**:
- Verify API credentials are correct
- Check store URL format
- Ensure API permissions are set to "Read/Write"
- Test connection manually

**Optimization Failures**:
- Check if products have sufficient information
- Verify character limits aren't exceeded
- Review any error messages in job logs
- Contact support for persistent issues

**Performance Issues**:
- Clear browser cache and cookies
- Disable browser extensions temporarily
- Try using a different browser
- Check internet connection stability

### Error Messages

**"API Rate Limit Exceeded"**:
- Wait for rate limit to reset (usually 1 hour)
- Reduce concurrent optimization jobs
- Upgrade plan for higher limits

**"Insufficient Product Data"**:
- Ensure products have titles and descriptions
- Add missing product information in your store
- Re-sync products after updates

**"Payment Method Failed"**:
- Update payment information in billing settings
- Check card expiration date
- Contact bank if payment keeps failing

### Getting Help

**Self-Service Options**:
- Knowledge base and FAQ
- Video tutorials
- Community forum
- API documentation

**Support Channels**:
- Email support: <EMAIL>
- Live chat (business hours)
- Phone support (enterprise plans)
- Dedicated account manager (enterprise)

**Support Information to Provide**:
- Account email address
- Store name and URL
- Error messages or screenshots
- Steps to reproduce the issue
- Browser and operating system details

## Mobile App Usage

### Mobile Dashboard

Access GridSpoke on mobile devices:

**Key Features**:
- View optimization progress
- Monitor job status
- Receive push notifications
- Quick product search
- Basic analytics dashboard

**Mobile-Optimized Actions**:
- Start simple optimization jobs
- Approve or reject optimized content
- View recent activity
- Access emergency support

### Push Notifications

Stay updated with mobile notifications:

**Notification Types**:
- Job completion alerts
- Critical error notifications
- Weekly performance summaries
- New feature announcements
- Security alerts

## Advanced Features

### API Integration

For developers and advanced users:

**Use Cases**:
- Custom automation workflows
- Third-party integrations
- Bulk data operations
- Real-time monitoring

**Getting Started**:
- Generate API keys in account settings
- Review API documentation
- Use provided SDKs for common languages
- Test with sandbox environment

### Webhook Configuration

Set up webhooks for real-time updates:

**Event Types**:
- Optimization completed
- Job status changes
- New products synced
- Error notifications

**Configuration**:
- Add webhook URLs in settings
- Configure event filters
- Set authentication headers
- Test webhook delivery

This user guide provides comprehensive information for effectively using GridSpoke to optimize your ecommerce SEO and improve search engine visibility.
