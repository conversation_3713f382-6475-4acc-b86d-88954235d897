#!/usr/bin/env python3
"""
AI Processing Engine Test and Demonstration Script.

This script demonstrates the complete AI processing capabilities including:
- Product optimization with different complexity levels
- Content generation for blogs, FAQs, and buyer's guides
- Streaming responses and real-time updates
- Rate limiting and token usage tracking
- Batch processing capabilities
"""

import asyncio
import json
import time
from typing import Dict, Any, List
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_product_optimization():
    """Test product optimization functionality."""
    logger.info("=== Testing Product Optimization ===")
    
    try:
        from api.agents import ProductOptimizer
        from api.agents.base import AgentConfig
        
        # Initialize the product optimizer
        config = AgentConfig()
        optimizer = ProductOptimizer(config)
        
        # Test product data
        product_data = {
            "name": "Wireless Bluetooth Headphones Pro",
            "category": "Electronics",
            "description": "High-quality wireless headphones with noise cancelling",
            "features": [
                "Active Noise Cancellation",
                "30-hour battery life", 
                "Quick charge (15 min = 3 hours)",
                "Premium drivers",
                "Comfortable over-ear design"
            ],
            "brand": "AudioTech",
            "price": 199.99,
            "target_keywords": ["wireless headphones", "noise cancelling", "bluetooth headphones"]
        }
        
        # Test different optimization types
        optimization_types = ["title_only", "description_only", "complete"]
        
        for opt_type in optimization_types:
            logger.info(f"\n--- Testing {opt_type} optimization ---")
            
            start_time = time.time()
            result = await optimizer.process(
                product_data,
                optimization_type=opt_type,
                complexity="standard"
            )
            processing_time = time.time() - start_time
            
            logger.info(f"Processing time: {processing_time:.2f}s")
            logger.info(f"Success: {result.success}")
            
            if result.success and result.content:
                if opt_type == "title_only" and "title" in result.content:
                    logger.info(f"Generated title: {result.content['title']}")
                elif opt_type == "description_only" and "description" in result.content:
                    logger.info(f"Generated description (first 100 chars): {result.content['description'][:100]}...")
                elif opt_type == "complete":
                    logger.info(f"Complete optimization:")
                    logger.info(f"  - Title: {result.content.get('title', 'N/A')}")
                    logger.info(f"  - Meta Description: {result.content.get('meta_description', 'N/A')}")
                    logger.info(f"  - SEO Score: {result.content.get('seo_score', 'N/A')}")
                    if result.content.get('keywords'):
                        logger.info(f"  - Primary Keywords: {result.content['keywords'].get('primary_keywords', [])}")
            else:
                logger.error(f"Optimization failed: {result.error_message}")
        
        # Test health check
        health = await optimizer.health_check()
        logger.info(f"\nAgent Health: {health['status']}")
        logger.info(f"Total Requests: {health['total_requests']}")
        logger.info(f"Error Rate: {health['error_rate']:.2%}")
        
        return True
        
    except Exception as e:
        logger.error(f"Product optimization test failed: {e}")
        return False


async def test_content_generation():
    """Test content generation functionality."""
    logger.info("\n=== Testing Content Generation ===")
    
    try:
        from api.agents import ContentGenerator
        from api.agents.base import AgentConfig
        
        # Initialize the content generator
        config = AgentConfig()
        generator = ContentGenerator(config)
        
        # Test blog post generation
        logger.info("\n--- Testing Blog Post Generation ---")
        blog_request = {
            "content_type": "blog_post",
            "topic": "Best Wireless Headphones for 2024",
            "target_keywords": ["wireless headphones", "best headphones 2024", "bluetooth headphones"],
            "word_count": 1200,
            "tone": "professional",
            "target_audience": "tech enthusiasts"
        }
        
        start_time = time.time()
        result = await generator.process(blog_request, complexity="standard")
        processing_time = time.time() - start_time
        
        logger.info(f"Blog generation time: {processing_time:.2f}s")
        logger.info(f"Success: {result.success}")
        
        if result.success and result.content:
            blog_content = result.content
            logger.info(f"Generated blog post:")
            logger.info(f"  - Title: {blog_content.get('title', 'N/A')}")
            logger.info(f"  - Word Count: {blog_content.get('word_count', 'N/A')}")
            logger.info(f"  - SEO Score: {blog_content.get('seo_score', 'N/A')}")
            logger.info(f"  - Keywords Used: {blog_content.get('target_keywords_used', [])}")
        
        # Test FAQ generation
        logger.info("\n--- Testing FAQ Generation ---")
        faq_request = {
            "content_type": "faq",
            "topic": "Wireless Headphones",
            "target_keywords": ["headphone battery", "bluetooth pairing", "noise cancellation"],
            "category": "Electronics",
            "products": [
                {
                    "name": "Wireless Headphones Pro",
                    "features": ["30h battery", "ANC", "Quick charge"]
                }
            ]
        }
        
        result = await generator.process(faq_request, complexity="standard")
        
        if result.success and result.content:
            faq_content = result.content
            logger.info(f"Generated FAQ:")
            logger.info(f"  - Total Questions: {faq_content.get('total_questions', 'N/A')}")
            logger.info(f"  - Keywords Covered: {faq_content.get('keywords_covered', [])}")
            
            faqs = faq_content.get('faqs', [])
            if faqs:
                logger.info(f"  - Sample Question: {faqs[0].get('question', 'N/A')}")
        
        return True
        
    except Exception as e:
        logger.error(f"Content generation test failed: {e}")
        return False


async def test_ai_service():
    """Test the high-level AI service."""
    logger.info("\n=== Testing AI Service ===")
    
    try:
        from api.services import AIService
        from api.services.ai_service import AIJobRequest
        
        # Initialize AI service
        service = AIService()
        
        # Test service health
        health = await service.health_check()
        logger.info(f"AI Service Health: {health['status']}")
        logger.info(f"Redis Connected: {health['redis_connected']}")
        logger.info(f"Active Jobs: {health['active_jobs']}")
        
        # Test single product optimization
        logger.info("\n--- Testing Single Product Optimization ---")
        
        product_data = {
            "name": "Smart Fitness Tracker",
            "category": "Wearable Technology",
            "features": ["Heart rate monitoring", "Sleep tracking", "GPS", "Waterproof"],
            "brand": "FitTech"
        }
        
        # Test without streaming
        result = await service.optimize_product(
            product_data,
            optimization_type="complete",
            complexity="standard",
            stream=False
        )
        
        logger.info(f"Optimization Success: {result.success}")
        logger.info(f"Processing Time: {result.processing_time:.2f}s")
        
        if result.success and result.content:
            logger.info(f"Generated title: {result.content.get('title', 'N/A')}")
        
        # Test content generation
        logger.info("\n--- Testing Content Generation via Service ---")
        
        content_request = {
            "content_type": "buyers_guide",
            "topic": "Fitness Trackers",
            "target_keywords": ["best fitness tracker", "fitness watch", "activity tracker"],
            "category": "Wearable Technology"
        }
        
        result = await service.generate_content(content_request, complexity="standard")
        
        logger.info(f"Content Generation Success: {result.success}")
        
        if result.success and result.content:
            guide_content = result.content
            logger.info(f"Generated guide title: {guide_content.get('title', 'N/A')}")
            logger.info(f"Word count: {guide_content.get('word_count', 'N/A')}")
        
        # Test usage statistics
        usage_stats = service.get_usage_stats()
        logger.info(f"\nUsage Statistics:")
        logger.info(f"  - Total Tokens: {usage_stats['total_tokens']}")
        logger.info(f"  - Total Requests: {usage_stats['total_requests']}")
        logger.info(f"  - Estimated Cost: ${usage_stats['estimated_cost']:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"AI service test failed: {e}")
        return False


async def test_batch_processing():
    """Test batch processing capabilities."""
    logger.info("\n=== Testing Batch Processing ===")
    
    try:
        from api.services import AIService
        
        service = AIService()
        
        # Create test products for batch processing
        test_products = [
            {
                "name": "Gaming Mechanical Keyboard",
                "category": "Computer Accessories",
                "features": ["RGB backlighting", "Cherry MX switches", "Programmable keys"],
                "brand": "GameTech"
            },
            {
                "name": "4K Webcam Pro",
                "category": "Computer Accessories", 
                "features": ["4K resolution", "Auto focus", "Noise reduction"],
                "brand": "CamTech"
            },
            {
                "name": "Ergonomic Office Chair",
                "category": "Office Furniture",
                "features": ["Lumbar support", "Adjustable height", "Breathable mesh"],
                "brand": "OfficeComfort"
            }
        ]
        
        logger.info(f"Starting batch optimization for {len(test_products)} products")
        
        # Start batch job
        job_id = await service.optimize_products_batch(
            test_products,
            optimization_type="title_only",  # Use simpler optimization for demo
            complexity="economy",
            priority="normal"
        )
        
        logger.info(f"Batch job started with ID: {job_id}")
        
        # Monitor job progress
        max_wait_time = 60  # Maximum wait time in seconds
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            job_status = await service.get_job_status(job_id)
            
            if job_status:
                logger.info(f"Job Status: {job_status.status}, Progress: {job_status.progress:.1f}%")
                
                if job_status.status in ["completed", "failed"]:
                    break
                    
            await asyncio.sleep(2)  # Check every 2 seconds
        
        # Get final status
        final_status = await service.get_job_status(job_id)
        if final_status:
            logger.info(f"Final Job Status: {final_status.status}")
            logger.info(f"Total Tokens Used: {final_status.total_tokens_used}")
            
            if final_status.status == "completed" and final_status.result:
                successful = sum(1 for r in final_status.result if r.get('success', False))
                logger.info(f"Successfully optimized {successful}/{len(test_products)} products")
        
        return True
        
    except Exception as e:
        logger.error(f"Batch processing test failed: {e}")
        return False


async def test_rate_limiting():
    """Test rate limiting functionality."""
    logger.info("\n=== Testing Rate Limiting ===")
    
    try:
        from api.core.rate_limiter import AIRateLimiter, RateLimitConfig
        
        # Initialize rate limiter with strict limits for testing
        config = RateLimitConfig(
            store_requests_per_minute=5,  # Very low limit for testing
            user_requests_per_minute=3
        )
        
        rate_limiter = AIRateLimiter(config)
        
        test_identifier = "test_store_123"
        
        logger.info("Testing request rate limiting...")
        
        # Test within limits
        for i in range(3):
            result = await rate_limiter.check_rate_limit(test_identifier, "requests")
            logger.info(f"Request {i+1}: Allowed={result['allowed']}, Remaining={result['remaining']}")
            
            if not result['allowed']:
                logger.info(f"Rate limit hit at request {i+1}, wait time: {result['wait_time']}s")
                break
        
        # Test token-based limiting
        logger.info("\nTesting token rate limiting...")
        
        token_result = await rate_limiter.check_rate_limit(
            test_identifier, 
            "tokens", 
            tokens=5000, 
            model="openai/gpt-4o"
        )
        
        logger.info(f"Token check: Allowed={token_result['allowed']}, Remaining={token_result['remaining']}")
        
        # Test cost-based limiting
        logger.info("\nTesting cost rate limiting...")
        
        cost_result = await rate_limiter.check_rate_limit(
            test_identifier,
            "cost",
            cost=0.50  # $0.50 cost
        )
        
        logger.info(f"Cost check: Allowed={cost_result['allowed']}")
        if 'remaining_hour' in cost_result:
            logger.info(f"Remaining budget this hour: ${cost_result['remaining_hour']:.2f}")
        
        # Get usage statistics
        usage_stats = await rate_limiter.get_usage_stats(test_identifier)
        logger.info(f"\nUsage Statistics: {usage_stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"Rate limiting test failed: {e}")
        return False


async def test_prompt_templates():
    """Test prompt template system."""
    logger.info("\n=== Testing Prompt Templates ===")
    
    try:
        from api.prompts.templates import PromptManager
        
        prompt_manager = PromptManager()
        
        # List available templates
        templates = prompt_manager.list_templates()
        logger.info("Available templates:")
        for category, template_list in templates.items():
            logger.info(f"  {category}: {template_list}")
        
        # Test SEO title template
        logger.info("\n--- Testing SEO Title Template ---")
        
        title_prompt = prompt_manager.format_template(
            "seo",
            "product_title_optimization",
            product_name="Premium Wireless Mouse",
            category="Computer Accessories",
            features=["Ergonomic design", "Long battery life", "Precision tracking"],
            brand="TechMouse",
            target_keywords=["wireless mouse", "computer mouse", "ergonomic mouse"]
        )
        
        logger.info("Generated title optimization prompt:")
        logger.info(title_prompt[:200] + "..." if len(title_prompt) > 200 else title_prompt)
        
        # Test blog post template
        logger.info("\n--- Testing Blog Post Template ---")
        
        blog_prompt = prompt_manager.format_template(
            "content",
            "blog_post_generation",
            topic="Best Computer Mice for Productivity",
            target_keywords=["best computer mouse", "productivity mouse", "office mouse"],
            word_count=1000,
            tone="professional"
        )
        
        logger.info("Generated blog post prompt:")
        logger.info(blog_prompt[:200] + "..." if len(blog_prompt) > 200 else blog_prompt)
        
        return True
        
    except Exception as e:
        logger.error(f"Prompt template test failed: {e}")
        return False


async def main():
    """Run all AI processing engine tests."""
    logger.info("Starting AI Processing Engine Test Suite")
    logger.info("=" * 60)
    
    test_results = {}
    
    # Run all tests
    tests = [
        ("Prompt Templates", test_prompt_templates),
        ("Product Optimization", test_product_optimization),
        ("Content Generation", test_content_generation),
        ("AI Service", test_ai_service),
        ("Rate Limiting", test_rate_limiting),
        ("Batch Processing", test_batch_processing),
    ]
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n{'='*20} Running {test_name} Test {'='*20}")
            success = await test_func()
            test_results[test_name] = "PASSED" if success else "FAILED"
        except Exception as e:
            logger.error(f"{test_name} test encountered error: {e}")
            test_results[test_name] = "ERROR"
        
        # Small delay between tests
        await asyncio.sleep(1)
    
    # Print final results
    logger.info("\n" + "="*60)
    logger.info("AI PROCESSING ENGINE TEST RESULTS")
    logger.info("="*60)
    
    for test_name, result in test_results.items():
        status_symbol = "✅" if result == "PASSED" else "❌"
        logger.info(f"{status_symbol} {test_name}: {result}")
    
    passed_tests = sum(1 for result in test_results.values() if result == "PASSED")
    total_tests = len(test_results)
    
    logger.info(f"\nOverall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED! AI Processing Engine is ready for production.")
    else:
        logger.warning("⚠️  Some tests failed. Please review the logs above.")


if __name__ == "__main__":
    asyncio.run(main())
