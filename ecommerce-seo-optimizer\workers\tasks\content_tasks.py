"""
Content Generation Tasks for GridSpoke Ecommerce SEO Optimizer
Handles blog posts, FAQs, buyer's guides, and other content generation using AI agents.
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from celery import current_task
from celery.exceptions import Retry, Ignore

# Add the API directory to the Python path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '../../api'))

from workers.celery_app import celery_app
from workers.utils.progress_tracker import track_progress, update_task_status
from workers.utils.redis_client import get_redis_client
from workers.utils.error_handler import handle_task_error, retry_with_backoff

logger = logging.getLogger(__name__)

@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    retry_backoff=True,
    acks_late=True
)
def generate_blog_post(self, store_id: str, topic: str, content_options: Dict[str, Any] = None):
    """
    Generate a blog post for a store using AI content generator.
    
    Args:
        store_id: Store identifier
        topic: Blog post topic
        content_options: Configuration options for content generation
    
    Returns:
        Dict containing generated blog post content
    """
    try:
        from api.services.ai_service import AIService
        from api.crud.store import get_store_by_id
        from api.core.database import get_db
        
        logger.info(f"Generating blog post for store {store_id} on topic: {topic}")
        
        # Initialize progress tracking
        track_progress(self.request.id, 0, 4, "Initializing blog post generation")
        
        # Get database session
        db = next(get_db())
        
        # Fetch store data
        track_progress(self.request.id, 1, 4, "Fetching store information")
        store = get_store_by_id(db, store_id)
        if not store:
            raise ValueError(f"Store {store_id} not found")
        
        # Initialize AI service
        track_progress(self.request.id, 2, 4, "Initializing AI content generator")
        ai_service = AIService()
        
        # Prepare content generation payload
        content_payload = {
            'content_type': 'blog_post',
            'topic': topic,
            'store_context': {
                'name': store.name,
                'description': store.description,
                'category': store.category,
                'target_audience': store.target_audience,
                'brand_voice': getattr(store, 'brand_voice', 'professional')
            },
            'options': content_options or {}
        }
        
        # Generate content
        track_progress(self.request.id, 3, 4, "Generating blog post content")
        content_result = ai_service.generate_content(content_payload)
        
        # Structure the blog post
        blog_post = {
            'title': content_result.get('title'),
            'excerpt': content_result.get('excerpt'),
            'content': content_result.get('content'),
            'meta_description': content_result.get('meta_description'),
            'keywords': content_result.get('keywords', []),
            'tags': content_result.get('tags', []),
            'featured_image_alt': content_result.get('featured_image_alt'),
            'word_count': len(content_result.get('content', '').split()),
            'seo_score': content_result.get('seo_score'),
            'readability_score': content_result.get('readability_score')
        }
        
        # Store content in Redis cache for 24 hours
        redis_client = get_redis_client()
        content_record = {
            'store_id': store_id,
            'content_type': 'blog_post',
            'topic': topic,
            'blog_post': blog_post,
            'ai_model': content_result.get('model_used'),
            'tokens_used': content_result.get('tokens_used', 0),
            'cost_usd': content_result.get('cost_usd', 0),
            'generated_at': datetime.utcnow().isoformat(),
            'task_id': self.request.id
        }
        
        redis_client.setex(
            f"generated_content:blog:{store_id}:{self.request.id}",
            86400,  # 24 hours
            json.dumps(content_record)
        )
        
        # Complete progress tracking
        track_progress(self.request.id, 4, 4, "Blog post generation completed")
        
        logger.info(f"Successfully generated blog post for store {store_id}")
        
        return {
            'status': 'success',
            'store_id': store_id,
            'content_type': 'blog_post',
            'topic': topic,
            'blog_post': blog_post,
            'tokens_used': content_result.get('tokens_used', 0),
            'cost_usd': content_result.get('cost_usd', 0),
            'task_id': self.request.id,
            'completed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error generating blog post: {str(exc)}")
        
        if self.request.retries < 3:
            countdown = retry_with_backoff(self.request.retries)
            raise self.retry(countdown=countdown, exc=exc)
        else:
            update_task_status(self.request.id, 'failed', str(exc))
            raise Ignore()

@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    acks_late=True
)
def generate_product_faqs(self, product_id: str, store_id: str, faq_options: Dict[str, Any] = None):
    """
    Generate FAQs for a specific product.
    
    Args:
        product_id: Product identifier
        store_id: Store identifier
        faq_options: Configuration options for FAQ generation
    
    Returns:
        Dict containing generated FAQ content
    """
    try:
        from api.services.ai_service import AIService
        from api.crud.product import get_product_by_id
        from api.core.database import get_db
        
        logger.info(f"Generating FAQs for product {product_id}")
        
        # Initialize progress tracking
        track_progress(self.request.id, 0, 3, "Initializing FAQ generation")
        
        # Get database session
        db = next(get_db())
        
        # Fetch product data
        track_progress(self.request.id, 1, 3, "Fetching product information")
        product = get_product_by_id(db, product_id)
        if not product:
            raise ValueError(f"Product {product_id} not found")
        
        # Initialize AI service
        ai_service = AIService()
        
        # Prepare FAQ generation payload
        faq_payload = {
            'content_type': 'product_faqs',
            'product_context': {
                'title': product.title,
                'description': product.description,
                'category': product.category,
                'price': product.price,
                'features': getattr(product, 'features', []),
                'specifications': getattr(product, 'specifications', {})
            },
            'options': faq_options or {'num_faqs': 5}
        }
        
        # Generate FAQs
        track_progress(self.request.id, 2, 3, "Generating FAQ content")
        faq_result = ai_service.generate_content(faq_payload)
        
        # Structure the FAQs
        faqs = {
            'product_id': product_id,
            'faqs': faq_result.get('faqs', []),
            'total_faqs': len(faq_result.get('faqs', [])),
            'schema_markup': faq_result.get('schema_markup'),
            'keywords_covered': faq_result.get('keywords_covered', [])
        }
        
        # Store FAQs in Redis cache
        redis_client = get_redis_client()
        faq_record = {
            'product_id': product_id,
            'store_id': store_id,
            'content_type': 'product_faqs',
            'faqs': faqs,
            'ai_model': faq_result.get('model_used'),
            'tokens_used': faq_result.get('tokens_used', 0),
            'cost_usd': faq_result.get('cost_usd', 0),
            'generated_at': datetime.utcnow().isoformat(),
            'task_id': self.request.id
        }
        
        redis_client.setex(
            f"generated_content:faqs:{product_id}",
            86400,  # 24 hours
            json.dumps(faq_record)
        )
        
        # Complete progress tracking
        track_progress(self.request.id, 3, 3, "FAQ generation completed")
        
        logger.info(f"Successfully generated {len(faqs['faqs'])} FAQs for product {product_id}")
        
        return {
            'status': 'success',
            'product_id': product_id,
            'store_id': store_id,
            'content_type': 'product_faqs',
            'faqs': faqs,
            'tokens_used': faq_result.get('tokens_used', 0),
            'cost_usd': faq_result.get('cost_usd', 0),
            'task_id': self.request.id,
            'completed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error generating FAQs: {str(exc)}")
        
        if self.request.retries < 3:
            countdown = retry_with_backoff(self.request.retries)
            raise self.retry(countdown=countdown, exc=exc)
        else:
            update_task_status(self.request.id, 'failed', str(exc))
            raise Ignore()

@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 90},
    acks_late=True
)
def generate_buyers_guide(self, category: str, store_id: str, guide_options: Dict[str, Any] = None):
    """
    Generate a comprehensive buyer's guide for a product category.
    
    Args:
        category: Product category
        store_id: Store identifier
        guide_options: Configuration options for guide generation
    
    Returns:
        Dict containing generated buyer's guide content
    """
    try:
        from api.services.ai_service import AIService
        from api.crud.store import get_store_by_id
        from api.crud.product import get_products_by_category
        from api.core.database import get_db
        
        logger.info(f"Generating buyer's guide for category {category}")
        
        # Initialize progress tracking
        track_progress(self.request.id, 0, 5, "Initializing buyer's guide generation")
        
        # Get database session
        db = next(get_db())
        
        # Fetch store and category products
        track_progress(self.request.id, 1, 5, "Fetching store and product information")
        store = get_store_by_id(db, store_id)
        if not store:
            raise ValueError(f"Store {store_id} not found")
        
        # Get products in category for context
        category_products = get_products_by_category(db, store_id, category, limit=20)
        
        # Initialize AI service
        track_progress(self.request.id, 2, 5, "Initializing AI content generator")
        ai_service = AIService()
        
        # Prepare guide generation payload
        guide_payload = {
            'content_type': 'buyers_guide',
            'category': category,
            'store_context': {
                'name': store.name,
                'description': store.description,
                'target_audience': store.target_audience
            },
            'product_samples': [
                {
                    'title': p.title,
                    'price': p.price,
                    'features': getattr(p, 'features', [])[:3]  # Limit features for context
                }
                for p in category_products[:10]  # Sample of products
            ],
            'options': guide_options or {}
        }
        
        # Generate buyer's guide
        track_progress(self.request.id, 3, 5, "Generating buyer's guide content")
        guide_result = ai_service.generate_content(guide_payload)
        
        # Structure the buyer's guide
        track_progress(self.request.id, 4, 5, "Structuring guide content")
        buyers_guide = {
            'title': guide_result.get('title'),
            'introduction': guide_result.get('introduction'),
            'sections': guide_result.get('sections', []),
            'key_features': guide_result.get('key_features', []),
            'buying_tips': guide_result.get('buying_tips', []),
            'price_ranges': guide_result.get('price_ranges', {}),
            'conclusion': guide_result.get('conclusion'),
            'meta_description': guide_result.get('meta_description'),
            'keywords': guide_result.get('keywords', []),
            'word_count': len(guide_result.get('content', '').split()),
            'toc': guide_result.get('table_of_contents', [])
        }
        
        # Store guide in Redis cache
        redis_client = get_redis_client()
        guide_record = {
            'category': category,
            'store_id': store_id,
            'content_type': 'buyers_guide',
            'buyers_guide': buyers_guide,
            'products_analyzed': len(category_products),
            'ai_model': guide_result.get('model_used'),
            'tokens_used': guide_result.get('tokens_used', 0),
            'cost_usd': guide_result.get('cost_usd', 0),
            'generated_at': datetime.utcnow().isoformat(),
            'task_id': self.request.id
        }
        
        redis_client.setex(
            f"generated_content:guide:{store_id}:{category}",
            86400,  # 24 hours
            json.dumps(guide_record)
        )
        
        # Complete progress tracking
        track_progress(self.request.id, 5, 5, "Buyer's guide generation completed")
        
        logger.info(f"Successfully generated buyer's guide for category {category}")
        
        return {
            'status': 'success',
            'category': category,
            'store_id': store_id,
            'content_type': 'buyers_guide',
            'buyers_guide': buyers_guide,
            'products_analyzed': len(category_products),
            'tokens_used': guide_result.get('tokens_used', 0),
            'cost_usd': guide_result.get('cost_usd', 0),
            'task_id': self.request.id,
            'completed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error generating buyer's guide: {str(exc)}")
        
        if self.request.retries < 3:
            countdown = retry_with_backoff(self.request.retries)
            raise self.retry(countdown=countdown, exc=exc)
        else:
            update_task_status(self.request.id, 'failed', str(exc))
            raise Ignore()

@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 60},
    acks_late=True
)
def generate_meta_descriptions(self, product_ids: List[str], store_id: str):
    """
    Generate meta descriptions for multiple products.
    
    Args:
        product_ids: List of product IDs
        store_id: Store identifier
    
    Returns:
        Dict containing generated meta descriptions
    """
    try:
        from api.services.ai_service import AIService
        from api.crud.product import get_products_by_ids, update_product
        from api.core.database import get_db
        
        logger.info(f"Generating meta descriptions for {len(product_ids)} products")
        
        # Initialize progress tracking
        total_products = len(product_ids)
        track_progress(self.request.id, 0, total_products, "Initializing meta description generation")
        
        # Get database session
        db = next(get_db())
        
        # Fetch products
        products = get_products_by_ids(db, product_ids)
        
        # Initialize AI service
        ai_service = AIService()
        
        results = []
        processed_count = 0
        
        for product in products:
            try:
                # Generate meta description
                meta_payload = {
                    'content_type': 'meta_description',
                    'product_context': {
                        'title': product.title,
                        'description': product.description[:200],  # Truncate for context
                        'category': product.category,
                        'keywords': product.keywords or []
                    }
                }
                
                meta_result = ai_service.generate_content(meta_payload)
                meta_description = meta_result.get('meta_description')
                
                # Update product with new meta description
                if meta_description:
                    update_product(db, product.id, {'meta_description': meta_description})
                    
                    results.append({
                        'product_id': product.id,
                        'meta_description': meta_description,
                        'status': 'success'
                    })
                else:
                    results.append({
                        'product_id': product.id,
                        'status': 'failed',
                        'error': 'No meta description generated'
                    })
                
                processed_count += 1
                track_progress(
                    self.request.id, 
                    processed_count, 
                    total_products, 
                    f"Generated meta descriptions for {processed_count}/{total_products} products"
                )
                
            except Exception as e:
                logger.error(f"Error generating meta description for product {product.id}: {str(e)}")
                results.append({
                    'product_id': product.id,
                    'status': 'failed',
                    'error': str(e)
                })
                processed_count += 1
        
        # Calculate summary
        successful_count = len([r for r in results if r.get('status') == 'success'])
        
        summary = {
            'status': 'completed',
            'store_id': store_id,
            'total_products': total_products,
            'successful_generations': successful_count,
            'failed_generations': total_products - successful_count,
            'results': results,
            'task_id': self.request.id,
            'completed_at': datetime.utcnow().isoformat()
        }
        
        logger.info(f"Meta description generation completed: {successful_count}/{total_products} successful")
        
        return summary
        
    except Exception as exc:
        logger.error(f"Error in meta description generation: {str(exc)}")
        
        if self.request.retries < 2:
            countdown = retry_with_backoff(self.request.retries)
            raise self.retry(countdown=countdown, exc=exc)
        else:
            update_task_status(self.request.id, 'failed', str(exc))
            raise Ignore()

@celery_app.task(bind=True, acks_late=True)
def generate_alt_text(self, product_id: str, image_urls: List[str]):
    """
    Generate alt text for product images.
    
    Args:
        product_id: Product identifier
        image_urls: List of image URLs
    
    Returns:
        Dict containing generated alt text for each image
    """
    try:
        from api.services.ai_service import AIService
        from api.crud.product import get_product_by_id
        from api.core.database import get_db
        
        logger.info(f"Generating alt text for {len(image_urls)} images")
        
        # Get database session
        db = next(get_db())
        
        # Fetch product for context
        product = get_product_by_id(db, product_id)
        if not product:
            raise ValueError(f"Product {product_id} not found")
        
        # Initialize AI service
        ai_service = AIService()
        
        alt_text_results = []
        
        for index, image_url in enumerate(image_urls):
            try:
                # Generate alt text
                alt_payload = {
                    'content_type': 'alt_text',
                    'image_context': {
                        'product_title': product.title,
                        'product_category': product.category,
                        'image_url': image_url,
                        'image_position': index + 1
                    }
                }
                
                alt_result = ai_service.generate_content(alt_payload)
                alt_text = alt_result.get('alt_text')
                
                alt_text_results.append({
                    'image_url': image_url,
                    'alt_text': alt_text,
                    'position': index + 1,
                    'status': 'success'
                })
                
            except Exception as e:
                logger.error(f"Error generating alt text for image {image_url}: {str(e)}")
                alt_text_results.append({
                    'image_url': image_url,
                    'alt_text': '',
                    'position': index + 1,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'product_id': product_id,
            'alt_text_results': alt_text_results,
            'total_images': len(image_urls),
            'successful_generations': len([r for r in alt_text_results if r.get('status') == 'success']),
            'task_id': self.request.id,
            'completed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Error generating alt text: {str(exc)}")
        raise
