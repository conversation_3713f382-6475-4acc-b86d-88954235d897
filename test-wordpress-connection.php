<?php
/**
 * Test script to verify WordPress plugin connection to GridSpoke API
 */

// API endpoint
$api_endpoint = 'http://localhost:8000/api/v1/';

// Test user credentials
$email = '<EMAIL>';
$password = 'test12345';

/**
 * Make HTTP request to GridSpoke API
 */
function make_request($method, $endpoint, $data = null, $access_token = null) {
    global $api_endpoint;
    
    $url = $api_endpoint . ltrim($endpoint, '/');
    
    $headers = array(
        'Content-Type: application/json',
        'User-Agent: GridSpoke-Test-Script/1.0'
    );
    
    // Add authentication header if token is provided
    if ($access_token) {
        $headers[] = 'Authorization: Bearer ' . $access_token;
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($method === 'POST' && $data !== null) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    } elseif ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return array(
        'status_code' => $http_code,
        'body' => json_decode($response, true)
    );
}

/**
 * Authenticate with email/password and get JWT tokens
 */
function authenticate($email, $password) {
    $payload = array(
        'email' => $email,
        'password' => $password
    );
    
    $response = make_request('POST', 'auth/login', $payload, false);
    
    if ($response['status_code'] === 200 && !empty($response['body']['access_token'])) {
        return array(
            'success' => true,
            'access_token' => $response['body']['access_token'],
            'refresh_token' => $response['body']['refresh_token'],
            'user' => $response['body']['user']
        );
    } else {
        return array(
            'success' => false,
            'message' => $response['body']['detail'] ?? 'Authentication failed'
        );
    }
}

/**
 * Test authenticated connection
 */
function test_authenticated_connection($access_token) {
    $response = make_request('GET', 'auth/me', null, $access_token);
    
    return array(
        'success' => $response['status_code'] === 200,
        'message' => $response['status_code'] === 200 ? 
            'Authentication successful' : 
            'Authentication failed with status: ' . $response['status_code']
    );
}

// Test basic connection
echo "Testing basic API connection...\n";
$response = make_request('GET', 'health');
if ($response['status_code'] === 200) {
    echo "✓ Basic connection successful\n";
} else {
    echo "✗ Basic connection failed with status: " . $response['status_code'] . "\n";
    exit(1);
}

// Test authentication
echo "\nTesting authentication...\n";
$auth_result = authenticate($email, $password);
if ($auth_result['success']) {
    echo "✓ Authentication successful\n";
    echo "Access token: " . substr($auth_result['access_token'], 0, 20) . "...\n";
} else {
    echo "✗ Authentication failed: " . $auth_result['message'] . "\n";
    exit(1);
}

// Test authenticated connection
echo "\nTesting authenticated connection...\n";
$auth_test = test_authenticated_connection($auth_result['access_token']);
if ($auth_test['success']) {
    echo "✓ Authenticated connection successful\n";
    echo "User: " . $auth_result['user']['email'] . "\n";
} else {
    echo "✗ Authenticated connection failed: " . $auth_test['message'] . "\n";
    exit(1);
}

echo "\nAll tests passed! WordPress plugin should be able to connect to the GridSpoke API.\n";
?>