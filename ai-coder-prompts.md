# AI Coder Implementation Prompts - Ecommerce SEO Service

## Project Overview Context (Include in Every Prompt)
```
Project: AI-powered ecommerce SEO optimization service
Architecture: Microservices with Docker Compose
Backend: FastAPI with Mirascope for AI agents
Frontend: Vanilla HTML/CSS/JavaScript dashboard
Database: PostgreSQL with pgvector
Queue: Celery with Redis
Integration: WordPress plugin for WooCommerce/SureCart
AI Provider: OpenRouter API for LLM access
```

---

## PHASE 1: Project Infrastructure Setup

### Research Prompt 1.1
```
Research the latest best practices for:
- https://fastapi.tiangolo.com/deployment/docker/
- https://docs.docker.com/compose/compose-file/compose-file-v3/
- https://github.com/tiangolo/full-stack-fastapi-postgresql
- https://docs.celeryq.dev/en/stable/getting-started/first-steps-with-celery.html

Focus on Docker Compose setup for FastAPI, PostgreSQL, Redis, Celery, and Nginx in 2024/2025.
```

### Implementation Prompt 1.2
```
Context: Starting new AI-powered ecommerce SEO service project.

Create initial project structure with Docker Compose setup:
- Root directory: ecommerce-seo-optimizer/
- Create docker-compose.yml with services: api (FastAPI), db (PostgreSQL), redis, celery-worker, celery-beat, nginx, frontend (static files)
- Create Dockerfile for each service
- Set up environment variables in .env.example
- Create directory structure: api/, frontend/, nginx/, workers/
- Configure nginx as reverse proxy for API and frontend
- Use PostgreSQL 15+ with pgvector extension
- Redis for message broker and caching
- All services should communicate through Docker network

Files to create:
- docker-compose.yml
- .env.example
- api/Dockerfile
- workers/Dockerfile
- nginx/nginx.conf
- nginx/Dockerfile
- frontend/Dockerfile
- .gitignore
- README.md with setup instructions
```

---

## PHASE 2: Backend API Foundation

### Research Prompt 2.1
```
Research latest FastAPI patterns and best practices:
- https://fastapi.tiangolo.com/tutorial/sql-databases/
- https://fastapi.tiangolo.com/advanced/security/
- https://docs.pydantic.dev/latest/concepts/models/
- https://www.sqlalchemy.org/docs/20/orm/quickstart.html

Focus on FastAPI project structure, SQLAlchemy 2.0 with async support, and JWT authentication.
```

### Implementation Prompt 2.2
```
Context: Project infrastructure created with Docker Compose. Working in api/ directory.

Previous files: Docker setup complete.

Create FastAPI backend foundation:
- Set up FastAPI application with proper project structure
- Implement SQLAlchemy 2.0 with async PostgreSQL connection
- Create base models: Store, Product, OptimizationJob, GeneratedContent
- Implement JWT authentication system
- Create CRUD operations base classes
- Set up Alembic for database migrations
- Implement API routers for: auth, stores, products, jobs
- Add CORS middleware configuration
- Create Pydantic schemas for request/response validation
- Implement error handling and logging

Files to create/modify:
- api/main.py
- api/core/config.py
- api/core/database.py
- api/core/security.py
- api/models/
- api/schemas/
- api/crud/
- api/api/v1/endpoints/
- api/alembic.ini
- api/requirements.txt
```

---

## PHASE 3: AI Integration with Mirascope

### Research Prompt 3.1
```
Research Mirascope and OpenRouter integration:
- https://docs.mirascope.io/latest/
- https://docs.mirascope.io/latest/learn/agents/
- https://docs.mirascope.io/latest/learn/streams/
- https://openrouter.ai/docs

Focus on Mirascope agent patterns, streaming responses, error handling, and OpenRouter API integration.
```

### Implementation Prompt 3.2
```
Context: FastAPI backend established with models and authentication.

Previous files: api/ structure with FastAPI, models, and schemas created.

Implement AI processing engine using Mirascope:
- Install and configure Mirascope with OpenRouter as provider
- Create base agent class for common functionality
- Implement ProductOptimizer agent for title/description/meta optimization
- Implement ContentGenerator agent for blogs/FAQs/guides
- Create prompt template management system
- Implement streaming response handling for real-time updates
- Add retry logic with exponential backoff
- Create rate limiting for API calls
- Implement token usage tracking
- Add prompt caching system for similar products

Files to create/modify:
- api/agents/base.py
- api/agents/product_optimizer.py
- api/agents/content_generator.py
- api/prompts/templates.py
- api/services/ai_service.py
- api/core/rate_limiter.py
- Update api/requirements.txt
```

---

## PHASE 4: Task Queue and Scheduling

### Research Prompt 4.1
```
Research Celery with FastAPI integration:
- https://docs.celeryq.dev/en/stable/django/first-steps-with-django.html
- https://docs.celeryq.dev/en/stable/userguide/periodic-tasks.html
- https://testdriven.io/blog/fastapi-and-celery/
- https://github.com/celery/celery/tree/main/examples

Focus on Celery task organization, progress tracking, and scheduled jobs with Celery Beat.
```

### Implementation Prompt 4.2
```
Context: AI agents implemented with Mirascope. FastAPI backend operational.

Previous files: api/ with agents/, services/, and core FastAPI structure.

Implement asynchronous task processing:
- Configure Celery with Redis broker
- Create task modules for: product optimization, content generation, bulk processing
- Implement Celery Beat for scheduled tasks
- Create progress tracking system with Redis
- Implement job status updates via WebSocket
- Add task priority queues
- Create task chains for complex workflows
- Implement error handling and retry mechanisms
- Add task result storage and cleanup
- Create admin endpoints for task monitoring

Files to create/modify:
- workers/celery_app.py
- workers/tasks/product_tasks.py
- workers/tasks/content_tasks.py
- workers/tasks/scheduled_tasks.py
- api/websocket/manager.py
- api/api/v1/endpoints/tasks.py
- workers/celeryconfig.py
- workers/requirements.txt
```

---

## PHASE 5: Frontend Dashboard (Vanilla JS)

### Research Prompt 5.1
```
Research modern vanilla JavaScript patterns:
- https://developer.mozilla.org/en-US/docs/Web/API/WebSocket
- https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API
- https://tailwindcss.com/docs/installation/play-cdn
- https://alpinejs.dev/start-here

Focus on WebSocket implementation, fetch API patterns, and modern CSS frameworks without build tools.
```

### Implementation Prompt 5.2
```
Context: Backend API complete with WebSocket support. Working in frontend/ directory.

Previous files: API endpoints created, WebSocket manager implemented.

Create vanilla JavaScript dashboard:
- Set up HTML structure with semantic markup
- Implement authentication flow with JWT token management
- Create dashboard layout with sidebar navigation
- Build product management interface with DataTable
- Implement bulk actions UI
- Create job queue visualization with progress bars
- Add WebSocket connection for real-time updates
- Build settings page for API configuration
- Implement charts for analytics (use Chart.js CDN)
- Create responsive design with Tailwind CSS (CDN)
- Add notification system for job updates
- Implement search and filter functionality
- Use Alpine.js for reactive components (CDN)

Files to create:
- frontend/index.html
- frontend/login.html
- frontend/dashboard.html
- frontend/css/styles.css
- frontend/js/auth.js
- frontend/js/api.js
- frontend/js/websocket.js
- frontend/js/products.js
- frontend/js/jobs.js
- frontend/js/dashboard.js
- frontend/js/utils.js
```

---

## PHASE 6: WordPress Plugin Development

### Research Prompt 6.1
```
Research WordPress plugin development:
- https://developer.wordpress.org/plugins/plugin-basics/
- https://developer.wordpress.org/plugins/
- https://developer.wordpress.org/plugins/hooks/
- https://developer.wordpress.org/plugins/http-api/
- https://developer.wordpress.org/plugins/cron/
- https://developer.wordpress.org/rest-api/using-the-rest-api/
- https://docs.woocommerce.com/document/woocommerce-rest-api/
- https://woocommerce.github.io/woocommerce-rest-api-docs/
- https://woocommerce.github.io/woocommerce-rest-api-docs/?python#rest-api-keys
- https://surecart.com/docs/developer/


Focus on REST API integration, WooCommerce hooks, and admin interface creation.
```

### Implementation Prompt 6.2
```
Context: Backend service fully operational with API endpoints. Creating WordPress integration.

Previous files: Complete backend with FastAPI endpoints at /api/v1/.

Create WordPress connector plugin:
- Initialize plugin with proper WordPress structure
- Create settings page for API key and endpoint configuration
- Implement WooCommerce product sync functionality
- Add SureCart compatibility layer
- Create webhook handlers for receiving optimized content
- Implement bulk action buttons in product list
- Add dashboard widget showing optimization status
- Create AJAX handlers for async operations
- Implement error logging and debugging tools
- Add automatic sync scheduling options
- Create product meta fields for optimization status
- Build admin notices for job completion

Files to create:
- wordpress-plugin/seo-optimizer-connector/seo-optimizer-connector.php
- wordpress-plugin/seo-optimizer-connector/includes/class-api-client.php
- wordpress-plugin/seo-optimizer-connector/includes/class-woocommerce-integration.php
- wordpress-plugin/seo-optimizer-connector/includes/class-surecart-integration.php
- wordpress-plugin/seo-optimizer-connector/includes/class-webhook-handler.php
- wordpress-plugin/seo-optimizer-connector/admin/settings-page.php
- wordpress-plugin/seo-optimizer-connector/admin/assets/admin.js
- wordpress-plugin/seo-optimizer-connector/admin/assets/admin.css
```

---

## PHASE 7: Advanced Features Implementation

### Research Prompt 7.1
```
Research advanced SEO and ecommerce optimization:
- https://developers.google.com/search/docs/appearance/structured-data/product
- https://schema.org/Product
- https://ogp.me/
- https://developer.mozilla.org/en-US/docs/Learn/HTML/Introduction_to_HTML/The_head_metadata_in_HTML

Focus on structured data, Open Graph tags, and technical SEO requirements.
```

### Implementation Prompt 7.2
```
Context: Core system operational with WordPress integration complete.

Previous files: All base functionality implemented across api/, workers/, frontend/, and WordPress plugin.

Add advanced optimization features:
- Implement structured data generation (JSON-LD)
- Create Open Graph tag optimization
- Add image alt text generation using AI
- Implement A/B testing framework for titles/descriptions
- Create competitor analysis endpoints
- Add keyword research functionality
- Implement multi-language support system
- Create SEO scoring algorithm
- Add canonical URL management
- Implement XML sitemap generation
- Create robots.txt optimization
- Add internal linking suggestions

Files to modify/create:
- api/services/seo_analysis.py
- api/services/structured_data.py
- api/agents/image_analyzer.py
- api/services/ab_testing.py
- api/services/competitor_analysis.py
- workers/tasks/seo_tasks.py
- api/api/v1/endpoints/analytics.py
```

---

## PHASE 8: Monitoring and Analytics

### Research Prompt 8.1
```
Research monitoring and analytics setup:
- https://prometheus.io/docs/instrumenting/exporters/
- https://docs.sentry.io/platforms/python/integrations/fastapi/
- https://docs.datadoghq.com/integrations/fastapi/

Focus on metrics collection, error tracking, and performance monitoring.
```

### Implementation Prompt 8.2
```
Context: Full application built with all features implemented. Familiarize yourself with the #codebase

Previous files: Complete application across all services.

Implement monitoring and analytics:
- Set up Prometheus metrics collection
- Create custom metrics for optimization performance
- Implement Sentry error tracking
- Add performance monitoring endpoints
- Create analytics dashboard data endpoints
- Implement cost tracking for API usage
- Add conversion tracking system
- Create reporting system with PDF generation
- Implement audit logging
- Add health check endpoints
- Create backup and restore functionality

Files to create/modify:
- api/monitoring/metrics.py
- api/monitoring/health.py
- docker-compose.monitoring.yml
- api/services/reporting.py
- api/services/analytics.py
```

---

## PHASE 9: Testing Suite

### Research Prompt 9.1
```
Research testing best practices:
- https://docs.pytest.org/en/stable/
- https://fastapi.tiangolo.com/tutorial/testing/
- https://docs.celeryq.dev/en/stable/userguide/testing.html

Focus on unit testing, integration testing, and load testing strategies.
```

### Implementation Prompt 9.2
```
Context: Complete application ready for testing.

Previous files: All application code implemented.

Create comprehensive testing suite:
- Write unit tests for all API endpoints
- Create integration tests for AI agents
- Implement Celery task testing
- Add WebSocket connection tests
- Create load testing scripts
- Implement security testing
- Add WordPress plugin testing
- Create mock OpenRouter responses for testing - with comments as reminders to implement full integration using API
- Implement database migration tests
- Add frontend JavaScript tests

Files to create:
- tests/unit/test_api.py
- tests/unit/test_agents.py
- tests/integration/test_workflows.py
- tests/load/locustfile.py
- tests/fixtures/
- .github/workflows/tests.yml
```

---

## PHASE 10: Deployment and Documentation

### Research Prompt 10.1
```
Research deployment strategies:
- https://docs.docker.com/compose/production/
- https://www.nginx.com/blog/deploying-nginx-nginx-plus-docker/
- https://letsencrypt.org/docs/

Focus on production Docker deployment, SSL setup, and security hardening.
```

### Implementation Prompt 10.2
```
Context: Application tested and ready for deployment.

Previous files: Complete application with testing suite.

Prepare for production deployment:
- Create production Docker Compose configuration
- Set up SSL certificates with Let's Encrypt
- Implement security headers in Nginx
- Create deployment scripts
- Write comprehensive documentation
- Create API documentation
- Add user guides for dashboard
- Create WordPress plugin documentation
- Implement backup strategies
- Create monitoring dashboards
- Add rate limiting and DDoS protection

Files to create:
- docker-compose.prod.yml
- scripts/deploy.sh
- scripts/backup.sh
- docs/API.md
- docs/USER_GUIDE.md
- docs/WORDPRESS_PLUGIN.md
- docs/DEPLOYMENT.md
- .github/workflows/deploy.yml
```

---

###
Deploy the app and run ALL tests in #tests.

## Critical Implementation Notes

**Include in relevant prompts:**

1. **State Management**: Always track which files were created/modified in previous steps
2. **Error Prevention**: Explicitly state "do not refactor existing working code unless specifically requested"
3. **Version Pinning**: Always use specific versions in requirements.txt and package.json
4. **Environment Variables**: Never hardcode sensitive data; always use environment variables
5. **Incremental Testing**: Test each phase before moving to the next
6. **Documentation**: Update README.md after each phase with new setup steps
7. **Git Commits**: Suggest creating a git commit after each successful phase

**Context Preservation Strategy:**
- Start each prompt with: "Context: [current state]. Previous files: [list of created files]"
- End each prompt with: "Files to create/modify: [explicit list]"
- Include: "Preserve existing functionality. Only add new features."

**Anti-Hallucination Patterns:**
- Be explicit about file paths
- Specify exact function/class names when referencing existing code
- Include "use only documented APIs" when integrating external services
- State "follow official documentation patterns" for    framework-specific code