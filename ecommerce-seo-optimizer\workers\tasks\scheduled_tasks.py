"""
Scheduled Tasks for GridSpoke Ecommerce SEO Optimizer
Handles periodic tasks like daily optimization runs, analytics, and maintenance.
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from celery import current_task, group, chain
from celery.exceptions import Ignore

# Add the API directory to the Python path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '../../api'))

from workers.celery_app import celery_app
from workers.utils.progress_tracker import track_progress, update_task_status
from workers.utils.redis_client import get_redis_client
from workers.utils.error_handler import handle_task_error

logger = logging.getLogger(__name__)

@celery_app.task(bind=True, acks_late=True)
def daily_optimization_run(self):
    """
    Daily optimization run scheduled for 2 AM UTC (off-peak hours).
    Optimizes products that haven't been optimized recently.
    """
    try:
        from api.crud.store import get_active_stores
        from api.crud.product import get_products_needing_optimization
        from api.core.database import get_db
        from workers.tasks.product_tasks import optimize_product_batch
        
        logger.info("Starting daily optimization run")
        
        # Get database session
        db = next(get_db())
        
        # Get all active stores
        active_stores = get_active_stores(db)
        
        optimization_summary = {
            'run_date': datetime.utcnow().date().isoformat(),
            'stores_processed': 0,
            'products_optimized': 0,
            'total_cost': 0.0,
            'stores': []
        }
        
        for store in active_stores:
            try:
                logger.info(f"Processing store {store.id} ({store.name})")
                
                # Get products that need optimization (not optimized in last 30 days)
                cutoff_date = datetime.utcnow() - timedelta(days=30)
                products_to_optimize = get_products_needing_optimization(
                    db, store.id, cutoff_date, limit=100
                )
                
                if not products_to_optimize:
                    logger.info(f"No products need optimization for store {store.id}")
                    continue
                
                product_ids = [p.id for p in products_to_optimize]
                
                # Start batch optimization for this store
                batch_task = optimize_product_batch.delay(
                    store_id=store.id,
                    product_ids=product_ids,
                    batch_size=20
                )
                
                # Wait for completion (with timeout)
                try:
                    batch_result = batch_task.get(timeout=3600)  # 1 hour timeout
                    
                    optimization_summary['stores_processed'] += 1
                    optimization_summary['products_optimized'] += batch_result.get('successful_optimizations', 0)
                    optimization_summary['total_cost'] += batch_result.get('total_cost_usd', 0)
                    
                    optimization_summary['stores'].append({
                        'store_id': store.id,
                        'store_name': store.name,
                        'products_processed': len(product_ids),
                        'successful_optimizations': batch_result.get('successful_optimizations', 0),
                        'cost_usd': batch_result.get('total_cost_usd', 0)
                    })
                    
                except Exception as e:
                    logger.error(f"Error optimizing store {store.id}: {str(e)}")
                    optimization_summary['stores'].append({
                        'store_id': store.id,
                        'store_name': store.name,
                        'status': 'failed',
                        'error': str(e)
                    })
                
            except Exception as e:
                logger.error(f"Error processing store {store.id}: {str(e)}")
                continue
        
        # Store daily summary in Redis
        redis_client = get_redis_client()
        redis_client.setex(
            f"daily_optimization_summary:{optimization_summary['run_date']}",
            86400 * 7,  # Keep for 7 days
            json.dumps(optimization_summary)
        )
        
        logger.info(f"Daily optimization completed: {optimization_summary['products_optimized']} products optimized across {optimization_summary['stores_processed']} stores")
        
        return optimization_summary
        
    except Exception as exc:
        logger.error(f"Error in daily optimization run: {str(exc)}")
        raise

@celery_app.task(bind=True, acks_late=True)
def weekly_seo_analysis(self):
    """
    Weekly SEO performance analysis scheduled for Monday 3 AM UTC.
    Analyzes SEO improvements and generates reports.
    """
    try:
        from api.crud.store import get_active_stores
        from api.crud.analytics import get_weekly_seo_metrics
        from api.services.reporting import generate_seo_report
        from api.core.database import get_db
        
        logger.info("Starting weekly SEO analysis")
        
        # Get database session
        db = next(get_db())
        
        # Get all active stores
        active_stores = get_active_stores(db)
        
        weekly_analysis = {
            'analysis_date': datetime.utcnow().date().isoformat(),
            'week_ending': (datetime.utcnow() - timedelta(days=datetime.utcnow().weekday())).date().isoformat(),
            'stores_analyzed': 0,
            'reports_generated': 0,
            'stores': []
        }
        
        for store in active_stores:
            try:
                logger.info(f"Analyzing SEO performance for store {store.id}")
                
                # Get weekly metrics
                metrics = get_weekly_seo_metrics(db, store.id)
                
                if not metrics:
                    logger.info(f"No metrics available for store {store.id}")
                    continue
                
                # Generate SEO report
                report_service = generate_seo_report()
                report = report_service.generate_weekly_report(store.id, metrics)
                
                weekly_analysis['stores_analyzed'] += 1
                weekly_analysis['reports_generated'] += 1
                
                weekly_analysis['stores'].append({
                    'store_id': store.id,
                    'store_name': store.name,
                    'metrics': {
                        'products_optimized_this_week': metrics.get('products_optimized', 0),
                        'avg_seo_score_improvement': metrics.get('avg_score_improvement', 0),
                        'content_pieces_generated': metrics.get('content_generated', 0),
                        'estimated_traffic_increase': metrics.get('traffic_increase', 0)
                    },
                    'report_generated': True
                })
                
                # Store individual store report
                redis_client = get_redis_client()
                redis_client.setex(
                    f"weekly_seo_report:{store.id}:{weekly_analysis['week_ending']}",
                    86400 * 30,  # Keep for 30 days
                    json.dumps(report)
                )
                
            except Exception as e:
                logger.error(f"Error analyzing store {store.id}: {str(e)}")
                weekly_analysis['stores'].append({
                    'store_id': store.id,
                    'store_name': store.name,
                    'status': 'failed',
                    'error': str(e)
                })
        
        # Store weekly analysis summary
        redis_client = get_redis_client()
        redis_client.setex(
            f"weekly_seo_analysis:{weekly_analysis['week_ending']}",
            86400 * 30,  # Keep for 30 days
            json.dumps(weekly_analysis)
        )
        
        logger.info(f"Weekly SEO analysis completed: {weekly_analysis['stores_analyzed']} stores analyzed")
        
        return weekly_analysis
        
    except Exception as exc:
        logger.error(f"Error in weekly SEO analysis: {str(exc)}")
        raise

@celery_app.task(bind=True, acks_late=True)
def check_store_updates(self):
    """
    Hourly task to check for store updates from WordPress plugins.
    Processes webhook data and triggers optimizations if needed.
    """
    try:
        from api.crud.webhook import get_pending_webhooks, mark_webhook_processed
        from api.crud.product import create_or_update_product
        from api.core.database import get_db
        from workers.tasks.product_tasks import optimize_single_product
        
        logger.info("Checking for store updates")
        
        # Get database session
        db = next(get_db())
        
        # Get pending webhooks
        pending_webhooks = get_pending_webhooks(db, limit=100)
        
        if not pending_webhooks:
            logger.info("No pending webhooks to process")
            return {'status': 'no_updates', 'processed_webhooks': 0}
        
        processed_count = 0
        optimization_tasks = []
        
        for webhook in pending_webhooks:
            try:
                webhook_data = json.loads(webhook.payload)
                webhook_type = webhook_data.get('type')
                
                if webhook_type == 'product_created':
                    # Handle new product creation
                    product_data = webhook_data.get('product')
                    
                    # Create product in our database
                    created_product = create_or_update_product(db, webhook.store_id, product_data)
                    
                    # Trigger optimization for new product
                    if created_product:
                        optimization_task = optimize_single_product.delay(
                            product_id=created_product.id,
                            store_id=webhook.store_id
                        )
                        optimization_tasks.append(optimization_task.id)
                
                elif webhook_type == 'product_updated':
                    # Handle product updates
                    product_data = webhook_data.get('product')
                    product_id = webhook_data.get('product_id')
                    
                    # Update product in our database
                    updated_product = create_or_update_product(
                        db, webhook.store_id, product_data, product_id
                    )
                    
                    # Trigger re-optimization if significant changes
                    if updated_product and self._should_reoptimize(product_data):
                        optimization_task = optimize_single_product.delay(
                            product_id=updated_product.id,
                            store_id=webhook.store_id
                        )
                        optimization_tasks.append(optimization_task.id)
                
                elif webhook_type == 'bulk_optimization_request':
                    # Handle bulk optimization requests from WordPress
                    product_ids = webhook_data.get('product_ids', [])
                    
                    if product_ids:
                        from workers.tasks.product_tasks import optimize_product_batch
                        bulk_task = optimize_product_batch.delay(
                            store_id=webhook.store_id,
                            product_ids=product_ids
                        )
                        optimization_tasks.append(bulk_task.id)
                
                # Mark webhook as processed
                mark_webhook_processed(db, webhook.id)
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Error processing webhook {webhook.id}: {str(e)}")
                continue
        
        update_summary = {
            'status': 'completed',
            'processed_webhooks': processed_count,
            'triggered_optimizations': len(optimization_tasks),
            'optimization_task_ids': optimization_tasks,
            'processed_at': datetime.utcnow().isoformat()
        }
        
        logger.info(f"Store updates processed: {processed_count} webhooks, {len(optimization_tasks)} optimizations triggered")
        
        return update_summary
        
    except Exception as exc:
        logger.error(f"Error checking store updates: {str(exc)}")
        raise

def _should_reoptimize(self, product_data: Dict[str, Any]) -> bool:
    """
    Determine if a product should be re-optimized based on changes.
    """
    significant_fields = ['title', 'description', 'category', 'price']
    return any(field in product_data for field in significant_fields)

@celery_app.task(bind=True, acks_late=True)
def monthly_analytics_report(self):
    """
    Monthly analytics aggregation scheduled for the 1st of each month at 4 AM UTC.
    Generates comprehensive monthly reports for all stores.
    """
    try:
        from api.crud.store import get_active_stores
        from api.crud.analytics import get_monthly_analytics
        from api.services.reporting import generate_monthly_report
        from api.core.database import get_db
        
        logger.info("Starting monthly analytics report generation")
        
        # Get database session
        db = next(get_db())
        
        # Calculate previous month dates
        today = datetime.utcnow().date()
        first_day_current_month = today.replace(day=1)
        last_day_previous_month = first_day_current_month - timedelta(days=1)
        first_day_previous_month = last_day_previous_month.replace(day=1)
        
        # Get all active stores
        active_stores = get_active_stores(db)
        
        monthly_report = {
            'report_month': last_day_previous_month.strftime('%Y-%m'),
            'generated_date': datetime.utcnow().date().isoformat(),
            'stores_processed': 0,
            'total_products_optimized': 0,
            'total_content_generated': 0,
            'total_cost_usd': 0.0,
            'stores': []
        }
        
        for store in active_stores:
            try:
                logger.info(f"Generating monthly report for store {store.id}")
                
                # Get monthly analytics data
                analytics = get_monthly_analytics(
                    db, store.id, first_day_previous_month, last_day_previous_month
                )
                
                if not analytics:
                    logger.info(f"No analytics data for store {store.id}")
                    continue
                
                # Generate detailed monthly report
                report_service = generate_monthly_report()
                detailed_report = report_service.generate_report(store.id, analytics)
                
                store_summary = {
                    'store_id': store.id,
                    'store_name': store.name,
                    'products_optimized': analytics.get('products_optimized', 0),
                    'content_pieces_generated': analytics.get('content_generated', 0),
                    'total_cost_usd': analytics.get('total_cost', 0.0),
                    'avg_seo_score_improvement': analytics.get('avg_score_improvement', 0),
                    'estimated_traffic_increase': analytics.get('traffic_increase_percentage', 0),
                    'roi_estimate': analytics.get('roi_estimate', 0)
                }
                
                monthly_report['stores'].append(store_summary)
                monthly_report['stores_processed'] += 1
                monthly_report['total_products_optimized'] += store_summary['products_optimized']
                monthly_report['total_content_generated'] += store_summary['content_pieces_generated']
                monthly_report['total_cost_usd'] += store_summary['total_cost_usd']
                
                # Store individual store report
                redis_client = get_redis_client()
                redis_client.setex(
                    f"monthly_report:{store.id}:{monthly_report['report_month']}",
                    86400 * 90,  # Keep for 90 days
                    json.dumps(detailed_report)
                )
                
            except Exception as e:
                logger.error(f"Error generating monthly report for store {store.id}: {str(e)}")
                continue
        
        # Store monthly summary
        redis_client = get_redis_client()
        redis_client.setex(
            f"monthly_analytics_summary:{monthly_report['report_month']}",
            86400 * 90,  # Keep for 90 days
            json.dumps(monthly_report)
        )
        
        logger.info(f"Monthly analytics completed: {monthly_report['stores_processed']} stores processed")
        
        return monthly_report
        
    except Exception as exc:
        logger.error(f"Error in monthly analytics report: {str(exc)}")
        raise

@celery_app.task(bind=True, acks_late=True)
def cleanup_old_task_results(self):
    """
    Daily cleanup task to remove old task results and cached data.
    Scheduled for 1:30 AM UTC daily.
    """
    try:
        redis_client = get_redis_client()
        
        # Define cleanup patterns and their retention periods (in seconds)
        cleanup_patterns = {
            'task_progress:*': 86400,  # 1 day
            'optimization_result:*': 86400 * 7,  # 7 days
            'generated_content:*': 86400 * 30,  # 30 days
            'batch_optimization:*': 86400 * 14,  # 14 days
            'daily_optimization_summary:*': 86400 * 30,  # 30 days
        }
        
        cleanup_summary = {
            'cleanup_date': datetime.utcnow().date().isoformat(),
            'patterns_processed': 0,
            'keys_deleted': 0,
            'total_space_freed': 0
        }
        
        for pattern, retention_seconds in cleanup_patterns.items():
            try:
                # Find keys matching pattern
                keys = redis_client.keys(pattern)
                
                deleted_count = 0
                cutoff_time = datetime.utcnow().timestamp() - retention_seconds
                
                for key in keys:
                    try:
                        # Check key age
                        key_info = redis_client.object('IDLETIME', key)
                        if key_info and key_info > retention_seconds:
                            redis_client.delete(key)
                            deleted_count += 1
                            
                    except Exception as e:
                        # If we can't get key info, delete keys older than cutoff
                        redis_client.delete(key)
                        deleted_count += 1
                
                cleanup_summary['keys_deleted'] += deleted_count
                cleanup_summary['patterns_processed'] += 1
                
                logger.info(f"Cleaned up {deleted_count} keys for pattern {pattern}")
                
            except Exception as e:
                logger.error(f"Error cleaning up pattern {pattern}: {str(e)}")
                continue
        
        # Also cleanup Celery result backend
        try:
            from celery.result import AsyncResult
            
            # Clean up old task results (older than 24 hours)
            cutoff_time = datetime.utcnow() - timedelta(hours=24)
            # Note: This would require additional logic to track task timestamps
            
        except Exception as e:
            logger.error(f"Error cleaning up Celery results: {str(e)}")
        
        logger.info(f"Cleanup completed: {cleanup_summary['keys_deleted']} keys deleted")
        
        return cleanup_summary
        
    except Exception as exc:
        logger.error(f"Error in cleanup task: {str(exc)}")
        raise

@celery_app.task(bind=True, acks_late=True)
def analyze_api_costs(self):
    """
    Weekly cost analysis task scheduled for Sunday 5 AM UTC.
    Analyzes OpenRouter API usage and costs.
    """
    try:
        from api.crud.analytics import get_weekly_cost_data
        from api.core.database import get_db
        
        logger.info("Starting weekly API cost analysis")
        
        # Get database session
        db = next(get_db())
        
        # Calculate week dates
        today = datetime.utcnow().date()
        week_start = today - timedelta(days=today.weekday() + 7)  # Previous week start
        week_end = week_start + timedelta(days=6)  # Previous week end
        
        # Get cost data for all stores
        cost_data = get_weekly_cost_data(db, week_start, week_end)
        
        cost_analysis = {
            'analysis_week': f"{week_start.isoformat()} to {week_end.isoformat()}",
            'total_api_calls': sum(store['api_calls'] for store in cost_data),
            'total_tokens_used': sum(store['tokens_used'] for store in cost_data),
            'total_cost_usd': sum(store['cost_usd'] for store in cost_data),
            'average_cost_per_optimization': 0,
            'most_expensive_store': None,
            'cost_by_model': {},
            'optimization_efficiency': {},
            'stores': cost_data
        }
        
        # Calculate averages and insights
        if cost_analysis['total_api_calls'] > 0:
            cost_analysis['average_cost_per_optimization'] = (
                cost_analysis['total_cost_usd'] / cost_analysis['total_api_calls']
            )
        
        # Find most expensive store
        if cost_data:
            most_expensive = max(cost_data, key=lambda x: x['cost_usd'])
            cost_analysis['most_expensive_store'] = {
                'store_id': most_expensive['store_id'],
                'store_name': most_expensive['store_name'],
                'cost_usd': most_expensive['cost_usd']
            }
        
        # Store cost analysis
        redis_client = get_redis_client()
        redis_client.setex(
            f"weekly_cost_analysis:{week_start.isoformat()}",
            86400 * 30,  # Keep for 30 days
            json.dumps(cost_analysis)
        )
        
        logger.info(f"Cost analysis completed: ${cost_analysis['total_cost_usd']:.2f} spent on {cost_analysis['total_api_calls']} API calls")
        
        return cost_analysis
        
    except Exception as exc:
        logger.error(f"Error in cost analysis: {str(exc)}")
        raise
