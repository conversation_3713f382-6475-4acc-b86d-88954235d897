"""
Product CRUD operations.
"""
import uuid
from typing import Optional, List, Dict, Any
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from datetime import datetime, timezone

from crud.base import CRUDBase
from models.product import Product, OptimizationStatus
from schemas.product import ProductCreate, ProductUpdate, ProductStatus


class CRUDProduct(CRUDBase[Product, ProductCreate, ProductUpdate]):
    """CRUD operations for Product model."""
    
    async def get_by_store(
        self, 
        db: AsyncSession, 
        *, 
        store_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[OptimizationStatus] = None
    ) -> List[Product]:
        """Get products by store ID."""
        query = select(Product).where(Product.store_id == store_id)

        if status:
            query = query.where(Product.optimization_status == status)
        
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_external_id(
        self, 
        db: AsyncSession, 
        *, 
        store_id: uuid.UUID,
        external_id: str
    ) -> Optional[Product]:
        """Get product by store and external ID."""
        query = select(Product).where(
            and_(
                Product.store_id == store_id,
                Product.external_id == external_id
            )
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_by_sku(
        self, 
        db: AsyncSession, 
        *, 
        store_id: uuid.UUID,
        sku: str
    ) -> Optional[Product]:
        """Get product by store and SKU."""
        query = select(Product).where(
            and_(
                Product.store_id == store_id,
                Product.sku == sku
            )
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def search_products(
        self,
        db: AsyncSession,
        *,
        store_id: uuid.UUID,
        search_term: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Product]:
        """Search products by name, description, or SKU."""
        search_filter = f"%{search_term}%"
        
        query = select(Product).where(
            and_(
                Product.store_id == store_id,
                or_(
                    Product.title.ilike(search_filter),
                    Product.description.ilike(search_filter),
                    Product.sku.ilike(search_filter)
                )
            )
        ).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_optimizable_products(
        self,
        db: AsyncSession,
        *,
        store_id: uuid.UUID,
        limit: int = 100
    ) -> List[Product]:
        """Get products that need optimization."""
        query = select(Product).where(
            and_(
                Product.store_id == store_id,
                Product.is_active.is_(True),
                or_(
                    Product.last_optimized.is_(None),
                    (Product.optimization_score.is_not(None) & (Product.optimization_score < 0.7))
                )
            )
        ).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_with_content(
        self,
        db: AsyncSession,
        *,
        product_id: uuid.UUID
    ) -> Optional[Product]:
        """Get product with generated content."""
        query = select(Product).options(
            selectinload(Product.generated_content)
        ).where(Product.id == product_id)
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def update_seo_score(
        self,
        db: AsyncSession,
        *,
        product_id: uuid.UUID,
        seo_score: float
    ) -> Optional[Product]:
        """Update product SEO score."""
        product = await self.get(db, id=product_id)
        if product:
            product.optimization_score = seo_score
            product.last_optimized = datetime.now(timezone.utc)
            db.add(product)
            await db.commit()
            await db.refresh(product)
        return product
    
    async def mark_optimized(
        self,
        db: AsyncSession,
        *,
        product_id: uuid.UUID
    ) -> Optional[Product]:
        """Mark product as optimized."""
        product = await self.get(db, id=product_id)
        if product:
            product.last_optimized = datetime.now(timezone.utc)
            db.add(product)
            await db.commit()
            await db.refresh(product)
        return product
    
    async def get_by_categories(
        self,
        db: AsyncSession,
        *,
        store_id: uuid.UUID,
        categories: List[str],
        skip: int = 0,
        limit: int = 100
    ) -> List[Product]:
        """Get products by categories."""
        # This requires PostgreSQL array operations
        query = select(Product).where(
            and_(
                Product.store_id == store_id,
                Product.categories.overlap(categories)
            )
        ).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def count_by_store(
        self,
        db: AsyncSession,
        *,
        store_id: uuid.UUID,
        status: Optional[ProductStatus] = None
    ) -> int:
        """Count products by store."""
        query = select(func.count(Product.id)).where(Product.store_id == store_id)
        
        if status:
            query = query.where(Product.status == status)
        
        result = await db.execute(query)
        return result.scalar()
    
    async def bulk_update_status(
        self,
        db: AsyncSession,
        *,
        product_ids: List[uuid.UUID],
        status: ProductStatus
    ) -> int:
        """Bulk update product status."""
        from sqlalchemy import update
        
        stmt = update(Product).where(
            Product.id.in_(product_ids)
        ).values(status=status)
        
        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount


# Create instance for dependency injection
product_crud = CRUDProduct(Product)
