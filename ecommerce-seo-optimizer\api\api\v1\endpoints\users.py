"""
User management endpoints.
"""
import uuid
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.database import get_db_session
from core.security import get_current_user, get_current_superuser
from crud.user import user_crud
from schemas.user import User, UserCreate, UserUpdate, UserProfile
from models.user import User as UserModel
import structlog

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=List[User])
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_superuser)
) -> Any:
    """Get all users (superuser only)."""
    users = await user_crud.get_multi(db, skip=skip, limit=limit)
    return users


@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get current user profile."""
    return current_user


@router.put("/me", response_model=User)
async def update_current_user(
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Update current user profile."""
    user = await user_crud.update(db, db_obj=current_user, obj_in=user_update)
    
    logger.info(
        "User profile updated",
        user_id=str(user.id),
        updated_fields=list(user_update.model_dump(exclude_unset=True).keys())
    )
    
    return user


@router.get("/{user_id}", response_model=User)
async def get_user(
    user_id: uuid.UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_superuser)
) -> Any:
    """Get user by ID (superuser only)."""
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return user


@router.put("/{user_id}", response_model=User)
async def update_user(
    user_id: uuid.UUID,
    user_update: UserUpdate,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_superuser)
) -> Any:
    """Update user by ID (superuser only)."""
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    user = await user_crud.update(db, db_obj=user, obj_in=user_update)
    
    logger.info(
        "User updated by admin",
        user_id=str(user.id),
        admin_id=str(current_user.id),
        updated_fields=list(user_update.model_dump(exclude_unset=True).keys())
    )
    
    return user


@router.delete("/{user_id}")
async def delete_user(
    user_id: uuid.UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_superuser)
) -> Any:
    """Delete user by ID (superuser only)."""
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )
    
    await user_crud.remove(db, id=user_id)
    
    logger.info(
        "User deleted by admin",
        user_id=str(user.id),
        admin_id=str(current_user.id)
    )
    
    return {"message": "User deleted successfully"}


@router.get("/me/settings")
async def get_user_settings(
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get current user settings."""
    # Return user's profile settings or empty dict if none
    return current_user.profile_settings or {}


@router.put("/me/settings")
async def update_user_settings(
    settings_data: dict,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Update current user settings."""
    # Validate custom AI model if provided
    if settings_data.get("ai_model") == "custom" and settings_data.get("custom_ai_model"):
        if not validate_custom_ai_model(settings_data.get("custom_ai_model")):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid custom AI model name. Please use format: provider/model-name"
            )
    
    # Validate custom vision model if provided
    if settings_data.get("vision_model") == "custom" and settings_data.get("custom_vision_model"):
        if not validate_custom_ai_model(settings_data.get("custom_vision_model")):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid custom vision model name. Please use format: provider/model-name"
            )
    
    # Update the user's profile_settings field
    updated_user = await user_crud.update(
        db, 
        db_obj=current_user, 
        obj_in={"profile_settings": settings_data}
    )
    
    logger.info(
        "User settings updated",
        user_id=str(updated_user.id),
        updated_fields=list(settings_data.keys())
    )
    
    return updated_user.profile_settings


def validate_custom_ai_model(model_name: str) -> bool:
    """Validate custom AI model name for safety."""
    if not model_name or not isinstance(model_name, str):
        return False
    
    # Check length
    if len(model_name) > 100:
        return False
    
    # Check for valid format: provider/model-name
    import re
    model_regex = re.compile(r'^[a-zA-Z0-9\-_]+\/[a-zA-Z0-9\-_.]+$')
    if not model_regex.match(model_name):
        return False
    
    # Check for potentially dangerous patterns
    dangerous_patterns = [
        '..', '<', '>', '"', "'", '`', '$', ';', '&', '|', 
        '(', ')', '[', ']', '{', '}', '\n', '\r', '\t'
    ]
    
    for pattern in dangerous_patterns:
        if pattern in model_name:
            return False
    
    # Check that it's not trying to access local files or system resources
    forbidden_prefixes = ['file://', 'http://', 'https://', '/', '\\']
    for prefix in forbidden_prefixes:
        if model_name.lower().startswith(prefix):
            return False
    
    return True