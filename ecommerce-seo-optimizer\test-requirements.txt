# Test Requirements - Core dependencies only
# This is a simplified version for testing without Rust dependencies

# Core FastAPI
fastapi==0.115.0
uvicorn==0.30.6

# Database (async)
sqlalchemy==2.0.34
alembic==1.13.2
asyncpg==0.29.0

# Vector Database
pgvector==0.3.2

# AI Framework (without mirascope for now)
openai==1.44.1

# Redis and Celery
redis==5.0.8
celery==5.4.0

# Data Validation
pydantic==2.8.2
pydantic-settings==2.4.0

# HTTP Client
httpx==0.27.2

# Security
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.9

# Utilities
python-dotenv==1.0.1
email-validator==2.2.0

# Testing
pytest==8.3.2
pytest-asyncio==0.24.0

# Development tools
black==24.8.0
isort==5.13.2

# Additional testing tools
pytest-mock==3.11.1
pytest-cov==4.0.0
requests==2.31.0
