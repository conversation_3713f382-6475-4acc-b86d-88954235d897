import asyncio
import sys
import os
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

from sqlalchemy.ext.asyncio import AsyncSession
from core.database import get_db_session
from crud.user import user_crud
from core.security import verify_password, get_password_hash

async def test_user_auth():
    """Test user authentication"""
    try:
        # Try to authenticate the demo user
        async with get_db_session() as db:
            user = await user_crud.get_by_email(db, email="<EMAIL>")
            if user:
                print(f"Found user: {user.email}")
                print(f"User ID: {user.id}")
                
                # Test password verification
                test_password = "demo123"
                password_hash = get_password_hash(test_password)
                print(f"Generated hash for 'demo123': {password_hash}")
                
                # Verify the hash we inserted
                print(f"Database hash: {user.hashed_password}")
                
                # Try to verify our hash
                is_valid = verify_password(test_password, password_hash)
                print(f"Our hash valid: {is_valid}")
                
                # Try to verify database hash
                is_db_valid = verify_password(test_password, user.hashed_password)
                print(f"DB hash valid: {is_db_valid}")
                
                if is_db_valid:
                    print("SUCCESS: User authentication would work!")
                else:
                    print("ISSUE: Password verification failed")
            else:
                print("User not found")
                
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_user_auth())