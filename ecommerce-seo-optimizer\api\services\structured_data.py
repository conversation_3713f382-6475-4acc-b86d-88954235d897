"""
Structured Data Service - JSON-LD Schema Generation
Implements advanced structured data generation for ecommerce products.
"""

import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from decimal import Decimal

from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession

from ..models.product import Product
from ..models.store import Store
from ..schemas.product import ProductResponse
from ..agents.seo_optimizer import SEOOptimizer
from ..core.config import settings


class StructuredDataSchema(BaseModel):
    """Schema for structured data output"""
    product_schema: Dict[str, Any]
    breadcrumb_schema: Optional[Dict[str, Any]] = None
    organization_schema: Optional[Dict[str, Any]] = None
    offer_schema: Dict[str, Any]
    review_schema: Optional[Dict[str, Any]] = None
    faq_schema: Optional[Dict[str, Any]] = None


class ProductStructuredData:
    """Core product schema.org/Product structured data"""
    
    @staticmethod
    def generate_base_product_schema(
        product: ProductResponse,
        store_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate base Product schema"""
        schema = {
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": product.title,
            "description": product.description or "",
            "sku": product.sku or str(product.id),
            "brand": {
                "@type": "Brand",
                "name": store_data.get("brand_name", store_data.get("name", "Store"))
            }
        }
        
        # Add product identifiers if available
        if product.gtin:
            schema["gtin"] = product.gtin
        if product.mpn:
            schema["mpn"] = product.mpn
        if product.upc:
            schema["gtin12"] = product.upc
        if product.ean:
            schema["gtin13"] = product.ean
            
        # Add category information
        if product.category:
            schema["category"] = product.category
            
        # Add images
        if product.images and len(product.images) > 0:
            schema["image"] = product.images
            
        # Add dimensions if available
        if hasattr(product, 'dimensions') and product.dimensions:
            if product.dimensions.get('weight'):
                schema["weight"] = {
                    "@type": "QuantitativeValue",
                    "value": product.dimensions['weight'],
                    "unitCode": "KGM"  # Kilograms
                }
            if product.dimensions.get('length'):
                schema["depth"] = {
                    "@type": "QuantitativeValue", 
                    "value": product.dimensions['length'],
                    "unitCode": "CMT"  # Centimeters
                }
            if product.dimensions.get('width'):
                schema["width"] = {
                    "@type": "QuantitativeValue",
                    "value": product.dimensions['width'], 
                    "unitCode": "CMT"
                }
            if product.dimensions.get('height'):
                schema["height"] = {
                    "@type": "QuantitativeValue",
                    "value": product.dimensions['height'],
                    "unitCode": "CMT"
                }
                
        return schema


class OfferStructuredData:
    """Schema.org/Offer structured data"""
    
    @staticmethod
    def generate_offer_schema(
        product: ProductResponse,
        store_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate Offer schema for product"""
        offer = {
            "@type": "Offer",
            "url": f"{store_data.get('url', '')}/product/{product.id}",
            "priceCurrency": product.currency or "USD",
            "price": str(product.price) if product.price else "0.00",
            "availability": "https://schema.org/InStock",  # Default to in stock
            "seller": {
                "@type": "Organization",
                "name": store_data.get("name", "Store")
            }
        }
        
        # Add stock status if available
        if hasattr(product, 'stock_status'):
            availability_map = {
                "in_stock": "https://schema.org/InStock",
                "out_of_stock": "https://schema.org/OutOfStock", 
                "limited_availability": "https://schema.org/LimitedAvailability",
                "preorder": "https://schema.org/PreOrder"
            }
            offer["availability"] = availability_map.get(
                product.stock_status, 
                "https://schema.org/InStock"
            )
            
        # Add condition
        offer["itemCondition"] = "https://schema.org/NewCondition"
        
        # Add valid dates if available
        if hasattr(product, 'sale_price_end_date') and product.sale_price_end_date:
            offer["priceValidUntil"] = product.sale_price_end_date.isoformat()
            
        # Add shipping details if available
        if store_data.get('shipping_policy'):
            offer["shippingDetails"] = {
                "@type": "OfferShippingDetails",
                "shippingRate": {
                    "@type": "MonetaryAmount",
                    "value": store_data['shipping_policy'].get('rate', '0.00'),
                    "currency": product.currency or "USD"
                }
            }
            
        return offer


class ReviewStructuredData:
    """Schema.org/Review and AggregateRating structured data"""
    
    @staticmethod
    def generate_review_schema(
        product: ProductResponse,
        reviews_data: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate review schema if review data exists"""
        if not reviews_data:
            return None
            
        schema = {}
        
        # Add aggregate rating if available
        if reviews_data.get('aggregate_rating'):
            aggregate = reviews_data['aggregate_rating']
            schema["aggregateRating"] = {
                "@type": "AggregateRating",
                "ratingValue": str(aggregate.get('rating_value', 5.0)),
                "reviewCount": str(aggregate.get('review_count', 0)),
                "bestRating": "5",
                "worstRating": "1"
            }
            
        # Add individual reviews
        if reviews_data.get('reviews') and len(reviews_data['reviews']) > 0:
            schema["review"] = []
            for review in reviews_data['reviews'][:5]:  # Limit to 5 reviews
                review_schema = {
                    "@type": "Review",
                    "reviewRating": {
                        "@type": "Rating",
                        "ratingValue": str(review.get('rating', 5)),
                        "bestRating": "5",
                        "worstRating": "1"
                    },
                    "author": {
                        "@type": "Person",
                        "name": review.get('author_name', 'Customer')
                    }
                }
                
                if review.get('review_body'):
                    review_schema["reviewBody"] = review['review_body']
                    
                if review.get('date_published'):
                    review_schema["datePublished"] = review['date_published']
                    
                schema["review"].append(review_schema)
                
        return schema if schema else None


class BreadcrumbStructuredData:
    """Schema.org/BreadcrumbList structured data"""
    
    @staticmethod
    def generate_breadcrumb_schema(
        product: ProductResponse,
        store_data: Dict[str, Any],
        category_path: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Generate breadcrumb navigation schema"""
        base_url = store_data.get('url', '')
        
        breadcrumbs = [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": base_url
            }
        ]
        
        position = 2
        
        # Add category path if available
        if category_path:
            for i, category in enumerate(category_path):
                breadcrumbs.append({
                    "@type": "ListItem", 
                    "position": position,
                    "name": category,
                    "item": f"{base_url}/category/{category.lower().replace(' ', '-')}"
                })
                position += 1
                
        # Add current product
        breadcrumbs.append({
            "@type": "ListItem",
            "position": position,
            "name": product.title,
            "item": f"{base_url}/product/{product.id}"
        })
        
        return {
            "@context": "https://schema.org/",
            "@type": "BreadcrumbList",
            "itemListElement": breadcrumbs
        }


class OrganizationStructuredData:
    """Schema.org/Organization structured data"""
    
    @staticmethod
    def generate_organization_schema(store_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate organization schema for the store"""
        schema = {
            "@context": "https://schema.org/",
            "@type": "Organization",
            "name": store_data.get("name", "Store"),
            "url": store_data.get("url", "")
        }
        
        # Add logo if available
        if store_data.get("logo"):
            schema["logo"] = store_data["logo"]
            
        # Add contact information
        if store_data.get("contact"):
            contact = store_data["contact"]
            schema["contactPoint"] = {
                "@type": "ContactPoint",
                "contactType": "customer service"
            }
            
            if contact.get("phone"):
                schema["contactPoint"]["telephone"] = contact["phone"]
            if contact.get("email"):
                schema["contactPoint"]["email"] = contact["email"]
                
        # Add address if available
        if store_data.get("address"):
            address = store_data["address"]
            schema["address"] = {
                "@type": "PostalAddress",
                "streetAddress": address.get("street", ""),
                "addressLocality": address.get("city", ""),
                "addressRegion": address.get("state", ""),
                "postalCode": address.get("zip", ""),
                "addressCountry": address.get("country", "US")
            }
            
        return schema


class FAQStructuredData:
    """Schema.org/FAQPage structured data"""
    
    @staticmethod
    def generate_faq_schema(
        faqs: List[Dict[str, str]]
    ) -> Optional[Dict[str, Any]]:
        """Generate FAQ schema for product page"""
        if not faqs or len(faqs) == 0:
            return None
            
        schema = {
            "@context": "https://schema.org/",
            "@type": "FAQPage",
            "mainEntity": []
        }
        
        for faq in faqs:
            if faq.get('question') and faq.get('answer'):
                schema["mainEntity"].append({
                    "@type": "Question",
                    "name": faq['question'],
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": faq['answer']
                    }
                })
                
        return schema if schema["mainEntity"] else None


class StructuredDataService:
    """Main service for generating all structured data types"""
    
    def __init__(self):
        self.seo_optimizer = SEOOptimizer()
        
    async def generate_complete_structured_data(
        self,
        db: AsyncSession,
        product: ProductResponse,
        store_id: str,
        include_reviews: bool = True,
        include_faqs: bool = True,
        category_path: Optional[List[str]] = None
    ) -> StructuredDataSchema:
        """Generate complete structured data for a product"""
        
        # Get store data
        store_data = await self._get_store_data(db, store_id)
        
        # Generate base product schema
        product_schema = ProductStructuredData.generate_base_product_schema(
            product, store_data
        )
        
        # Generate offer schema
        offer_schema = OfferStructuredData.generate_offer_schema(
            product, store_data
        )
        
        # Add offer to product schema
        product_schema["offers"] = offer_schema
        
        # Generate additional schemas
        breadcrumb_schema = BreadcrumbStructuredData.generate_breadcrumb_schema(
            product, store_data, category_path
        )
        
        organization_schema = OrganizationStructuredData.generate_organization_schema(
            store_data
        )
        
        # Generate review schema if requested
        review_schema = None
        if include_reviews:
            reviews_data = await self._get_reviews_data(db, product.id)
            review_schema = ReviewStructuredData.generate_review_schema(
                product, reviews_data
            )
            if review_schema:
                product_schema.update(review_schema)
                
        # Generate FAQ schema if requested
        faq_schema = None
        if include_faqs:
            faqs = await self._generate_product_faqs(product)
            faq_schema = FAQStructuredData.generate_faq_schema(faqs)
            
        return StructuredDataSchema(
            product_schema=product_schema,
            breadcrumb_schema=breadcrumb_schema,
            organization_schema=organization_schema,
            offer_schema=offer_schema,
            review_schema=review_schema,
            faq_schema=faq_schema
        )
        
    async def generate_json_ld_scripts(
        self,
        structured_data: StructuredDataSchema
    ) -> List[str]:
        """Convert structured data to JSON-LD script tags"""
        scripts = []
        
        # Product schema (includes offer and review data)
        scripts.append(
            f'<script type="application/ld+json">\n'
            f'{json.dumps(structured_data.product_schema, indent=2, ensure_ascii=False)}\n'
            f'</script>'
        )
        
        # Breadcrumb schema
        if structured_data.breadcrumb_schema:
            scripts.append(
                f'<script type="application/ld+json">\n'
                f'{json.dumps(structured_data.breadcrumb_schema, indent=2, ensure_ascii=False)}\n'
                f'</script>'
            )
            
        # Organization schema  
        if structured_data.organization_schema:
            scripts.append(
                f'<script type="application/ld+json">\n'
                f'{json.dumps(structured_data.organization_schema, indent=2, ensure_ascii=False)}\n'
                f'</script>'
            )
            
        # FAQ schema
        if structured_data.faq_schema:
            scripts.append(
                f'<script type="application/ld+json">\n'
                f'{json.dumps(structured_data.faq_schema, indent=2, ensure_ascii=False)}\n'
                f'</script>'
            )
            
        return scripts
        
    async def validate_structured_data(
        self,
        schema_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate structured data against Schema.org standards"""
        # Basic validation
        errors = []
        warnings = []
        
        # Check required fields for Product schema
        if schema_data.get("@type") == "Product":
            required_fields = ["name", "offers"]
            for field in required_fields:
                if not schema_data.get(field):
                    errors.append(f"Missing required field: {field}")
                    
            # Check offer structure
            offers = schema_data.get("offers", {})
            if offers:
                offer_required = ["price", "priceCurrency", "availability"]
                for field in offer_required:
                    if not offers.get(field):
                        warnings.append(f"Missing recommended offer field: {field}")
                        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "schema_type": schema_data.get("@type", "Unknown")
        }
        
    async def _get_store_data(self, db: AsyncSession, store_id: str) -> Dict[str, Any]:
        """Get store data for structured data generation"""
        # This would typically query the database for store information
        # For now, return mock data structure
        return {
            "name": "Sample Store",
            "url": "https://example.com",
            "brand_name": "Sample Brand",
            "logo": "https://example.com/logo.jpg",
            "contact": {
                "phone": "******-123-4567",
                "email": "<EMAIL>"
            },
            "address": {
                "street": "123 Commerce St",
                "city": "Commerce City", 
                "state": "CO",
                "zip": "80022",
                "country": "US"
            },
            "shipping_policy": {
                "rate": "9.99"
            }
        }
        
    async def _get_reviews_data(
        self, 
        db: AsyncSession, 
        product_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get review data for a product"""
        # This would query review data from database
        # For now, return mock structure
        return {
            "aggregate_rating": {
                "rating_value": 4.5,
                "review_count": 27
            },
            "reviews": [
                {
                    "rating": 5,
                    "author_name": "John D.",
                    "review_body": "Excellent product, highly recommended!",
                    "date_published": "2025-01-15"
                }
            ]
        }
        
    async def _generate_product_faqs(
        self,
        product: ProductResponse
    ) -> List[Dict[str, str]]:
        """Generate FAQ data using AI for the product"""
        try:
            # Use SEO optimizer to generate FAQs
            faq_prompt = f"""
            Generate 3-5 frequently asked questions and answers for this product:
            Product: {product.title}
            Description: {product.description or 'No description available'}
            Category: {product.category or 'General'}
            
            Format as JSON array: [{"question": "...", "answer": "..."}]
            Focus on common customer concerns, shipping, returns, and product specifications.
            """
            
            # This would use the AI service to generate FAQs
            # For now, return sample FAQs
            return [
                {
                    "question": "What materials is this product made from?",
                    "answer": "This product is made from high-quality materials designed for durability and performance."
                },
                {
                    "question": "What is the return policy?",
                    "answer": "We offer a 30-day return policy for all products in original condition."
                },
                {
                    "question": "How long does shipping take?",
                    "answer": "Standard shipping takes 3-5 business days, expedited shipping is available."
                }
            ]
        except Exception as e:
            # Return empty list if FAQ generation fails
            return []
