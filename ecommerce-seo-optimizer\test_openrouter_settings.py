#!/usr/bin/env python3
"""
Test script to verify OpenRouter API key functionality.
"""
import requests
import json
import os

# Configuration
BASE_URL = "http://localhost:8000"
API_PREFIX = "/api/v1"

# Test user credentials (using the test user created in the system)
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "demo12345"

def test_openrouter_settings():
    """Test the OpenRouter API key settings functionality."""
    print("Testing OpenRouter API key settings functionality...")
    
    # Step 1: Login to get authentication token
    print("1. Logging in...")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}{API_PREFIX}/auth/login",
            json=login_data
        )
        
        if response.status_code != 200:
            print(f"Login failed with status {response.status_code}")
            print(response.text)
            return False
            
        auth_data = response.json()
        token = auth_data.get("access_token")
        if not token:
            print("No access token in response")
            return False
            
        print("Login successful!")
        headers = {"Authorization": f"Bearer {token}"}
        
    except Exception as e:
        print(f"Error during login: {e}")
        return False
    
    # Step 2: Get current user settings
    print("2. Getting current user settings...")
    try:
        response = requests.get(
            f"{BASE_URL}{API_PREFIX}/users/me/settings",
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"Failed to get user settings with status {response.status_code}")
            print(response.text)
            return False
            
        settings = response.json()
        print(f"Current settings: {settings}")
        
    except Exception as e:
        print(f"Error getting user settings: {e}")
        return False
    
    # Step 3: Update user settings with OpenRouter API key
    print("3. Updating user settings with OpenRouter API key...")
    test_api_key = "sk-or-test1234567890abcdefg"  # Test API key
    updated_settings = {
        "auto_optimize": True,
        "email_notifications": True,
        "scheduled_optimization": True,
        "ai_model": "gpt-4",
        "optimization_frequency": "weekly",
        "openrouter_api_key": test_api_key
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}{API_PREFIX}/users/me/settings",
            headers=headers,
            json=updated_settings
        )
        
        if response.status_code != 200:
            print(f"Failed to update user settings with status {response.status_code}")
            print(response.text)
            return False
            
        updated_settings_response = response.json()
        print(f"Updated settings: {updated_settings_response}")
        
        # Verify the API key was saved
        if updated_settings_response.get("openrouter_api_key") == test_api_key:
            print("OpenRouter API key saved successfully!")
        else:
            print("Warning: OpenRouter API key may not have been saved correctly")
            
    except Exception as e:
        print(f"Error updating user settings: {e}")
        return False
    
    # Step 4: Verify settings were saved by retrieving them again
    print("4. Verifying settings were saved...")
    try:
        response = requests.get(
            f"{BASE_URL}{API_PREFIX}/users/me/settings",
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"Failed to get user settings with status {response.status_code}")
            print(response.text)
            return False
            
        settings = response.json()
        print(f"Retrieved settings: {settings}")
        
        if settings.get("openrouter_api_key") == test_api_key:
            print("OpenRouter API key verified successfully!")
            return True
        else:
            print("OpenRouter API key verification failed!")
            return False
            
    except Exception as e:
        print(f"Error verifying user settings: {e}")
        return False

if __name__ == "__main__":
    print("Starting OpenRouter API key settings test...")
    success = test_openrouter_settings()
    if success:
        print("\nAll tests passed! OpenRouter API key functionality is working.")
    else:
        print("\nSome tests failed. Please check the implementation.")