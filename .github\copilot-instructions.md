# AI Coding Agent Instructions - GridSpoke (Ecommerce SEO Service)

## Project Architecture Overview
This is an **AI-Powered Ecommerce SEO Service Suite** built with microservices architecture:
- **Backend**: FastAPI + Mirascope for AI agent coordination
- **AI Provider**: OpenRouter API accessing multiple LLMs (GPT-4, Claude, etc.)
- **Database**: PostgreSQL with pgvector for embeddings and vector search
- **Task Queue**: Celery + Redis for async bulk processing of thousands of products
- **Frontend**: Multipage HTML, CSS, JS dashboard
- **Integration**: WordPress plugin connecting WooCommerce/SureCart stores
- **Deployment**: Docker Compose with service isolation

## Core Business Logic
The system optimizes ecommerce products through **AI-generated content**:
1. **Product Optimization Engine**: Generates SEO titles, descriptions, meta tags, alt text
2. **Bulk Processing**: Handles thousands of products simultaneously via Celery queues
3. **Smart Scheduling**: Runs optimization jobs during off-peak hours to minimize API costs
4. **Real-time Monitoring**: WebSocket-powered dashboard showing job progress and analytics
5. **WordPress Integration**: Lightweight plugin syncs product data bidirectionally

## Development Phases & Priorities
Follow this **phased approach** (reference `ecommerce-seo-service-plan.md`):
- **Phase 1-2**: Docker infrastructure + FastAPI backend + Mirascope AI agents
- **Phase 3**: React dashboard with real-time job monitoring
- **Phase 4**: WordPress plugin for WooCommerce/SureCart
- **Phase 5-6**: Advanced features (A/B testing, analytics, multilingual)

## Key Implementation Patterns

### AI Agent Architecture (Mirascope)
```python
class ProductOptimizer(OpenAICall):
    api_key = os.getenv("OPENROUTER_API_KEY")
    base_url = "https://openrouter.ai/api/v1"
    model = "anthropic/claude-3-opus"
    # Structured output for SEO optimization
```
- Use **OpenRouter** as LLM provider for cost efficiency and model diversity
- Implement **prompt caching** for similar products to reduce API costs
- Structure all AI responses with **Pydantic models** for type safety

### Database Schema Patterns
Reference the **database schema** in `ecommerce-seo-service-plan.md`:
- Use **UUID primary keys** for all entities
- Store **JSONB** for flexible fields (keywords, optimization metadata)
- Implement **pgvector** for semantic product search and similarity matching
- Track **optimization history** and **AI model versions** for analytics

### Task Queue Architecture
```python
# Celery task patterns for bulk processing
@celery_app.task(bind=True)
def optimize_product_batch(self, store_id: str, product_ids: list):
    # Progress tracking with Redis
    # WebSocket updates to dashboard
```
- Use **Celery Beat** for scheduled optimization jobs (2 AM daily)
- Implement **progress tracking** with Redis for real-time dashboard updates
- Design **task chains** for complex workflows (optimize → generate content → update store)

### WordPress Integration Strategy
Build a **lightweight connector plugin** (not full ecommerce):
- **Zero local processing** - all AI computation on your backend
- **API key authentication** for secure backend communication
- **Webhook handling** for receiving optimized content back
- **Bulk action triggers** from WordPress admin interface

## Development Workflow Commands
```bash
# Development setup (when implemented)
docker-compose up -d                    # Start all services
docker-compose exec api alembic upgrade head  # Run migrations
docker-compose exec api python create_superuser.py  # Create admin user

# Task monitoring
docker-compose exec redis redis-cli monitor  # Watch Redis activity
docker-compose logs -f celery-worker         # Monitor task processing
```

## Cost Optimization Strategies
- **Cache AI responses** for similar products in Redis
- **Batch similar requests** to reduce API calls
- **Smart model selection**: Use cheaper models for simple tasks, premium for complex
- **Off-peak scheduling**: Process during OpenRouter's lower-rate hours
- **Incremental updates**: Only optimize changed products

## External Dependencies & APIs
- **OpenRouter API**: Primary LLM provider - handle rate limits and fallbacks
- **WordPress REST API**: For plugin integration - use proper authentication
- **WooCommerce/SureCart APIs**: Product sync - handle webhook security
- **pgvector**: For semantic search - requires proper indexing strategies

## Project-Specific Conventions
- All async operations use **Celery tasks**, not FastAPI background tasks
- **WebSocket connections** managed through Redis pub/sub for scalability  
- **Prompt templates** stored in dedicated files, not hardcoded
- **Environment-specific configs** via Docker environment variables
- **API versioning** through `/api/v1/` prefix for future compatibility

## Critical Files to Reference
- `ecommerce-seo-service-plan.md`: Complete technical specification
- `ai-coder-prompts.md`: Phase-by-phase implementation prompts
- `.github/instructions/App_Summary.instructions.md`: High-level project context
