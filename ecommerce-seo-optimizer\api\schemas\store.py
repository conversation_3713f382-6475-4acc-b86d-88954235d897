"""
Store schemas for request/response validation.
"""
import uuid
from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, HttpUrl
from enum import Enum


class StorePlatform(str, Enum):
    """Supported ecommerce platforms."""
    WOOCOMMERCE = "woocommerce"
    SURECART = "surecart"
    SHOPIFY = "shopify"
    MAGENTO = "magento"
    CUSTOM = "custom"


class StoreBase(BaseModel):
    """Base store schema with common fields."""
    name: str = Field(..., min_length=1, max_length=255)
    platform: StorePlatform
    store_url: HttpUrl
    description: Optional[str] = Field(None, max_length=1000)
    is_active: bool = True


class StoreCreate(StoreBase):
    """Schema for store creation."""
    api_credentials: Optional[Dict[str, Any]] = Field(default_factory=dict)


class StoreUpdate(BaseModel):
    """Schema for store updates."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    platform: Optional[StorePlatform] = None
    store_url: Optional[HttpUrl] = None
    description: Optional[str] = Field(None, max_length=1000)
    is_active: Optional[bool] = None
    api_credentials: Optional[Dict[str, Any]] = None


class StoreInDB(StoreBase):
    """Schema for store in database."""
    id: uuid.UUID
    owner_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Store(StoreInDB):
    """Schema for store response."""
    pass


class StoreWithStats(Store):
    """Store schema with statistics."""
    total_products: int = 0
    optimized_products: int = 0
    optimization_rate: float = 0.0
    last_sync_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class StoreSyncRequest(BaseModel):
    """Schema for store synchronization request."""
    force_update: bool = False
    product_limit: Optional[int] = Field(None, ge=1, le=10000)


class StoreSyncResponse(BaseModel):
    """Schema for store synchronization response."""
    job_id: uuid.UUID
    message: str
    estimated_completion: Optional[datetime] = None
