"""
Rate limiting system for AI API calls.

This module provides rate limiting functionality to prevent API quota exhaustion
and ensure fair usage across different stores and operations.
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
from dataclasses import dataclass, field

import redis
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from limits import RateLimitItemPerSecond, RateLimitItemPerMinute, RateLimitItemPerHour

from ..core.config import settings


logger = logging.getLogger(__name__)


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""
    
    # Global limits
    global_requests_per_minute: int = 60
    global_requests_per_hour: int = 1000
    
    # Per-store limits
    store_requests_per_minute: int = 20
    store_requests_per_hour: int = 300
    
    # Per-user limits
    user_requests_per_minute: int = 10
    user_requests_per_hour: int = 100
    
    # Model-specific limits (tokens per minute)
    model_limits: Dict[str, int] = field(default_factory=lambda: {
        "openai/gpt-3.5-turbo": 40000,
        "openai/gpt-4o": 10000,
        "anthropic/claude-3-opus": 8000,
        "anthropic/claude-3-sonnet": 15000,
    })
    
    # Cost limits (USD per hour)
    cost_limit_per_hour: float = 10.0
    cost_limit_per_day: float = 100.0


@dataclass
class UsageRecord:
    """Track usage for rate limiting."""
    
    requests: int = 0
    tokens: int = 0
    cost: float = 0.0
    last_request: Optional[datetime] = None
    window_start: Optional[datetime] = None
    
    def reset_window(self):
        """Reset the tracking window."""
        self.requests = 0
        self.tokens = 0
        self.cost = 0.0
        self.window_start = datetime.utcnow()
    
    def add_usage(self, tokens: int = 0, cost: float = 0.0):
        """Add usage data."""
        self.requests += 1
        self.tokens += tokens
        self.cost += cost
        self.last_request = datetime.utcnow()
        
        if not self.window_start:
            self.window_start = datetime.utcnow()


class TokenBucket:
    """Token bucket algorithm for rate limiting."""
    
    def __init__(self, capacity: int, refill_rate: float):
        """
        Initialize token bucket.
        
        Args:
            capacity: Maximum number of tokens
            refill_rate: Tokens added per second
        """
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def consume(self, tokens: int = 1) -> bool:
        """
        Try to consume tokens from the bucket.
        
        Args:
            tokens: Number of tokens to consume
            
        Returns:
            True if tokens were consumed, False if not enough tokens
        """
        async with self._lock:
            now = time.time()
            
            # Add tokens based on time elapsed
            time_elapsed = now - self.last_refill
            tokens_to_add = time_elapsed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + tokens_to_add)
            self.last_refill = now
            
            # Check if we can consume the requested tokens
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            
            return False
    
    def get_wait_time(self, tokens: int = 1) -> float:
        """Get the time to wait before the requested tokens are available."""
        if self.tokens >= tokens:
            return 0.0
        
        tokens_needed = tokens - self.tokens
        return tokens_needed / self.refill_rate


class AIRateLimiter:
    """
    Advanced rate limiter for AI API calls.
    
    Features:
    - Multiple rate limiting strategies (requests, tokens, cost)
    - Per-store and per-user limits
    - Model-specific token limits
    - Cost-based limiting
    - Graceful backoff with wait times
    - Redis-backed for distributed rate limiting
    """
    
    def __init__(self, config: Optional[RateLimitConfig] = None):
        """Initialize the rate limiter."""
        self.config = config or RateLimitConfig()
        
        # Initialize Redis for distributed rate limiting
        try:
            self.redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
            self.redis_client.ping()
            self.redis_available = True
        except Exception as e:
            logger.warning(f"Redis not available for rate limiting: {e}")
            self.redis_available = False
            self.redis_client = None
        
        # In-memory fallback tracking
        self.usage_tracking: Dict[str, UsageRecord] = {}
        self.token_buckets: Dict[str, TokenBucket] = {}
        
        # Initialize FastAPI rate limiter for HTTP endpoints
        self.http_limiter = Limiter(
            key_func=get_remote_address,
            storage_uri=settings.REDIS_URL if self.redis_available else "memory://"
        )
        
        logger.info("AIRateLimiter initialized")
    
    def _get_usage_key(self, identifier: str, window: str) -> str:
        """Generate Redis key for usage tracking."""
        timestamp = int(time.time())
        
        if window == "minute":
            timestamp = timestamp // 60  # Round to minute
        elif window == "hour":
            timestamp = timestamp // 3600  # Round to hour
        elif window == "day":
            timestamp = timestamp // 86400  # Round to day
        
        return f"rate_limit:{identifier}:{window}:{timestamp}"
    
    async def check_rate_limit(
        self,
        identifier: str,
        limit_type: str = "requests",
        tokens: int = 0,
        cost: float = 0.0,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Check if the request is within rate limits.
        
        Args:
            identifier: Unique identifier (store_id, user_id, etc.)
            limit_type: Type of limit to check (requests, tokens, cost)
            tokens: Number of tokens for the request
            cost: Estimated cost for the request
            model: AI model being used
            
        Returns:
            Dictionary with rate limit status and wait time
        """
        try:
            # Check different types of limits
            if limit_type == "requests":
                return await self._check_request_limit(identifier)
            elif limit_type == "tokens":
                return await self._check_token_limit(identifier, tokens, model)
            elif limit_type == "cost":
                return await self._check_cost_limit(identifier, cost)
            else:
                raise ValueError(f"Unknown limit type: {limit_type}")
                
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            # Fail open - allow the request if rate limiting fails
            return {
                "allowed": True,
                "wait_time": 0.0,
                "remaining": 999,
                "error": str(e)
            }
    
    async def _check_request_limit(self, identifier: str) -> Dict[str, Any]:
        """Check request-based rate limits."""
        if self.redis_available:
            return await self._check_redis_limit(
                identifier,
                self.config.store_requests_per_minute,
                "minute"
            )
        else:
            return await self._check_memory_limit(
                identifier,
                self.config.store_requests_per_minute,
                60  # 60 seconds
            )
    
    async def _check_token_limit(self, identifier: str, tokens: int, model: Optional[str]) -> Dict[str, Any]:
        """Check token-based rate limits."""
        if not model or model not in self.config.model_limits:
            # Use default limit if model not specified
            token_limit = 10000
        else:
            token_limit = self.config.model_limits[model]
        
        bucket_key = f"{identifier}:{model or 'default'}"
        
        if bucket_key not in self.token_buckets:
            # Create token bucket (refill rate = limit per minute / 60)
            self.token_buckets[bucket_key] = TokenBucket(
                capacity=token_limit,
                refill_rate=token_limit / 60.0
            )
        
        bucket = self.token_buckets[bucket_key]
        allowed = await bucket.consume(tokens)
        
        return {
            "allowed": allowed,
            "wait_time": bucket.get_wait_time(tokens) if not allowed else 0.0,
            "remaining": max(0, int(bucket.tokens)),
            "limit": token_limit
        }
    
    async def _check_cost_limit(self, identifier: str, cost: float) -> Dict[str, Any]:
        """Check cost-based rate limits."""
        # Use hourly cost limit
        if self.redis_available:
            return await self._check_redis_cost_limit(identifier, cost)
        else:
            return await self._check_memory_cost_limit(identifier, cost)
    
    async def _check_redis_limit(self, identifier: str, limit: int, window: str) -> Dict[str, Any]:
        """Check rate limit using Redis."""
        key = self._get_usage_key(identifier, window)
        
        try:
            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            pipe.get(key)
            pipe.incr(key)
            pipe.expire(key, 3600 if window == "hour" else 60)  # Set appropriate TTL
            
            results = pipe.execute()
            current_count = int(results[1])
            
            allowed = current_count <= limit
            remaining = max(0, limit - current_count)
            
            # Calculate wait time if limit exceeded
            wait_time = 0.0
            if not allowed:
                if window == "minute":
                    wait_time = 60.0
                elif window == "hour":
                    wait_time = 3600.0
            
            return {
                "allowed": allowed,
                "wait_time": wait_time,
                "remaining": remaining,
                "limit": limit,
                "current": current_count
            }
            
        except Exception as e:
            logger.error(f"Redis rate limit check failed: {e}")
            return {"allowed": True, "wait_time": 0.0, "remaining": limit}
    
    async def _check_memory_limit(self, identifier: str, limit: int, window_seconds: int) -> Dict[str, Any]:
        """Check rate limit using in-memory tracking."""
        now = datetime.utcnow()
        
        if identifier not in self.usage_tracking:
            self.usage_tracking[identifier] = UsageRecord()
        
        usage = self.usage_tracking[identifier]
        
        # Reset window if expired
        if (not usage.window_start or 
            (now - usage.window_start).total_seconds() >= window_seconds):
            usage.reset_window()
        
        # Check if within limit
        allowed = usage.requests < limit
        remaining = max(0, limit - usage.requests)
        
        if allowed:
            usage.add_usage()
        
        # Calculate wait time
        wait_time = 0.0
        if not allowed and usage.window_start:
            elapsed = (now - usage.window_start).total_seconds()
            wait_time = max(0, window_seconds - elapsed)
        
        return {
            "allowed": allowed,
            "wait_time": wait_time,
            "remaining": remaining,
            "limit": limit,
            "current": usage.requests
        }
    
    async def _check_redis_cost_limit(self, identifier: str, cost: float) -> Dict[str, Any]:
        """Check cost limit using Redis."""
        hour_key = self._get_usage_key(f"{identifier}:cost", "hour")
        day_key = self._get_usage_key(f"{identifier}:cost", "day")
        
        try:
            # Get current costs
            pipe = self.redis_client.pipeline()
            pipe.get(hour_key)
            pipe.get(day_key)
            results = pipe.execute()
            
            current_hour_cost = float(results[0] or 0)
            current_day_cost = float(results[1] or 0)
            
            # Check limits
            hour_allowed = (current_hour_cost + cost) <= self.config.cost_limit_per_hour
            day_allowed = (current_day_cost + cost) <= self.config.cost_limit_per_day
            
            allowed = hour_allowed and day_allowed
            
            if allowed:
                # Update costs
                pipe = self.redis_client.pipeline()
                pipe.incrbyfloat(hour_key, cost)
                pipe.expire(hour_key, 3600)
                pipe.incrbyfloat(day_key, cost)
                pipe.expire(day_key, 86400)
                pipe.execute()
            
            return {
                "allowed": allowed,
                "wait_time": 3600.0 if not hour_allowed else 0.0,  # Wait until next hour
                "remaining_hour": max(0, self.config.cost_limit_per_hour - current_hour_cost),
                "remaining_day": max(0, self.config.cost_limit_per_day - current_day_cost),
                "current_hour_cost": current_hour_cost,
                "current_day_cost": current_day_cost
            }
            
        except Exception as e:
            logger.error(f"Redis cost limit check failed: {e}")
            return {"allowed": True, "wait_time": 0.0}
    
    async def _check_memory_cost_limit(self, identifier: str, cost: float) -> Dict[str, Any]:
        """Check cost limit using in-memory tracking."""
        # Simplified in-memory cost tracking
        cost_key = f"{identifier}:cost"
        now = datetime.utcnow()
        
        if cost_key not in self.usage_tracking:
            self.usage_tracking[cost_key] = UsageRecord()
        
        usage = self.usage_tracking[cost_key]
        
        # Reset if hour has passed
        if (not usage.window_start or 
            (now - usage.window_start).total_seconds() >= 3600):
            usage.reset_window()
        
        allowed = (usage.cost + cost) <= self.config.cost_limit_per_hour
        
        if allowed:
            usage.cost += cost
            usage.last_request = now
            if not usage.window_start:
                usage.window_start = now
        
        return {
            "allowed": allowed,
            "wait_time": 3600.0 if not allowed else 0.0,
            "remaining": max(0, self.config.cost_limit_per_hour - usage.cost),
            "current_cost": usage.cost
        }
    
    @asynccontextmanager
    async def rate_limited_call(
        self,
        identifier: str,
        tokens: int = 0,
        cost: float = 0.0,
        model: Optional[str] = None
    ):
        """
        Context manager for rate-limited AI calls.
        
        Args:
            identifier: Unique identifier for rate limiting
            tokens: Number of tokens for the request
            cost: Estimated cost for the request
            model: AI model being used
            
        Yields:
            Context for the AI call
            
        Raises:
            RateLimitExceeded: If rate limit is exceeded
        """
        # Check all applicable rate limits
        checks = [
            await self.check_rate_limit(identifier, "requests"),
            await self.check_rate_limit(identifier, "tokens", tokens, model=model),
            await self.check_rate_limit(identifier, "cost", cost=cost)
        ]
        
        # Find the most restrictive limit
        max_wait_time = 0.0
        blocking_limit = None
        
        for check in checks:
            if not check["allowed"]:
                if check["wait_time"] > max_wait_time:
                    max_wait_time = check["wait_time"]
                    blocking_limit = check
        
        if blocking_limit:
            logger.warning(f"Rate limit exceeded for {identifier}: {blocking_limit}")
            raise RateLimitExceeded(f"Rate limit exceeded. Wait {max_wait_time:.1f} seconds.")
        
        try:
            yield
        except Exception as e:
            # Log the error but don't modify rate limiting
            logger.error(f"Error during rate-limited call: {e}")
            raise
    
    async def get_usage_stats(self, identifier: str) -> Dict[str, Any]:
        """Get usage statistics for an identifier."""
        stats = {
            "identifier": identifier,
            "requests_remaining": 0,
            "tokens_remaining": 0,
            "cost_remaining": 0.0,
            "current_usage": {}
        }
        
        try:
            # Get request limits
            request_check = await self.check_rate_limit(identifier, "requests")
            stats["requests_remaining"] = request_check.get("remaining", 0)
            
            # Get cost limits
            cost_check = await self.check_rate_limit(identifier, "cost")
            stats["cost_remaining"] = cost_check.get("remaining", 0.0)
            
            # Add detailed usage if available
            if self.redis_available:
                stats["current_usage"] = await self._get_redis_usage_stats(identifier)
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get usage stats: {e}")
            return stats
    
    async def _get_redis_usage_stats(self, identifier: str) -> Dict[str, Any]:
        """Get detailed usage stats from Redis."""
        try:
            keys_to_check = [
                self._get_usage_key(identifier, "minute"),
                self._get_usage_key(identifier, "hour"),
                self._get_usage_key(f"{identifier}:cost", "hour"),
                self._get_usage_key(f"{identifier}:cost", "day")
            ]
            
            values = self.redis_client.mget(keys_to_check)
            
            return {
                "requests_this_minute": int(values[0] or 0),
                "requests_this_hour": int(values[1] or 0),
                "cost_this_hour": float(values[2] or 0),
                "cost_this_day": float(values[3] or 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to get Redis usage stats: {e}")
            return {}
    
    def get_http_limiter(self) -> Limiter:
        """Get the FastAPI rate limiter for HTTP endpoints."""
        return self.http_limiter
    
    async def cleanup_expired_data(self) -> int:
        """Clean up expired rate limiting data."""
        cleaned_count = 0
        
        if not self.redis_available:
            # Clean up in-memory data
            now = datetime.utcnow()
            expired_keys = []
            
            for key, usage in self.usage_tracking.items():
                if (usage.last_request and 
                    (now - usage.last_request).total_seconds() > 3600):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.usage_tracking[key]
                cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} expired rate limit records")
        return cleaned_count
