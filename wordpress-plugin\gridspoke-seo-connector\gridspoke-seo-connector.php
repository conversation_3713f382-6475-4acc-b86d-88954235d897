<?php
/**
 * Plugin Name: GridSpoke SEO Optimizer Connector
 * Plugin URI: https://gridspoke.com/wordpress-plugin
 * Description: Connect your WooCommerce/SureCart store to GridSpoke's AI-powered SEO optimization service for automated product content generation.
 * Version: 1.0.0
 * Author: GridSpoke
 * Author URI: https://gridspoke.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: gridspoke-seo
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 4.0
 * WC tested up to: 8.5
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('GRIDSPOKE_SEO_VERSION', '1.0.0');
define('GRIDSPOKE_SEO_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GRIDSPOKE_SEO_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('GRIDSPOKE_SEO_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main GridSpoke SEO Connector Class
 */
class GridSpoke_SEO_Connector {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->includes();
        $this->init_classes();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        add_action('admin_notices', array($this, 'admin_notices'));
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Include required files
     */
    private function includes() {
        // Core classes
        require_once GRIDSPOKE_SEO_PLUGIN_PATH . 'includes/class-gridspoke-api-client.php';
        require_once GRIDSPOKE_SEO_PLUGIN_PATH . 'includes/class-gridspoke-settings.php';
        require_once GRIDSPOKE_SEO_PLUGIN_PATH . 'includes/class-gridspoke-logger.php';
        require_once GRIDSPOKE_SEO_PLUGIN_PATH . 'includes/class-gridspoke-webhook-handler.php';
        
        // Integration classes
        if (class_exists('WooCommerce')) {
            require_once GRIDSPOKE_SEO_PLUGIN_PATH . 'includes/class-gridspoke-woocommerce-integration.php';
        }
        
        if (defined('SURECART_PLUGIN_FILE')) {
            require_once GRIDSPOKE_SEO_PLUGIN_PATH . 'includes/class-gridspoke-surecart-integration.php';
        }
        
        // Admin classes - always include for class existence check
        require_once GRIDSPOKE_SEO_PLUGIN_PATH . 'admin/class-gridspoke-admin.php';
    }
    
    /**
     * Initialize classes
     */
    private function init_classes() {
        // Initialize core components
        GridSpoke_Settings::get_instance();
        GridSpoke_Logger::get_instance();
        GridSpoke_Webhook_Handler::get_instance();
        
        // Initialize integrations if available
        if (class_exists('WooCommerce')) {
            GridSpoke_WooCommerce_Integration::get_instance();
        }
        
        if (defined('SURECART_PLUGIN_FILE')) {
            GridSpoke_SureCart_Integration::get_instance();
        }
        
        // Initialize admin if needed
        if (is_admin()) {
            GridSpoke_Admin::get_instance();
        }
    }
    
    /**
     * Plugin initialization
     */
    public function init() {
        // Check for required plugins
        $this->check_requirements();
        
        // Initialize webhook endpoint
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        
        // Schedule sync if enabled
        if (!wp_next_scheduled('gridspoke_seo_sync_products')) {
            $sync_interval = get_option('gridspoke_seo_sync_interval', 'daily');
            wp_schedule_event(time(), $sync_interval, 'gridspoke_seo_sync_products');
        }
        
        add_action('gridspoke_seo_sync_products', array($this, 'scheduled_sync'));
    }
    
    /**
     * Register REST API routes for webhooks
     */
    public function register_rest_routes() {
        register_rest_route('gridspoke-seo/v1', '/webhook', array(
            'methods' => 'POST',
            'callback' => array('GridSpoke_Webhook_Handler', 'handle_webhook'),
            'permission_callback' => array('GridSpoke_Webhook_Handler', 'verify_webhook_signature'),
        ));
    }
    
    /**
     * Check plugin requirements
     */
    private function check_requirements() {
        $requirements_met = true;
        
        // Check if at least one supported ecommerce plugin is active
        if (!class_exists('WooCommerce') && !defined('SURECART_PLUGIN_FILE')) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . 
                     esc_html__('GridSpoke SEO Connector requires WooCommerce or SureCart to be installed and activated.', 'gridspoke-seo') . 
                     '</p></div>';
            });
            $requirements_met = false;
        }
        
        // Check PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . 
                     esc_html__('GridSpoke SEO Connector requires PHP 7.4 or higher.', 'gridspoke-seo') . 
                     '</p></div>';
            });
            $requirements_met = false;
        }
        
        return $requirements_met;
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Set default options
        $default_settings = array(
            'api_endpoint' => 'https://api.gridspoke.com/v1',
            'api_key' => '',
            'auto_sync' => false,
            'sync_interval' => 'daily',
            'webhook_secret' => wp_generate_password(32, false),
            'log_level' => 'info',
            'optimization_fields' => array('title', 'description', 'meta_description'),
        );
        
        add_option('gridspoke_seo_settings', $default_settings);
        
        // Create custom database table for optimization tracking
        $this->create_optimization_table();
        
        // Flush rewrite rules for webhook endpoints
        flush_rewrite_rules();
        
        // Log activation
        GridSpoke_Logger::log('Plugin activated successfully', 'info');
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('gridspoke_seo_sync_products');
        
        // Log deactivation
        GridSpoke_Logger::log('Plugin deactivated', 'info');
    }
    
    /**
     * Create optimization tracking table
     */
    private function create_optimization_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            product_type varchar(20) NOT NULL DEFAULT 'woocommerce',
            optimization_id varchar(255) NOT NULL,
            status varchar(20) NOT NULL DEFAULT 'pending',
            fields_optimized text,
            ai_model varchar(100),
            tokens_used int,
            error_message text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY optimization_id (optimization_id),
            KEY product_lookup (product_id, product_type),
            KEY status_index (status)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Load text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain('gridspoke-seo', false, dirname(GRIDSPOKE_SEO_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Display admin notices
     */
    public function admin_notices() {
        $settings = get_option('gridspoke_seo_settings', array());
        
        // Consider plugin configured if either:
        // - API key is set (legacy mode), OR
        // - User has authenticated via email/password and we have tokens, OR
        // - Email/password present (wizard completed), OR
        // - Store has been linked (store_id present)
        $has_api_key = !empty($settings['api_key']);
        $has_tokens = !empty(get_transient('gridspoke_access_token')) || !empty(get_option('gridspoke_refresh_token'));
        $has_credentials = !empty($settings['email']) && !empty($settings['password']);
        $has_store = !empty(get_option('gridspoke_store_id'));
        $configured = $has_api_key || $has_tokens || $has_credentials || $has_store;

        if (!$configured && current_user_can('manage_options')) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p>' . sprintf(
                esc_html__('GridSpoke SEO Connector is installed but not fully configured. %sOpen the Setup Wizard%s to connect your account, or %sreview settings%s.', 'gridspoke-seo'),
                '<a href="' . esc_url(admin_url('admin.php?page=gridspoke-setup-wizard')) . '">',
                '</a>',
                '<a href="' . esc_url(admin_url('admin.php?page=gridspoke-seo-settings')) . '">',
                '</a>'
            ) . '</p>';
            echo '</div>';
        }
    }
    
    /**
     * Scheduled product sync
     */
    public function scheduled_sync() {
        $settings = get_option('gridspoke_seo_settings', array());
        
        if (!empty($settings['auto_sync']) && !empty($settings['api_key'])) {
            // Trigger sync for WooCommerce
            if (class_exists('WooCommerce')) {
                $woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
                $woo_integration->sync_products();
            }
            
            // Trigger sync for SureCart
            if (defined('SURECART_PLUGIN_FILE')) {
                $sure_integration = GridSpoke_SureCart_Integration::get_instance();
                $sure_integration->sync_products();
            }
        }
    }
    
    /**
     * Get plugin settings
     */
    public static function get_settings() {
        return get_option('gridspoke_seo_settings', array());
    }
    
    /**
     * Update plugin settings
     */
    public static function update_settings($settings) {
        return update_option('gridspoke_seo_settings', $settings);
    }
}

// Initialize the plugin
function gridspoke_seo_init() {
    return GridSpoke_SEO_Connector::get_instance();
}

// Hook into plugins_loaded to ensure all plugins are loaded
add_action('plugins_loaded', 'gridspoke_seo_init', 0);

// Uninstall hook
register_uninstall_hook(__FILE__, 'gridspoke_seo_uninstall');

function gridspoke_seo_uninstall() {
    // Delete all plugin options
    delete_option('gridspoke_seo_settings');
    
    // Delete optimization tracking table
    global $wpdb;
    $table_name = $wpdb->prefix . 'gridspoke_optimizations';
    $wpdb->query("DROP TABLE IF EXISTS $table_name");
    
    // Clear any remaining scheduled events
    wp_clear_scheduled_hook('gridspoke_seo_sync_products');
}
