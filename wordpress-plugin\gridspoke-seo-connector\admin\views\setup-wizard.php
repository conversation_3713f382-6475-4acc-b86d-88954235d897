<?php
if (!defined('ABSPATH')) { exit; }
$settings = GridSpoke_SEO_Connector::get_settings();
?>
<div class="wrap gridspoke-setup-wizard">
  <h1><?php esc_html_e('GridSpoke Setup Wizard', 'gridspoke-seo'); ?></h1>
  <ol class="gridspoke-steps">
    <li class="active" data-step="1"><?php esc_html_e('Connect Account', 'gridspoke-seo'); ?></li>
    <li data-step="2"><?php esc_html_e('Link Store', 'gridspoke-seo'); ?></li>
    <li data-step="3"><?php esc_html_e('Initial Sync', 'gridspoke-seo'); ?></li>
  </ol>

  <div class="gridspoke-step" data-step="1">
    <h2><?php esc_html_e('Connect your GridSpoke account', 'gridspoke-seo'); ?></h2>
    <p><?php esc_html_e('Enter your GridSpoke credentials to authenticate and authorize this site.', 'gridspoke-seo'); ?></p>
    <table class="form-table">
      <tr>
        <th><label for="gridspoke_email"><?php esc_html_e('Email', 'gridspoke-seo'); ?></label></th>
        <td><input type="email" id="gridspoke_email" class="regular-text" value="<?php echo esc_attr($settings['email'] ?? ''); ?>" /></td>
      </tr>
      <tr>
        <th><label for="gridspoke_password"><?php esc_html_e('Password', 'gridspoke-seo'); ?></label></th>
        <td><input type="password" id="gridspoke_password" class="regular-text" value="<?php echo esc_attr($settings['password'] ?? ''); ?>" /></td>
      </tr>
    </table>
    <p>
      <button class="button button-primary" id="gridspoke_test_auth"><?php esc_html_e('Test & Connect', 'gridspoke-seo'); ?></button>
      <span id="gridspoke_auth_status"></span>
    </p>
  </div>

  <div class="gridspoke-step" data-step="2" style="display:none;">
    <h2><?php esc_html_e('Link your store', 'gridspoke-seo'); ?></h2>
    <p><?php esc_html_e('We will create or link your WooCommerce store in GridSpoke automatically.', 'gridspoke-seo'); ?></p>
    <p>
      <button class="button" id="gridspoke_link_store"><?php esc_html_e('Link Store', 'gridspoke-seo'); ?></button>
      <span id="gridspoke_store_status"></span>
    </p>
  </div>

  <div class="gridspoke-step" data-step="3" style="display:none;">
    <h2><?php esc_html_e('Initial product sync', 'gridspoke-seo'); ?></h2>
    <p><?php esc_html_e('Sync a sample of your products now so your dashboard has data immediately.', 'gridspoke-seo'); ?></p>
    <p>
      <?php
        $product_counts = wp_count_posts('product');
        $published_total = isset($product_counts->publish) ? (int) $product_counts->publish : 0;
        $to_sync = min(20, max(0, $published_total));
        $label = $to_sync === 1
          ? sprintf(esc_html__('Sync %d Product', 'gridspoke-seo'), $to_sync)
          : sprintf(esc_html__('Sync %d Products', 'gridspoke-seo'), $to_sync);
      ?>
      <button class="button" id="gridspoke_sync_sample" <?php echo $to_sync === 0 ? 'disabled' : ''; ?>>
        <?php echo $to_sync > 0 ? esc_html($label) : esc_html__('No published products', 'gridspoke-seo'); ?>
      </button>
      <span id="gridspoke_sync_status"></span>
    </p>
    <p>
      <a href="<?php echo esc_url(admin_url('admin.php?page=gridspoke-seo')); ?>" class="button button-primary"><?php esc_html_e('Finish & Open Dashboard', 'gridspoke-seo'); ?></a>
    </p>
  </div>

  <p class="gridspoke-wizard-nav">
    <button class="button" id="gridspoke_prev" disabled><?php esc_html_e('Back', 'gridspoke-seo'); ?></button>
    <button class="button button-primary" id="gridspoke_next" disabled><?php esc_html_e('Next', 'gridspoke-seo'); ?></button>
  </p>
</div>
