"""
Models package initialization.
Handles circular imports and forward references between models.
"""

# Import base first
from .base import BaseModel

# Import models in dependency order
from .user import User
from .store import Store
from .product import Product
from .optimization_job import OptimizationJob
from .generated_content import GeneratedContent

# Configure relationships after all models are imported
def configure_relationships():
    """Configure model relationships after all models are imported."""
    # This resolves forward references in relationships
    pass

# Export all models
__all__ = [
    "BaseModel",
    "User", 
    "Store",
    "Product",
    "OptimizationJob", 
    "GeneratedContent",
    "configure_relationships"
]
