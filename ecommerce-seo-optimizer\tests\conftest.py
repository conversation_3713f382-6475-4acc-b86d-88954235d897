# GridSpoke Ecommerce SEO Optimizer - Test Configuration
"""
Pytest configuration and global fixtures for GridSpoke testing suite.
This follows the testing strategy from phase9-testing-suite-research.md
"""

import pytest
import asyncio
import os
import json
from datetime import datetime
from unittest.mock import MagicMock
from typing import Generator

from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

# GridSpoke imports
try:
    from api.main import app
    from api.core.database import get_db, Base
    from api.core.config import settings
    from api.models.store import Store
    from api.models.product import Product
    from api.models.optimization_job import OptimizationJob
except ImportError:
    # Fallback for when modules aren't available
    app = None
    get_db = None
    Base = None
    settings = None

# Test Database Configuration
SQLALCHEMY_TEST_DATABASE_URL = "sqlite:///./test_gridspoke.db"

test_engine = create_engine(
    SQLALCHEMY_TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
    echo=False  # Set to True for SQL debugging
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


# Database Fixtures
@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine for the session"""
    if Base is not None:
        Base.metadata.create_all(bind=test_engine)
    yield test_engine
    if Base is not None:
        Base.metadata.drop_all(bind=test_engine)


@pytest.fixture
def db_session(db_engine) -> Generator[Session, None, None]:
    """Create fresh database session for each test"""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def test_client(db_session) -> TestClient:
    """FastAPI test client with database override"""
    if app is None or get_db is None:
        pytest.skip("FastAPI app not available")
    
    def override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as client:
        yield client
    
    app.dependency_overrides.clear()


# Celery Testing Configuration
@pytest.fixture(scope='session')
def celery_config():
    """Celery configuration for testing"""
    return {
        'broker_url': 'redis://localhost:6379/15',  # Test Redis DB
        'result_backend': 'redis://localhost:6379/15',
        'task_always_eager': False,  # Test real async behavior
        'task_eager_propagates': True,
        'task_store_eager_result': True,
        'result_expires': 3600,
        'worker_hijack_root_logger': False,
        'worker_log_color': False,
    }


@pytest.fixture(scope='session')
def celery_includes():
    """Include GridSpoke task modules for testing"""
    return [
        'workers.tasks.product_tasks',
        'workers.tasks.content_tasks',
        'workers.tasks.scheduled_tasks',
    ]


@pytest.fixture(scope='session')
def celery_worker_parameters():
    """Celery worker parameters for testing"""
    return {
        'queues': ['optimization', 'content', 'scheduled'],
        'shutdown_timeout': 30,
        'without_heartbeat': True,  # Disable heartbeats for testing
    }


@pytest.fixture(scope='session')
def celery_enable_logging():
    """Enable logging in embedded workers"""
    return True


# GridSpoke-Specific Test Data Fixtures
@pytest.fixture
def sample_product():
    """Sample product data for testing GridSpoke optimization"""
    return {
        "id": "prod_test_123",
        "external_id": "wp_prod_123",
        "name": "Wireless Bluetooth Headphones",
        "description": "Basic wireless headphones with standard features",
        "price": 99.99,
        "category": "Electronics",
        "brand": "TechBrand",
        "keywords": ["wireless", "bluetooth", "headphones"],
        "images": ["https://example.com/headphones.jpg"],
        "sku": "WBH-001",
        "weight": 0.5,
        "dimensions": "20x15x8cm"
    }


@pytest.fixture
def sample_store():
    """Sample WordPress store for testing"""
    return {
        "id": "store_test_456",
        "name": "Tech Electronics Store",
        "url": "https://techstore.example.com",
        "platform": "woocommerce",
        "api_key": "wc_test_api_key_123",
        "api_secret": "wc_test_secret_456",
        "webhook_secret": "webhook_secret_789",
        "optimization_settings": {
            "auto_optimize": True,
            "schedule_time": "02:00",
            "optimization_types": ["title", "description", "meta"]
        }
    }


@pytest.fixture
def sample_optimization_job():
    """Sample optimization job for testing"""
    return {
        "id": "job_test_789",
        "store_id": "store_test_456",
        "job_type": "bulk_optimization",
        "status": "queued",
        "total_items": 10,
        "processed_items": 0,
        "optimization_type": "full",
        "created_at": datetime.utcnow(),
        "priority": "normal"
    }


# Mock OpenRouter API Responses
@pytest.fixture
def mock_openrouter_response():
    """
    Mock OpenRouter API response for testing AI optimization.
    
    NOTE: This is a mock for testing purposes. 
    For full integration testing, use the actual OpenRouter API with test keys.
    See: https://openrouter.ai/docs for API documentation.
    """
    return {
        "choices": [{
            "message": {
                "content": json.dumps({
                    "title": "Premium Wireless Bluetooth Headphones - Superior Sound Quality",
                    "description": "Experience crystal-clear audio with our premium wireless Bluetooth headphones featuring advanced noise cancellation, 30-hour battery life, and comfortable over-ear design. Perfect for music lovers, professionals, and gamers seeking exceptional sound quality and comfort.",
                    "meta_title": "Premium Wireless Bluetooth Headphones | Superior Sound - TechBrand",
                    "meta_description": "Premium wireless Bluetooth headphones with superior sound quality, noise cancellation, and 30-hour battery. Free shipping on orders over $50.",
                    "alt_text": "Premium wireless Bluetooth headphones with sleek black design and comfortable padding",
                    "keywords": [
                        "premium wireless headphones",
                        "bluetooth headphones noise cancellation", 
                        "superior sound quality headphones",
                        "long battery wireless headphones",
                        "comfortable over ear headphones"
                    ],
                    "seo_score": 85.5,
                    "optimization_notes": [
                        "Enhanced title with premium positioning and key benefits",
                        "Extended description with specific features and use cases",
                        "Added structured keywords for better search visibility",
                        "Optimized meta description with call-to-action"
                    ]
                })
            }
        }],
        "usage": {
            "prompt_tokens": 150,
            "completion_tokens": 200,
            "total_tokens": 350
        },
        "model": "anthropic/claude-3-opus",
        "id": "chatcmpl-test-123"
    }


@pytest.fixture
def mock_openrouter_error_response():
    """Mock OpenRouter API error response for testing error handling"""
    return {
        "error": {
            "message": "Rate limit exceeded",
            "type": "rate_limit_error",
            "code": "rate_limit_exceeded"
        }
    }


@pytest.fixture
def mock_wordpress_product():
    """Mock WordPress/WooCommerce product data"""
    return {
        "id": 123,
        "name": "Test Product",
        "type": "simple",
        "status": "publish",
        "description": "Test product description",
        "short_description": "Short description",
        "sku": "TEST-001",
        "price": "99.99",
        "regular_price": "99.99",
        "sale_price": "",
        "categories": [{"id": 15, "name": "Electronics"}],
        "tags": [{"id": 20, "name": "wireless"}],
        "images": [{
            "id": 456,
            "src": "https://example.com/image.jpg",
            "alt": "Test product image"
        }],
        "meta_data": [
            {
                "key": "_yoast_wpseo_title",
                "value": "Original SEO Title"
            },
            {
                "key": "_yoast_wpseo_metadesc", 
                "value": "Original meta description"
            }
        ]
    }


# Authentication Fixtures
@pytest.fixture
def auth_headers():
    """Authentication headers for API testing"""
    return {
        "Authorization": "Bearer test_jwt_token_123",
        "Content-Type": "application/json"
    }


@pytest.fixture
def admin_user():
    """Mock admin user for testing"""
    return {
        "id": "user_admin_123",
        "email": "<EMAIL>",
        "username": "admin",
        "role": "admin",
        "is_active": True,
        "store_ids": ["store_test_456"]
    }


# WebSocket Testing Fixtures
@pytest.fixture
def mock_websocket_manager():
    """Mock WebSocket manager for testing real-time updates"""
    manager = MagicMock()
    manager.connect = MagicMock()
    manager.disconnect = MagicMock()
    manager.send_personal_message = MagicMock()
    manager.broadcast = MagicMock()
    return manager


# Performance Testing Fixtures
@pytest.fixture
def performance_test_products():
    """Generate multiple products for performance testing"""
    products = []
    for i in range(100):
        products.append({
            "id": f"prod_perf_{i}",
            "name": f"Test Product {i}",
            "description": f"Description for test product {i}",
            "price": 10.00 + i,
            "category": f"Category {i % 10}",
            "brand": f"Brand {i % 5}"
        })
    return products


# Async Testing Configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Test Environment Configuration
@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment variables"""
    os.environ.update({
        "TESTING": "true",
        "DATABASE_URL": SQLALCHEMY_TEST_DATABASE_URL,
        "REDIS_URL": "redis://localhost:6379/15",
        "OPENROUTER_API_KEY": "test_openrouter_key",
        "SECRET_KEY": "test_secret_key_for_jwt",
        "ENVIRONMENT": "test"
    })
    yield
    # Cleanup is handled by pytest


# Test Database Helpers
@pytest.fixture
def create_test_store(db_session):
    """Helper to create test store in database"""
    def _create_store(**kwargs):
        if Store is None:
            pytest.skip("Store model not available")
        
        defaults = {
            "name": "Test Store",
            "url": "https://teststore.example.com",
            "platform": "woocommerce",
            "api_key": "test_api_key"
        }
        defaults.update(kwargs)
        
        store = Store(**defaults)
        db_session.add(store)
        db_session.commit()
        db_session.refresh(store)
        return store
    
    return _create_store


@pytest.fixture
def create_test_product(db_session):
    """Helper to create test product in database"""
    def _create_product(**kwargs):
        if Product is None:
            pytest.skip("Product model not available")
            
        defaults = {
            "external_id": "test_prod_123",
            "name": "Test Product",
            "description": "Test product description",
            "price": 99.99,
            "store_id": "test_store_id"
        }
        defaults.update(kwargs)
        
        product = Product(**defaults)
        db_session.add(product)
        db_session.commit()
        db_session.refresh(product)
        return product
    
    return _create_product


# Pytest Configuration
def pytest_configure(config):
    """Pytest configuration"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (may take several seconds)"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "load: marks tests as load/performance tests"
    )
    config.addinivalue_line(
        "markers", "security: marks tests as security tests"
    )
    config.addinivalue_line(
        "markers", "openrouter: marks tests that require OpenRouter API"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers"""
    for item in items:
        # Mark slow tests
        if "test_bulk" in item.name or "test_load" in item.name:
            item.add_marker(pytest.mark.slow)
        
        # Mark integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Mark load tests
        if "load" in str(item.fspath):
            item.add_marker(pytest.mark.load)
