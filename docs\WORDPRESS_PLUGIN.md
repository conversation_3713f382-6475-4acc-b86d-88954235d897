# GridSpoke WordPress Plugin Documentation

## Overview

The GridSpoke WordPress plugin provides seamless integration between your WordPress/WooCommerce store and the GridSpoke AI-powered SEO optimization service. This lightweight connector enables automatic product optimization without requiring any local AI processing.

## Features

- **Zero Local Processing**: All AI computation happens on GridSpoke servers
- **Automatic Product Sync**: Bidirectional synchronization with your store
- **Bulk Optimization**: Process hundreds of products simultaneously
- **Real-time Updates**: Receive optimized content via webhooks
- **WordPress Admin Integration**: Native WordPress interface
- **WooCommerce & SureCart Support**: Works with popular ecommerce plugins
- **Secure API Communication**: Encrypted data transmission

## Installation

### Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- WooCommerce 4.0+ or SureCart 1.0+ (for ecommerce features)
- SSL certificate (HTTPS required)
- GridSpoke account with API access

### Installation Methods

#### Method 1: WordPress Admin (Recommended)

1. **Download Plugin**:
   - Log into your GridSpoke dashboard
   - Go to Integrations → WordPress Plugin
   - Download the latest plugin zip file

2. **Install via WordPress Admin**:
   - Go to Plugins → Add New → Upload Plugin
   - Choose the downloaded zip file
   - Click "Install Now"
   - Activate the plugin

#### Method 2: Manual Installation

1. **Upload Files**:
   ```bash
   # Extract plugin to your WordPress plugins directory
   unzip gridspoke-wordpress.zip
   mv gridspoke/ /path/to/wordpress/wp-content/plugins/
   ```

2. **Activate Plugin**:
   - Go to WordPress Admin → Plugins
   - Find "GridSpoke SEO Optimizer"
   - Click "Activate"

### Configuration

1. **Get API Credentials**:
   - Log into GridSpoke dashboard
   - Go to Account → API Keys
   - Generate new API key for WordPress
   - Copy the API key and Store ID

2. **Configure Plugin**:
   - Go to WordPress Admin → GridSpoke → Settings
   - Enter your API credentials:
     - API Key: Your GridSpoke API key
     - Store ID: Your store identifier
     - API Endpoint: `https://your-gridspoke-domain.com/api/v1`
   - Test the connection
   - Save settings

## Configuration Settings

### Basic Settings

**API Configuration**:
- **API Key**: Your GridSpoke API authentication key
- **Store ID**: Unique identifier for your store in GridSpoke
- **API Endpoint**: GridSpoke API base URL
- **Timeout**: API request timeout (default: 30 seconds)

**Sync Settings**:
- **Auto Sync**: Automatically sync new products
- **Sync Frequency**: How often to check for changes (hourly, daily)
- **Batch Size**: Number of products to sync at once (default: 50)
- **Include Images**: Sync product images for AI analysis

### Advanced Settings

**Optimization Preferences**:
- **Default AI Model**: GPT-4, GPT-3.5, or Claude
- **Auto-Apply**: Automatically apply optimized content
- **Approval Required**: Require manual approval before applying changes
- **Backup Original**: Keep backup of original content

**Content Settings**:
- **Fields to Optimize**: Select which fields to optimize
  - Product titles
  - Product descriptions
  - Meta descriptions
  - Image alt text
  - Keywords
- **Content Length**: Preferred length for optimized content
- **Brand Guidelines**: Upload brand voice guidelines

### Security Settings

**Webhook Security**:
- **Webhook Secret**: Secret key for validating webhook requests
- **IP Whitelist**: Restrict webhooks to specific IP addresses
- **SSL Verification**: Require valid SSL certificates

**Data Protection**:
- **Data Encryption**: Encrypt sensitive data before transmission
- **Local Storage**: Minimize local data storage
- **Audit Logging**: Log all API communications

## Using the Plugin

### Product Management

#### Viewing Products

1. **Products List**:
   - Go to GridSpoke → Products
   - View all synced products with optimization status
   - Filter by optimization status, category, or date
   - Search for specific products

2. **Product Details**:
   - Click on any product to view details
   - See optimization history
   - Compare original vs. optimized content
   - View SEO scores and improvements

#### Manual Product Sync

1. **Sync Individual Products**:
   - Go to Products → Edit Product
   - Find the "GridSpoke SEO" meta box
   - Click "Sync to GridSpoke"
   - Monitor sync status

2. **Bulk Sync**:
   - Go to GridSpoke → Sync
   - Select products or categories to sync
   - Choose sync options
   - Start bulk sync process

### Optimization Workflows

#### Starting Optimization Jobs

1. **Single Product Optimization**:
   ```php
   // In product edit screen
   - Click "Optimize with GridSpoke" button
   - Configure optimization settings
   - Start optimization
   - Receive notification when complete
   ```

2. **Bulk Optimization**:
   - Go to GridSpoke → Optimize
   - Select products or use filters
   - Configure optimization settings:
     - AI model selection
     - Target keywords
     - Content types to optimize
   - Review estimated cost
   - Start bulk optimization

3. **Scheduled Optimization**:
   - Go to GridSpoke → Schedule
   - Set up recurring optimization jobs
   - Choose frequency (daily, weekly, monthly)
   - Select products and settings
   - Schedule runs during off-peak hours

#### Approval Workflows

1. **Manual Approval**:
   - Receive notification of completed optimization
   - Go to GridSpoke → Pending Reviews
   - Review optimized content side-by-side with original
   - Approve, reject, or request changes
   - Apply approved content to products

2. **Auto-Approval**:
   - Configure auto-approval rules in settings
   - Set quality thresholds
   - Define content guidelines
   - Monitor auto-applied changes

### Monitoring and Analytics

#### Optimization Status

**Dashboard Widget**:
- Add GridSpoke widget to WordPress dashboard
- View optimization statistics
- Monitor current job progress
- Quick access to common actions

**Status Indicators**:
- Green: Successfully optimized
- Yellow: Optimization in progress
- Red: Optimization failed
- Gray: Not yet optimized

#### Performance Tracking

1. **SEO Metrics**:
   - View SEO score improvements
   - Track keyword rankings
   - Monitor traffic impact
   - Analyze conversion changes

2. **Cost Tracking**:
   - Monitor monthly optimization costs
   - Track cost per product
   - Set budget alerts
   - View ROI calculations

## API Integration

### WordPress Hooks and Filters

#### Action Hooks

```php
// Triggered when product is synced to GridSpoke
do_action('gridspoke_product_synced', $product_id, $gridspoke_id);

// Triggered when optimization starts
do_action('gridspoke_optimization_started', $job_id, $product_ids);

// Triggered when optimization completes
do_action('gridspoke_optimization_completed', $job_id, $results);

// Triggered when optimized content is applied
do_action('gridspoke_content_applied', $product_id, $changes);
```

#### Filter Hooks

```php
// Modify product data before sending to GridSpoke
$product_data = apply_filters('gridspoke_product_data', $data, $product_id);

// Modify optimization settings
$settings = apply_filters('gridspoke_optimization_settings', $settings, $product_id);

// Filter optimized content before applying
$content = apply_filters('gridspoke_optimized_content', $content, $product_id);
```

### Custom Development

#### Custom Product Fields

```php
// Add custom fields to sync with GridSpoke
function add_custom_product_fields($data, $product_id) {
    $product = wc_get_product($product_id);
    
    $data['custom_fields'] = [
        'brand' => $product->get_meta('_brand'),
        'model' => $product->get_meta('_model'),
        'specifications' => $product->get_meta('_specifications')
    ];
    
    return $data;
}
add_filter('gridspoke_product_data', 'add_custom_product_fields', 10, 2);
```

#### Custom Optimization Logic

```php
// Custom optimization approval logic
function custom_approval_logic($auto_approve, $content, $product_id) {
    $product = wc_get_product($product_id);
    
    // Auto-approve for specific categories
    $approved_categories = ['electronics', 'clothing'];
    $product_categories = wp_get_post_terms($product_id, 'product_cat');
    
    foreach ($product_categories as $category) {
        if (in_array($category->slug, $approved_categories)) {
            return true;
        }
    }
    
    return $auto_approve;
}
add_filter('gridspoke_auto_approve', 'custom_approval_logic', 10, 3);
```

## Webhook Handling

### Webhook Endpoints

The plugin automatically sets up webhook endpoints to receive updates from GridSpoke:

**Endpoints**:
- `/wp-json/gridspoke/v1/webhook/optimization-complete`
- `/wp-json/gridspoke/v1/webhook/sync-status`
- `/wp-json/gridspoke/v1/webhook/job-progress`

### Security

**Webhook Verification**:
```php
// Webhook signature verification
function verify_webhook_signature($payload, $signature, $secret) {
    $expected_signature = hash_hmac('sha256', $payload, $secret);
    return hash_equals($signature, $expected_signature);
}
```

## WooCommerce Integration

### Product Data Sync

**Synced Fields**:
- Product title and description
- Short description
- Product categories and tags
- Product images and gallery
- Price and inventory status
- Product attributes and variations
- Meta fields and custom data

**Variation Support**:
- Sync product variations separately
- Optimize variation-specific content
- Handle variation images and descriptions

### Order Integration

**Optimization Triggers**:
- Optimize products when they receive orders
- Prioritize best-selling products
- Schedule optimization for trending products

## SureCart Integration

### Cart Data Sync

**Supported Features**:
- Product synchronization
- Price and inventory updates
- Category and tag management
- Image optimization
- Meta description optimization

**Configuration**:
```php
// SureCart specific settings
'surecart_integration' => [
    'sync_products' => true,
    'sync_prices' => true,
    'optimize_checkout_pages' => false,
    'api_version' => 'v1'
]
```

## Troubleshooting

### Common Issues

#### API Connection Problems

**Error**: "Failed to connect to GridSpoke API"

**Solutions**:
1. Verify API key is correct
2. Check API endpoint URL
3. Ensure SSL certificate is valid
4. Test network connectivity
5. Check firewall settings

```php
// Test API connection
function test_gridspoke_connection() {
    $api = new GridSpoke_API();
    $result = $api->test_connection();
    
    if ($result['success']) {
        echo "Connection successful";
    } else {
        echo "Connection failed: " . $result['error'];
    }
}
```

#### Webhook Delivery Issues

**Error**: "Webhook verification failed"

**Solutions**:
1. Verify webhook secret is configured correctly
2. Check IP whitelist settings
3. Ensure SSL is enabled
4. Test webhook endpoint manually

```bash
# Test webhook endpoint
curl -X POST https://yoursite.com/wp-json/gridspoke/v1/webhook/test \
  -H "Content-Type: application/json" \
  -H "X-GridSpoke-Signature: sha256=YOUR_SIGNATURE" \
  -d '{"test": "data"}'
```

#### Sync Failures

**Error**: "Product sync failed"

**Solutions**:
1. Check product has required fields
2. Verify file permissions
3. Test with single product first
4. Check error logs for details

### Debugging

#### Enable Debug Mode

```php
// Add to wp-config.php
define('GRIDSPOKE_DEBUG', true);
define('GRIDSPOKE_LOG_LEVEL', 'debug');
```

#### Log Files

**Log Locations**:
- `/wp-content/uploads/gridspoke/logs/api.log`
- `/wp-content/uploads/gridspoke/logs/webhook.log`
- `/wp-content/uploads/gridspoke/logs/sync.log`

**Log Analysis**:
```bash
# View recent API errors
tail -f /wp-content/uploads/gridspoke/logs/api.log | grep ERROR

# Monitor webhook activity
tail -f /wp-content/uploads/gridspoke/logs/webhook.log
```

## Performance Optimization

### Database Optimization

**Indexes**:
```sql
-- Add indexes for better performance
CREATE INDEX idx_gridspoke_product_id ON wp_postmeta (meta_key, meta_value(20)) 
WHERE meta_key = '_gridspoke_product_id';

CREATE INDEX idx_gridspoke_sync_status ON wp_postmeta (meta_key, meta_value(20)) 
WHERE meta_key = '_gridspoke_sync_status';
```

**Cleanup**:
```php
// Clean up old optimization data
function cleanup_old_gridspoke_data() {
    global $wpdb;
    
    // Remove data older than 6 months
    $wpdb->query($wpdb->prepare(
        "DELETE FROM {$wpdb->postmeta} 
         WHERE meta_key LIKE '_gridspoke_%' 
         AND post_id IN (
             SELECT ID FROM {$wpdb->posts} 
             WHERE post_modified < %s
         )",
        date('Y-m-d', strtotime('-6 months'))
    ));
}
```

### Caching

**API Response Caching**:
```php
// Cache API responses
function cache_gridspoke_response($key, $data, $expiration = 3600) {
    wp_cache_set($key, $data, 'gridspoke', $expiration);
}

function get_cached_gridspoke_response($key) {
    return wp_cache_get($key, 'gridspoke');
}
```

**Background Processing**:
```php
// Use WP Cron for background tasks
function schedule_gridspoke_sync() {
    if (!wp_next_scheduled('gridspoke_sync_products')) {
        wp_schedule_event(time(), 'hourly', 'gridspoke_sync_products');
    }
}
add_action('wp', 'schedule_gridspoke_sync');

function gridspoke_sync_products() {
    // Perform sync in background
    $sync = new GridSpoke_Sync();
    $sync->sync_pending_products();
}
add_action('gridspoke_sync_products', 'gridspoke_sync_products');
```

## Security Best Practices

### Data Protection

1. **Encrypt Sensitive Data**:
   - Encrypt API keys in database
   - Use secure communication channels
   - Implement proper key management

2. **Validate All Inputs**:
   - Sanitize webhook data
   - Validate API responses
   - Use WordPress security functions

3. **Access Control**:
   - Implement capability checks
   - Use nonces for forms
   - Restrict admin access

### Code Examples

```php
// Secure API key storage
function encrypt_api_key($api_key) {
    $key = wp_salt('auth');
    return openssl_encrypt($api_key, 'AES-256-CBC', $key, 0, $iv);
}

// Validate webhook data
function validate_webhook_data($data) {
    if (!is_array($data) || empty($data['product_id'])) {
        wp_die('Invalid webhook data', 'GridSpoke Webhook', ['response' => 400]);
    }
    
    return sanitize_data($data);
}

// Capability check
function check_gridspoke_permissions() {
    if (!current_user_can('manage_woocommerce')) {
        wp_die('Insufficient permissions');
    }
}
```

This documentation provides comprehensive guidance for installing, configuring, and using the GridSpoke WordPress plugin to optimize your ecommerce SEO automatically.
