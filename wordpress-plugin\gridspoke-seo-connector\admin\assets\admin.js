/**
 * GridSpoke SEO Connector - Admin JavaScript
 * Handles admin interface interactions and AJAX requests
 * Updated: Fixed Test Authentication button functionality
 */

(function($) {
    'use strict';

    // GridSpoke Admin object
    window.GridSpokeAdmin = {
        
        // Configuration
        config: {
            ajaxUrl: gridspoke_admin_vars.ajax_url,
            nonce: gridspoke_admin_vars.nonce,
            apiEndpoint: gridspoke_admin_vars.api_endpoint || '',
            apiKey: gridspoke_admin_vars.api_key || '',
            storeId: gridspoke_admin_vars.store_id || ''
        },

        // Initialize admin functionality
        init: function() {
            this.bindEvents();
            this.initConnectionTest();
            this.initBulkActions();
            this.initProgressMonitoring();
            this.setupPolling();
        },

        // Bind event handlers
        bindEvents: function() {
            // Settings page events
            $('#gridspoke-test-connection').on('click', this.testConnection.bind(this));
            $('#test-connection').on('click', this.testConnection.bind(this));
            $('#test-authentication').on('click', this.testAuthentication.bind(this));
            $('#gridspoke-sync-products').on('click', this.syncProducts.bind(this));
            $('#gridspoke-settings-form').on('submit', this.saveSettings.bind(this));
            
            // Product management events
            $('.gridspoke-optimize-single').on('click', this.optimizeSingleProduct.bind(this));
            $('#gridspoke-bulk-optimize').on('click', this.bulkOptimizeProducts.bind(this));
            
            // Job monitoring events
            $('.gridspoke-job-cancel').on('click', this.cancelJob.bind(this));
            $('.gridspoke-job-retry').on('click', this.retryJob.bind(this));
            
            // Dashboard widget events
            $('#gridspoke-refresh-stats').on('click', this.refreshDashboardStats.bind(this));
        },

        // Test API connection
        testConnection: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            const $status = $('#connection-status');
            
            $button.prop('disabled', true).text('Testing...');
            $status.removeClass('success error').addClass('testing').text('Testing connection...');
            
            const apiEndpoint = $('#gridspoke_api_endpoint').val();
            const apiKey = $('#gridspoke_api_key').val();
            
            if (!apiEndpoint || !apiKey) {
                this.showError('Please enter both API endpoint and API key');
                $button.prop('disabled', false).text('Test Connection');
                return;
            }
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_test_connection',
                    nonce: this.config.nonce,
                    api_endpoint: apiEndpoint,
                    api_key: apiKey
                },
                success: function(response) {
                    if (response.success) {
                        $status.removeClass('testing error').addClass('success')
                               .text('✓ Connection successful');
                        this.showSuccess('Successfully connected to GridSpoke API');
                    } else {
                        $status.removeClass('testing success').addClass('error')
                               .text('✗ Connection failed');
                        this.showError(response.data || 'Connection test failed');
                    }
                }.bind(this),
                error: function() {
                    $status.removeClass('testing success').addClass('error')
                           .text('✗ Connection failed');
                    this.showError('Unable to test connection. Please try again.');
                }.bind(this),
                complete: function() {
                    $button.prop('disabled', false).text('Test Connection');
                }
            });
        },

        // Test authentication with email/password
        testAuthentication: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            const $status = $('#authentication-status');
            
            $button.prop('disabled', true).text('Testing...');
            if ($status.length) {
                $status.removeClass('success error').addClass('testing').text('Testing authentication...');
            }
            
            const apiEndpoint = $('input[name="gridspoke_seo_settings[api_endpoint]"]').val() || 
                              $('#gridspoke_api_endpoint').val() || 
                              $('input[name="gridspoke_api_endpoint"]').val();
            const email = $('input[name="gridspoke_seo_settings[email]"]').val() || 
                         $('#gridspoke_email').val() || 
                         $('input[name="gridspoke_email"]').val();
            const password = $('input[name="gridspoke_seo_settings[password]"]').val() || 
                           $('#gridspoke_password').val() || 
                           $('input[name="gridspoke_password"]').val();
            
            if (!apiEndpoint || !email || !password) {
                this.showError('Please enter API endpoint, email, and password');
                $button.prop('disabled', false).text('Test Authentication');
                return;
            }
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_test_authentication',
                    nonce: this.config.nonce,
                    api_endpoint: apiEndpoint,
                    email: email,
                    password: password
                },
                success: function(response) {
                    if (response.success) {
                        if ($status.length) {
                            $status.removeClass('testing error').addClass('success')
                                   .text('✓ Authentication successful');
                        }
                        this.showSuccess('Successfully authenticated with GridSpoke API! Starting product sync...');
                        
                        // Trigger product sync after successful authentication
                        this.syncProductsAfterAuth();
                    } else {
                        if ($status.length) {
                            $status.removeClass('testing success').addClass('error')
                                   .text('✗ Authentication failed');
                        }
                        this.showError(response.data?.message || 'Authentication failed');
                    }
                }.bind(this),
                error: function() {
                    if ($status.length) {
                        $status.removeClass('testing success').addClass('error')
                               .text('✗ Authentication failed');
                    }
                    this.showError('Unable to test authentication. Please try again.');
                }.bind(this),
                complete: function() {
                    $button.prop('disabled', false).text('Test Authentication');
                }
            });
        },

        // Sync products after successful authentication
        syncProductsAfterAuth: function() {
            console.log('Starting product sync after authentication...');
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_sync_all_products',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        this.showSuccess('Products synced successfully! Check the GridSpoke dashboard.');
                        console.log('Product sync completed:', response.data);
                    } else {
                        console.warn('Product sync failed:', response.data);
                    }
                }.bind(this),
                error: function() {
                    console.error('Product sync request failed');
                }
            });
        },

        // Sync products with GridSpoke
        syncProducts: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            const $progress = $('#sync-progress');
            
            $button.prop('disabled', true).text('Syncing...');
            $progress.show().find('.progress-bar').css('width', '0%');
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_sync_products',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        this.showSuccess(`Successfully synced ${response.data.synced_count} products`);
                        this.updateSyncProgress(100);
                        
                        // Refresh product list if visible
                        this.refreshProductList();
                    } else {
                        this.showError(response.data || 'Product sync failed');
                    }
                }.bind(this),
                error: function() {
                    this.showError('Product sync failed. Please try again.');
                }.bind(this),
                complete: function() {
                    $button.prop('disabled', false).text('Sync Products');
                    setTimeout(function() {
                        $progress.hide();
                    }, 2000);
                }
            });
        },

        // Save settings
        saveSettings: function(e) {
            e.preventDefault();
            
            const $form = $(e.target);
            const $submitButton = $form.find('[type="submit"]');
            
            $submitButton.prop('disabled', true).val('Saving...');
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: $form.serialize() + '&action=gridspoke_save_settings&nonce=' + this.config.nonce,
                success: function(response) {
                    if (response.success) {
                        this.showSuccess('Settings saved successfully');
                        
                        // Update config with new values
                        this.config.apiEndpoint = $('#gridspoke_api_endpoint').val();
                        this.config.apiKey = $('#gridspoke_api_key').val();
                        this.config.storeId = response.data.store_id || '';
                    } else {
                        this.showError(response.data || 'Failed to save settings');
                    }
                }.bind(this),
                error: function() {
                    this.showError('Failed to save settings. Please try again.');
                }.bind(this),
                complete: function() {
                    $submitButton.prop('disabled', false).val('Save Settings');
                }
            });
        },

        // Optimize single product
        optimizeSingleProduct: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            const productId = $button.data('product-id');
            const optimizationType = $button.data('optimization-type') || 'full';
            
            if (!productId) {
                this.showError('Product ID not found');
                return;
            }
            
            $button.prop('disabled', true).text('Optimizing...');
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_optimize_product',
                    nonce: this.config.nonce,
                    product_id: productId,
                    optimization_type: optimizationType
                },
                success: function(response) {
                    if (response.success) {
                        this.showSuccess('Product optimization started');
                        
                        // Start monitoring the job
                        if (response.data.job_id) {
                            this.monitorJob(response.data.job_id, $button);
                        }
                    } else {
                        this.showError(response.data || 'Failed to start optimization');
                        $button.prop('disabled', false).text('Optimize');
                    }
                }.bind(this),
                error: function() {
                    this.showError('Failed to start optimization. Please try again.');
                    $button.prop('disabled', false).text('Optimize');
                }.bind(this)
            });
        },

        // Bulk optimize products
        bulkOptimizeProducts: function(e) {
            e.preventDefault();
            
            const selectedProducts = [];
            $('.product-checkbox:checked').each(function() {
                selectedProducts.push($(this).val());
            });
            
            if (selectedProducts.length === 0) {
                this.showError('Please select at least one product to optimize');
                return;
            }
            
            const $button = $(e.target);
            const optimizationType = $('#bulk-optimization-type').val() || 'full';
            
            $button.prop('disabled', true).text(`Optimizing ${selectedProducts.length} products...`);
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_bulk_optimize',
                    nonce: this.config.nonce,
                    product_ids: selectedProducts,
                    optimization_type: optimizationType
                },
                success: function(response) {
                    if (response.success) {
                        this.showSuccess(`Bulk optimization started for ${selectedProducts.length} products`);
                        
                        // Start monitoring the job
                        if (response.data.job_id) {
                            this.monitorJob(response.data.job_id, $button);
                        }
                        
                        // Clear selections
                        $('.product-checkbox:checked').prop('checked', false);
                    } else {
                        this.showError(response.data || 'Failed to start bulk optimization');
                        $button.prop('disabled', false).text('Bulk Optimize');
                    }
                }.bind(this),
                error: function() {
                    this.showError('Failed to start bulk optimization. Please try again.');
                    $button.prop('disabled', false).text('Bulk Optimize');
                }.bind(this)
            });
        },

        // Monitor optimization job progress
        monitorJob: function(jobId, $triggerButton) {
            const $progressContainer = $('#optimization-progress');
            const $progressBar = $progressContainer.find('.progress-bar');
            const $progressText = $progressContainer.find('.progress-text');
            
            $progressContainer.show();
            
            const checkStatus = function() {
                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'gridspoke_check_job_status',
                        nonce: this.config.nonce,
                        job_id: jobId
                    },
                    success: function(response) {
                        if (response.success) {
                            const job = response.data;
                            const progress = job.progress || 0;
                            
                            $progressBar.css('width', progress + '%');
                            $progressText.text(`${Math.round(progress)}% - ${job.status || 'Processing'}`);
                            
                            if (job.status === 'completed') {
                                this.showSuccess('Optimization completed successfully');
                                $progressText.text('100% - Completed');
                                $triggerButton.prop('disabled', false).text('Optimize');
                                
                                // Refresh product list to show updated data
                                this.refreshProductList();
                                
                                setTimeout(function() {
                                    $progressContainer.hide();
                                }, 3000);
                            } else if (job.status === 'failed') {
                                this.showError('Optimization failed: ' + (job.error_message || 'Unknown error'));
                                $progressText.text('Failed');
                                $triggerButton.prop('disabled', false).text('Optimize');
                                
                                setTimeout(function() {
                                    $progressContainer.hide();
                                }, 3000);
                            } else {
                                // Continue monitoring
                                setTimeout(checkStatus.bind(this), 2000);
                            }
                        } else {
                            // Job not found or error - stop monitoring
                            $triggerButton.prop('disabled', false).text('Optimize');
                            $progressContainer.hide();
                        }
                    }.bind(this),
                    error: function() {
                        // Continue monitoring on error (temporary network issues)
                        setTimeout(checkStatus.bind(this), 5000);
                    }.bind(this)
                });
            }.bind(this);
            
            // Start monitoring
            checkStatus();
        },

        // Cancel optimization job
        cancelJob: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            const jobId = $button.data('job-id');
            
            if (!confirm('Are you sure you want to cancel this optimization job?')) {
                return;
            }
            
            $button.prop('disabled', true).text('Cancelling...');
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_cancel_job',
                    nonce: this.config.nonce,
                    job_id: jobId
                },
                success: function(response) {
                    if (response.success) {
                        this.showSuccess('Job cancelled successfully');
                        this.refreshJobsList();
                    } else {
                        this.showError(response.data || 'Failed to cancel job');
                    }
                }.bind(this),
                error: function() {
                    this.showError('Failed to cancel job. Please try again.');
                }.bind(this),
                complete: function() {
                    $button.prop('disabled', false).text('Cancel');
                }
            });
        },

        // Retry failed job
        retryJob: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            const jobId = $button.data('job-id');
            
            $button.prop('disabled', true).text('Retrying...');
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_retry_job',
                    nonce: this.config.nonce,
                    job_id: jobId
                },
                success: function(response) {
                    if (response.success) {
                        this.showSuccess('Job restarted successfully');
                        this.refreshJobsList();
                    } else {
                        this.showError(response.data || 'Failed to retry job');
                    }
                }.bind(this),
                error: function() {
                    this.showError('Failed to retry job. Please try again.');
                }.bind(this),
                complete: function() {
                    $button.prop('disabled', false).text('Retry');
                }
            });
        },

        // Refresh dashboard stats
        refreshDashboardStats: function(e) {
            e.preventDefault();
            
            const $button = $(e.target);
            const $widget = $button.closest('.gridspoke-dashboard-widget');
            
            $button.prop('disabled', true).text('Refreshing...');
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_refresh_stats',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update widget content
                        $widget.find('.stats-content').html(response.data.html);
                        this.showSuccess('Stats refreshed');
                    } else {
                        this.showError(response.data || 'Failed to refresh stats');
                    }
                }.bind(this),
                error: function() {
                    this.showError('Failed to refresh stats. Please try again.');
                }.bind(this),
                complete: function() {
                    $button.prop('disabled', false).text('Refresh');
                }
            });
        },

        // Initialize connection test on page load
        initConnectionTest: function() {
            if (this.config.apiEndpoint && this.config.apiKey) {
                // Auto-test connection if credentials are available
                setTimeout(function() {
                    $('#gridspoke-test-connection').trigger('click');
                }, 1000);
            }
        },

        // Initialize bulk actions
        initBulkActions: function() {
            // Select all products checkbox
            $('#select-all-products').on('change', function() {
                $('.product-checkbox').prop('checked', $(this).is(':checked'));
                this.updateBulkActionButton();
            }.bind(this));
            
            // Individual product checkboxes
            $('.product-checkbox').on('change', this.updateBulkActionButton.bind(this));
        },

        // Update bulk action button state
        updateBulkActionButton: function() {
            const selectedCount = $('.product-checkbox:checked').length;
            const $button = $('#gridspoke-bulk-optimize');
            
            if (selectedCount > 0) {
                $button.prop('disabled', false).text(`Optimize ${selectedCount} Products`);
            } else {
                $button.prop('disabled', true).text('Select Products to Optimize');
            }
        },

        // Initialize progress monitoring
        initProgressMonitoring: function() {
            // Check for active jobs on page load
            this.checkActiveJobs();
        },

        // Check for active jobs
        checkActiveJobs: function() {
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gridspoke_get_active_jobs',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success && response.data.jobs.length > 0) {
                        // Monitor active jobs
                        response.data.jobs.forEach(function(job) {
                            this.monitorJob(job.id, $('#optimization-button-' + job.id));
                        }.bind(this));
                    }
                }.bind(this)
            });
        },

        // Setup polling for updates
        setupPolling: function() {
            // Poll for job updates every 30 seconds
            setInterval(function() {
                this.checkActiveJobs();
                this.updateDashboardStats();
            }.bind(this), 30000);
        },

        // Update dashboard stats periodically
        updateDashboardStats: function() {
            if ($('.gridspoke-dashboard-widget').length > 0) {
                $('#gridspoke-refresh-stats').trigger('click');
            }
        },

        // Refresh product list
        refreshProductList: function() {
            if ($('.gridspoke-products-table').length > 0) {
                location.reload(); // Simple refresh for now
            }
        },

        // Refresh jobs list
        refreshJobsList: function() {
            if ($('.gridspoke-jobs-table').length > 0) {
                location.reload(); // Simple refresh for now
            }
        },

        // Update sync progress
        updateSyncProgress: function(percentage) {
            $('#sync-progress .progress-bar').css('width', percentage + '%');
            $('#sync-progress .progress-text').text(percentage + '% Complete');
        },

        // Utility functions
        showSuccess: function(message) {
            this.showNotice(message, 'success');
        },

        showError: function(message) {
            this.showNotice(message, 'error');
        },

        showNotice: function(message, type) {
            const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        GridSpokeAdmin.init();
    });

})(jQuery);
