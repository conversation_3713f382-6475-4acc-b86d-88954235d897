# GridSpoke Alerting Rules
# Phase 8: Production alerting for GridSpoke SEO optimization platform

groups:
  
  # =============================================================================
  # GRIDSPOKE BUSINESS CRITICAL ALERTS
  # =============================================================================
  
  - name: gridspoke.business.critical
    interval: 30s
    rules:
      
      # AI optimization failure rate too high
      - alert: GridSpokeOptimizationFailureRateHigh
        expr: |
          (
            rate(gridspoke_optimization_requests_total{status="error"}[5m]) / 
            rate(gridspoke_optimization_requests_total[5m])
          ) * 100 > 10
        for: 2m
        labels:
          severity: critical
          service: gridspoke
          component: ai-optimization
        annotations:
          summary: "GridSpoke optimization failure rate is {{ $value }}%"
          description: "AI optimization failure rate has been above 10% for more than 2 minutes. This indicates issues with AI API or processing pipeline."
          runbook_url: "https://docs.gridspoke.ai/runbooks/optimization-failures"
          
      # AI API cost spike
      - alert: GridSpokeAICostSpike
        expr: |
          rate(gridspoke_ai_cost_usd_total[1h]) > 50
        for: 5m
        labels:
          severity: warning
          service: gridspoke
          component: ai-cost
        annotations:
          summary: "GridSpoke AI costs are ${{ $value }}/hour"
          description: "AI API costs have exceeded $50/hour. Review usage patterns and consider cost optimization."
          
      # No optimizations processed recently
      - alert: GridSpokeOptimizationStalled
        expr: |
          increase(gridspoke_optimization_requests_total[30m]) == 0
        for: 30m
        labels:
          severity: warning
          service: gridspoke
          component: processing
        annotations:
          summary: "GridSpoke has not processed any optimizations in 30 minutes"
          description: "No optimization requests have been processed recently. Check Celery workers and task queue."
          
      # SEO improvement rate dropped
      - alert: GridSpokeSEOImprovementLow
        expr: |
          gridspoke_seo_improvement_score{metric_type="improvement_rate"} < 50
        for: 1h
        labels:
          severity: warning
          service: gridspoke
          component: seo-quality
        annotations:
          summary: "GridSpoke SEO improvement rate is {{ $value }}%"
          description: "SEO improvement rate has dropped below 50%. Review AI prompts and optimization algorithms."

  # =============================================================================
  # GRIDSPOKE INFRASTRUCTURE ALERTS
  # =============================================================================
  
  - name: gridspoke.infrastructure.critical
    interval: 30s
    rules:
      
      # Database connection issues
      - alert: GridSpokePostgreSQLDown
        expr: |
          gridspoke_db_connections_active == 0
        for: 1m
        labels:
          severity: critical
          service: gridspoke
          component: database
        annotations:
          summary: "GridSpoke PostgreSQL database is unreachable"
          description: "No active database connections detected. Database may be down or unreachable."
          
      # Redis connection issues
      - alert: GridSpokeRedisDown
        expr: |
          up{job="redis-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: gridspoke
          component: cache
        annotations:
          summary: "GridSpoke Redis is down"
          description: "Redis is unreachable. This will affect caching and Celery task queue."
          
      # Celery workers down
      - alert: GridSpokeCeleryWorkersDown
        expr: |
          gridspoke_celery_queue_size > 100 and 
          rate(gridspoke_celery_tasks_total[5m]) == 0
        for: 5m
        labels:
          severity: critical
          service: gridspoke
          component: workers
        annotations:
          summary: "GridSpoke Celery workers appear to be down"
          description: "Tasks are queuing but not being processed. Check Celery workers."
          
      # High database query latency
      - alert: GridSpokeDatabaseLatencyHigh
        expr: |
          histogram_quantile(0.95, rate(gridspoke_db_query_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
          service: gridspoke
          component: database
        annotations:
          summary: "GridSpoke database queries are slow"
          description: "95th percentile query time is {{ $value }}s. Check database performance."

  # =============================================================================
  # GRIDSPOKE PERFORMANCE ALERTS
  # =============================================================================
  
  - name: gridspoke.performance
    interval: 30s
    rules:
      
      # High API response time
      - alert: GridSpokeAPIResponseTimeSlow
        expr: |
          histogram_quantile(0.95, rate(gridspoke_http_request_duration_seconds_bucket[5m])) > 5
        for: 3m
        labels:
          severity: warning
          service: gridspoke
          component: api
        annotations:
          summary: "GridSpoke API response time is {{ $value }}s"
          description: "95th percentile API response time exceeds 5 seconds."
          
      # High optimization processing time
      - alert: GridSpokeOptimizationSlow
        expr: |
          histogram_quantile(0.95, rate(gridspoke_optimization_duration_seconds_bucket[10m])) > 300
        for: 5m
        labels:
          severity: warning
          service: gridspoke
          component: optimization
        annotations:
          summary: "GridSpoke optimizations taking {{ $value }}s"
          description: "Product optimizations are taking longer than 5 minutes."
          
      # Memory usage high
      - alert: GridSpokeMemoryUsageHigh
        expr: |
          (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: gridspoke
          component: system
        annotations:
          summary: "GridSpoke system memory usage is {{ $value }}%"
          description: "System memory usage is above 85%. Consider scaling or optimizing memory usage."
          
      # Disk space low
      - alert: GridSpokeDiskSpaceLow
        expr: |
          (1 - (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"})) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: gridspoke
          component: system
        annotations:
          summary: "GridSpoke disk space usage is {{ $value }}%"
          description: "Disk space usage is above 80%. Clean up logs or expand storage."

  # =============================================================================
  # GRIDSPOKE EXTERNAL DEPENDENCIES
  # =============================================================================
  
  - name: gridspoke.external.dependencies
    interval: 60s
    rules:
      
      # OpenRouter API issues
      - alert: GridSpokeOpenRouterAPIDown
        expr: |
          probe_success{job="blackbox-http", instance="https://openrouter.ai/api/v1/models"} == 0
        for: 2m
        labels:
          severity: critical
          service: gridspoke
          component: external-api
        annotations:
          summary: "OpenRouter API is unreachable"
          description: "Cannot reach OpenRouter API. AI optimizations will fail."
          
      # WordPress API issues
      - alert: GridSpokeWordPressAPIDown
        expr: |
          probe_success{job="blackbox-http", instance="https://api.wordpress.org/stats/"} == 0
        for: 5m
        labels:
          severity: warning
          service: gridspoke
          component: external-api
        annotations:
          summary: "WordPress API is unreachable"
          description: "WordPress API checks are failing. May affect plugin integrations."
          
      # SSL certificate expiring
      - alert: GridSpokeSSLCertificateExpiring
        expr: |
          probe_ssl_earliest_cert_expiry - time() < 86400 * 7
        for: 1h
        labels:
          severity: warning
          service: gridspoke
          component: security
        annotations:
          summary: "SSL certificate expiring in {{ $value | humanizeDuration }}"
          description: "SSL certificate for GridSpoke will expire soon. Renew certificate."

  # =============================================================================
  # GRIDSPOKE WORDPRESS INTEGRATION
  # =============================================================================
  
  - name: gridspoke.wordpress.integration
    interval: 60s
    rules:
      
      # WordPress sync failure rate high
      - alert: GridSpokeWordPressSyncFailureHigh
        expr: |
          (
            rate(gridspoke_wordpress_sync_requests_total{status="failed"}[10m]) / 
            rate(gridspoke_wordpress_sync_requests_total[10m])
          ) * 100 > 20
        for: 5m
        labels:
          severity: warning
          service: gridspoke
          component: wordpress-sync
        annotations:
          summary: "WordPress sync failure rate is {{ $value }}%"
          description: "WordPress synchronization failure rate is above 20%."
          
      # WordPress webhook latency high
      - alert: GridSpokeWordPressWebhookSlow
        expr: |
          histogram_quantile(0.95, rate(gridspoke_wordpress_webhook_duration_seconds_bucket[5m])) > 10
        for: 3m
        labels:
          severity: warning
          service: gridspoke
          component: wordpress-webhook
        annotations:
          summary: "WordPress webhook processing taking {{ $value }}s"
          description: "WordPress webhook processing is slower than expected."

  # =============================================================================
  # GRIDSPOKE BUSINESS METRICS
  # =============================================================================
  
  - name: gridspoke.business.metrics
    interval: 300s  # 5 minutes
    rules:
      
      # Daily optimization target not met
      - alert: GridSpokeDailyOptimizationTargetMissed
        expr: |
          increase(gridspoke_optimization_requests_total{status="success"}[24h]) < 1000
        for: 1h
        labels:
          severity: info
          service: gridspoke
          component: business-metrics
        annotations:
          summary: "Daily optimization target may not be met"
          description: "Only {{ $value }} optimizations completed in the last 24 hours. Target is 1000."
          
      # Cost per optimization too high
      - alert: GridSpokeCostPerOptimizationHigh
        expr: |
          (
            increase(gridspoke_ai_cost_usd_total[1h]) / 
            increase(gridspoke_optimization_requests_total{status="success"}[1h])
          ) > 0.20
        for: 30m
        labels:
          severity: warning
          service: gridspoke
          component: cost-optimization
        annotations:
          summary: "Cost per optimization is ${{ $value }}"
          description: "Average cost per optimization exceeds $0.20. Review AI model usage."
          
      # Store churn detected
      - alert: GridSpokeStoreChurnDetected
        expr: |
          increase(gridspoke_active_users{time_window="24h"}[24h]) < 0
        for: 2h
        labels:
          severity: info
          service: gridspoke
          component: business-metrics
        annotations:
          summary: "Store churn detected: {{ $value }} stores"
          description: "Number of active stores has decreased in the last 24 hours."

  # =============================================================================
  # GRIDSPOKE SYSTEM HEALTH
  # =============================================================================
  
  - name: gridspoke.system.health
    interval: 60s
    rules:
      
      # Service health check failing
      - alert: GridSpokeHealthCheckFailing
        expr: |
          up{job="gridspoke-api"} == 0
        for: 1m
        labels:
          severity: critical
          service: gridspoke
          component: health
        annotations:
          summary: "GridSpoke API health check is failing"
          description: "GridSpoke API is not responding to health checks."
          
      # Component degraded
      - alert: GridSpokeComponentDegraded
        expr: |
          gridspoke_component_health_status{status="degraded"} == 1
        for: 5m
        labels:
          severity: warning
          service: gridspoke
          component: health
        annotations:
          summary: "GridSpoke component {{ $labels.component }} is degraded"
          description: "Component {{ $labels.component }} is in degraded state."
          
      # Multiple components unhealthy
      - alert: GridSpokeMultipleComponentsUnhealthy
        expr: |
          count(gridspoke_component_health_status{status="unhealthy"} == 1) >= 2
        for: 2m
        labels:
          severity: critical
          service: gridspoke
          component: health
        annotations:
          summary: "Multiple GridSpoke components are unhealthy"
          description: "{{ $value }} components are currently unhealthy. System stability at risk."
