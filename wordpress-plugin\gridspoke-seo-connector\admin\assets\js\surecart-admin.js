/**
 * GridSpoke SEO SureCart Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        initializeSureCartIntegration();
    });
    
    /**
     * Initialize SureCart integration
     */
    function initializeSureCartIntegration() {
        // Sync single SureCart product
        $('#gridspoke-sync-surecart-product').on('click', function() {
            var $button = $(this);
            var $status = $('#gridspoke-surecart-optimization-status');
            var productId = $button.data('product-id');
            
            $button.prop('disabled', true).text(gridspoke_surecart_ajax.messages.syncing);
            
            $.ajax({
                url: gridspoke_surecart_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_sync_surecart_product',
                    nonce: gridspoke_surecart_ajax.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        showMetaBoxNotice(response.data.message, 'success');
                        
                        // Refresh meta box content
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        showMetaBoxNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showMetaBoxNotice(gridspoke_surecart_ajax.messages.error, 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Sync to GridSpoke');
                }
            });
        });
        
        // Optimize single SureCart product
        $('#gridspoke-optimize-surecart-product').on('click', function() {
            var $button = $(this);
            var $status = $('#gridspoke-surecart-optimization-status');
            var productId = $button.data('product-id');
            
            $button.prop('disabled', true).text(gridspoke_surecart_ajax.messages.optimizing);
            
            $.ajax({
                url: gridspoke_surecart_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_optimize_surecart_product',
                    nonce: gridspoke_surecart_ajax.nonce,
                    product_id: productId
                },
                success: function(response) {
                    if (response.success) {
                        showMetaBoxNotice(response.data.message, 'success');
                        
                        if (response.data.optimization_id) {
                            showMetaBoxNotice('Optimization ID: ' + response.data.optimization_id, 'info');
                        }
                    } else {
                        showMetaBoxNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showMetaBoxNotice(gridspoke_surecart_ajax.messages.error, 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Request Optimization');
                }
            });
        });
        
        // Add bulk actions to SureCart product list
        if ($('#bulk-action-selector-top').length > 0) {
            $('#bulk-action-selector-top, #bulk-action-selector-bottom').append(
                '<option value="gridspoke_optimize">Optimize with GridSpoke</option>' +
                '<option value="gridspoke_sync">Sync to GridSpoke</option>'
            );
        }
        
        // Handle bulk actions
        $('#doaction, #doaction2').on('click', function(e) {
            var action = $(this).prev('select').val();
            
            if (action === 'gridspoke_optimize' || action === 'gridspoke_sync') {
                var selectedProducts = [];
                $('input[name="post[]"]:checked').each(function() {
                    selectedProducts.push($(this).val());
                });
                
                if (selectedProducts.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one product.');
                    return;
                }
                
                var actionText = action === 'gridspoke_optimize' ? 'optimize' : 'sync';
                var confirmMessage = 'Are you sure you want to ' + actionText + ' ' + selectedProducts.length + ' selected products?';
                
                if (!confirm(confirmMessage)) {
                    e.preventDefault();
                    return;
                }
                
                // Show loading notice
                var $notice = $('<div class="notice notice-info"><p>Processing ' + selectedProducts.length + ' products...</p></div>');
                $('.wrap h1').after($notice);
            }
        });
    }
    
    /**
     * Show notice in meta box
     */
    function showMetaBoxNotice(message, type) {
        var $notice = $('<div class="gridspoke-metabox-notice gridspoke-notice-' + type + '">' + message + '</div>');
        $('#gridspoke-surecart-optimization-status').prepend($notice);
        
        setTimeout(function() {
            $notice.fadeOut();
        }, 5000);
    }
    
})(jQuery);
