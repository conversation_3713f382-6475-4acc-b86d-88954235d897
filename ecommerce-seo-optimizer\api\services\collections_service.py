"""
Smart Collections & Categories Service for GridSpoke Ecommerce SEO Optimizer
Handles automatic collection creation, category optimization, and internal linking strategies.
"""

import logging
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from pydantic import BaseModel, Field

# Runtime imports - these will be available when deployed
try:
    from ..core.database import get_db
    from ..crud.product import get_products, get_product_by_id
    from ..crud.store import get_store_by_id
except ImportError:
    # Development environment - these imports will work when deployed
    pass
from ..models.product import Product
from ..models.collection import Collection, CollectionProduct
from ..agents.content_generator import ContentGenerator
from ..agents.product_optimizer import ProductOptimizer

logger = logging.getLogger(__name__)


class CollectionRule(BaseModel):
    """Rule for automatic collection creation"""
    name: str
    criteria: Dict[str, Any]
    priority: int = 1
    min_products: int = 3
    max_products: int = 50


class CategoryOptimization(BaseModel):
    """Category page optimization result"""
    category_name: str
    seo_title: str
    meta_description: str
    description: str
    h1_tag: str
    breadcrumbs: List[str]
    internal_links: List[Dict[str, str]]
    schema_markup: Dict[str, Any]


class InternalLinkSuggestion(BaseModel):
    """Internal linking suggestion between products/categories"""
    source_id: str
    source_type: str  # 'product' or 'category'
    target_id: str
    target_type: str  # 'product' or 'category'
    anchor_text: str
    relevance_score: float
    link_type: str  # 'related', 'upsell', 'category', 'brand'


class SmartCollectionsService:
    """Service for managing smart collections and category optimization"""
    
    def __init__(self):
        self.content_generator = ContentGenerator()
        self.product_optimizer = ProductOptimizer()
        
        # Predefined collection rules
        self.default_rules = [
            CollectionRule(
                name="Best Sellers",
                criteria={"order_count": {"$gte": 10}},
                priority=1,
                max_products=20
            ),
            CollectionRule(
                name="New Arrivals",
                criteria={"days_since_created": {"$lte": 30}},
                priority=2,
                max_products=25
            ),
            CollectionRule(
                name="Sale Items",
                criteria={"discount_percentage": {"$gte": 10}},
                priority=3
            ),
            CollectionRule(
                name="High Rated",
                criteria={"average_rating": {"$gte": 4.0}},
                priority=4
            ),
            CollectionRule(
                name="Under $50",
                criteria={"price": {"$lte": 50}},
                priority=5
            ),
            CollectionRule(
                name="Premium Collection",
                criteria={"price": {"$gte": 200}},
                priority=6
            )
        ]
    
    async def create_automatic_collections(
        self, 
        store_id: str, 
        custom_rules: Optional[List[CollectionRule]] = None
    ) -> List[Dict[str, Any]]:
        """
        Create automatic collections based on product attributes.
        
        Args:
            store_id: Store identifier
            custom_rules: Custom collection rules to apply
            
        Returns:
            List of created collections
        """
        logger.info(f"Creating automatic collections for store {store_id}")
        
        db = next(get_db())
        created_collections = []
        
        try:
            # Get all products for the store
            products = get_products(db, store_id=store_id, limit=1000)
            
            if not products:
                logger.warning(f"No products found for store {store_id}")
                return []
            
            # Use custom rules if provided, otherwise use defaults
            rules = custom_rules or self.default_rules
            
            for rule in rules:
                try:
                    # Find products matching the rule criteria
                    matching_products = self._filter_products_by_criteria(
                        products, rule.criteria
                    )
                    
                    if len(matching_products) >= rule.min_products:
                        # Create collection
                        collection_data = await self._create_collection(
                            db, store_id, rule, matching_products
                        )
                        created_collections.append(collection_data)
                        
                        logger.info(
                            f"Created collection '{rule.name}' with {len(matching_products)} products"
                        )
                    else:
                        logger.info(
                            f"Skipping collection '{rule.name}' - only {len(matching_products)} products found (minimum: {rule.min_products})"
                        )
                        
                except Exception as e:
                    logger.error(f"Error creating collection '{rule.name}': {str(e)}")
                    continue
            
            return created_collections
            
        except Exception as e:
            logger.error(f"Error creating automatic collections: {str(e)}")
            raise
        finally:
            db.close()
    
    def _filter_products_by_criteria(
        self, 
        products: List[Product], 
        criteria: Dict[str, Any]
    ) -> List[Product]:
        """Filter products based on criteria"""
        matching_products = []
        
        for product in products:
            matches = True
            
            for field, condition in criteria.items():
                try:
                    if field == "order_count":
                        value = getattr(product, 'order_count', 0)
                    elif field == "days_since_created":
                        if product.created_at:
                            days_diff = (datetime.utcnow() - product.created_at).days
                            value = days_diff
                        else:
                            continue
                    elif field == "discount_percentage":
                        # Calculate discount if original_price exists
                        if hasattr(product, 'original_price') and product.original_price:
                            discount = ((product.original_price - product.price) / product.original_price) * 100
                            value = discount
                        else:
                            continue
                    elif field == "average_rating":
                        value = getattr(product, 'average_rating', 0)
                    elif field == "price":
                        value = float(product.price) if product.price else 0
                    else:
                        value = getattr(product, field, None)
                    
                    # Check condition
                    if isinstance(condition, dict):
                        for operator, expected in condition.items():
                            if operator == "$gte" and value < expected:
                                matches = False
                                break
                            elif operator == "$lte" and value > expected:
                                matches = False
                                break
                            elif operator == "$eq" and value != expected:
                                matches = False
                                break
                    else:
                        if value != condition:
                            matches = False
                            break
                    
                    if not matches:
                        break
                        
                except (AttributeError, ValueError, TypeError):
                    matches = False
                    break
            
            if matches:
                matching_products.append(product)
        
        return matching_products
    
    async def _create_collection(
        self,
        db: Session,
        store_id: str,
        rule: CollectionRule,
        products: List[Product]
    ) -> Dict[str, Any]:
        """Create a collection with SEO-optimized content"""
        
        # Generate SEO content for the collection
        collection_content = await self._generate_collection_content(
            rule.name, products[:rule.max_products]
        )
        
        # Create collection record
        collection = Collection(
            store_id=store_id,
            name=rule.name,
            description=collection_content["description"],
            seo_title=collection_content["seo_title"],
            meta_description=collection_content["meta_description"],
            handle=self._generate_handle(rule.name),
            is_auto_generated=True,
            collection_type="automatic",
            rules=rule.dict(),
            created_at=datetime.utcnow()
        )
        
        db.add(collection)
        db.flush()  # Get the collection ID
        
        # Add products to collection
        for product in products[:rule.max_products]:
            collection_product = CollectionProduct(
                collection_id=collection.id,
                product_id=product.id,
                position=products.index(product)
            )
            db.add(collection_product)
        
        db.commit()
        
        return {
            "id": str(collection.id),
            "name": collection.name,
            "product_count": len(products[:rule.max_products]),
            "seo_title": collection.seo_title,
            "meta_description": collection.meta_description,
            "description": collection.description
        }
    
    async def _generate_collection_content(
        self, 
        collection_name: str, 
        products: List[Product]
    ) -> Dict[str, str]:
        """Generate SEO-optimized content for collection"""
        
        # Extract product categories and common attributes  
        categories = list(set([p.category for p in products if p.category]))
        brands = list(set([getattr(p, 'brand', None) for p in products if getattr(p, 'brand', None)]))
        
        # Generate content using AI
        content_request = {
            "content_type": "collection_description",
            "collection_name": collection_name,
            "product_count": len(products),
            "categories": categories,
            "brands": brands,
            "products": [
                {
                    "name": p.title,
                    "category": p.category,
                    "price": float(p.price) if p.price else 0
                }
                for p in products[:5]  # Sample products
            ]
        }
        
        try:
            result = await self.content_generator.process(content_request)
            
            if result.success and result.content:
                content = result.content
                return {
                    "seo_title": content.get("seo_title", f"{collection_name} Collection"),
                    "meta_description": content.get("meta_description", f"Discover our {collection_name.lower()} collection"),
                    "description": content.get("description", f"Explore our curated {collection_name.lower()} collection.")
                }
        except Exception as e:
            logger.error(f"Error generating collection content: {str(e)}")
        
        # Fallback content
        return {
            "seo_title": f"{collection_name} Collection",
            "meta_description": f"Discover our {collection_name.lower()} collection featuring {len(products)} carefully selected products.",
            "description": f"Explore our curated {collection_name.lower()} collection with {len(products)} products."
        }
    
    def _generate_handle(self, name: str) -> str:
        """Generate URL-friendly handle"""
        return name.lower().replace(" ", "-").replace("'", "")
    
    async def optimize_category_pages(
        self,
        store_id: str,
        categories: Optional[List[str]] = None
    ) -> List[CategoryOptimization]:
        """
        Generate SEO-optimized content for category pages.
        
        Args:
            store_id: Store identifier
            categories: Specific categories to optimize (if None, optimizes all)
            
        Returns:
            List of category optimizations
        """
        logger.info(f"Optimizing category pages for store {store_id}")
        
        db = next(get_db())
        optimizations = []
        
        try:
            # Get all categories if none specified
            if not categories:
                result = db.query(Product.category).filter(
                    Product.store_id == store_id,
                    Product.category.isnot(None)
                ).distinct().all()
                categories = [r[0] for r in result if r[0]]
            
            for category in categories:
                try:
                    # Get products in this category
                    products = db.query(Product).filter(
                        and_(
                            Product.store_id == store_id,
                            Product.category == category
                        )
                    ).all()
                    
                    if not products:
                        continue
                    
                    # Generate category optimization
                    optimization = await self._optimize_single_category(
                        category, products
                    )
                    optimizations.append(optimization)
                    
                except Exception as e:
                    logger.error(f"Error optimizing category '{category}': {str(e)}")
                    continue
            
            return optimizations
            
        except Exception as e:
            logger.error(f"Error optimizing category pages: {str(e)}")
            raise
        finally:
            db.close()
    
    async def _optimize_single_category(
        self,
        category: str,
        products: List[Product]
    ) -> CategoryOptimization:
        """Optimize a single category page"""
        
        # Generate category content using AI
        content_request = {
            "content_type": "category_page",
            "category": category,
            "product_count": len(products),
            "price_range": {
                "min": min([float(p.price) for p in products if p.price]),
                "max": max([float(p.price) for p in products if p.price])
            },
            "brands": list(set([getattr(p, 'brand', None) for p in products if getattr(p, 'brand', None)])),
            "sample_products": [
                {"name": p.title, "price": float(p.price) if p.price else 0}
                for p in products[:3]
            ]
        }
        
        try:
            result = await self.content_generator.process(content_request)
            
            if result.success and result.content:
                content = result.content
                
                return CategoryOptimization(
                    category_name=category,
                    seo_title=content.get("seo_title", f"{category} - Shop Now"),
                    meta_description=content.get("meta_description", f"Shop {category.lower()} products"),
                    description=content.get("description", f"Discover our {category.lower()} collection"),
                    h1_tag=content.get("h1_tag", category),
                    breadcrumbs=content.get("breadcrumbs", ["Home", category]),
                    internal_links=content.get("internal_links", []),
                    schema_markup=self._generate_category_schema(category, products)
                )
        except Exception as e:
            logger.error(f"Error generating category content: {str(e)}")
        
        # Fallback optimization
        return CategoryOptimization(
            category_name=category,
            seo_title=f"{category} - Shop Quality Products",
            meta_description=f"Shop our {category.lower()} collection with {len(products)} products. Find the perfect item for your needs.",
            description=f"Discover our extensive {category.lower()} collection featuring {len(products)} carefully selected products.",
            h1_tag=category,
            breadcrumbs=["Home", category],
            internal_links=[],
            schema_markup=self._generate_category_schema(category, products)
        )
    
    def _generate_category_schema(
        self,
        category: str,
        products: List[Product]
    ) -> Dict[str, Any]:
        """Generate schema markup for category page"""
        
        return {
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": category,
            "description": f"{category} products collection",
            "numberOfItems": len(products),
            "mainEntity": {
                "@type": "ItemList",
                "numberOfItems": len(products),
                "itemListElement": [
                    {
                        "@type": "Product",
                        "name": product.title,
                        "url": f"/products/{product.id}",
                        "position": idx + 1
                    }
                    for idx, product in enumerate(products[:10])  # Limit for performance
                ]
            }
        }
    
    async def generate_internal_linking_strategy(
        self,
        store_id: str,
        max_suggestions: int = 50
    ) -> List[InternalLinkSuggestion]:
        """
        Generate intelligent internal linking suggestions.
        
        Args:
            store_id: Store identifier
            max_suggestions: Maximum number of suggestions to return
            
        Returns:
            List of internal linking suggestions
        """
        logger.info(f"Generating internal linking strategy for store {store_id}")
        
        db = next(get_db())
        suggestions = []
        
        try:
            # Get all products
            products = get_products(db, store_id=store_id, limit=500)
            
            if len(products) < 2:
                return []
            
            # Generate product-to-product links
            suggestions.extend(
                await self._generate_product_links(products)
            )
            
            # Generate category-to-product links
            suggestions.extend(
                await self._generate_category_links(db, store_id, products)
            )
            
            # Sort by relevance score and limit results
            suggestions.sort(key=lambda x: x.relevance_score, reverse=True)
            
            return suggestions[:max_suggestions]
            
        except Exception as e:
            logger.error(f"Error generating internal linking strategy: {str(e)}")
            raise
        finally:
            db.close()
    
    async def _generate_product_links(
        self,
        products: List[Product]
    ) -> List[InternalLinkSuggestion]:
        """Generate product-to-product internal links"""
        suggestions = []
        
        # Group products by category for related product links
        category_groups = {}
        for product in products:
            if product.category:
                if product.category not in category_groups:
                    category_groups[product.category] = []
                category_groups[product.category].append(product)
        
        # Generate related product links within categories
        for category, category_products in category_groups.items():
            if len(category_products) < 2:
                continue
                
            for product in category_products:
                for related_product in category_products:
                    if product.id == related_product.id:
                        continue
                    
                    # Calculate relevance based on various factors
                    relevance_score = self._calculate_product_relevance(
                        product, related_product
                    )
                    
                    if relevance_score > 0.3:  # Minimum relevance threshold
                        suggestions.append(
                            InternalLinkSuggestion(
                                source_id=str(product.id),
                                source_type="product",
                                target_id=str(related_product.id),
                                target_type="product",
                                anchor_text=f"Similar: {related_product.title}",
                                relevance_score=relevance_score,
                                link_type="related"
                            )
                        )
        
        # Generate upsell links (higher priced products in same category)
        for category, category_products in category_groups.items():
            sorted_products = sorted(
                category_products,
                key=lambda p: float(p.price) if p.price else 0
            )
            
            for i, product in enumerate(sorted_products[:-1]):
                # Link to next higher-priced product
                target_product = sorted_products[i + 1]
                
                suggestions.append(
                    InternalLinkSuggestion(
                        source_id=str(product.id),
                        source_type="product",
                        target_id=str(target_product.id),
                        target_type="product",
                        anchor_text=f"Upgrade to {target_product.title}",
                        relevance_score=0.7,
                        link_type="upsell"
                    )
                )
        
        return suggestions
    
    def _calculate_product_relevance(
        self,
        product1: Product,
        product2: Product
    ) -> float:
        """Calculate relevance score between two products"""
        score = 0.0
        
        # Same category bonus
        if product1.category == product2.category:
            score += 0.4
        
        # Same brand bonus
        brand1 = getattr(product1, 'brand', None)
        brand2 = getattr(product2, 'brand', None)
        if brand1 and brand2 and brand1 == brand2:
            score += 0.3
        
        # Similar price range bonus
        if product1.price and product2.price:
            price1 = float(product1.price)
            price2 = float(product2.price)
            price_diff_ratio = abs(price1 - price2) / max(price1, price2)
            
            if price_diff_ratio < 0.2:  # Within 20% price range
                score += 0.2
            elif price_diff_ratio < 0.5:  # Within 50% price range
                score += 0.1
        
        # Title similarity bonus (basic keyword matching)
        if product1.title and product2.title:
            title1_words = set(product1.title.lower().split())
            title2_words = set(product2.title.lower().split())
            common_words = title1_words.intersection(title2_words)
            
            if len(common_words) > 0:
                similarity = len(common_words) / max(len(title1_words), len(title2_words))
                score += similarity * 0.3
        
        return min(score, 1.0)  # Cap at 1.0
    
    async def _generate_category_links(
        self,
        db: Session,
        store_id: str,
        products: List[Product]
    ) -> List[InternalLinkSuggestion]:
        """Generate category-to-product links"""
        suggestions = []
        
        # Get all categories
        categories = db.query(Product.category).filter(
            Product.store_id == store_id,
            Product.category.isnot(None)
        ).distinct().all()
        
        for category_row in categories:
            category = category_row[0]
            
            # Get top products in this category (by order count or rating)
            category_products = db.query(Product).filter(
                and_(
                    Product.store_id == store_id,
                    Product.category == category
                )
            ).order_by(
                Product.price.asc(),  # Use price as a fallback sort
                Product.created_at.desc()
            ).limit(5).all()
            
            for product in category_products:
                suggestions.append(
                    InternalLinkSuggestion(
                        source_id=category,
                        source_type="category",
                        target_id=str(product.id),
                        target_type="product",
                        anchor_text=f"Featured: {product.title}",
                        relevance_score=0.8,
                        link_type="category"
                    )
                )
        
        return suggestions
    
    async def update_collections_automatically(
        self,
        store_id: str
    ) -> Dict[str, int]:
        """
        Update existing automatic collections with new products.
        
        Args:
            store_id: Store identifier
            
        Returns:
            Dictionary with update statistics
        """
        logger.info(f"Updating automatic collections for store {store_id}")
        
        db = next(get_db())
        stats = {"updated": 0, "products_added": 0, "products_removed": 0}
        
        try:
            # Get all automatic collections for the store
            collections = db.query(Collection).filter(
                and_(
                    Collection.store_id == store_id,
                    Collection.is_auto_generated == True
                )
            ).all()
            
            for collection in collections:
                try:
                    # Get current products in collection
                    current_products = db.query(Product).join(
                        CollectionProduct,
                        Product.id == CollectionProduct.product_id
                    ).filter(
                        CollectionProduct.collection_id == collection.id
                    ).all()
                    
                    # Get all store products
                    all_products = get_products(db, store_id=store_id, limit=1000)
                    
                    # Apply collection rules to find matching products
                    collection_rule = CollectionRule(**collection.rules)
                    matching_products = self._filter_products_by_criteria(
                        all_products, collection_rule.criteria
                    )
                    
                    # Limit to max products
                    matching_products = matching_products[:collection_rule.max_products]
                    
                    # Find products to add and remove
                    current_product_ids = set([p.id for p in current_products])
                    matching_product_ids = set([p.id for p in matching_products])
                    
                    to_add = matching_product_ids - current_product_ids
                    to_remove = current_product_ids - matching_product_ids
                    
                    # Add new products
                    for product_id in to_add:
                        collection_product = CollectionProduct(
                            collection_id=collection.id,
                            product_id=product_id,
                            position=len(current_products)
                        )
                        db.add(collection_product)
                        stats["products_added"] += 1
                    
                    # Remove products that no longer match
                    if to_remove:
                        db.query(CollectionProduct).filter(
                            and_(
                                CollectionProduct.collection_id == collection.id,
                                CollectionProduct.product_id.in_(to_remove)
                            )
                        ).delete(synchronize_session=False)
                        stats["products_removed"] += len(to_remove)
                    
                    if to_add or to_remove:
                        collection.updated_at = datetime.utcnow()
                        stats["updated"] += 1
                    
                except Exception as e:
                    logger.error(f"Error updating collection {collection.id}: {str(e)}")
                    continue
            
            db.commit()
            return stats
            
        except Exception as e:
            logger.error(f"Error updating automatic collections: {str(e)}")
            db.rollback()
            raise
        finally:
            db.close()


# Convenience functions for easy import
async def create_automatic_collections(store_id: str, custom_rules: Optional[List[CollectionRule]] = None):
    """Create automatic collections for a store"""
    service = SmartCollectionsService()
    return await service.create_automatic_collections(store_id, custom_rules)


async def optimize_category_pages(store_id: str, categories: Optional[List[str]] = None):
    """Optimize category pages for a store"""
    service = SmartCollectionsService()
    return await service.optimize_category_pages(store_id, categories)


async def generate_internal_linking_strategy(store_id: str, max_suggestions: int = 50):
    """Generate internal linking strategy for a store"""
    service = SmartCollectionsService()
    return await service.generate_internal_linking_strategy(store_id, max_suggestions)
