import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from core.database import get_db_session
from crud.user import user_crud
from schemas.user import UserCreate
from core.security import create_access_token

async def create_test_user_and_get_token():
    db: AsyncSession = await anext(get_db_session())
    try:
        user_in = UserCreate(email="<EMAIL>", password="testpassword123")
        user = await user_crud.get_by_email(db, email=user_in.email)
        if not user:
            user = await user_crud.create(db, obj_in=user_in)
            print(f"User {user.email} created successfully.")
        else:
            print(f"User {user.email} already exists.")

        access_token = create_access_token(data={"sub": str(user.id)})
        print(f"Access token: {access_token}")

    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(create_test_user_and_get_token())
