class WebSocketManager {
    constructor(url) {
        this.url = url;
        this.socket = null;
        this.reconnectInterval = 1000; // start with 1 second
        this.maxReconnectInterval = 30000; // max 30 seconds
        this.listeners = new Map();
        this.subscriptions = new Map(); // For subscribe/unsubscribe functionality
    }

    connect() {
        this.socket = new WebSocket(this.url);

        this.socket.onopen = () => {
            console.log("WebSocket connected");
            this.reconnectInterval = 1000; // reset reconnect interval on successful connection
            this.emit("open");
        };

        this.socket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.emit(message.event, message.data);
        };

        this.socket.onclose = () => {
            console.log("WebSocket disconnected. Attempting to reconnect...");
            this.emit("close");
            setTimeout(() => this.connect(), this.reconnectInterval);
            this.reconnectInterval = Math.min(this.reconnectInterval * 2, this.maxReconnectInterval);
        };

        this.socket.onerror = (error) => {
            console.error("WebSocket error:", error);
            this.emit("error", error);
            this.socket.close(); // this will trigger the onclose event and reconnection logic
        };
    }

    on(eventName, callback) {
        if (!this.listeners.has(eventName)) {
            this.listeners.set(eventName, []);
        }
        this.listeners.get(eventName).push(callback);
    }

    emit(eventName, data) {
        if (this.listeners.has(eventName)) {
            this.listeners.get(eventName).forEach(callback => callback(data));
        }
    }

    // Subscribe method for products.js and jobs.js compatibility
    subscribe(channel, callback) {
        if (!this.subscriptions.has(channel)) {
            this.subscriptions.set(channel, []);
        }
        this.subscriptions.get(channel).push(callback);
        this.on(channel, callback);
    }

    // Unsubscribe method for compatibility
    unsubscribe(channel, callback = null) {
        if (this.subscriptions.has(channel)) {
            if (callback) {
                const callbacks = this.subscriptions.get(channel);
                const index = callbacks.indexOf(callback);
                if (index > -1) {
                    callbacks.splice(index, 1);
                }
            } else {
                // Remove all callbacks for this channel
                this.subscriptions.delete(channel);
            }
        }
    }

    send(message) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(message));
        } else {
            console.error("WebSocket is not connected.");
        }
    }
}

// Create a global WebSocketClient instance for compatibility with products.js and jobs.js
// This will be initialized when the dashboard connects to WebSocket
window.WebSocketClient = null;