"""
Optimization job management endpoints.
"""
import uuid
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from core.database import get_db_session
from core.security import get_current_user
from models.user import User as UserModel
from models.optimization_job import OptimizationJob
from schemas.job import Optimization<PERSON>ob as OptimizationJobSchema, JobCancellationResponse
from crud import crud_job
import structlog

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=List[OptimizationJobSchema])
async def get_jobs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    store_id: uuid.UUID = Query(None),
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get optimization jobs for a specific store or all user's stores."""
    try:
        if store_id:
            # Verify user has access to this store
            from crud.store import store_crud
            store = await store_crud.get_by_owner_and_id(db, owner_id=current_user.id, store_id=store_id)
            if not store:
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access to store denied")
            
            # Get jobs for specific store
            jobs = await crud_job.get_jobs_by_store(db, store_id=store_id, skip=skip, limit=limit)
        else:
            # Get jobs for all user's stores
            from crud.store import store_crud
            user_stores = await store_crud.get_user_stores(db, user_id=current_user.id)
            store_ids = [store.id for store in user_stores]
            if not store_ids:
                return []
            jobs = await crud_job.get_jobs_by_stores(db, store_ids=store_ids, skip=skip, limit=limit)
        return jobs
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error fetching jobs", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to fetch jobs")


@router.get("/{job_id}", response_model=OptimizationJobSchema)
async def get_job(
    job_id: uuid.UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get job by ID."""
    job = await crud_job.get_job(db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Job not found")
    return job


@router.post("/{job_id}/cancel", response_model=JobCancellationResponse)
async def cancel_job(
    job_id: uuid.UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Cancel a running job."""
    job = await crud_job.cancel_job(db, job_id=job_id)
    if not job:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Job not found or already finished")
    return {"job_id": job_id, "message": "Job cancelled successfully", "status": job.status}
