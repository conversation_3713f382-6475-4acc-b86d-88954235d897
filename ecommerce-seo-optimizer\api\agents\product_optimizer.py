"""
Product Optimizer Agent for ecommerce SEO optimization.

This agent specializes in optimizing product content including titles, descriptions,
meta descriptions, keywords, and other SEO elements.
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

# from mirascope import llm  # TODO: Fix Mirascope import
from pydantic import BaseModel, Field

from .base import BaseAgent, AgentConfig, OptimizationResult
from ..prompts.templates import PromptManager


class ProductData(BaseModel):
    """Product data structure for optimization."""
    
    product_id: Optional[str] = None
    name: str
    category: str
    description: Optional[str] = None
    features: Optional[List[str]] = None
    specifications: Optional[Dict[str, Any]] = None
    brand: Optional[str] = None
    price: Optional[float] = None
    price_range: Optional[str] = None
    target_keywords: Optional[List[str]] = None
    current_title: Optional[str] = None
    current_meta_description: Optional[str] = None
    images: Optional[List[str]] = None
    target_audience: Optional[str] = None


class OptimizedContent(BaseModel):
    """Structure for optimized product content."""
    
    title: Optional[str] = None
    meta_description: Optional[str] = None
    description: Optional[str] = None
    keywords: Optional[Dict[str, List[str]]] = None
    alt_texts: Optional[List[str]] = None
    schema_markup: Optional[Dict[str, Any]] = None
    seo_score: Optional[float] = None
    recommendations: Optional[List[str]] = None


from ..core.rate_limiter import AIRateLimiter

class ProductOptimizer(BaseAgent):
    """
    AI agent specialized in product SEO optimization.
    
    Capabilities:
    - Generate SEO-optimized product titles
    - Create compelling meta descriptions  
    - Generate comprehensive product descriptions
    - Research and suggest relevant keywords
    - Create alt text for product images
    - Generate schema markup for rich snippets
    - Provide SEO recommendations and scoring
    """
    
    def __init__(self, config: Optional[AgentConfig] = None):
        """Initialize the Product Optimizer agent."""
        super().__init__(config)
        self.prompt_manager = PromptManager()
        self.rate_limiter = AIRateLimiter()
        self.logger.info("ProductOptimizer agent initialized")
    
    async def _process_non_streaming(self, input_data: Dict[str, Any], **kwargs) -> OptimizationResult:
        """
        Process product data and return optimized content.
        
        Args:
            input_data: Product data dictionary
            **kwargs: Additional parameters (optimization_type, complexity, etc.)
            
        Returns:
            OptimizationResult with optimized content
        """
        stream = kwargs.get('stream', False)
        if stream:
            return self._process_streaming(input_data, **kwargs)
        else:
            return await self._process_non_streaming(input_data, **kwargs)

    async def process(self, input_data: Dict[str, Any], **kwargs) -> Union[OptimizationResult, AsyncGenerator[Dict[str, Any], None]]:
        """
        Process product data and return optimized content.
        
        Args:
            input_data: Product data dictionary
            **kwargs: Additional parameters (optimization_type, complexity, etc.)
            
        Returns:
            OptimizationResult with optimized content
        """
        stream = kwargs.get('stream', False)
        if stream:
            return self._process_streaming(input_data, **kwargs)
        else:
            return await self._process_non_streaming(input_data, **kwargs)

    async def _process_non_streaming(self, input_data: Dict[str, Any], **kwargs) -> OptimizationResult:
        try:
            # Validate and parse input data
            product_data = ProductData(**input_data)
            optimization_type = kwargs.get('optimization_type', 'complete')
            complexity = kwargs.get('complexity', 'standard')
            
            self.state.current_task = f"Optimizing product: {product_data.name}"
            self.state.update_progress(0.0)
            
            # Check cache first
            cache_key = self.cache_key("product_optimization", {
                "name": product_data.name,
                "category": product_data.category,
                "optimization_type": optimization_type
            })
            
            cached_result = await self.get_cached_result(cache_key)
            if cached_result:
                self.logger.info(f"Using cached result for product: {product_data.name}")
                return OptimizationResult(
                    success=True,
                    content=cached_result,
                    model_used="cached",
                    processing_time=0.0
                )
            
            # Perform optimization based on type
            start_time = time.time()
            
            if optimization_type == 'complete':
                result = await self._optimize_complete(product_data, complexity)
            elif optimization_type == 'title_only':
                result = await self._optimize_title(product_data, complexity)
            elif optimization_type == 'description_only':
                result = await self._optimize_description(product_data, complexity)
            elif optimization_type == 'keywords_only':
                result = await self._generate_keywords(product_data, complexity)
            else:
                raise ValueError(f"Unknown optimization type: {optimization_type}")
            
            processing_time = time.time() - start_time
            
            # Cache the result
            await self.cache_result(cache_key, result.model_dump(), ttl=3600)
            
            self.state.update_progress(100.0)
            
            return OptimizationResult(
                success=True,
                content=result.model_dump(),
                model_used=self.select_model("product_optimization", complexity),
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Product optimization failed: {e}")
            self.state.increment_error()
            
            return OptimizationResult(
                success=False,
                error_message=str(e)
            )

    async def _process_streaming(self, input_data: Dict[str, Any], **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """Process product data and stream updates."""
        try:
            product_data = ProductData(**input_data)
            optimization_type = kwargs.get('optimization_type', 'complete')
            complexity = kwargs.get('complexity', 'standard')

            yield {"status": "starting", "progress": 0, "message": f"Starting {optimization_type} for {product_data.name}"}

            if optimization_type == 'complete':
                result = await self._optimize_complete(product_data, complexity)
            elif optimization_type == 'title_only':
                result = await self._optimize_title(product_data, complexity)
            elif optimization_type == 'description_only':
                result = await self._optimize_description(product_data, complexity)
            elif optimization_type == 'keywords_only':
                result = await self._generate_keywords(product_data, complexity)
            else:
                raise ValueError(f"Unknown optimization type: {optimization_type}")

            yield {"status": "completed", "progress": 100, "result": result.model_dump()}

        except Exception as e:
            yield {"status": "error", "message": str(e)}
    
    async def _optimize_complete(self, product_data: ProductData, complexity: str) -> OptimizedContent:
        """Perform complete product optimization."""
        self.logger.info(f"Starting complete optimization for: {product_data.name}")
        
        # Run optimizations in parallel for efficiency
        tasks = [
            self._generate_title(product_data, complexity),
            self._generate_meta_description(product_data, complexity),
            self._generate_description(product_data, complexity),
            self._generate_keywords(product_data, complexity)
        ]
        
        # Update progress incrementally
        self.state.update_progress(20.0)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        self.state.update_progress(80.0)
        
        # Process results and handle any exceptions
        title = results[0] if not isinstance(results[0], Exception) else None
        meta_description = results[1] if not isinstance(results[1], Exception) else None
        description = results[2] if not isinstance(results[2], Exception) else None
        keywords = results[3] if not isinstance(results[3], Exception) else None
        
        # Generate additional content if images are provided
        alt_texts = None
        if product_data.images:
            alt_texts = await self._generate_alt_texts(product_data, complexity)
        
        # Generate schema markup
        schema_markup = await self._generate_schema_markup(product_data, title, description)
        
        self.state.update_progress(95.0)
        
        # Calculate SEO score
        seo_score = self._calculate_seo_score(title, meta_description, description, keywords)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(product_data, title, meta_description, description)
        
        return OptimizedContent(
            title=title,
            meta_description=meta_description,
            description=description,
            keywords=keywords,
            alt_texts=alt_texts,
            schema_markup=schema_markup,
            seo_score=seo_score,
            recommendations=recommendations
        )
    
    async def _optimize_title(self, product_data: ProductData, complexity: str) -> OptimizedContent:
        """Optimize only the product title."""
        title = await self._generate_title(product_data, complexity)
        return OptimizedContent(title=title)
    
    async def _optimize_description(self, product_data: ProductData, complexity: str) -> OptimizedContent:
        """Optimize only the product description."""
        description = await self._generate_description(product_data, complexity)
        return OptimizedContent(description=description)
    
    @llm.call(provider="openai")
    async def _generate_title(self, product_data: ProductData, complexity: str) -> str:
        """Generate SEO-optimized product title."""
        model = self.select_model("title", complexity)
        
        # Configure the call with OpenRouter settings
        self.__class__._generate_title.model = model
        self.__class__._generate_title.api_key = self.config.api_key
        self.__class__._generate_title.base_url = self.config.base_url
        
        prompt = self.prompt_manager.format_template(
            "seo",
            "product_title_optimization",
            product_name=product_data.name,
            category=product_data.category,
            features=product_data.features or [],
            brand=product_data.brand,
            target_keywords=product_data.target_keywords or [],
            price_range=product_data.price_range
        )
        
        async with self.rate_limiter.rate_limited_call(identifier=product_data.store_id, tokens=len(prompt.split()), cost=0.0001, model=model):
            return prompt
    
    @llm.call(provider="openai")
    async def _generate_meta_description(self, product_data: ProductData, complexity: str) -> str:
        """Generate SEO-optimized meta description."""
        model = self.select_model("meta_description", complexity)
        
        # Configure the call
        self.__class__._generate_meta_description.model = model
        self.__class__._generate_meta_description.api_key = self.config.api_key
        self.__class__._generate_meta_description.base_url = self.config.base_url
        
        # First generate title if not provided
        title = product_data.current_title
        if not title:
            title = await self._generate_title(product_data, complexity)
        
        prompt = self.prompt_manager.format_template(
            "seo",
            "meta_description_optimization",
            product_name=product_data.name,
            category=product_data.category,
            title=title,
            features=product_data.features or [],
            brand=product_data.brand
        )
        
        async with self.rate_limiter.rate_limited_call(identifier=product_data.store_id, tokens=len(prompt.split()), cost=0.0001, model=model):
            return prompt
    
    @llm.call(provider="openai")
    async def _generate_description(self, product_data: ProductData, complexity: str) -> str:
        """Generate comprehensive product description."""
        model = self.select_model("product_description", complexity)
        
        # Configure the call
        self.__class__._generate_description.model = model
        self.__class__._generate_description.api_key = self.config.api_key
        self.__class__._generate_description.base_url = self.config.base_url
        
        prompt = self.prompt_manager.format_template(
            "seo",
            "product_description_optimization",
            product_name=product_data.name,
            category=product_data.category,
            features=product_data.features or [],
            specifications=product_data.specifications or {},
            brand=product_data.brand,
            target_audience=product_data.target_audience
        )
        
        async with self.rate_limiter.rate_limited_call(identifier=product_data.store_id, tokens=len(prompt.split()), cost=0.0005, model=model):
            return prompt
    
    @llm.call(provider="openai")
    async def _generate_keywords(self, product_data: ProductData, complexity: str) -> Dict[str, List[str]]:
        """Generate relevant keywords for the product."""
        model = self.select_model("keywords", complexity)
        
        # Configure the call
        self.__class__._generate_keywords.model = model
        self.__class__._generate_keywords.api_key = self.config.api_key
        self.__class__._generate_keywords.base_url = self.config.base_url
        
        prompt = self.prompt_manager.format_template(
            "seo",
            "keyword_generation",
            product_name=product_data.name,
            category=product_data.category,
            features=product_data.features or [],
            target_audience=product_data.target_audience
        )
        
        async with self.rate_limiter.rate_limited_call(identifier=product_data.store_id, tokens=len(prompt.split()), cost=0.0002, model=model):
            # The prompt should return JSON, so we need to parse it
            result = await self.call_with_retry(lambda: prompt)
        
        try:
            return json.loads(result)
        except json.JSONDecodeError:
            # Fallback if JSON parsing fails
            return {
                "primary_keywords": [product_data.name.lower()],
                "long_tail_keywords": [f"{product_data.name.lower()} {product_data.category.lower()}"],
                "buyer_intent_keywords": [f"buy {product_data.name.lower()}"],
                "local_keywords": [],
                "competitor_keywords": []
            }
    
    async def _generate_alt_texts(self, product_data: ProductData, complexity: str) -> List[str]:
        """Generate alt text for product images."""
        if not product_data.images:
            return []
        
        alt_texts = []
        for i, image_url in enumerate(product_data.images):
            # Generate descriptive alt text for each image
            image_description = f"Product image {i+1} of {product_data.name}"
            
            prompt = self.prompt_manager.format_template(
                "product",
                "alt_text_generation",
                product_name=product_data.name,
                image_description=image_description,
                category=product_data.category,
                key_features=product_data.features or []
            )
            
            alt_text = await self.call_with_retry(
                lambda: self._call_llm_for_alt_text(prompt, complexity)
            )
            alt_texts.append(alt_text)
        
        return alt_texts
    
    async def _call_llm_for_alt_text(self, prompt: str, complexity: str, store_id: str) -> str:
        """Helper method to call LLM for alt text generation."""
        model = self.select_model("alt_text", complexity)
        
        @llm.call(
            provider="openai",
            model=model,
            api_key=self.config.api_key,
            base_url=self.config.base_url
        )
        def generate_alt_text(text: str) -> str:
            return text
        
        async with self.rate_limiter.rate_limited_call(identifier=store_id, tokens=len(prompt.split()), cost=0.0001, model=model):
            return await generate_alt_text(prompt)
    
    async def _generate_schema_markup(
        self,
        product_data: ProductData,
        title: Optional[str],
        description: Optional[str]
    ) -> Dict[str, Any]:
        """Generate JSON-LD schema markup for the product."""
        
        schema = {
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": title or product_data.name,
            "description": description or product_data.description,
            "category": product_data.category
        }
        
        if product_data.brand:
            schema["brand"] = {
                "@type": "Brand",
                "name": product_data.brand
            }
        
        if product_data.price:
            schema["offers"] = {
                "@type": "Offer",
                "price": str(product_data.price),
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
            }
        
        if product_data.images:
            schema["image"] = product_data.images
        
        return schema
    
    def _calculate_seo_score(
        self,
        title: Optional[str],
        meta_description: Optional[str], 
        description: Optional[str],
        keywords: Optional[Dict[str, List[str]]]
    ) -> float:
        """Calculate a basic SEO score for the optimized content."""
        score = 0.0
        max_score = 100.0
        
        # Title scoring (25 points)
        if title:
            if len(title) <= 60:
                score += 15
            if len(title) >= 30:
                score += 10
        
        # Meta description scoring (25 points)
        if meta_description:
            if len(meta_description) <= 155:
                score += 15
            if len(meta_description) >= 120:
                score += 10
        
        # Description scoring (25 points)
        if description:
            word_count = len(description.split())
            if 300 <= word_count <= 500:
                score += 25
            elif 200 <= word_count < 300 or 500 < word_count <= 600:
                score += 15
            else:
                score += 5
        
        # Keywords scoring (25 points)
        if keywords:
            total_keywords = sum(len(kw_list) for kw_list in keywords.values())
            if total_keywords >= 15:
                score += 25
            elif total_keywords >= 10:
                score += 20
            elif total_keywords >= 5:
                score += 15
            else:
                score += 10
        
        return min(score, max_score)
    
    def _generate_recommendations(
        self,
        product_data: ProductData,
        title: Optional[str],
        meta_description: Optional[str],
        description: Optional[str]
    ) -> List[str]:
        """Generate SEO recommendations based on the optimized content."""
        recommendations = []
        
        # Title recommendations
        if title and len(title) > 60:
            recommendations.append("Consider shortening the title to under 60 characters for better search display")
        
        # Meta description recommendations  
        if meta_description and len(meta_description) > 155:
            recommendations.append("Shorten meta description to under 155 characters for full display in search results")
        
        # Description recommendations
        if description:
            word_count = len(description.split())
            if word_count < 300:
                recommendations.append("Consider expanding the product description to 300-500 words for better SEO")
            elif word_count > 500:
                recommendations.append("Consider condensing the description to 300-500 words for optimal readability")
        
        # General recommendations
        if not product_data.features:
            recommendations.append("Add product features to improve content relevance and SEO")
        
        if not product_data.specifications:
            recommendations.append("Include technical specifications to capture more search queries")
        
        if not product_data.target_keywords:
            recommendations.append("Research and define target keywords for better optimization")
        
        return recommendations
    
    async def optimize_batch(
        self,
        products: List[Dict[str, Any]],
        optimization_type: str = "complete",
        complexity: str = "standard"
    ) -> List[OptimizationResult]:
        """
        Optimize multiple products in batch.
        
        Args:
            products: List of product data dictionaries
            optimization_type: Type of optimization to perform
            complexity: Complexity level for AI models
            
        Returns:
            List of OptimizationResult objects
        """
        self.logger.info(f"Starting batch optimization for {len(products)} products")
        
        results = []
        total_products = len(products)
        
        for i, product_data in enumerate(products):
            try:
                # Update progress
                progress = (i / total_products) * 100
                self.state.update_progress(progress)
                
                # Optimize individual product
                result = await self.process(
                    product_data,
                    optimization_type=optimization_type,
                    complexity=complexity
                )
                results.append(result)
                
                # Small delay to avoid overwhelming the API
                await asyncio.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Failed to optimize product {i}: {e}")
                results.append(OptimizationResult(
                    success=False,
                    error_message=str(e)
                ))
        
        self.state.update_progress(100.0)
        self.logger.info(f"Completed batch optimization: {len(results)} products processed")
        
        return results
