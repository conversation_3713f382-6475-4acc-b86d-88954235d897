/**
 * GridSpoke SEO Optimizer - Authentication Module
 * Handles user authentication, JWT token management, and auth state
 */

const Auth = {
    // Configuration
    config: {
        apiBaseUrl: '/api/v1',
        tokenStorageKey: 'gridspoke_auth_token',
        userStorageKey: 'gridspoke_user_data',
        refreshTokenKey: 'gridspoke_refresh_token',
        rememberMe<PERSON>ey: 'gridspoke_remember_me'
    },

    // Current authentication state
    state: {
        isAuthenticated: false,
        user: null,
        token: null,
        refreshToken: null
    },

    /**\n     * Validate JWT token\n     * @param {string} token - JWT token\n     * @returns {boolean} Token validity\n     */\n    isTokenValid(token) {\n        if (!token) return false;\n\n        try {
            // Remove extra quotes if present (from JSON.stringify storage)
            const cleanToken = typeof token === 'string' ? token.replace(/^"|"$/g, '') : token;
            
            // Basic format check
            const parts = cleanToken.split('.');
            if (parts.length !== 3) return false;
            
            // Check if payload is valid JSON
            const payload = JSON.parse(atob(parts[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            
            // Check if token is expired (with 5 minute buffer)
            return payload.exp > (currentTime + 300);
        } catch (error) {
            console.error('Error validating token:', error);
            return false;
        }
    },

    /**
     * Initialize authentication module
     */
    init() {
        return new Promise((resolve) => {
            this.loadStoredAuth();
            this.setupTokenRefresh();
            resolve();
        });
    },

    /**\n     * Load stored authentication data\n     */\n    loadStoredAuth() {\n        try {\n            const token = localStorage.getItem(this.config.tokenStorageKey);\n            const user = JSON.parse(localStorage.getItem(this.config.userStorageKey));\n            const refreshToken = localStorage.getItem(this.config.refreshTokenKey);\n\n            console.log('Loaded stored auth:', { token, user, refreshToken });\n\n            if (token && user) {\n                // Clean tokens (remove quotes if present)
                const cleanToken = typeof token === 'string' ? token.replace(/^"|"$/g, '') : token;
                const cleanRefreshToken = typeof refreshToken === 'string' ? 
                    refreshToken.replace(/^"|"$/g, '') : refreshToken;\n                \n                // Verify token is not expired\n                if (this.isTokenValid(cleanToken)) {\n                    this.state.isAuthenticated = true;\n                    this.state.user = user;\n                    this.state.token = cleanToken;\n                    this.state.refreshToken = cleanRefreshToken;\n                    \n                    // Set default authorization header\n                    this.setAuthHeader(cleanToken);\n                } else {\n                    // Token expired, try to refresh\n                    if (cleanRefreshToken) {\n                        this.refreshAccessToken();\n                    } else {\n                        this.clearAuth();\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Error loading stored auth:', error);\n            this.clearAuth();\n        }\n    },

    /**
     * Login user with email and password
     * @param {string} email - User email
     * @param {string} password - User password
     * @param {boolean} rememberMe - Whether to remember the user
     * @returns {Promise} Login promise
     */
    async login(email, password, rememberMe = false) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email,
                    password
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.detail || 'Login failed');
            }

            // Clean tokens (remove quotes if present)
            const cleanAccessToken = typeof data.access_token === 'string' ? 
                data.access_token.replace(/^"|"$/g, '') : data.access_token;
            const cleanRefreshToken = typeof data.refresh_token === 'string' ? 
                data.refresh_token.replace(/^"|"$/g, '') : data.refresh_token;

            // Store authentication data
            this.state.isAuthenticated = true;
            this.state.user = data.user;
            this.state.token = cleanAccessToken;
            this.state.refreshToken = cleanRefreshToken;

            // Store in localStorage
            const storageType = rememberMe ? 'permanent' : 'session';
            this.storeAuth({
                access_token: cleanAccessToken,
                refresh_token: cleanRefreshToken,
                user: data.user
            }, storageType);

            // Set authorization header
            this.setAuthHeader(cleanAccessToken);

            // Store remember me preference
            Utils.storage.set(this.config.rememberMeKey, rememberMe);

            return data;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    },

    /**
     * Logout user
     */
    async logout() {
        try {
            // Call logout endpoint if token exists
            if (this.state.token) {
                await fetch(`${this.config.apiBaseUrl}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.state.token}`,
                        'Content-Type': 'application/json'
                    }
                });
            }
        } catch (error) {
            console.error('Logout API error:', error);
            // Continue with local logout even if API call fails
        } finally {
            this.clearAuth();
            window.location.href = 'login.html';
        }
    },

    /**
     * Register new user
     * @param {Object} userData - User registration data
     * @returns {Promise} Registration promise
     */
    async register(userData) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData)
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.detail || 'Registration failed');
            }

            return data;
        } catch (error) {
            console.error('Registration error:', error);
            throw error;
        }
    },

    /**
     * Refresh access token
     * @returns {Promise} Refresh promise
     */
    async refreshAccessToken() {
        try {
            if (!this.state.refreshToken) {
                throw new Error('No refresh token available');
            }

            // Clean the refresh token (remove quotes if present)
            const cleanRefreshToken = typeof this.state.refreshToken === 'string' ? 
                this.state.refreshToken.replace(/^"|"$/g, '') : this.state.refreshToken;

            const response = await fetch(`${this.config.apiBaseUrl}/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    refresh_token: cleanRefreshToken
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.detail || 'Token refresh failed');
            }

            // Update stored tokens
            this.state.token = data.access_token;
            if (data.refresh_token) {
                this.state.refreshToken = data.refresh_token;
            }

            // Update stored auth
            Utils.storage.set(this.config.tokenStorageKey, data.access_token);
            if (data.refresh_token) {
                Utils.storage.set(this.config.refreshTokenKey, data.refresh_token);
            }

            // Set authorization header
            this.setAuthHeader(data.access_token);

            return data;
        } catch (error) {
            console.error('Token refresh error:', error);
            this.clearAuth();
            throw error;
        }
    },

    /**
     * Request password reset
     * @param {string} email - User email
     * @returns {Promise} Reset request promise
     */
    async requestPasswordReset(email) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/auth/password-reset`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.detail || 'Password reset request failed');
            }

            return data;
        } catch (error) {
            console.error('Password reset request error:', error);
            throw error;
        }
    },

    /**
     * Reset password with token
     * @param {string} token - Reset token
     * @param {string} newPassword - New password
     * @returns {Promise} Reset promise
     */
    async resetPassword(token, newPassword) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/auth/password-reset/confirm`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token,
                    new_password: newPassword
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.detail || 'Password reset failed');
            }

            return data;
        } catch (error) {
            console.error('Password reset error:', error);
            throw error;
        }
    },

    /**
     * Store authentication data
     * @param {Object} authData - Authentication data
     * @param {string} type - Storage type ('permanent' or 'session')
     */
    storeAuth(authData, type = 'session') {
        // Clean tokens (remove quotes if present)
        const cleanAccessToken = typeof authData.access_token === 'string' ? 
            authData.access_token.replace(/^"|"$/g, '') : authData.access_token;
        const cleanRefreshToken = typeof authData.refresh_token === 'string' ? 
            authData.refresh_token.replace(/^"|"$/g, '') : authData.refresh_token;
        
        Utils.storage.set(this.config.tokenStorageKey, cleanAccessToken);
        Utils.storage.set(this.config.userStorageKey, authData.user);
        
        if (cleanRefreshToken) {
            Utils.storage.set(this.config.refreshTokenKey, cleanRefreshToken);
        }
    },

    /**
     * Clear authentication data
     */
    clearAuth() {
        this.state.isAuthenticated = false;
        this.state.user = null;
        this.state.token = null;
        this.state.refreshToken = null;

        Utils.storage.remove(this.config.tokenStorageKey);
        Utils.storage.remove(this.config.userStorageKey);
        Utils.storage.remove(this.config.refreshTokenKey);
        Utils.storage.remove(this.config.rememberMeKey);

        // Clear authorization header
        this.clearAuthHeader();
    },

    setAuthHeader(token) {
        // Remove extra quotes if present (from JSON.stringify storage)
        const cleanToken = typeof token === 'string' ? token.replace(/^"|"$/g, '') : token;
        // This will be used by the API module
        window.authToken = cleanToken;
        console.log('Auth token set:', cleanToken ? 'Present' : 'None');
    },

    /**
     * Clear authorization header
     */
    clearAuthHeader() {
        delete window.authToken;
    },

    /**
     * Check if user is authenticated
     * @returns {boolean} Authentication status
     */
    isAuthenticated() {
        console.log('isAuthenticated check:', {
            isAuthenticated: this.state.isAuthenticated,
            token: this.state.token,
            isTokenValid: this.isTokenValid(this.state.token)
        });
        return this.state.isAuthenticated && this.state.token && this.isTokenValid(this.state.token);
    },

    /**
     * Get current user data
     * @returns {Object|null} User data
     */
    getUser() {
        return this.state.user;
    },

    /**
     * Get current token
     * @returns {string|null} JWT token
     */
    getToken() {
        if (!this.state.token) return null;
        // Remove extra quotes that may be added by JSON storage
        const cleanToken = typeof this.state.token === 'string' ? 
            this.state.token.replace(/^"|"$/g, '') : this.state.token;
        return cleanToken;
    },

    /**
     * Setup automatic token refresh
     */
    setupTokenRefresh() {
        // Refresh token 5 minutes before expiration
        setInterval(() => {
            if (this.state.token && this.state.refreshToken) {
                try {
                    // Clean token
                    // Clean token
            const cleanToken = typeof this.state.token === 'string' ? 
                this.state.token.replace(/^"|"$/g, '') : this.state.token;
                    
                    const payload = JSON.parse(atob(cleanToken.split('.')[1]));
                    const currentTime = Math.floor(Date.now() / 1000);
                    const timeUntilExpiry = payload.exp - currentTime;
                    
                    // Refresh if token expires in less than 5 minutes
                    if (timeUntilExpiry < 300) {
                        this.refreshAccessToken().catch(() => {
                            // If refresh fails, clear auth
                            this.clearAuth();
                        });
                    }
                } catch (error) {
                    console.error('Error checking token expiry:', error);
                }
            }
        }, 60000); // Check every minute
    },

    /**
     * Check authentication and redirect if needed
     * @param {string} redirectUrl - URL to redirect to if not authenticated
     */
    requireAuth(redirectUrl = 'login.html') {
        if (!this.isAuthenticated()) {
            window.location.href = redirectUrl;
            return false;
        }
        return true;
    },

    /**
     * Update user profile
     * @param {Object} userData - Updated user data
     * @returns {Promise} Update promise
     */
    async updateProfile(userData) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/auth/profile`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${this.state.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.detail || 'Profile update failed');
            }

            // Update stored user data
            this.state.user = { ...this.state.user, ...data };
            Utils.storage.set(this.config.userStorageKey, this.state.user);

            return data;
        } catch (error) {
            console.error('Profile update error:', error);
            throw error;
        }
    },

    /**
     * Change password
     * @param {string} currentPassword - Current password
     * @param {string} newPassword - New password
     * @returns {Promise} Change password promise
     */
    async changePassword(currentPassword, newPassword) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/auth/change-password`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.state.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    current_password: currentPassword,
                    new_password: newPassword
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.detail || 'Password change failed');
            }

            return data;
        } catch (error) {
            console.error('Password change error:', error);
            throw error;
        }
    }
};

// Initialize authentication when module loads
if (typeof document !== 'undefined') {
    // Use a small delay to ensure other modules are loaded
    setTimeout(() => {
        Auth.init().catch(error => {
            console.error('Auth initialization error:', error);
        });
    }, 10);
}

// Export for module use (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Auth;
}
