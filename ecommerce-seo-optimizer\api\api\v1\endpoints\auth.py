"""
Authentication endpoints.
"""
from datetime import <PERSON><PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi import Body
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import get_settings
from core.database import get_db_session
from core.security import authenticate_user, create_token_pair, get_current_user
from crud.user import user_crud
from schemas.auth import Token, TokenRefresh
from schemas.user import User, UserCreate
from models.user import User as UserModel
import structlog

logger = structlog.get_logger(__name__)
router = APIRouter()
settings = get_settings()


@router.post("/register", response_model=User, status_code=status.HTTP_201_CREATED)
async def register(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_db_session)
) -> Any:
    """Register a new user."""
    # Check if user already exists
    existing_user = await user_crud.get_by_email(db, email=user_in.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create new user
    user = await user_crud.create(db, obj_in=user_in)
    
    logger.info(
        "User registered",
        user_id=str(user.id),
        email=user.email
    )
    
    return user


@router.post("/login", response_model=Token)
async def login(
    email: str = Body(..., embed=True),
    password: str = Body(..., embed=True),
    db: AsyncSession = Depends(get_db_session)
) -> Any:
    """Login user and return JWT tokens. Accepts JSON body with email & password."""
    user = await authenticate_user(db, email, password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled"
        )
    
    # Create access and refresh tokens
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    token_pair = create_token_pair(user_id=user.id)
    response_payload = {
        **token_pair,
        "user": User.model_validate(user)  # pydantic v2 safe conversion
    }

    logger.info(
        "User logged in",
        user_id=str(user.id),
        email=user.email
    )
    
    return response_payload


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token_data: TokenRefresh,
    db: AsyncSession = Depends(get_db_session)
) -> Any:
    """Refresh access token using refresh token."""
    from core.security import verify_refresh_token
    
    try:
        payload = await verify_refresh_token(token_data.refresh_token)
        if payload is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
            
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # Get user to verify they still exist and are active
        user = await user_crud.get(db, id=user_id)
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        # Create new access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        new_tokens = create_token_pair(user_id=user.id)
        
        logger.info(
            "Token refreshed",
            user_id=str(user.id)
        )
        
        return new_tokens
        
    except Exception as e:
        logger.warning(
            "Token refresh failed",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )


@router.post("/logout")
async def logout(
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Logout user (client should discard tokens)."""
    logger.info(
        "User logged out",
        user_id=str(current_user.id)
    )
    
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=User)
async def get_current_user_info(
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get current user information."""
    return current_user
