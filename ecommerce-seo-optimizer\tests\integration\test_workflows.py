# GridSpoke Integration Tests - Workflows
"""
Integration tests for GridSpoke workflows including API + Database,
Celery tasks, AI agents, WebSocket connections, and external services.
"""

import pytest
import asyncio
import json
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime, timedelta


class TestDatabaseIntegration:
    """Test database integration with API endpoints"""
    
    def test_store_crud_workflow(self, test_client, auth_headers, create_test_store, db_session):
        """Test complete store CRUD workflow with database"""
        # Create store
        store_data = {
            "name": "Integration Test Store",
            "url": "https://integration-test.com",
            "platform": "woocommerce",
            "api_key": "test_api_key_integration"
        }
        
        with patch('api.crud.store.create_store') as mock_create:
            mock_store = create_test_store(**store_data)
            mock_create.return_value = mock_store
            
            # Test create
            response = test_client.post(
                "/api/v1/stores",
                json=store_data,
                headers=auth_headers
            )
            assert response.status_code == 201
            
            # Test read
            store_id = response.json()["id"]
            response = test_client.get(
                f"/api/v1/stores/{store_id}",
                headers=auth_headers
            )
            assert response.status_code == 200
            assert response.json()["name"] == store_data["name"]
            
            # Test update
            update_data = {"name": "Updated Store Name"}
            response = test_client.put(
                f"/api/v1/stores/{store_id}",
                json=update_data,
                headers=auth_headers
            )
            assert response.status_code == 200
            
            # Test delete
            response = test_client.delete(
                f"/api/v1/stores/{store_id}",
                headers=auth_headers
            )
            assert response.status_code == 204
    
    def test_product_optimization_workflow(self, test_client, auth_headers, create_test_product, create_test_store, db_session):
        """Test product optimization with database persistence"""
        # Setup
        store = create_test_store(name="Test Store")
        product = create_test_product(
            name="Test Product",
            store_id=store.id,
            description="Basic product description"
        )
        
        optimization_data = {
            "product_id": product.id,
            "optimization_type": "full"
        }
        
        with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
            mock_optimize.return_value = {
                "title": "Optimized Test Product - Premium Quality",
                "description": "Enhanced product description with SEO optimization...",
                "meta_title": "Optimized Test Product | Premium Quality",
                "meta_description": "Enhanced product with premium features...",
                "seo_score": 87.5,
                "optimization_id": "opt_12345"
            }
            
            # Test optimization
            response = test_client.post(
                "/api/v1/optimize/product",
                json=optimization_data,
                headers=auth_headers
            )
            
            assert response.status_code == 200
            result = response.json()
            assert "optimization_id" in result
            assert result["seo_score"] > 80
            
            # Verify database was updated
            updated_product = db_session.get(type(product), product.id)
            if updated_product:
                assert "Optimized" in updated_product.name or "optimization_id" in result
    
    def test_optimization_job_lifecycle(self, test_client, auth_headers, create_test_store, db_session):
        """Test complete optimization job lifecycle with database tracking"""
        store = create_test_store(name="Job Test Store")
        
        job_data = {
            "store_id": str(store.id),
            "product_ids": ["prod_1", "prod_2", "prod_3"],
            "optimization_type": "full"
        }
        
        with patch('workers.tasks.product_tasks.optimize_products_bulk.delay') as mock_task:
            with patch('api.crud.optimization_job.create_job') as mock_create_job:
                # Mock job creation
                mock_job = {
                    "id": "job_integration_123",
                    "status": "queued",
                    "total_items": 3,
                    "processed_items": 0,
                    "created_at": datetime.utcnow().isoformat()
                }
                mock_create_job.return_value = mock_job
                mock_task.return_value = MagicMock(id="celery_task_456")
                
                # Create job
                response = test_client.post(
                    "/api/v1/optimize/bulk",
                    json=job_data,
                    headers=auth_headers
                )
                assert response.status_code == 202
                job_id = response.json()["job_id"]
                
                # Check initial status
                with patch('api.crud.optimization_job.get_job') as mock_get_job:
                    mock_get_job.return_value = mock_job
                    response = test_client.get(
                        f"/api/v1/jobs/{job_id}",
                        headers=auth_headers
                    )
                    assert response.status_code == 200
                    assert response.json()["status"] == "queued"
                
                # Simulate job progress
                with patch('api.crud.optimization_job.get_job') as mock_get_job:
                    mock_job_progress = {**mock_job, "status": "processing", "processed_items": 2}
                    mock_get_job.return_value = mock_job_progress
                    response = test_client.get(
                        f"/api/v1/jobs/{job_id}",
                        headers=auth_headers
                    )
                    assert response.json()["processed_items"] == 2
                
                # Simulate job completion
                with patch('api.crud.optimization_job.get_job') as mock_get_job:
                    mock_job_complete = {**mock_job, "status": "completed", "processed_items": 3}
                    mock_get_job.return_value = mock_job_complete
                    response = test_client.get(
                        f"/api/v1/jobs/{job_id}",
                        headers=auth_headers
                    )
                    assert response.json()["status"] == "completed"


class TestCeleryTaskIntegration:
    """Test Celery task integration with proper worker setup"""
    
    @pytest.mark.slow
    def test_single_product_optimization_task(self, celery_app, celery_worker, sample_product):
        """Test single product optimization Celery task"""
        try:
            from workers.tasks.product_tasks import optimize_single_product
            
            with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
                mock_optimize.return_value = {
                    "title": "Optimized Product Title",
                    "description": "Optimized product description",
                    "seo_score": 85.0
                }
                
                # Execute task
                result = optimize_single_product.delay(
                    product_id=sample_product["id"],
                    store_id="store_123",
                    optimization_type="full"
                )
                
                # Wait for completion (with timeout)
                task_result = result.get(timeout=10)
                
                assert task_result is not None
                assert "title" in task_result
                assert task_result["seo_score"] > 80
                mock_optimize.assert_called_once()
                
        except ImportError:
            pytest.skip("Celery tasks not available")
    
    @pytest.mark.slow
    def test_bulk_optimization_task_with_progress(self, celery_app, celery_worker):
        """Test bulk optimization task with progress tracking"""
        try:
            from workers.tasks.product_tasks import optimize_products_bulk
            
            product_ids = ["prod_1", "prod_2", "prod_3"]
            
            with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
                with patch('workers.utils.progress_tracker.update_job_progress') as mock_progress:
                    mock_optimize.return_value = {"title": "Optimized", "seo_score": 85}
                    
                    # Execute bulk task
                    result = optimize_products_bulk.delay(
                        store_id="store_123",
                        product_ids=product_ids,
                        optimization_type="full"
                    )
                    
                    # Wait for completion
                    task_result = result.get(timeout=30)
                    
                    assert task_result["total_products"] == 3
                    assert task_result["status"] == "completed"
                    assert mock_optimize.call_count == 3
                    # Progress should be updated multiple times
                    assert mock_progress.call_count >= 3
                    
        except ImportError:
            pytest.skip("Celery tasks not available")
    
    def test_task_error_handling_and_retry(self, celery_app):
        """Test Celery task error handling and retry mechanism"""
        try:
            from workers.tasks.product_tasks import optimize_single_product
            
            with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
                with patch.object(optimize_single_product, 'retry') as mock_retry:
                    # Simulate API error
                    mock_optimize.side_effect = Exception("OpenRouter API Error")
                    mock_retry.side_effect = Exception("Max retries exceeded")
                    
                    # Execute task (should fail and retry)
                    with pytest.raises(Exception):
                        optimize_single_product.apply(
                            args=["prod_123", "store_456", "full"]
                        )
                    
                    # Verify retry was attempted
                    mock_retry.assert_called()
                    
        except ImportError:
            pytest.skip("Celery tasks not available")
    
    @pytest.mark.slow
    def test_scheduled_task_execution(self, celery_app, celery_worker):
        """Test scheduled optimization task execution"""
        try:
            from workers.tasks.scheduled_tasks import daily_optimization_job
            
            with patch('api.crud.store.get_stores_for_auto_optimization') as mock_stores:
                with patch('workers.tasks.product_tasks.optimize_products_bulk.delay') as mock_bulk:
                    mock_stores.return_value = [
                        {"id": "store_1", "name": "Store 1"},
                        {"id": "store_2", "name": "Store 2"}
                    ]
                    mock_bulk.return_value = MagicMock(id="bulk_task_123")
                    
                    # Execute scheduled task
                    result = daily_optimization_job.delay()
                    
                    # Wait for completion
                    task_result = result.get(timeout=10)
                    
                    assert task_result["stores_processed"] == 2
                    assert mock_bulk.call_count == 2
                    
        except ImportError:
            pytest.skip("Scheduled tasks not available")


class TestAIAgentIntegration:
    """Test AI agent integration with real workflow"""
    
    def test_product_optimizer_integration(self, sample_product, mock_openrouter_response):
        """Test ProductOptimizer agent integration with mocked OpenRouter"""
        try:
            from api.agents.product_optimizer import ProductOptimizer
            
            with patch('requests.post') as mock_post:
                # Mock OpenRouter API response
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = mock_openrouter_response
                mock_post.return_value = mock_response
                
                optimizer = ProductOptimizer()
                result = optimizer.optimize_full_product(sample_product)
                
                assert "title" in result
                assert "description" in result
                assert "seo_score" in result
                assert result["seo_score"] > 80
                
                # Verify API was called
                mock_post.assert_called_once()
                call_args = mock_post.call_args
                assert "openrouter.ai" in str(call_args)
                
        except ImportError:
            pytest.skip("ProductOptimizer agent not available")
    
    def test_content_generator_integration(self, sample_product):
        """Test ContentGenerator agent integration"""
        try:
            from api.agents.content_generator import ContentGenerator
            
            with patch('requests.post') as mock_post:
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    "choices": [{
                        "message": {
                            "content": json.dumps({
                                "title": "Ultimate Guide to Wireless Headphones",
                                "content": "Complete guide content...",
                                "word_count": 1200
                            })
                        }
                    }]
                }
                mock_post.return_value = mock_response
                
                generator = ContentGenerator()
                result = generator.generate_blog_post(
                    topic="wireless headphones guide",
                    target_keywords=["wireless", "bluetooth", "headphones"]
                )
                
                assert "title" in result
                assert "content" in result
                assert result["word_count"] > 1000
                
        except ImportError:
            pytest.skip("ContentGenerator agent not available")
    
    def test_ai_service_cost_tracking(self, sample_product):
        """Test AI service usage and cost tracking integration"""
        try:
            from api.services.ai_service import AIService
            
            with patch('requests.post') as mock_post:
                # Mock response with usage data
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    "choices": [{"message": {"content": "Optimized content"}}],
                    "usage": {
                        "prompt_tokens": 150,
                        "completion_tokens": 200,
                        "total_tokens": 350
                    },
                    "model": "anthropic/claude-3-opus"
                }
                mock_post.return_value = mock_response
                
                with patch('api.services.cost_tracker.track_usage') as mock_track:
                    ai_service = AIService()
                    result = ai_service.optimize_product(sample_product)
                    
                    # Verify usage was tracked
                    mock_track.assert_called_once()
                    track_args = mock_track.call_args[1]
                    assert track_args["model"] == "anthropic/claude-3-opus"
                    assert track_args["tokens"] == 350
                    
        except ImportError:
            pytest.skip("AIService not available")


class TestWebSocketIntegration:
    """Test WebSocket integration for real-time updates"""
    
    @pytest.mark.asyncio
    async def test_websocket_connection_lifecycle(self, test_client):
        """Test WebSocket connection and message handling"""
        try:
            from api.websocket.manager import WebSocketManager
            
            manager = WebSocketManager()
            
            # Mock WebSocket connection
            mock_websocket = MagicMock()
            mock_websocket.accept = AsyncMock()
            mock_websocket.send_text = AsyncMock()
            mock_websocket.receive_text = AsyncMock(return_value='{"type": "ping"}')
            
            # Test connection
            await manager.connect(mock_websocket, "user_123")
            assert "user_123" in manager.active_connections
            
            # Test message sending
            await manager.send_personal_message("Test message", "user_123")
            mock_websocket.send_text.assert_called_with("Test message")
            
            # Test disconnection
            manager.disconnect(mock_websocket)
            assert "user_123" not in manager.active_connections
            
        except ImportError:
            pytest.skip("WebSocket manager not available")
    
    @pytest.mark.asyncio
    async def test_optimization_progress_websocket(self, mock_websocket_manager):
        """Test optimization progress updates via WebSocket"""
        try:
            from workers.tasks.product_tasks import optimize_products_bulk
            
            with patch('workers.utils.progress_tracker.send_websocket_update') as mock_ws_update:
                with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
                    mock_optimize.return_value = {"title": "Optimized", "seo_score": 85}
                    
                    # Simulate task execution with progress updates
                    product_ids = ["prod_1", "prod_2"]
                    
                    # This would normally trigger WebSocket updates
                    result = await asyncio.create_task(
                        asyncio.to_thread(
                            optimize_products_bulk.apply,
                            args=["store_123", product_ids, "full"]
                        )
                    )
                    
                    # Verify WebSocket updates were sent
                    assert mock_ws_update.call_count >= 2  # Progress updates
                    
        except ImportError:
            pytest.skip("WebSocket integration not available")


class TestExternalServiceIntegration:
    """Test integration with external services"""
    
    def test_openrouter_api_integration(self, sample_product):
        """
        Test OpenRouter API integration with proper error handling.
        
        NOTE: This test uses mocked responses. For full integration testing:
        1. Set OPENROUTER_API_KEY environment variable
        2. Use test quotas to avoid costs
        3. Implement proper rate limiting
        4. Monitor API usage and costs
        
        OpenRouter Documentation: https://openrouter.ai/docs
        """
        try:
            from api.services.openrouter_client import OpenRouterClient
            
            with patch('requests.post') as mock_post:
                # Mock successful response
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    "choices": [{
                        "message": {
                            "content": json.dumps({
                                "title": "Optimized Product Title",
                                "description": "Optimized description"
                            })
                        }
                    }],
                    "usage": {"total_tokens": 250}
                }
                mock_post.return_value = mock_response
                
                # Test client
                client = OpenRouterClient(api_key="test_key")
                result = client.generate_optimization(sample_product)
                
                assert result is not None
                assert "title" in result
                mock_post.assert_called_once()
                
                # Verify correct API endpoint
                call_args = mock_post.call_args
                assert "openrouter.ai/api/v1" in str(call_args)
                
        except ImportError:
            pytest.skip("OpenRouterClient not available")
    
    def test_openrouter_error_handling(self):
        """Test OpenRouter API error handling and retry logic"""
        try:
            from api.services.openrouter_client import OpenRouterClient
            
            with patch('requests.post') as mock_post:
                # Mock rate limit error
                mock_response = MagicMock()
                mock_response.status_code = 429
                mock_response.json.return_value = {
                    "error": {
                        "message": "Rate limit exceeded",
                        "type": "rate_limit_error"
                    }
                }
                mock_post.return_value = mock_response
                
                client = OpenRouterClient(api_key="test_key")
                
                # Should handle rate limit error appropriately
                with pytest.raises(Exception) as exc_info:
                    client.generate_optimization({"name": "Test Product"})
                
                assert "rate limit" in str(exc_info.value).lower()
                
        except ImportError:
            pytest.skip("OpenRouterClient not available")
    
    def test_wordpress_api_integration(self, mock_wordpress_product):
        """Test WordPress/WooCommerce API integration"""
        try:
            from api.services.wordpress_client import WordPressClient
            
            with patch('requests.get') as mock_get:
                with patch('requests.post') as mock_post:
                    # Mock product fetch
                    mock_get.return_value.status_code = 200
                    mock_get.return_value.json.return_value = mock_wordpress_product
                    
                    # Mock product update
                    mock_post.return_value.status_code = 200
                    mock_post.return_value.json.return_value = {"id": 123, "status": "updated"}
                    
                    client = WordPressClient(
                        base_url="https://teststore.com",
                        api_key="test_key",
                        api_secret="test_secret"
                    )
                    
                    # Test product fetch
                    product = client.get_product(123)
                    assert product["id"] == 123
                    
                    # Test product update
                    update_data = {
                        "name": "Updated Product Name",
                        "description": "Updated description"
                    }
                    result = client.update_product(123, update_data)
                    assert result["status"] == "updated"
                    
                    mock_get.assert_called_once()
                    mock_post.assert_called_once()
                    
        except ImportError:
            pytest.skip("WordPressClient not available")
    
    def test_redis_cache_integration(self):
        """Test Redis cache integration for optimization results"""
        try:
            from api.services.cache_service import CacheService
            
            with patch('redis.Redis') as mock_redis:
                mock_redis_instance = MagicMock()
                mock_redis.return_value = mock_redis_instance
                
                cache = CacheService()
                
                # Test cache set
                optimization_result = {
                    "title": "Cached Title",
                    "description": "Cached Description",
                    "seo_score": 85
                }
                cache.set_optimization_result("prod_123", optimization_result, ttl=3600)
                
                # Test cache get
                mock_redis_instance.get.return_value = json.dumps(optimization_result)
                cached_result = cache.get_optimization_result("prod_123")
                
                assert cached_result["title"] == "Cached Title"
                assert cached_result["seo_score"] == 85
                
                mock_redis_instance.set.assert_called_once()
                mock_redis_instance.get.assert_called_once()
                
        except ImportError:
            pytest.skip("CacheService not available")


class TestSecurityIntegration:
    """Test security-related integrations"""
    
    def test_jwt_authentication_integration(self, test_client):
        """Test JWT authentication across different endpoints"""
        # Test without token
        response = test_client.get("/api/v1/stores")
        assert response.status_code == 401
        
        # Test with invalid token
        response = test_client.get(
            "/api/v1/stores",
            headers={"Authorization": "Bearer invalid_token"}
        )
        assert response.status_code == 401
        
        # Test with valid token (mocked)
        with patch('api.core.security.verify_token') as mock_verify:
            mock_verify.return_value = {"user_id": "user_123", "role": "admin"}
            
            response = test_client.get(
                "/api/v1/stores",
                headers={"Authorization": "Bearer valid_token"}
            )
            
            # Should not be 401 (might be 200 or other valid response)
            assert response.status_code != 401
    
    def test_webhook_signature_verification(self, test_client):
        """Test webhook signature verification for WordPress integration"""
        webhook_payload = {
            "action": "product_updated",
            "product_id": 123,
            "store_id": "store_123"
        }
        
        # Test without signature
        response = test_client.post(
            "/api/v1/webhooks/wordpress",
            json=webhook_payload
        )
        assert response.status_code == 401
        
        # Test with invalid signature
        response = test_client.post(
            "/api/v1/webhooks/wordpress",
            json=webhook_payload,
            headers={"X-WP-Signature": "invalid_signature"}
        )
        assert response.status_code == 401
        
        # Test with valid signature (mocked)
        with patch('api.services.webhook_service.verify_signature') as mock_verify:
            mock_verify.return_value = True
            
            response = test_client.post(
                "/api/v1/webhooks/wordpress",
                json=webhook_payload,
                headers={"X-WP-Signature": "valid_signature"}
            )
            
            # Should be accepted
            assert response.status_code in [200, 202]


class TestPerformanceIntegration:
    """Test performance-related integrations"""
    
    @pytest.mark.slow
    def test_concurrent_optimization_requests(self, test_client, auth_headers, performance_test_products):
        """Test handling multiple concurrent optimization requests"""
        import concurrent.futures
        import time
        
        def optimize_product(product):
            return test_client.post(
                "/api/v1/optimize/product",
                json=product,
                headers=auth_headers
            )
        
        with patch('api.services.ai_service.optimize_product_content') as mock_optimize:
            mock_optimize.return_value = {
                "title": "Optimized Title",
                "seo_score": 85
            }
            
            # Test with 10 concurrent requests
            test_products = performance_test_products[:10]
            
            start_time = time.time()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(optimize_product, product) for product in test_products]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            end_time = time.time()
            
            # All requests should succeed
            assert all(r.status_code in [200, 429] for r in results)  # 429 = rate limited, acceptable
            successful_requests = [r for r in results if r.status_code == 200]
            assert len(successful_requests) >= 5  # At least half should succeed
            
            # Should complete in reasonable time
            assert (end_time - start_time) < 30  # 30 seconds max
    
    def test_database_query_performance(self, test_client, auth_headers, create_test_store, create_test_product, db_session):
        """Test database query performance with multiple records"""
        # Create test data
        stores = [create_test_store(name=f"Store {i}") for i in range(5)]
        products = []
        for store in stores:
            for j in range(10):
                products.append(create_test_product(
                    name=f"Product {j}",
                    store_id=store.id
                ))
        
        import time
        
        # Test products list query performance
        start_time = time.time()
        response = test_client.get(
            "/api/v1/products?page=1&per_page=20",
            headers=auth_headers
        )
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 2.0  # Should complete in under 2 seconds
        
        # Test search query performance
        start_time = time.time()
        response = test_client.get(
            "/api/v1/products?search=Product&category=electronics",
            headers=auth_headers
        )
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 3.0  # Search queries can be slightly slower


class TestMonitoringIntegration:
    """Test monitoring and analytics integration"""
    
    def test_metrics_collection_integration(self, test_client, auth_headers):
        """Test metrics collection during API operations"""
        try:
            from api.monitoring.metrics import track_optimization_request
            
            with patch('api.monitoring.metrics.OPTIMIZATION_REQUESTS_TOTAL.inc') as mock_metric:
                # Make API request that should trigger metrics
                response = test_client.post(
                    "/api/v1/optimize/product",
                    json={"name": "Test Product", "price": 99.99},
                    headers=auth_headers
                )
                
                # Metrics should be tracked
                mock_metric.assert_called()
                
        except ImportError:
            pytest.skip("Metrics collection not available")
    
    def test_health_check_integration(self, test_client):
        """Test health check integration with all services"""
        with patch('api.monitoring.health.check_database_health') as mock_db:
            with patch('api.monitoring.health.check_redis_health') as mock_redis:
                with patch('api.monitoring.health.check_openrouter_health') as mock_api:
                    mock_db.return_value = {"status": "healthy", "response_time": 0.05}
                    mock_redis.return_value = {"status": "healthy", "response_time": 0.02}
                    mock_api.return_value = {"status": "healthy", "response_time": 0.89}
                    
                    response = test_client.get("/health")
                    
                    assert response.status_code == 200
                    health_data = response.json()
                    assert health_data["status"] == "healthy"
                    assert "services" in health_data
                    assert len(health_data["services"]) >= 3
