<?php
/**
 * Test WordPress Plugin Sync Functionality
 * This script simulates what the WordPress plugin should do
 */

echo "=== Testing WordPress Plugin Sync ===\n";

function test_gridspoke_sync() {
    $api_base = 'http://localhost:8000/api/v1';
    
    // Step 1: Authenticate
    echo "1. Authenticating with GridSpoke...\n";
    $auth_data = [
        'email' => '<EMAIL>',
        'password' => 'test12345'
    ];
    
    $auth_response = make_request('POST', $api_base . '/auth/login', $auth_data);
    if (!$auth_response || $auth_response['status'] !== 200) {
        echo "   ERROR: Authentication failed\n";
        return false;
    }
    
    $token = $auth_response['data']['access_token'];
    echo "   ✓ Authentication successful\n";
    
    // Step 2: Check/Create WordPress store
    echo "2. Checking/Creating WordPress store...\n";
    $headers = ['Authorization: Bearer ' . $token];
    
    $stores_response = make_request('GET', $api_base . '/stores', null, $headers);
    $wordpress_store_id = null;
    
    if ($stores_response && $stores_response['status'] === 200) {
        foreach ($stores_response['data'] as $store) {
            if ($store['name'] === 'Test WordPress Store') {
                $wordpress_store_id = $store['id'];
                echo "   ✓ Found existing WordPress store: {$wordpress_store_id}\n";
                break;
            }
        }
    }
    
    if (!$wordpress_store_id) {
        echo "   Creating new WordPress store...\n";
        $store_data = [
            'name' => 'Test WordPress Store',
            'store_url' => 'http://localhost:10010',
            'platform' => 'woocommerce',
            'domain' => 'localhost:10010'
        ];
        
        $create_store_response = make_request('POST', $api_base . '/stores', $store_data, $headers);
        if ($create_store_response && $create_store_response['status'] === 201) {
            $wordpress_store_id = $create_store_response['data']['id'];
            echo "   ✓ Created new WordPress store: {$wordpress_store_id}\n";
        } else {
            echo "   ERROR: Failed to create WordPress store\n";
            return false;
        }
    }
    
    // Step 3: Sync the "Test Dummy" product
    echo "3. Syncing Test Dummy product...\n";
    $test_product = [
        'store_id' => $wordpress_store_id,
        'external_id' => 'wp_test_dummy_1',
        'name' => 'Test Dummy',
        'sku' => 'TEST-DUMMY-001',
        'description' => 'This is a test dummy product from WooCommerce for GridSpoke SEO optimization testing.',
        'price' => 29.99,
        'categories' => ['Electronics', 'Test Products'],
        'tags' => ['test', 'dummy', 'sample'],
        'images' => ['http://localhost:10010/wp-content/uploads/test-dummy.jpg'],
        'original_data' => [
            'woocommerce_id' => 123,
            'post_type' => 'product',
            'status' => 'publish'
        ]
    ];
    
    $sync_response = make_request('POST', $api_base . '/products/bulk/sync', [$test_product], $headers);
    
    if ($sync_response && ($sync_response['status'] === 200 || $sync_response['status'] === 201)) {
        echo "   ✓ Successfully synced Test Dummy product\n";
        echo "   Created: " . ($sync_response['data']['created'] ?? 0) . " products\n";
        echo "   Updated: " . ($sync_response['data']['updated'] ?? 0) . " products\n";
        
        // Step 4: Verify the product appears in the products list
        echo "4. Verifying product sync...\n";
        $products_response = make_request('GET', $api_base . "/products?store_id={$wordpress_store_id}", null, $headers);
        
        if ($products_response && $products_response['status'] === 200) {
            $products = $products_response['data'];
            echo "   Found " . count($products) . " products in WordPress store:\n";
            foreach ($products as $product) {
                echo "   - {$product['name']} (ID: {$product['id']})\n";
            }
            
            $test_dummy_found = false;
            foreach ($products as $product) {
                if ($product['name'] === 'Test Dummy') {
                    $test_dummy_found = true;
                    break;
                }
            }
            
            if ($test_dummy_found) {
                echo "   ✓ Test Dummy product successfully synced and verified!\n";
                echo "\n=== SUCCESS ===\n";
                echo "The WordPress plugin sync is now working!\n";
                echo "Check the GridSpoke dashboard at http://localhost:3000\n";
                echo "It should now show the Test Dummy product instead of fake data.\n";
                return true;
            } else {
                echo "   ERROR: Test Dummy product not found in synced products\n";
            }
        } else {
            echo "   ERROR: Failed to verify product sync\n";
        }
    } else {
        echo "   ERROR: Failed to sync Test Dummy product\n";
        if (isset($sync_response['data'])) {
            echo "   Response: " . json_encode($sync_response['data']) . "\n";
        }
    }
    
    return false;
}

function make_request($method, $url, $data = null, $headers = []) {
    $default_headers = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $headers = array_merge($default_headers, $headers);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false) {
        return null;
    }
    
    return [
        'status' => $status_code,
        'data' => json_decode($response, true)
    ];
}

// Run the test
test_gridspoke_sync();
?>
