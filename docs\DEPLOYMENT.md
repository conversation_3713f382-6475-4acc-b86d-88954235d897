# GridSpoke Production Deployment Guide

## Overview

This guide covers the complete production deployment process for GridSpoke, an AI-powered ecommerce SEO optimization service. GridSpoke uses a microservices architecture with Docker Compose for orchestration.

## Architecture Components

- **API Service**: FastAPI backend with Mirascope AI framework
- **Database**: PostgreSQL 15+ with pgvector extension
- **Cache/Queue**: Redis for caching and Celery message broker
- **Workers**: Celery workers for async AI processing tasks
- **Reverse Proxy**: Nginx with SSL termination and rate limiting
- **Frontend**: Static dashboard served by Nginx
- **Monitoring**: Prometheus + Grafana for metrics and alerting

## Prerequisites

### System Requirements

- **OS**: Ubuntu 20.04 LTS or later (recommended)
- **CPU**: Minimum 4 cores (8+ recommended for production)
- **RAM**: Minimum 8GB (16GB+ recommended)
- **Storage**: 50GB+ SSD for application + additional space for backups
- **Network**: Static IP address and domain name

### Software Dependencies

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install additional tools
sudo apt install -y curl wget git htop nginx-utils
```

### Domain and DNS Setup

1. **Domain Configuration**:
   - Point your domain A record to your server IP
   - Set up www subdomain (CNAME to main domain)
   - Create admin subdomain for monitoring dashboards

2. **DNS Verification**:
   ```bash
   # Verify DNS propagation
   nslookup your-domain.com
   nslookup www.your-domain.com
   nslookup admin.your-domain.com
   ```

## Pre-Deployment Setup

### 1. Clone Repository

```bash
cd /opt
sudo git clone https://github.com/your-username/gridspoke.git
sudo chown -R $USER:$USER gridspoke/
cd gridspoke/
```

### 2. Environment Configuration

```bash
# Copy production environment template
cp .env.prod.example .env.prod

# Edit configuration (see Environment Variables section below)
nano .env.prod
```

### 3. Directory Structure Setup

```bash
# Create necessary directories
mkdir -p logs backups monitoring/prometheus monitoring/grafana/{dashboards,datasources}
mkdir -p certbot/{conf,www} nginx/ssl

# Set proper permissions
chmod +x scripts/*.sh
```

## Environment Variables

Edit `.env.prod` with your production values:

### Core Application Settings
```bash
ENV=production
SECRET_KEY=your-super-secret-key-here-change-this-in-production
DOMAIN_NAME=your-domain.com
SSL_EMAIL=<EMAIL>
```

### Database Configuration
```bash
POSTGRES_DB=gridspoke_prod
POSTGRES_USER=gridspoke_user
POSTGRES_PASSWORD=secure-postgres-password-here
```

### AI Service Configuration
```bash
OPENROUTER_API_KEY=your-openrouter-api-key-here
```

### Security Settings
```bash
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
```

### Monitoring & Alerting
```bash
SENTRY_DSN=your-sentry-dsn-here
GRAFANA_PASSWORD=secure-grafana-password-here
```

**Important**: Never commit `.env.prod` to version control. Keep it secure and backed up separately.

## Deployment Process

### First-Time Deployment

```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Run first-time deployment (includes SSL setup)
./scripts/deploy.sh --first-time
```

The first-time deployment will:
1. Set up SSL certificates via Let's Encrypt
2. Build all Docker images
3. Initialize the database
4. Start all services
5. Run health checks
6. Set up monitoring

### Subsequent Deployments

```bash
# Standard deployment with backup
./scripts/deploy.sh

# Skip backup (for testing)
./scripts/deploy.sh --skip-backup

# Deploy specific version
./scripts/deploy.sh v1.2.3
```

## SSL Certificate Management

### Automatic Certificate Renewal

SSL certificates are automatically renewed via cron job:

```bash
# Add to crontab (runs daily at 2 AM)
sudo crontab -e

# Add this line:
0 2 * * * /opt/gridspoke/scripts/renew-ssl.sh >/dev/null 2>&1
```

### Manual Certificate Renewal

```bash
# Renew certificates manually
docker-compose -f docker-compose.prod.yml run --rm certbot renew

# Reload nginx after renewal
docker-compose -f docker-compose.prod.yml exec nginx nginx -s reload
```

## Security Configuration

### Firewall Setup

```bash
# Install and configure UFW
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow essential ports
sudo ufw allow ssh
sudo ufw allow 80/tcp   # HTTP (redirects to HTTPS)
sudo ufw allow 443/tcp  # HTTPS

# Optional: Allow monitoring (restrict to your IP)
sudo ufw allow from YOUR_IP_ADDRESS to any port 3000  # Grafana
sudo ufw allow from YOUR_IP_ADDRESS to any port 9090  # Prometheus
```

### Security Headers

Nginx is configured with security headers:
- HSTS (Strict Transport Security)
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Content Security Policy
- Referrer Policy

### Rate Limiting

Built-in rate limiting:
- API endpoints: 10 requests/second with burst of 20
- Authentication endpoints: 1 request/second with burst of 5
- Connection limits: 20 concurrent connections per IP

## Monitoring and Logging

### Grafana Dashboards

Access Grafana at `https://admin.your-domain.com/grafana/`

Default dashboards include:
- System metrics (CPU, memory, disk, network)
- Application metrics (API response times, error rates)
- AI service metrics (token usage, API costs)
- Database performance metrics

### Prometheus Metrics

Key metrics collected:
- HTTP request duration and count
- Database connection pool usage
- Celery task queue length and processing time
- Redis memory usage and hit rates
- Custom business metrics (optimizations per day, etc.)

### Log Management

Logs are stored in structured JSON format:

```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f api

# View worker logs
docker-compose -f docker-compose.prod.yml logs -f celery-worker

# View nginx access logs
docker-compose -f docker-compose.prod.yml logs -f nginx
```

### Alerting

Set up alerts in Grafana for:
- High error rates (>5% for 5 minutes)
- High response times (>2s average for 5 minutes)
- Low disk space (<10% remaining)
- Database connection failures
- Celery queue backup (>100 pending tasks)

## Backup and Recovery

### Automated Backups

Backups run automatically daily at 3 AM:

```bash
# Setup backup cron job
sudo crontab -e

# Add this line:
0 3 * * * /opt/gridspoke/scripts/backup.sh --upload-to-s3 >/dev/null 2>&1
```

### Manual Backup

```bash
# Create backup
./scripts/backup.sh

# Create backup and upload to S3
./scripts/backup.sh --upload-to-s3
```

### Backup Contents

Each backup includes:
- Complete PostgreSQL database dump
- Redis data snapshots
- Static files and media uploads
- Configuration files
- SSL certificates
- System information and metrics

### Recovery Process

```bash
# Stop services
docker-compose -f docker-compose.prod.yml down

# Restore from backup
./scripts/restore.sh backups/gridspoke_backup_YYYY-MM-DD-HHMMSS.tar.gz

# Start services
docker-compose -f docker-compose.prod.yml up -d
```

## Performance Optimization

### Database Optimization

```sql
-- Add indexes for common queries
CREATE INDEX CONCURRENTLY idx_products_store_id_status ON products(store_id, optimization_status);
CREATE INDEX CONCURRENTLY idx_jobs_status_created ON optimization_jobs(status, created_at);

-- Configure PostgreSQL (in postgresql.conf)
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

### Redis Optimization

Redis is configured with:
- Memory limit: 512MB
- Eviction policy: allkeys-lru
- Persistence: RDB snapshots every 5 minutes

### Celery Worker Scaling

Scale workers based on load:

```bash
# Scale up workers
docker-compose -f docker-compose.prod.yml up -d --scale celery-worker=5

# Monitor queue length
docker-compose -f docker-compose.prod.yml exec redis redis-cli llen celery
```

## Troubleshooting

### Common Issues

1. **SSL Certificate Issues**:
   ```bash
   # Check certificate expiry
   docker-compose -f docker-compose.prod.yml exec nginx openssl x509 -in /etc/letsencrypt/live/your-domain.com/fullchain.pem -text -noout | grep "Not After"
   
   # Manually renew
   docker-compose -f docker-compose.prod.yml run --rm certbot renew --force-renewal
   ```

2. **Database Connection Issues**:
   ```bash
   # Check database status
   docker-compose -f docker-compose.prod.yml exec db pg_isready -U gridspoke_user
   
   # Check connections
   docker-compose -f docker-compose.prod.yml exec db psql -U gridspoke_user -d gridspoke_prod -c "SELECT count(*) FROM pg_stat_activity;"
   ```

3. **High Memory Usage**:
   ```bash
   # Check container resource usage
   docker stats
   
   # Check system memory
   free -h
   
   # Restart memory-intensive services
   docker-compose -f docker-compose.prod.yml restart celery-worker
   ```

4. **AI Service Errors**:
   ```bash
   # Check OpenRouter API status
   curl -H "Authorization: Bearer $OPENROUTER_API_KEY" https://openrouter.ai/api/v1/models
   
   # Check worker logs for AI errors
   docker-compose -f docker-compose.prod.yml logs celery-worker | grep ERROR
   ```

### Health Checks

Built-in health checks for all services:

```bash
# Check all service health
docker-compose -f docker-compose.prod.yml ps

# Detailed health check
curl -f https://your-domain.com/health
```

### Log Analysis

```bash
# Search for errors in last hour
docker-compose -f docker-compose.prod.yml logs --since 1h | grep ERROR

# Check API response times
docker-compose -f docker-compose.prod.yml logs nginx | grep "POST /api" | tail -100

# Monitor real-time logs
docker-compose -f docker-compose.prod.yml logs -f --tail=50
```

## Scaling Guidelines

### Horizontal Scaling

For high-traffic scenarios:

1. **Load Balancer**: Add multiple API instances behind a load balancer
2. **Database Read Replicas**: Set up PostgreSQL read replicas for read-heavy workloads
3. **Redis Cluster**: Implement Redis clustering for cache scaling
4. **CDN**: Use CloudFlare or AWS CloudFront for static asset delivery

### Vertical Scaling

Resource recommendations by traffic level:

- **Small (< 1000 products/day)**: 2 CPU, 4GB RAM
- **Medium (1000-10000 products/day)**: 4 CPU, 8GB RAM
- **Large (10000+ products/day)**: 8+ CPU, 16GB+ RAM

## Maintenance

### Regular Maintenance Tasks

**Weekly**:
- Review system metrics and alerts
- Check backup integrity
- Update security patches
- Review error logs

**Monthly**:
- Rotate log files
- Update Docker images
- Review and optimize database queries
- Check SSL certificate expiry (auto-renewed but verify)

**Quarterly**:
- Full security audit
- Performance optimization review
- Backup and disaster recovery testing
- Dependency updates

### Update Process

```bash
# 1. Backup before updates
./scripts/backup.sh

# 2. Pull latest changes
git pull origin main

# 3. Deploy updates
./scripts/deploy.sh

# 4. Verify deployment
curl -f https://your-domain.com/health
```

## Disaster Recovery

### Recovery Procedures

1. **Complete System Failure**:
   - Provision new server
   - Install dependencies
   - Restore from latest backup
   - Update DNS if needed

2. **Database Corruption**:
   - Stop application services
   - Restore database from backup
   - Restart services

3. **SSL Certificate Issues**:
   - Obtain new certificates
   - Update nginx configuration
   - Restart nginx service

### RTO/RPO Targets

- **Recovery Time Objective (RTO)**: < 4 hours
- **Recovery Point Objective (RPO)**: < 24 hours (daily backups)

## Support and Maintenance

### Monitoring Alerts

Set up notification channels in Grafana:
- Email notifications for critical alerts
- Slack integration for team notifications
- PagerDuty for 24/7 critical alerts

### Log Retention

- Application logs: 30 days
- Access logs: 90 days
- Error logs: 180 days
- Backup retention: 30 days local, 1 year S3

### Documentation Updates

Keep deployment documentation updated with:
- Configuration changes
- New monitoring metrics
- Updated troubleshooting procedures
- Performance optimization notes

This deployment guide ensures a robust, secure, and scalable production environment for GridSpoke with comprehensive monitoring, backup, and recovery procedures.
