# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

GridSpoke is an AI-powered ecommerce SEO optimizer that connects to WordPress/WooCommerce and SureCart platforms to automatically optimize product content for search engines. It uses OpenRouter API (with Claude 3 Opus and other models) for content generation and optimization.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐
│   Load Balancer │───▶│     Nginx    │───▶│  FastAPI    │
│    (Optional)   │    │  SSL + Proxy │    │   Backend   │
└─────────────────┘    └──────────────┘    └─────────────┘
                              │                    │
                              ▼                    ▼
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐
│   Static Files  │    │   Frontend   │    │   Celery    │
│   (Dashboard)   │    │  Dashboard   │    │  Workers    │
└─────────────────┘    └──────────────┘    └─────────────┘
                                                  │
                              ┌───────────────────┼───────────────────┐
                              ▼                   ▼                   ▼
                    ┌──────────────┐    ┌─────────────┐    ┌─────────────┐
                    │ PostgreSQL   │    │    Redis    │    │ Monitoring  │
                    │  + pgvector  │    │   Cache     │    │ (Grafana)   │
                    └──────────────┘    └─────────────┘    └─────────────┘
```

## Common Commands

### Development Setup
```bash
# Start development environment
docker-compose up -d

# Run tests
docker-compose exec api pytest

# Run specific test file
docker-compose exec api pytest tests/unit/test_api.py

# Run tests with coverage
docker-compose exec api pytest --cov=api --cov-report=html

# Apply database migrations
docker-compose exec api alembic upgrade head

# Create new migration
docker-compose exec api alembic revision --autogenerate -m "Migration description"
```

### Production Deployment
```bash
# First-time deployment
./scripts/deploy.sh --first-time

# Subsequent deployments
./scripts/deploy.sh

# Deploy specific version
./scripts/deploy.sh v1.2.3
```

### Testing
```bash
# Run unit tests
pytest tests/unit/

# Run integration tests
pytest tests/integration/

# Run security tests
pytest tests/security/

# Run load tests
pytest tests/load/

# Run all tests
pytest
```

### Database Operations
```bash
# Create database backup
./scripts/backup.sh

# Connect to database
docker-compose exec db psql -U gridspoke_user -d gridspoke_db

# Run database migrations
docker-compose exec api alembic upgrade head
```

### Monitoring
```bash
# Check service health
docker-compose exec api curl http://localhost:8000/health

# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f api
```

## Code Structure

- `api/` - Main FastAPI backend with models, schemas, and endpoints
- `frontend/` - Dashboard frontend (HTML/CSS/JS)
- `workers/` - Celery workers for async processing
- `scripts/` - Deployment and maintenance scripts
- `docs/` - Comprehensive documentation
- `tests/` - Full test suite (unit, integration, load, security)
- `monitoring/` - Prometheus and Grafana configurations
- `wordpress-plugin/` - WordPress integration plugin

## Key Components

### Backend (FastAPI)
- REST API with automatic OpenAPI documentation
- JWT-based authentication
- SQLAlchemy ORM with async PostgreSQL support
- Redis caching and session management
- WebSocket support for real-time updates
- Rate limiting and security middleware

### AI Services
- OpenRouter integration (Claude 3 Opus, GPT-4, etc.)
- Product optimization agents
- Content generation and analysis
- Keyword research and competitor analysis
- Structured data generation

### Async Processing (Celery)
- Product optimization tasks
- Content generation tasks
- Scheduled optimization jobs
- Bulk processing workflows
- Progress tracking and monitoring

### Frontend
- Vanilla JavaScript dashboard
- Real-time WebSocket updates
- Job monitoring and management
- Product browsing and optimization
- Settings configuration

### WordPress Integration
- WooCommerce plugin
- SureCart integration
- Webhook handling
- API synchronization
- Settings management

## Environment Configuration

The application uses environment variables for configuration. For development, copy `.env.example` to `.env`. For production, use `.env.prod.example` as a template.

Key environment variables:
- `OPENROUTER_API_KEY` - API key for OpenRouter
- `SECRET_KEY` - JWT secret key
- `DATABASE_URL` - PostgreSQL connection string
- `REDIS_URL` - Redis connection string

## Security Features

- JWT token authentication
- Rate limiting
- Input validation and sanitization
- Secure headers (HSTS, CSP, etc.)
- SSL/TLS encryption
- Database encryption at rest
- Security audit logging
- CORS configuration