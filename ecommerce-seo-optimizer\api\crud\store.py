"""
Store CRUD operations.
"""
import uuid
from typing import Optional, List
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from crud.base import CRUDBase
from models.store import Store
from models.user import User
from schemas.store import StoreCreate, StoreUpdate


class CRUDStore(CRUDBase[Store, StoreCreate, StoreUpdate]):
    """CRUD operations for Store model."""
    
    async def get_by_owner(
        self, 
        db: AsyncSession, 
        *, 
        owner_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[Store]:
        """Get stores by owner ID."""
        query = select(Store).where(
            Store.owner_id == owner_id
        ).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_user_stores(
        self, 
        db: AsyncSession, 
        *, 
        user_id: uuid.UUID
    ) -> List[Store]:
        """Get all stores for a user."""
        query = select(Store).where(Store.owner_id == user_id)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_by_owner_and_id(
        self, 
        db: AsyncSession, 
        *, 
        owner_id: uuid.UUID,
        store_id: uuid.UUID
    ) -> Optional[Store]:
        """Get store by owner and store ID."""
        query = select(Store).where(
            and_(Store.id == store_id, Store.owner_id == owner_id)
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_by_url(
        self, 
        db: AsyncSession, 
        *, 
        store_url: str
    ) -> Optional[Store]:
        """Get store by URL."""
        query = select(Store).where(Store.store_url == store_url)
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_active_stores(
        self, 
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100
    ) -> List[Store]:
        """Get all active stores."""
        query = select(Store).where(
            Store.is_active == True
        ).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def create_for_owner(
        self, 
        db: AsyncSession, 
        *, 
        obj_in: StoreCreate, 
        owner_id: uuid.UUID
    ) -> Store:
        """Create store for specific owner."""
        create_data = obj_in.model_dump()
        create_data["owner_id"] = owner_id
        
        db_obj = Store(**create_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get_with_stats(
        self, 
        db: AsyncSession, 
        *, 
        store_id: uuid.UUID
    ) -> Optional[Store]:
        """Get store with product statistics."""
        query = select(Store).options(
            selectinload(Store.products)
        ).where(Store.id == store_id)
        
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def count_by_owner(
        self, 
        db: AsyncSession, 
        *, 
        owner_id: uuid.UUID
    ) -> int:
        """Count stores by owner."""
        from sqlalchemy import func
        
        query = select(func.count(Store.id)).where(Store.owner_id == owner_id)
        result = await db.execute(query)
        return result.scalar()
    
    async def deactivate(
        self, 
        db: AsyncSession, 
        *, 
        store_id: uuid.UUID
    ) -> Optional[Store]:
        """Deactivate a store."""
        store = await self.get(db, id=store_id)
        if store:
            store.is_active = False
            db.add(store)
            await db.commit()
            await db.refresh(store)
        return store
    
    async def activate(
        self, 
        db: AsyncSession, 
        *, 
        store_id: uuid.UUID
    ) -> Optional[Store]:
        """Activate a store."""
        store = await self.get(db, id=store_id)
        if store:
            store.is_active = True
            db.add(store)
            await db.commit()
            await db.refresh(store)
        return store


# Create instance for dependency injection
store_crud = CRUDStore(Store)
