<?php
/**
 * PHPUnit bootstrap file for GridSpoke SEO Connector tests
 */

// Define test environment
define('GRIDSPOKE_SEO_TESTING', true);

// WordPress test environment
$_tests_dir = getenv('WP_TESTS_DIR');

if (!$_tests_dir) {
    $_tests_dir = rtrim(sys_get_temp_dir(), '/\\') . '/wordpress-tests-lib';
}

if (!file_exists($_tests_dir . '/includes/functions.php')) {
    echo "Could not find $_tests_dir/includes/functions.php, have you run bin/install-wp-tests.sh ?" . PHP_EOL;
    exit(1);
}

// Give access to tests_add_filter() function
require_once $_tests_dir . '/includes/functions.php';

/**
 * Manually load the plugin being tested
 */
function _manually_load_plugin() {
    // Load WooCommerce if available
    if (file_exists(WP_PLUGIN_DIR . '/woocommerce/woocommerce.php')) {
        require_once WP_PLUGIN_DIR . '/woocommerce/woocommerce.php';
    }
    
    // Load our plugin
    require dirname(dirname(__FILE__)) . '/gridspoke-seo-connector.php';
}

tests_add_filter('muplugins_loaded', '_manually_load_plugin');

// Start up the WP testing environment
require $_tests_dir . '/includes/bootstrap.php';

// Load test utilities
require_once dirname(__FILE__) . '/includes/class-gridspoke-test-case.php';
require_once dirname(__FILE__) . '/includes/class-gridspoke-api-mock.php';
require_once dirname(__FILE__) . '/includes/class-gridspoke-test-data.php';
