"""
WebSocket and Real-time Features Testing for GridSpoke
Tests WebSocket connections, real-time updates, and task progress tracking
"""
import pytest
import asyncio
import json
from unittest.mock import MagicMock, AsyncMock, patch
from fastapi import <PERSON>AP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.testclient import TestClient
from typing import List, Dict, Any
import uuid


def test_websocket_basic_functionality():
    """Test basic WebSocket functionality"""
    app = FastAPI(title="GridSpoke WebSocket Test")
    
    # Store active connections
    active_connections: List[WebSocket] = []
    
    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        await websocket.accept()
        active_connections.append(websocket)
        try:
            while True:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Echo the message back with a timestamp
                response = {
                    "type": "echo",
                    "original_message": message,
                    "timestamp": "2025-01-27T10:00:00Z",
                    "connection_id": len(active_connections)
                }
                await websocket.send_text(json.dumps(response))
        except WebSocketDisconnect:
            active_connections.remove(websocket)
    
    @app.get("/connections")
    def get_active_connections():
        return {"active_connections": len(active_connections)}
    
    # Test with TestClient WebSocket support
    with TestClient(app) as client:
        # Test REST endpoint
        response = client.get("/connections")
        assert response.status_code == 200
        assert response.json()["active_connections"] == 0


def test_optimization_progress_websocket():
    """Test WebSocket for optimization progress updates"""
    app = FastAPI(title="GridSpoke Progress WebSocket")
    
    # Mock task progress storage
    task_progress: Dict[str, Dict[str, Any]] = {}
    active_connections: Dict[str, WebSocket] = {}
    
    @app.websocket("/ws/progress/{task_id}")
    async def progress_websocket(websocket: WebSocket, task_id: str):
        await websocket.accept()
        active_connections[task_id] = websocket
        
        try:
            # Send initial status
            if task_id in task_progress:
                await websocket.send_text(json.dumps(task_progress[task_id]))
            else:
                initial_status = {
                    "task_id": task_id,
                    "status": "starting",
                    "progress": 0,
                    "message": "Optimization task initiated"
                }
                task_progress[task_id] = initial_status
                await websocket.send_text(json.dumps(initial_status))
            
            # Listen for client messages
            while True:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": "2025-01-27T10:00:00Z"
                    }))
                
        except WebSocketDisconnect:
            if task_id in active_connections:
                del active_connections[task_id]
    
    async def simulate_optimization_progress(task_id: str):
        """Simulate optimization progress updates"""
        progress_steps = [
            {"status": "analyzing", "progress": 25, "message": "Analyzing product content"},
            {"status": "optimizing", "progress": 50, "message": "Generating SEO optimizations"},
            {"status": "validating", "progress": 75, "message": "Validating optimization results"},
            {"status": "completed", "progress": 100, "message": "Optimization completed successfully"}
        ]
        
        for step in progress_steps:
            task_progress[task_id] = {
                "task_id": task_id,
                **step,
                "timestamp": "2025-01-27T10:00:00Z"
            }
            
            # Send update to connected WebSocket if exists
            if task_id in active_connections:
                try:
                    websocket = active_connections[task_id]
                    await websocket.send_text(json.dumps(task_progress[task_id]))
                except:
                    # Connection closed
                    pass
            
            await asyncio.sleep(0.1)  # Simulate processing time
    
    @app.post("/api/v1/optimize/{product_id}")
    async def start_optimization(product_id: str):
        task_id = str(uuid.uuid4())
        
        # Start the optimization simulation
        asyncio.create_task(simulate_optimization_progress(task_id))
        
        return {
            "task_id": task_id,
            "product_id": product_id,
            "status": "started",
            "websocket_url": f"/ws/progress/{task_id}"
        }
    
    @app.get("/api/v1/tasks/{task_id}")
    def get_task_status(task_id: str):
        if task_id in task_progress:
            return task_progress[task_id]
        return {"error": "Task not found"}, 404
    
    # Test the endpoints
    with TestClient(app) as client:
        # Start an optimization task
        response = client.post("/api/v1/optimize/product_123")
        assert response.status_code == 200
        
        task_data = response.json()
        assert "task_id" in task_data
        assert task_data["product_id"] == "product_123"
        assert task_data["status"] == "started"
        
        task_id = task_data["task_id"]
        
        # Check task status
        response = client.get(f"/api/v1/tasks/{task_id}")
        assert response.status_code == 200


def test_websocket_error_handling():
    """Test WebSocket error handling and recovery"""
    app = FastAPI(title="GridSpoke WebSocket Error Handling")
    
    connection_attempts = []
    
    @app.websocket("/ws/test")
    async def test_websocket(websocket: WebSocket):
        connection_attempts.append("connected")
        await websocket.accept()
        
        try:
            while True:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                if message.get("action") == "error":
                    # Simulate an error condition
                    raise Exception("Simulated error")
                elif message.get("action") == "disconnect":
                    # Graceful disconnect
                    await websocket.close()
                    break
                else:
                    # Normal message handling
                    response = {"status": "ok", "received": message}
                    await websocket.send_text(json.dumps(response))
                    
        except WebSocketDisconnect:
            connection_attempts.append("disconnected")
        except Exception as e:
            connection_attempts.append(f"error: {str(e)}")
            await websocket.close(code=1011, reason="Internal error")
    
    @app.get("/connection-log")
    def get_connection_log():
        return {"attempts": connection_attempts}
    
    with TestClient(app) as client:
        response = client.get("/connection-log")
        assert response.status_code == 200


def test_websocket_broadcasting():
    """Test WebSocket message broadcasting to multiple clients"""
    app = FastAPI(title="GridSpoke WebSocket Broadcasting")
    
    active_connections: List[WebSocket] = []
    message_history: List[Dict[str, Any]] = []
    
    @app.websocket("/ws/broadcast")
    async def broadcast_websocket(websocket: WebSocket):
        await websocket.accept()
        active_connections.append(websocket)
        
        # Send connection confirmation
        await websocket.send_text(json.dumps({
            "type": "connected",
            "connection_count": len(active_connections)
        }))
        
        try:
            while True:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Add to history
                broadcast_message = {
                    "type": "broadcast",
                    "message": message,
                    "sender_id": id(websocket),
                    "timestamp": "2025-01-27T10:00:00Z",
                    "connection_count": len(active_connections)
                }
                message_history.append(broadcast_message)
                
                # Broadcast to all connected clients
                for connection in active_connections.copy():
                    try:
                        await connection.send_text(json.dumps(broadcast_message))
                    except:
                        # Remove dead connections
                        active_connections.remove(connection)
                        
        except WebSocketDisconnect:
            if websocket in active_connections:
                active_connections.remove(websocket)
    
    @app.get("/broadcast-stats")
    def get_broadcast_stats():
        return {
            "active_connections": len(active_connections),
            "message_count": len(message_history),
            "recent_messages": message_history[-5:] if message_history else []
        }
    
    @app.post("/api/broadcast")
    async def send_broadcast_message(message: dict):
        """Send a message to all connected WebSocket clients"""
        if not active_connections:
            return {"error": "No active connections"}
        
        broadcast_message = {
            "type": "server_broadcast",
            "message": message,
            "timestamp": "2025-01-27T10:00:00Z",
            "connection_count": len(active_connections)
        }
        
        sent_count = 0
        for connection in active_connections.copy():
            try:
                await connection.send_text(json.dumps(broadcast_message))
                sent_count += 1
            except:
                active_connections.remove(connection)
        
        return {
            "message_sent": True,
            "recipients": sent_count,
            "message": broadcast_message
        }
    
    with TestClient(app) as client:
        # Test broadcast stats endpoint
        response = client.get("/broadcast-stats")
        assert response.status_code == 200
        stats = response.json()
        assert "active_connections" in stats
        assert "message_count" in stats


class TestGridSpokeWebSocketFeatures:
    """Test GridSpoke-specific WebSocket features"""
    
    def test_optimization_task_websocket_integration(self):
        """Test integration between optimization tasks and WebSocket updates"""
        
        class MockOptimizationEngine:
            def __init__(self):
                self.tasks = {}
            
            async def optimize_product(self, product_id: str, websocket_callback=None):
                """Mock product optimization with WebSocket updates"""
                task_id = str(uuid.uuid4())
                
                steps = [
                    {"step": "content_analysis", "progress": 20, "message": "Analyzing product content"},
                    {"step": "keyword_research", "progress": 40, "message": "Researching keywords"},
                    {"step": "seo_optimization", "progress": 60, "message": "Optimizing SEO elements"},
                    {"step": "quality_check", "progress": 80, "message": "Quality assurance check"},
                    {"step": "completed", "progress": 100, "message": "Optimization completed"}
                ]
                
                for step in steps:
                    task_update = {
                        "task_id": task_id,
                        "product_id": product_id,
                        **step,
                        "timestamp": "2025-01-27T10:00:00Z"
                    }
                    
                    self.tasks[task_id] = task_update
                    
                    if websocket_callback:
                        await websocket_callback(task_update)
                    
                    await asyncio.sleep(0.01)  # Simulate processing
                
                # Final result
                result = {
                    "task_id": task_id,
                    "product_id": product_id,
                    "original_title": "Wireless Headphones",
                    "optimized_title": "Premium Wireless Bluetooth Headphones - Superior Sound Quality",
                    "seo_score": 92.5,
                    "keywords": ["wireless", "bluetooth", "headphones", "premium", "audio"],
                    "recommendations": [
                        "Title length optimized for SEO",
                        "Key benefits highlighted",
                        "Target keywords incorporated"
                    ]
                }
                
                self.tasks[task_id].update(result)
                return result
        
        async def test_optimization_with_websocket():
            engine = MockOptimizationEngine()
            updates_received = []
            
            async def websocket_callback(update):
                updates_received.append(update)
            
            result = await engine.optimize_product("prod_123", websocket_callback)
            
            assert len(updates_received) == 5  # 5 progress steps
            assert updates_received[0]["progress"] == 20
            assert updates_received[-1]["progress"] == 100
            assert result["seo_score"] == 92.5
            assert len(result["keywords"]) == 5
        
        asyncio.run(test_optimization_with_websocket())
    
    def test_real_time_analytics_websocket(self):
        """Test real-time analytics updates via WebSocket"""
        
        class MockAnalyticsEngine:
            def __init__(self):
                self.metrics = {
                    "total_optimizations": 0,
                    "avg_seo_score": 0.0,
                    "active_tasks": 0,
                    "completion_rate": 100.0
                }
            
            async def update_metrics(self, websocket_callback=None):
                """Simulate real-time metrics updates"""
                import random
                
                # Simulate metric changes
                self.metrics["total_optimizations"] += random.randint(1, 5)
                self.metrics["avg_seo_score"] = round(random.uniform(75.0, 95.0), 1)
                self.metrics["active_tasks"] = random.randint(0, 10)
                self.metrics["completion_rate"] = round(random.uniform(95.0, 100.0), 1)
                
                metrics_update = {
                    "type": "analytics_update",
                    "metrics": self.metrics.copy(),
                    "timestamp": "2025-01-27T10:00:00Z"
                }
                
                if websocket_callback:
                    await websocket_callback(metrics_update)
                
                return metrics_update
        
        async def test_analytics_updates():
            analytics = MockAnalyticsEngine()
            updates_received = []
            
            async def websocket_callback(update):
                updates_received.append(update)
            
            # Simulate multiple updates
            for _ in range(3):
                await analytics.update_metrics(websocket_callback)
                await asyncio.sleep(0.01)
            
            assert len(updates_received) == 3
            assert all(update["type"] == "analytics_update" for update in updates_received)
            assert all("metrics" in update for update in updates_received)
            
            # Verify metrics are changing
            first_metrics = updates_received[0]["metrics"]
            last_metrics = updates_received[-1]["metrics"]
            
            # At least one metric should have changed
            metrics_changed = any(
                first_metrics[key] != last_metrics[key] 
                for key in first_metrics.keys()
            )
            assert metrics_changed
        
        asyncio.run(test_analytics_updates())


if __name__ == "__main__":
    # Run quick tests
    test_websocket_basic_functionality()
    test_optimization_progress_websocket()
    print("✅ All WebSocket tests passed!")
