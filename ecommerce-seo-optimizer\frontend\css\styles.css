/* GridSpoke SEO Optimizer - Custom Styles */

/* Base styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Custom button styles */
.btn-gridspoke {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease-in-out;
}

.btn-gridspoke:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Loading spinner */
.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Progress bar animations */
.progress-bar {
    transition: width 0.3s ease-in-out;
}

/* Table styles */
.table-gridspoke {
    width: 100%;
    border-collapse: collapse;
}

.table-gridspoke th,
.table-gridspoke td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.table-gridspoke th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table-gridspoke tbody tr:hover {
    background-color: #f9fafb;
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-pending {
    background-color: #fef3c7;
    color: #d97706;
}

.status-running {
    background-color: #dbeafe;
    color: #2563eb;
}

.status-completed {
    background-color: #d1fae5;
    color: #059669;
}

.status-failed {
    background-color: #fee2e2;
    color: #dc2626;
}

/* Card hover effects */
.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
}

/* Notification styles */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 1rem;
    max-width: 400px;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification.notification-success {
    border-left: 4px solid #10b981;
}

.notification.notification-error {
    border-left: 4px solid #ef4444;
}

.notification.notification-warning {
    border-left: 4px solid #f59e0b;
}

.notification.notification-info {
    border-left: 4px solid #3b82f6;
}

/* Form validation styles */
.form-error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.form-success {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

/* Search and filter styles */
.search-container {
    position: relative;
}

.search-input {
    padding-left: 2.5rem;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-gridspoke {
        font-size: 0.875rem;
    }
    
    .table-gridspoke th,
    .table-gridspoke td {
        padding: 8px;
    }
    
    .notification {
        left: 1rem;
        right: 1rem;
        max-width: none;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        background-color: #111827;
        color: #f9fafb;
    }
    
    .dark-mode .table-gridspoke th {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode .table-gridspoke td {
        border-color: #374151;
    }
    
    .dark-mode .table-gridspoke tbody tr:hover {
        background-color: #1f2937;
    }
}

/* WebSocket connection status */
.ws-status {
    position: fixed;
    bottom: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 1000;
}

.ws-connected {
    background-color: #d1fae5;
    color: #059669;
    border: 1px solid #a7f3d0;
}

.ws-disconnected {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.ws-connecting {
    background-color: #fef3c7;
    color: #d97706;
    border: 1px solid #fde68a;
}

/* Pulse animation for loading states */
.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Chart container styles */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Custom dropdown styles */
.dropdown-menu {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 0.5rem 0;
    max-height: 200px;
    overflow-y: auto;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    color: #374151;
    text-decoration: none;
    transition: background-color 0.2s ease-in-out;
}

.dropdown-item:hover {
    background-color: #f9fafb;
    color: #111827;
}

/* Modal overlay */
.modal-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

/* Toast notifications positioning */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    pointer-events: none;
}

.toast {
    pointer-events: auto;
    margin-bottom: 0.5rem;
}
