            const forbiddenPrefixes = ['file://', 'http://', 'https://', '/', ''];
            for (const prefix of forbiddenPrefixes) {
                if (modelName.toLowerCase().startsWith(prefix)) {
                    return false;
                }
            }
            
            return true;
        },
    };
};

// Global init function for Alpine.js
window.init = function() {
    console.log('Global init called');
    // This will be called by Alpine.js x-init
};
