# GridSpoke Advanced SEO Tasks
# P    def generate_structured_data(self, product_id: str, schema_types: Optional[List[str]] = None) -> Dict[str, Any]:ase 7: Advanced SEO processing tasks for comprehensive optimization

from typing import Dict, Any, List, Optional, Union
import asyncio
import logging
from datetime import datetime, timedelta
import json

# Runtime imports - these will be available when deployed
try:
    from celery import Celery
    from workers.celery_app import celery_app
    from workers.utils.progress_tracker import track_progress
    from workers.utils.error_handler import handle_task_error
    from api.agents.product_optimizer import ProductOptimizer
    from api.agents.content_generator import ContentGenerator
    from api.services.ai_service import AIService
    from api.core.database import get_db
    from api.models.product import Product
    from api.models.optimization_job import OptimizationJob
    from api.models.generated_content import GeneratedContent
    from sqlalchemy import select, update
    from sqlalchemy.orm import selectinload
except ImportError:
    # These will be available at runtime when properly deployed
    pass

logger = logging.getLogger(__name__)

# =============================================================================
# STRUCTURED DATA GENERATION TASKS
# =============================================================================

@celery_app.task(bind=True, name="generate_structured_data")
def generate_structured_data(self, product_id: str, schema_types: List[str] = None) -> Dict[str, Any]:
    """
    Generate structured data (JSON-LD) for a product.
    
    Args:
        product_id: Product identifier
        schema_types: List of schema types to generate (Product, Review, Offer, etc.)
    
    Returns:
        Dict containing generated structured data
    """
    progress_tracker = ProgressTracker(self.request.id)
    error_handler = ErrorHandler(self.request.id)
    
    try:
        progress_tracker.update_progress(0, "Starting structured data generation")
        
        # Default schema types if not provided
        if not schema_types:
            schema_types = ["Product", "Offer", "Review", "BreadcrumbList"]
        
        async def _generate():
            async with get_db_session() as session:
                # Get product with related data
                stmt = select(Product).options(
                    selectinload(Product.store),
                    selectinload(Product.images),
                    selectinload(Product.reviews)
                ).where(Product.id == product_id)
                
                result = await session.execute(stmt)
                product = result.scalar_one_or_none()
                
                if not product:
                    raise ValueError(f"Product {product_id} not found")
                
                progress_tracker.update_progress(25, "Product data loaded")
                
                # Initialize AI service
                ai_service = AIService()
                
                structured_data = {}
                total_schemas = len(schema_types)
                
                for i, schema_type in enumerate(schema_types):
                    progress_tracker.update_progress(
                        25 + (i * 50 // total_schemas), 
                        f"Generating {schema_type} schema"
                    )
                    
                    if schema_type == "Product":
                        structured_data["product"] = await _generate_product_schema(product, ai_service)
                    elif schema_type == "Offer":
                        structured_data["offer"] = await _generate_offer_schema(product, ai_service)
                    elif schema_type == "Review":
                        structured_data["review"] = await _generate_review_schema(product, ai_service)
                    elif schema_type == "BreadcrumbList":
                        structured_data["breadcrumb"] = await _generate_breadcrumb_schema(product, ai_service)
                    elif schema_type == "FAQ":
                        structured_data["faq"] = await _generate_faq_schema(product, ai_service)
                
                progress_tracker.update_progress(75, "Saving structured data")
                
                # Save to database
                content_record = GeneratedContent(
                    product_id=product_id,
                    content_type="structured_data",
                    content=json.dumps(structured_data),
                    ai_model="gridspoke-seo-engine",
                    tokens_used=sum(schema.get("tokens_used", 0) for schema in structured_data.values()),
                    metadata={
                        "schema_types": schema_types,
                        "generated_at": datetime.utcnow().isoformat()
                    }
                )
                
                session.add(content_record)
                await session.commit()
                
                progress_tracker.update_progress(100, "Structured data generation complete")
                
                return {
                    "status": "completed",
                    "product_id": product_id,
                    "structured_data": structured_data,
                    "content_id": str(content_record.id),
                    "schema_types": schema_types
                }
        
        return asyncio.run(_generate())
        
    except Exception as e:
        error_handler.handle_error(e, {
            "product_id": product_id,
            "schema_types": schema_types
        })
        progress_tracker.update_progress(0, f"Error: {str(e)}", status="failed")
        raise

async def _generate_product_schema(product: Product, ai_service: AIService) -> Dict[str, Any]:
    """Generate Product schema markup."""
    
    # Base product schema
    schema = {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": product.name,
        "description": product.description,
        "sku": product.sku,
        "brand": {
            "@type": "Brand",
            "name": product.brand or product.store.name
        }
    }
    
    # Add images
    if product.images:
        schema["image"] = [img.url for img in product.images]
    
    # Add offers
    if product.price:
        schema["offers"] = {
            "@type": "Offer",
            "url": f"{product.store.url}/products/{product.slug}",
            "priceCurrency": "USD",
            "price": str(product.price),
            "availability": "https://schema.org/InStock" if product.stock_quantity > 0 else "https://schema.org/OutOfStock"
        }
    
    # Add category
    if product.category:
        schema["category"] = product.category
    
    # AI-enhance missing fields
    if not product.description or len(product.description) < 100:
        enhanced_desc = await ai_service.enhance_product_description(
            product.name, 
            product.short_description or "",
            product.category
        )
        schema["description"] = enhanced_desc["description"]
    
    return {"schema": schema, "tokens_used": 0}

async def _generate_offer_schema(product: Product, ai_service: AIService) -> Dict[str, Any]:
    """Generate Offer schema markup."""
    
    schema = {
        "@context": "https://schema.org/",
        "@type": "Offer",
        "url": f"{product.store.url}/products/{product.slug}",
        "priceCurrency": "USD",
        "price": str(product.price) if product.price else "0",
        "availability": "https://schema.org/InStock" if product.stock_quantity > 0 else "https://schema.org/OutOfStock",
        "seller": {
            "@type": "Organization",
            "name": product.store.name,
            "url": product.store.url
        }
    }
    
    # Add sale price if available
    if product.sale_price and product.sale_price < product.price:
        schema["priceValidUntil"] = (datetime.utcnow() + timedelta(days=30)).isoformat()
    
    return {"schema": schema, "tokens_used": 0}

async def _generate_review_schema(product: Product, ai_service: AIService) -> Dict[str, Any]:
    """Generate Review schema markup."""
    
    if not product.reviews:
        return {"schema": None, "tokens_used": 0}
    
    reviews = []
    for review in product.reviews[:5]:  # Top 5 reviews
        reviews.append({
            "@type": "Review",
            "author": {
                "@type": "Person",
                "name": review.author_name
            },
            "reviewRating": {
                "@type": "Rating",
                "ratingValue": review.rating,
                "bestRating": "5"
            },
            "reviewBody": review.content
        })
    
    # Calculate aggregate rating
    total_rating = sum(r.rating for r in product.reviews)
    avg_rating = total_rating / len(product.reviews)
    
    schema = {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": product.name,
        "review": reviews,
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": round(avg_rating, 1),
            "reviewCount": len(product.reviews)
        }
    }
    
    return {"schema": schema, "tokens_used": 0}

async def _generate_breadcrumb_schema(product: Product, ai_service: AIService) -> Dict[str, Any]:
    """Generate BreadcrumbList schema markup."""
    
    items = [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": product.store.url
        }
    ]
    
    # Add category breadcrumbs
    if product.category:
        items.append({
            "@type": "ListItem",
            "position": 2,
            "name": product.category,
            "item": f"{product.store.url}/category/{product.category.lower().replace(' ', '-')}"
        })
    
    # Add product
    items.append({
        "@type": "ListItem",
        "position": len(items) + 1,
        "name": product.name,
        "item": f"{product.store.url}/products/{product.slug}"
    })
    
    schema = {
        "@context": "https://schema.org/",
        "@type": "BreadcrumbList",
        "itemListElement": items
    }
    
    return {"schema": schema, "tokens_used": 0}

async def _generate_faq_schema(product: Product, ai_service: AIService) -> Dict[str, Any]:
    """Generate FAQ schema markup using AI."""
    
    # Generate FAQs using AI
    faq_prompt = f"""
    Generate 5 frequently asked questions and answers for this product:
    
    Product: {product.name}
    Description: {product.description}
    Category: {product.category}
    Price: ${product.price}
    
    Make the FAQs relevant to potential buyers and include information about:
    - Product features and benefits
    - Shipping and delivery
    - Returns and warranty
    - Compatibility or sizing
    - Usage instructions
    
    Format as JSON array with 'question' and 'answer' fields.
    """
    
    faqs_response = await ai_service.generate_content(
        prompt=faq_prompt,
        content_type="faq",
        max_tokens=800
    )
    
    try:
        faqs_data = json.loads(faqs_response["content"])
        
        faq_items = []
        for i, faq in enumerate(faqs_data[:5]):
            faq_items.append({
                "@type": "Question",
                "name": faq["question"],
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": faq["answer"]
                }
            })
        
        schema = {
            "@context": "https://schema.org/",
            "@type": "FAQPage",
            "mainEntity": faq_items
        }
        
        return {"schema": schema, "tokens_used": faqs_response.get("tokens_used", 0)}
        
    except (json.JSONDecodeError, KeyError) as e:
        logger.warning(f"Failed to parse FAQ response for product {product.id}: {e}")
        return {"schema": None, "tokens_used": 0}

# =============================================================================
# OPEN GRAPH OPTIMIZATION TASKS
# =============================================================================

@celery_app.task(bind=True, name="optimize_open_graph_tags")
def optimize_open_graph_tags(self, product_id: str, target_platforms: List[str] = None) -> Dict[str, Any]:
    """
    Generate optimized Open Graph tags for social media platforms.
    
    Args:
        product_id: Product identifier
        target_platforms: List of platforms to optimize for (facebook, twitter, pinterest, etc.)
    
    Returns:
        Dict containing optimized Open Graph tags
    """
    progress_tracker = ProgressTracker(self.request.id)
    error_handler = ErrorHandler(self.request.id)
    
    try:
        progress_tracker.update_progress(0, "Starting Open Graph optimization")
        
        if not target_platforms:
            target_platforms = ["facebook", "twitter", "pinterest", "linkedin"]
        
        async def _optimize():
            async with get_db_session() as session:
                # Get product
                stmt = select(Product).options(
                    selectinload(Product.store),
                    selectinload(Product.images)
                ).where(Product.id == product_id)
                
                result = await session.execute(stmt)
                product = result.scalar_one_or_none()
                
                if not product:
                    raise ValueError(f"Product {product_id} not found")
                
                progress_tracker.update_progress(25, "Product loaded")
                
                ai_service = AIService()
                og_tags = {}
                
                for i, platform in enumerate(target_platforms):
                    progress_tracker.update_progress(
                        25 + (i * 50 // len(target_platforms)),
                        f"Optimizing for {platform}"
                    )
                    
                    if platform == "facebook":
                        og_tags["facebook"] = await _generate_facebook_og_tags(product, ai_service)
                    elif platform == "twitter":
                        og_tags["twitter"] = await _generate_twitter_card_tags(product, ai_service)
                    elif platform == "pinterest":
                        og_tags["pinterest"] = await _generate_pinterest_tags(product, ai_service)
                    elif platform == "linkedin":
                        og_tags["linkedin"] = await _generate_linkedin_tags(product, ai_service)
                
                progress_tracker.update_progress(75, "Saving Open Graph data")
                
                # Save to database
                content_record = GeneratedContent(
                    product_id=product_id,
                    content_type="open_graph_tags",
                    content=json.dumps(og_tags),
                    ai_model="gridspoke-seo-engine",
                    tokens_used=sum(tags.get("tokens_used", 0) for tags in og_tags.values()),
                    metadata={
                        "platforms": target_platforms,
                        "generated_at": datetime.utcnow().isoformat()
                    }
                )
                
                session.add(content_record)
                await session.commit()
                
                progress_tracker.update_progress(100, "Open Graph optimization complete")
                
                return {
                    "status": "completed",
                    "product_id": product_id,
                    "og_tags": og_tags,
                    "content_id": str(content_record.id),
                    "platforms": target_platforms
                }
        
        return asyncio.run(_optimize())
        
    except Exception as e:
        error_handler.handle_error(e, {
            "product_id": product_id,
            "target_platforms": target_platforms
        })
        progress_tracker.update_progress(0, f"Error: {str(e)}", status="failed")
        raise

async def _generate_facebook_og_tags(product: Product, ai_service: AIService) -> Dict[str, Any]:
    """Generate Facebook-optimized Open Graph tags."""
    
    # Generate engaging description for Facebook
    fb_prompt = f"""
    Create an engaging Facebook post description for this product that encourages clicks and sharing:
    
    Product: {product.name}
    Description: {product.description}
    Price: ${product.price}
    
    Requirements:
    - 125 characters or less
    - Include emotional appeal
    - Mention key benefit
    - Include call-to-action
    - Use engaging language
    
    Return only the description text.
    """
    
    fb_description = await ai_service.generate_content(
        prompt=fb_prompt,
        content_type="social_media",
        max_tokens=100
    )
    
    tags = {
        "og:title": product.name[:60],  # Facebook truncates at ~60 chars
        "og:description": fb_description["content"][:125],
        "og:type": "product",
        "og:url": f"{product.store.url}/products/{product.slug}",
        "og:site_name": product.store.name,
        "product:price:amount": str(product.price) if product.price else "0",
        "product:price:currency": "USD",
        "product:availability": "in stock" if product.stock_quantity > 0 else "out of stock"
    }
    
    # Add image if available
    if product.images:
        tags["og:image"] = product.images[0].url
        tags["og:image:width"] = "1200"
        tags["og:image:height"] = "630"
        tags["og:image:alt"] = product.images[0].alt_text or product.name
    
    return {"tags": tags, "tokens_used": fb_description.get("tokens_used", 0)}

async def _generate_twitter_card_tags(product: Product, ai_service: AIService) -> Dict[str, Any]:
    """Generate Twitter Card tags."""
    
    # Generate Twitter-optimized description
    twitter_prompt = f"""
    Create a Twitter-optimized description for this product:
    
    Product: {product.name}
    Description: {product.description}
    Price: ${product.price}
    
    Requirements:
    - 200 characters or less
    - Include hashtags
    - Mention key feature
    - Twitter-friendly tone
    
    Return only the description text.
    """
    
    twitter_description = await ai_service.generate_content(
        prompt=twitter_prompt,
        content_type="social_media",
        max_tokens=150
    )
    
    tags = {
        "twitter:card": "summary_large_image",
        "twitter:title": product.name[:70],  # Twitter truncates at ~70 chars
        "twitter:description": twitter_description["content"][:200],
        "twitter:site": f"@{product.store.name.replace(' ', '').lower()}",
    }
    
    # Add image if available
    if product.images:
        tags["twitter:image"] = product.images[0].url
        tags["twitter:image:alt"] = product.images[0].alt_text or product.name
    
    return {"tags": tags, "tokens_used": twitter_description.get("tokens_used", 0)}

async def _generate_pinterest_tags(product: Product, ai_service: AIService) -> Dict[str, Any]:
    """Generate Pinterest-optimized tags."""
    
    # Pinterest uses Open Graph tags but with some specific considerations
    pinterest_prompt = f"""
    Create a Pinterest-optimized description for this product:
    
    Product: {product.name}
    Description: {product.description}
    Category: {product.category}
    
    Requirements:
    - 500 characters or less
    - Include relevant keywords
    - Inspirational tone
    - DIY or lifestyle angle if applicable
    
    Return only the description text.
    """
    
    pinterest_description = await ai_service.generate_content(
        prompt=pinterest_prompt,
        content_type="social_media",
        max_tokens=200
    )
    
    tags = {
        "og:title": product.name,
        "og:description": pinterest_description["content"][:500],
        "og:type": "product",
        "article:author": product.store.name,
        "og:rich_attachment": "true"
    }
    
    # Pinterest prefers vertical images
    if product.images:
        tags["og:image"] = product.images[0].url
        tags["og:image:width"] = "735"
        tags["og:image:height"] = "1102"
    
    return {"tags": tags, "tokens_used": pinterest_description.get("tokens_used", 0)}

async def _generate_linkedin_tags(product: Product, ai_service: AIService) -> Dict[str, Any]:
    """Generate LinkedIn-optimized tags."""
    
    # LinkedIn prefers professional, business-focused content
    linkedin_prompt = f"""
    Create a LinkedIn-optimized description for this product:
    
    Product: {product.name}
    Description: {product.description}
    
    Requirements:
    - Professional tone
    - Business benefits focus
    - 300 characters or less
    - Include value proposition
    
    Return only the description text.
    """
    
    linkedin_description = await ai_service.generate_content(
        prompt=linkedin_prompt,
        content_type="social_media",
        max_tokens=150
    )
    
    tags = {
        "og:title": product.name,
        "og:description": linkedin_description["content"][:300],
        "og:type": "website",
        "og:site_name": product.store.name
    }
    
    if product.images:
        tags["og:image"] = product.images[0].url
        tags["og:image:width"] = "1200"
        tags["og:image:height"] = "627"
    
    return {"tags": tags, "tokens_used": linkedin_description.get("tokens_used", 0)}

# =============================================================================
# CANONICAL URL OPTIMIZATION TASKS
# =============================================================================

@celery_app.task(bind=True, name="optimize_canonical_urls")
def optimize_canonical_urls(self, store_id: str) -> Dict[str, Any]:
    """
    Optimize canonical URLs for all products in a store to prevent duplicate content.
    
    Args:
        store_id: Store identifier
    
    Returns:
        Dict containing optimization results
    """
    progress_tracker = ProgressTracker(self.request.id)
    error_handler = ErrorHandler(self.request.id)
    
    try:
        progress_tracker.update_progress(0, "Starting canonical URL optimization")
        
        async def _optimize():
            async with get_db_session() as session:
                # Get all products for the store
                stmt = select(Product).where(Product.store_id == store_id)
                result = await session.execute(stmt)
                products = result.scalars().all()
                
                if not products:
                    raise ValueError(f"No products found for store {store_id}")
                
                progress_tracker.update_progress(25, f"Found {len(products)} products")
                
                canonical_urls = {}
                duplicate_issues = []
                
                for i, product in enumerate(products):
                    progress_tracker.update_progress(
                        25 + (i * 50 // len(products)),
                        f"Processing product {i+1}/{len(products)}"
                    )
                    
                    # Generate canonical URL
                    canonical_url = _generate_canonical_url(product)
                    canonical_urls[product.id] = canonical_url
                    
                    # Check for potential duplicates
                    similar_products = [
                        p for p in products 
                        if p.id != product.id and _are_products_similar(product, p)
                    ]
                    
                    if similar_products:
                        duplicate_issues.append({
                            "primary_product": {
                                "id": product.id,
                                "name": product.name,
                                "canonical_url": canonical_url
                            },
                            "similar_products": [
                                {
                                    "id": p.id,
                                    "name": p.name,
                                    "similarity_score": _calculate_similarity_score(product, p)
                                }
                                for p in similar_products
                            ]
                        })
                
                progress_tracker.update_progress(75, "Saving canonical URL data")
                
                # Save optimization results
                optimization_data = {
                    "canonical_urls": canonical_urls,
                    "duplicate_issues": duplicate_issues,
                    "total_products": len(products),
                    "issues_found": len(duplicate_issues)
                }
                
                # Update products with canonical URLs
                for product_id, canonical_url in canonical_urls.items():
                    await session.execute(
                        update(Product)
                        .where(Product.id == product_id)
                        .values(canonical_url=canonical_url)
                    )
                
                await session.commit()
                
                progress_tracker.update_progress(100, "Canonical URL optimization complete")
                
                return {
                    "status": "completed",
                    "store_id": store_id,
                    "optimization_data": optimization_data
                }
        
        return asyncio.run(_optimize())
        
    except Exception as e:
        error_handler.handle_error(e, {"store_id": store_id})
        progress_tracker.update_progress(0, f"Error: {str(e)}", status="failed")
        raise

def _generate_canonical_url(product: Product) -> str:
    """Generate canonical URL for a product."""
    
    # Clean product name for URL
    clean_name = product.name.lower()
    clean_name = ''.join(c if c.isalnum() or c in '-_' else '-' for c in clean_name)
    clean_name = '-'.join(word for word in clean_name.split('-') if word)
    
    # Include SKU if available for uniqueness
    if product.sku:
        clean_sku = product.sku.lower().replace(' ', '-')
        return f"{product.store.url}/products/{clean_name}-{clean_sku}"
    else:
        return f"{product.store.url}/products/{clean_name}"

def _are_products_similar(product1: Product, product2: Product) -> bool:
    """Check if two products are similar enough to potentially cause duplicate content issues."""
    
    # Check name similarity
    name_similarity = _calculate_text_similarity(product1.name, product2.name)
    if name_similarity > 0.8:
        return True
    
    # Check description similarity
    if product1.description and product2.description:
        desc_similarity = _calculate_text_similarity(product1.description, product2.description)
        if desc_similarity > 0.7:
            return True
    
    # Check category and price similarity
    if (product1.category == product2.category and 
        product1.price and product2.price and
        abs(product1.price - product2.price) < (product1.price * 0.1)):  # Within 10% price range
        return True
    
    return False

def _calculate_similarity_score(product1: Product, product2: Product) -> float:
    """Calculate overall similarity score between two products."""
    
    scores = []
    
    # Name similarity
    name_sim = _calculate_text_similarity(product1.name, product2.name)
    scores.append(name_sim * 0.4)  # 40% weight
    
    # Description similarity
    if product1.description and product2.description:
        desc_sim = _calculate_text_similarity(product1.description, product2.description)
        scores.append(desc_sim * 0.3)  # 30% weight
    
    # Category similarity
    if product1.category and product2.category:
        cat_sim = 1.0 if product1.category == product2.category else 0.0
        scores.append(cat_sim * 0.2)  # 20% weight
    
    # Price similarity
    if product1.price and product2.price:
        price_diff = abs(product1.price - product2.price) / max(product1.price, product2.price)
        price_sim = max(0, 1 - price_diff)
        scores.append(price_sim * 0.1)  # 10% weight
    
    return sum(scores) / len(scores) if scores else 0.0

def _calculate_text_similarity(text1: str, text2: str) -> float:
    """Calculate text similarity using simple token overlap."""
    
    if not text1 or not text2:
        return 0.0
    
    # Simple tokenization
    tokens1 = set(text1.lower().split())
    tokens2 = set(text2.lower().split())
    
    # Calculate Jaccard similarity
    intersection = tokens1.intersection(tokens2)
    union = tokens1.union(tokens2)
    
    return len(intersection) / len(union) if union else 0.0

# =============================================================================
# XML SITEMAP GENERATION TASKS
# =============================================================================

@celery_app.task(bind=True, name="generate_xml_sitemap")
def generate_xml_sitemap(self, store_id: str, include_images: bool = True) -> Dict[str, Any]:
    """
    Generate XML sitemap for a store's products.
    
    Args:
        store_id: Store identifier
        include_images: Whether to include image sitemaps
    
    Returns:
        Dict containing sitemap generation results
    """
    progress_tracker = ProgressTracker(self.request.id)
    error_handler = ErrorHandler(self.request.id)
    
    try:
        progress_tracker.update_progress(0, "Starting XML sitemap generation")
        
        async def _generate():
            async with get_db_session() as session:
                # Get all published products for the store
                stmt = select(Product).options(
                    selectinload(Product.images)
                ).where(
                    Product.store_id == store_id,
                    Product.status == "published"
                )
                
                result = await session.execute(stmt)
                products = result.scalars().all()
                
                progress_tracker.update_progress(25, f"Found {len(products)} products")
                
                # Generate main sitemap
                main_sitemap = _generate_main_sitemap(products)
                
                progress_tracker.update_progress(50, "Generated main sitemap")
                
                # Generate image sitemap if requested
                image_sitemap = None
                if include_images:
                    image_sitemap = _generate_image_sitemap(products)
                    progress_tracker.update_progress(75, "Generated image sitemap")
                
                # Save sitemaps
                sitemap_data = {
                    "main_sitemap": main_sitemap,
                    "image_sitemap": image_sitemap,
                    "product_count": len(products),
                    "generated_at": datetime.utcnow().isoformat()
                }
                
                # TODO: Save to file system or database
                # For now, just return the data
                
                progress_tracker.update_progress(100, "XML sitemap generation complete")
                
                return {
                    "status": "completed",
                    "store_id": store_id,
                    "sitemap_data": sitemap_data
                }
        
        return asyncio.run(_generate())
        
    except Exception as e:
        error_handler.handle_error(e, {"store_id": store_id})
        progress_tracker.update_progress(0, f"Error: {str(e)}", status="failed")
        raise

def _generate_main_sitemap(products: List[Product]) -> str:
    """Generate main XML sitemap."""
    
    from xml.etree.ElementTree import Element, SubElement, tostring
    from xml.dom import minidom
    
    # Create root element
    urlset = Element("urlset")
    urlset.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
    
    for product in products:
        url = SubElement(urlset, "url")
        
        # Location
        loc = SubElement(url, "loc")
        loc.text = f"{product.store.url}/products/{product.slug}"
        
        # Last modified
        lastmod = SubElement(url, "lastmod")
        lastmod.text = product.updated_at.strftime("%Y-%m-%d")
        
        # Change frequency (products usually change monthly)
        changefreq = SubElement(url, "changefreq")
        changefreq.text = "monthly"
        
        # Priority (products are important)
        priority = SubElement(url, "priority")
        priority.text = "0.8"
    
    # Pretty print
    rough_string = tostring(urlset, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    
    return reparsed.toprettyxml(indent="  ")

def _generate_image_sitemap(products: List[Product]) -> str:
    """Generate image XML sitemap."""
    
    from xml.etree.ElementTree import Element, SubElement, tostring
    from xml.dom import minidom
    
    # Create root element
    urlset = Element("urlset")
    urlset.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
    urlset.set("xmlns:image", "http://www.google.com/schemas/sitemap-image/1.1")
    
    for product in products:
        if not product.images:
            continue
            
        url = SubElement(urlset, "url")
        
        # Location
        loc = SubElement(url, "loc")
        loc.text = f"{product.store.url}/products/{product.slug}"
        
        # Add images
        for image in product.images:
            image_elem = SubElement(url, "image:image")
            
            # Image location
            image_loc = SubElement(image_elem, "image:loc")
            image_loc.text = image.url
            
            # Image caption (alt text)
            if image.alt_text:
                image_caption = SubElement(image_elem, "image:caption")
                image_caption.text = image.alt_text
            
            # Image title
            image_title = SubElement(image_elem, "image:title")
            image_title.text = f"{product.name} - Image"
    
    # Pretty print
    rough_string = tostring(urlset, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    
    return reparsed.toprettyxml(indent="  ")

# =============================================================================
# TASK REGISTRATION
# =============================================================================

# Register all tasks with Celery
__all__ = [
    'generate_structured_data',
    'optimize_open_graph_tags', 
    'optimize_canonical_urls',
    'generate_xml_sitemap'
]
