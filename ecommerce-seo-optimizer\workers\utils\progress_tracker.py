"""
Progress Tracking Utilities for GridSpoke Workers
Handles real-time progress updates for long-running tasks.
"""

import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from workers.utils.redis_client import get_redis_client

logger = logging.getLogger(__name__)

def track_progress(task_id: str, current: int, total: int, status_message: str = "Processing"):
    """
    Track task progress and store in Redis for real-time dashboard updates.
    
    Args:
        task_id: Celery task ID
        current: Current progress count
        total: Total items to process
        status_message: Human-readable status message
    """
    try:
        # Calculate percentage
        percentage = int((current / total) * 100) if total > 0 else 0
        
        # Prepare progress data
        progress_data = {
            'task_id': task_id,
            'current': current,
            'total': total,
            'percentage': percentage,
            'status': status_message,
            'updated_at': datetime.utcnow().isoformat()
        }
        
        # Store in Redis with 1 hour expiration
        redis_client = get_redis_client()
        redis_client.setex(
            f"task_progress:{task_id}",
            3600,  # 1 hour
            json.dumps(progress_data)
        )
        
        # Also publish to Redis pub/sub for WebSocket broadcasting
        redis_client.publish(
            f"task_updates:{task_id}",
            json.dumps({
                'type': 'progress_update',
                'data': progress_data
            })
        )
        
        logger.debug(f"Progress updated for task {task_id}: {current}/{total} ({percentage}%)")
        
    except Exception as e:
        logger.error(f"Error tracking progress for task {task_id}: {str(e)}")

def get_task_progress(task_id: str) -> Optional[Dict[str, Any]]:
    """
    Get current progress for a task.
    
    Args:
        task_id: Celery task ID
    
    Returns:
        Dict containing progress data or None if not found
    """
    try:
        redis_client = get_redis_client()
        progress_data = redis_client.get(f"task_progress:{task_id}")
        
        if progress_data:
            return json.loads(progress_data)
        
        return None
        
    except Exception as e:
        logger.error(f"Error getting progress for task {task_id}: {str(e)}")
        return None

def update_task_status(task_id: str, status: str, details: str = None):
    """
    Update task status (started, completed, failed, etc.).
    
    Args:
        task_id: Celery task ID
        status: Task status (pending, progress, success, failure)
        details: Additional details or error message
    """
    try:
        # Prepare status data
        status_data = {
            'task_id': task_id,
            'status': status,
            'details': details,
            'updated_at': datetime.utcnow().isoformat()
        }
        
        # Store in Redis
        redis_client = get_redis_client()
        redis_client.setex(
            f"task_status:{task_id}",
            7200,  # 2 hours
            json.dumps(status_data)
        )
        
        # Publish status update
        redis_client.publish(
            f"task_updates:{task_id}",
            json.dumps({
                'type': 'status_update',
                'data': status_data
            })
        )
        
        logger.info(f"Status updated for task {task_id}: {status}")
        
    except Exception as e:
        logger.error(f"Error updating status for task {task_id}: {str(e)}")

def get_task_status(task_id: str) -> Optional[Dict[str, Any]]:
    """
    Get current status for a task.
    
    Args:
        task_id: Celery task ID
    
    Returns:
        Dict containing status data or None if not found
    """
    try:
        redis_client = get_redis_client()
        status_data = redis_client.get(f"task_status:{task_id}")
        
        if status_data:
            return json.loads(status_data)
        
        return None
        
    except Exception as e:
        logger.error(f"Error getting status for task {task_id}: {str(e)}")
        return None

def start_task_tracking(task_id: str, task_type: str, total_items: int, metadata: Dict[str, Any] = None):
    """
    Initialize tracking for a new task.
    
    Args:
        task_id: Celery task ID
        task_type: Type of task (optimization, content_generation, etc.)
        total_items: Total number of items to process
        metadata: Additional task metadata
    """
    try:
        # Initialize progress
        track_progress(task_id, 0, total_items, "Task started")
        
        # Set initial status
        update_task_status(task_id, 'started', f"Started {task_type} task")
        
        # Store task metadata
        task_metadata = {
            'task_id': task_id,
            'task_type': task_type,
            'total_items': total_items,
            'started_at': datetime.utcnow().isoformat(),
            'metadata': metadata or {}
        }
        
        redis_client = get_redis_client()
        redis_client.setex(
            f"task_metadata:{task_id}",
            7200,  # 2 hours
            json.dumps(task_metadata)
        )
        
        logger.info(f"Started tracking for task {task_id} ({task_type})")
        
    except Exception as e:
        logger.error(f"Error starting task tracking for {task_id}: {str(e)}")

def complete_task_tracking(task_id: str, result: Dict[str, Any]):
    """
    Mark task as completed and store final results.
    
    Args:
        task_id: Celery task ID
        result: Task completion result
    """
    try:
        # Update status to completed
        update_task_status(task_id, 'completed', 'Task completed successfully')
        
        # Store final result
        completion_data = {
            'task_id': task_id,
            'completed_at': datetime.utcnow().isoformat(),
            'result': result
        }
        
        redis_client = get_redis_client()
        redis_client.setex(
            f"task_result:{task_id}",
            7200,  # 2 hours
            json.dumps(completion_data)
        )
        
        # Publish completion event
        redis_client.publish(
            f"task_updates:{task_id}",
            json.dumps({
                'type': 'task_completed',
                'data': completion_data
            })
        )
        
        logger.info(f"Completed tracking for task {task_id}")
        
    except Exception as e:
        logger.error(f"Error completing task tracking for {task_id}: {str(e)}")

def fail_task_tracking(task_id: str, error_message: str, error_details: Dict[str, Any] = None):
    """
    Mark task as failed and store error information.
    
    Args:
        task_id: Celery task ID
        error_message: Error message
        error_details: Additional error details
    """
    try:
        # Update status to failed
        update_task_status(task_id, 'failed', error_message)
        
        # Store error details
        error_data = {
            'task_id': task_id,
            'failed_at': datetime.utcnow().isoformat(),
            'error_message': error_message,
            'error_details': error_details or {}
        }
        
        redis_client = get_redis_client()
        redis_client.setex(
            f"task_error:{task_id}",
            7200,  # 2 hours
            json.dumps(error_data)
        )
        
        # Publish failure event
        redis_client.publish(
            f"task_updates:{task_id}",
            json.dumps({
                'type': 'task_failed',
                'data': error_data
            })
        )
        
        logger.error(f"Failed tracking for task {task_id}: {error_message}")
        
    except Exception as e:
        logger.error(f"Error failing task tracking for {task_id}: {str(e)}")

def get_all_task_info(task_id: str) -> Dict[str, Any]:
    """
    Get comprehensive information about a task.
    
    Args:
        task_id: Celery task ID
    
    Returns:
        Dict containing all available task information
    """
    try:
        redis_client = get_redis_client()
        
        # Get all task-related data
        progress = get_task_progress(task_id)
        status = get_task_status(task_id)
        
        metadata_data = redis_client.get(f"task_metadata:{task_id}")
        metadata = json.loads(metadata_data) if metadata_data else None
        
        result_data = redis_client.get(f"task_result:{task_id}")
        result = json.loads(result_data) if result_data else None
        
        error_data = redis_client.get(f"task_error:{task_id}")
        error = json.loads(error_data) if error_data else None
        
        return {
            'task_id': task_id,
            'progress': progress,
            'status': status,
            'metadata': metadata,
            'result': result,
            'error': error
        }
        
    except Exception as e:
        logger.error(f"Error getting task info for {task_id}: {str(e)}")
        return {'task_id': task_id, 'error': 'Failed to retrieve task information'}

def cleanup_task_tracking(task_id: str):
    """
    Clean up all tracking data for a completed task.
    
    Args:
        task_id: Celery task ID
    """
    try:
        redis_client = get_redis_client()
        
        # Define all possible keys for this task
        keys_to_delete = [
            f"task_progress:{task_id}",
            f"task_status:{task_id}",
            f"task_metadata:{task_id}",
            f"task_result:{task_id}",
            f"task_error:{task_id}"
        ]
        
        # Delete all keys
        for key in keys_to_delete:
            redis_client.delete(key)
        
        logger.info(f"Cleaned up tracking data for task {task_id}")
        
    except Exception as e:
        logger.error(f"Error cleaning up task tracking for {task_id}: {str(e)}")

def get_active_tasks() -> List[Dict[str, Any]]:
    """
    Get list of all active tasks being tracked.
    
    Returns:
        List of active task information
    """
    try:
        redis_client = get_redis_client()
        
        # Find all task progress keys
        progress_keys = redis_client.keys("task_progress:*")
        
        active_tasks = []
        
        for key in progress_keys:
            try:
                task_id = key.decode('utf-8').split(':')[1]
                task_info = get_all_task_info(task_id)
                
                # Only include tasks that are still active
                if task_info.get('status', {}).get('status') not in ['completed', 'failed']:
                    active_tasks.append(task_info)
                    
            except Exception as e:
                logger.error(f"Error processing active task key {key}: {str(e)}")
                continue
        
        return active_tasks
        
    except Exception as e:
        logger.error(f"Error getting active tasks: {str(e)}")
        return []
