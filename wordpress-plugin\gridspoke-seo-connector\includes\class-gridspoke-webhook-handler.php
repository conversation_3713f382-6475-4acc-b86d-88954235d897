<?php
/**
 * Webhook Handler for GridSpoke SEO Connector
 * 
 * Handles incoming webhooks from GridSpoke backend
 * when optimization tasks are completed.
 */

if (!defined('ABSPATH')) {
    exit;
}

class GridSpoke_Webhook_Handler {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Nothing to initialize here since hooks are registered in main plugin
    }
    
    /**
     * Handle incoming webhook
     */
    public static function handle_webhook($request) {
        $instance = self::get_instance();
        
        // Get webhook payload
        $payload = $request->get_json_params();
        
        if (empty($payload)) {
            GridSpoke_Logger::log('Webhook received with empty payload', 'warning');
            return new WP_REST_Response(array(
                'error' => 'Empty payload'
            ), 400);
        }
        
        // Log webhook reception
        GridSpoke_Logger::log('Webhook received: ' . wp_json_encode(array(
            'event' => $payload['event'] ?? 'unknown',
            'optimization_id' => $payload['optimization_id'] ?? 'unknown'
        )), 'info');
        
        // Route to appropriate handler based on event type
        $event_type = $payload['event'] ?? '';
        
        switch ($event_type) {
            case 'optimization.completed':
                return $instance->handle_optimization_completed($payload);
                
            case 'optimization.failed':
                return $instance->handle_optimization_failed($payload);
                
            case 'sync.completed':
                return $instance->handle_sync_completed($payload);
                
            case 'sync.failed':
                return $instance->handle_sync_failed($payload);
                
            default:
                GridSpoke_Logger::log('Unknown webhook event: ' . $event_type, 'warning');
                return new WP_REST_Response(array(
                    'error' => 'Unknown event type'
                ), 400);
        }
    }
    
    /**
     * Verify webhook signature
     */
    public static function verify_webhook_signature($request) {
        $settings = GridSpoke_SEO_Connector::get_settings();
        $webhook_secret = $settings['webhook_secret'] ?? '';
        
        if (empty($webhook_secret)) {
            GridSpoke_Logger::log('Webhook secret not configured', 'error');
            return false;
        }
        
        // Get signature from header
        $signature_header = $request->get_header('X-GridSpoke-Signature');
        
        if (empty($signature_header)) {
            GridSpoke_Logger::log('Webhook signature header missing', 'warning');
            return false;
        }
        
        // Get raw body
        $raw_body = $request->get_body();
        
        // Calculate expected signature
        $expected_signature = hash_hmac('sha256', $raw_body, $webhook_secret);
        
        // Compare signatures
        if (!hash_equals($expected_signature, $signature_header)) {
            GridSpoke_Logger::log('Webhook signature verification failed', 'warning');
            return false;
        }
        
        return true;
    }
    
    /**
     * Handle optimization completed webhook
     */
    private function handle_optimization_completed($payload) {
        $optimization_id = $payload['optimization_id'] ?? '';
        $optimized_content = $payload['optimized_content'] ?? array();
        $products = $payload['products'] ?? array();
        
        if (empty($optimization_id) || empty($products)) {
            return new WP_REST_Response(array(
                'error' => 'Missing required data'
            ), 400);
        }
        
        $updated_count = 0;
        $errors = array();
        
        foreach ($products as $product_data) {
            $product_id = $product_data['id'] ?? '';
            $product_type = $product_data['type'] ?? 'woocommerce';
            $content = $product_data['optimized_content'] ?? array();
            
            if (empty($product_id) || empty($content)) {
                continue;
            }
            
            // Update product content based on type
            $success = false;
            
            if ($product_type === 'woocommerce' && class_exists('WooCommerce')) {
                $woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
                $success = $woo_integration->update_product_content($product_id, $content);
            } elseif ($product_type === 'surecart' && defined('SURECART_PLUGIN_FILE')) {
                $sure_integration = GridSpoke_SureCart_Integration::get_instance();
                $success = $sure_integration->update_product_content($product_id, $content);
            }
            
            if ($success) {
                $updated_count++;
                
                // Update optimization status in database
                $this->update_optimization_status($optimization_id, $product_id, $product_type, 'completed', array(
                    'fields_optimized' => array_keys($content),
                    'ai_model' => $payload['ai_model'] ?? null,
                    'tokens_used' => $payload['tokens_used'] ?? null
                ));
                
            } else {
                $errors[] = sprintf('Failed to update product %s', $product_id);
                
                // Update optimization status as failed
                $this->update_optimization_status($optimization_id, $product_id, $product_type, 'failed', array(
                    'error_message' => 'Failed to update product content'
                ));
            }
        }
        
        // Send admin notification if enabled
        $this->send_admin_notification('optimization_completed', array(
            'optimization_id' => $optimization_id,
            'updated_count' => $updated_count,
            'total_count' => count($products),
            'errors' => $errors
        ));
        
        GridSpoke_Logger::log(sprintf('Optimization completed: %d/%d products updated', $updated_count, count($products)), 'info');
        
        return new WP_REST_Response(array(
            'success' => true,
            'updated_count' => $updated_count,
            'total_count' => count($products),
            'errors' => $errors
        ), 200);
    }
    
    /**
     * Handle optimization failed webhook
     */
    private function handle_optimization_failed($payload) {
        $optimization_id = $payload['optimization_id'] ?? '';
        $error_message = $payload['error_message'] ?? 'Unknown error';
        $products = $payload['products'] ?? array();
        
        if (empty($optimization_id)) {
            return new WP_REST_Response(array(
                'error' => 'Missing optimization ID'
            ), 400);
        }
        
        // Update optimization status for affected products
        foreach ($products as $product_data) {
            $product_id = $product_data['id'] ?? '';
            $product_type = $product_data['type'] ?? 'woocommerce';
            
            if ($product_id) {
                $this->update_optimization_status($optimization_id, $product_id, $product_type, 'failed', array(
                    'error_message' => $error_message
                ));
            }
        }
        
        // Send admin notification
        $this->send_admin_notification('optimization_failed', array(
            'optimization_id' => $optimization_id,
            'error_message' => $error_message,
            'product_count' => count($products)
        ));
        
        GridSpoke_Logger::log(sprintf('Optimization failed: %s (ID: %s)', $error_message, $optimization_id), 'error');
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Failure notification processed'
        ), 200);
    }
    
    /**
     * Handle sync completed webhook
     */
    private function handle_sync_completed($payload) {
        $sync_id = $payload['sync_id'] ?? '';
        $synced_count = $payload['synced_count'] ?? 0;
        $store_type = $payload['store_type'] ?? 'woocommerce';
        
        // Send admin notification
        $this->send_admin_notification('sync_completed', array(
            'sync_id' => $sync_id,
            'synced_count' => $synced_count,
            'store_type' => $store_type
        ));
        
        GridSpoke_Logger::log(sprintf('Sync completed: %d %s products synced', $synced_count, $store_type), 'info');
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Sync completion notification processed'
        ), 200);
    }
    
    /**
     * Handle sync failed webhook
     */
    private function handle_sync_failed($payload) {
        $sync_id = $payload['sync_id'] ?? '';
        $error_message = $payload['error_message'] ?? 'Unknown error';
        $store_type = $payload['store_type'] ?? 'woocommerce';
        
        // Send admin notification
        $this->send_admin_notification('sync_failed', array(
            'sync_id' => $sync_id,
            'error_message' => $error_message,
            'store_type' => $store_type
        ));
        
        GridSpoke_Logger::log(sprintf('Sync failed: %s (ID: %s)', $error_message, $sync_id), 'error');
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Sync failure notification processed'
        ), 200);
    }
    
    /**
     * Update optimization status in database
     */
    private function update_optimization_status($optimization_id, $product_id, $product_type, $status, $data = array()) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        
        // Check if record exists
        $existing = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT id FROM $table_name WHERE optimization_id = %s AND product_id = %s AND product_type = %s",
                $optimization_id,
                $product_id,
                $product_type
            )
        );
        
        $update_data = array(
            'status' => $status,
            'updated_at' => current_time('mysql')
        );
        
        // Add optional data
        if (!empty($data['fields_optimized'])) {
            $update_data['fields_optimized'] = wp_json_encode($data['fields_optimized']);
        }
        
        if (!empty($data['ai_model'])) {
            $update_data['ai_model'] = $data['ai_model'];
        }
        
        if (!empty($data['tokens_used'])) {
            $update_data['tokens_used'] = intval($data['tokens_used']);
        }
        
        if (!empty($data['error_message'])) {
            $update_data['error_message'] = $data['error_message'];
        }
        
        if ($existing) {
            // Update existing record
            $wpdb->update(
                $table_name,
                $update_data,
                array(
                    'optimization_id' => $optimization_id,
                    'product_id' => $product_id,
                    'product_type' => $product_type
                )
            );
        } else {
            // Insert new record
            $insert_data = array_merge($update_data, array(
                'optimization_id' => $optimization_id,
                'product_id' => $product_id,
                'product_type' => $product_type,
                'created_at' => current_time('mysql')
            ));
            
            $wpdb->insert($table_name, $insert_data);
        }
    }
    
    /**
     * Send admin notification
     */
    private function send_admin_notification($type, $data) {
        $settings = GridSpoke_SEO_Connector::get_settings();
        
        // Check if notifications are enabled
        if (empty($settings['admin_notifications'])) {
            return;
        }
        
        $admin_email = get_option('admin_email');
        
        if (empty($admin_email)) {
            return;
        }
        
        $subject = '';
        $message = '';
        $site_name = get_bloginfo('name');
        
        switch ($type) {
            case 'optimization_completed':
                $subject = sprintf(__('[%s] GridSpoke Optimization Completed', 'gridspoke-seo'), $site_name);
                $message = sprintf(
                    __('Product optimization has been completed on %s.

Details:
- Optimization ID: %s
- Products Updated: %d out of %d
- Errors: %d

You can view the results in your WordPress admin dashboard.', 'gridspoke-seo'),
                    $site_name,
                    $data['optimization_id'],
                    $data['updated_count'],
                    $data['total_count'],
                    count($data['errors'])
                );
                break;
                
            case 'optimization_failed':
                $subject = sprintf(__('[%s] GridSpoke Optimization Failed', 'gridspoke-seo'), $site_name);
                $message = sprintf(
                    __('Product optimization has failed on %s.

Details:
- Optimization ID: %s
- Error: %s
- Products Affected: %d

Please check your GridSpoke settings and try again.', 'gridspoke-seo'),
                    $site_name,
                    $data['optimization_id'],
                    $data['error_message'],
                    $data['product_count']
                );
                break;
                
            case 'sync_completed':
                $subject = sprintf(__('[%s] GridSpoke Sync Completed', 'gridspoke-seo'), $site_name);
                $message = sprintf(
                    __('Product sync has been completed on %s.

Details:
- Sync ID: %s
- Products Synced: %d
- Store Type: %s

Your products are now available for optimization in GridSpoke.', 'gridspoke-seo'),
                    $site_name,
                    $data['sync_id'],
                    $data['synced_count'],
                    $data['store_type']
                );
                break;
                
            case 'sync_failed':
                $subject = sprintf(__('[%s] GridSpoke Sync Failed', 'gridspoke-seo'), $site_name);
                $message = sprintf(
                    __('Product sync has failed on %s.

Details:
- Sync ID: %s
- Error: %s
- Store Type: %s

Please check your GridSpoke settings and try again.', 'gridspoke-seo'),
                    $site_name,
                    $data['sync_id'],
                    $data['error_message'],
                    $data['store_type']
                );
                break;
        }
        
        if ($subject && $message) {
            wp_mail($admin_email, $subject, $message);
        }
    }
    
    /**
     * Test webhook endpoint
     */
    public static function test_webhook() {
        $test_payload = array(
            'event' => 'test',
            'timestamp' => current_time('c'),
            'message' => 'This is a test webhook from GridSpoke'
        );
        
        GridSpoke_Logger::log('Test webhook received: ' . wp_json_encode($test_payload), 'info');
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Test webhook received successfully',
            'timestamp' => current_time('c')
        ), 200);
    }
    
    /**
     * Get webhook statistics
     */
    public function get_webhook_stats() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        
        $stats = array(
            'total_optimizations' => $wpdb->get_var("SELECT COUNT(*) FROM $table_name"),
            'completed_optimizations' => $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'completed'"),
            'failed_optimizations' => $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'failed'"),
            'pending_optimizations' => $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'pending'"),
            'last_optimization' => $wpdb->get_var("SELECT MAX(updated_at) FROM $table_name"),
        );
        
        // Get optimizations by product type
        $by_type = $wpdb->get_results("SELECT product_type, COUNT(*) as count FROM $table_name GROUP BY product_type", ARRAY_A);
        $stats['by_product_type'] = array();
        
        foreach ($by_type as $row) {
            $stats['by_product_type'][$row['product_type']] = intval($row['count']);
        }
        
        return $stats;
    }
    
    /**
     * Clean up old optimization records
     */
    public function cleanup_old_records($days = 30) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        
        $deleted = $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM $table_name WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
                $days
            )
        );
        
        if ($deleted !== false) {
            GridSpoke_Logger::log(sprintf('Cleaned up %d old optimization records', $deleted), 'info');
        }
        
        return $deleted;
    }
}
