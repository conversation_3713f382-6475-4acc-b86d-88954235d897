"""
Progress tracking utility for GridSpoke tasks.
Provides functionality to track and update task progress.
"""
import logging
import json
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis not available, using in-memory storage for progress tracking")

logger = logging.getLogger(__name__)

class ProgressTracker:
    """Tracks progress of Celery tasks."""
    
    def __init__(self):
        """Initialize progress tracker."""
        self.redis_client = None
        self.in_memory_storage = {}
        
        # Try to initialize Redis client
        if REDIS_AVAILABLE:
            try:
                import os
                # Get Redis configuration from environment
                redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
                
                # Create Redis client using URL
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                
                # Test connection
                self.redis_client.ping()
                logger.info(f"Redis client initialized for progress tracking using URL: {redis_url}")
            except Exception as e:
                logger.warning(f"Failed to initialize Redis client: {e}")
                self.redis_client = None
    
    async def start_task_tracking(self, task_id: str, task_type: str, store_id: Optional[str] = None) -> None:
        """
        Start tracking a task.
        
        Args:
            task_id: Task ID
            task_type: Type of task
            store_id: Store ID (optional)
        """
        try:
            progress_data = {
                "task_id": task_id,
                "task_type": task_type,
                "store_id": store_id,
                "status": "started",
                "progress": 0,
                "details": {},
                "started_at": datetime.utcnow().isoformat()
            }
            
            # Store in Redis or in-memory
            if self.redis_client:
                key = f"task_progress:{task_id}"
                self.redis_client.setex(
                    key,
                    86400,  # 24 hours TTL
                    json.dumps(progress_data)
                )
            else:
                self.in_memory_storage[task_id] = progress_data
                
            logger.debug(f"Started tracking task {task_id}")
            
        except Exception as e:
            logger.error(f"Failed to start tracking task {task_id}: {e}")
    
    async def update_task_progress(self, task_id: str, progress: int, details: Optional[Dict[str, Any]] = None) -> None:
        """
        Update task progress.
        
        Args:
            task_id: Task ID
            progress: Progress percentage (0-100)
            details: Additional progress details (optional)
        """
        try:
            # Get current progress data
            if self.redis_client:
                key = f"task_progress:{task_id}"
                progress_json = self.redis_client.get(key)
                if progress_json:
                    progress_data = json.loads(progress_json)
                else:
                    progress_data = {"task_id": task_id}
            else:
                progress_data = self.in_memory_storage.get(task_id, {"task_id": task_id})
            
            # Update progress
            progress_data["progress"] = max(0, min(100, progress))
            progress_data["updated_at"] = datetime.utcnow().isoformat()
            
            if details:
                progress_data["details"] = details
                
            # Store updated progress
            if self.redis_client:
                key = f"task_progress:{task_id}"
                self.redis_client.setex(
                    key,
                    86400,  # 24 hours TTL
                    json.dumps(progress_data)
                )
            else:
                self.in_memory_storage[task_id] = progress_data
                
            logger.debug(f"Updated progress for task {task_id}: {progress}%")
            
        except Exception as e:
            logger.error(f"Failed to update progress for task {task_id}: {e}")
    
    async def update_task_status(self, task_id: str, status: str, details: Optional[Dict[str, Any]] = None) -> None:
        """
        Update task status.
        
        Args:
            task_id: Task ID
            status: New status
            details: Additional status details (optional)
        """
        try:
            # Get current progress data
            if self.redis_client:
                key = f"task_progress:{task_id}"
                progress_json = self.redis_client.get(key)
                if progress_json:
                    progress_data = json.loads(progress_json)
                else:
                    progress_data = {"task_id": task_id}
            else:
                progress_data = self.in_memory_storage.get(task_id, {"task_id": task_id})
            
            # Update status
            progress_data["status"] = status
            progress_data["status_updated_at"] = datetime.utcnow().isoformat()
            
            if details:
                progress_data["status_details"] = details
                
            # If task is completed or failed, add completion timestamp
            if status in ["completed", "failed", "cancelled"]:
                progress_data["completed_at"] = datetime.utcnow().isoformat()
                
            # Store updated progress
            if self.redis_client:
                key = f"task_progress:{task_id}"
                self.redis_client.setex(
                    key,
                    86400,  # 24 hours TTL
                    json.dumps(progress_data)
                )
            else:
                self.in_memory_storage[task_id] = progress_data
                
            logger.debug(f"Updated status for task {task_id}: {status}")
            
        except Exception as e:
            logger.error(f"Failed to update status for task {task_id}: {e}")
    
    async def get_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get current task progress.
        
        Args:
            task_id: Task ID
            
        Returns:
            Dict with progress information or None if not found
        """
        try:
            # Get progress data
            if self.redis_client:
                key = f"task_progress:{task_id}"
                progress_json = self.redis_client.get(key)
                if progress_json:
                    return json.loads(progress_json)
            else:
                return self.in_memory_storage.get(task_id)
                
            return None
            
        except Exception as e:
            logger.error(f"Failed to get progress for task {task_id}: {e}")
            return None
    
    async def cleanup_expired_progress(self, ttl_hours: int = 24) -> int:
        """
        Clean up expired progress tracking data.
        
        Args:
            ttl_hours: Hours after which data is considered expired
            
        Returns:
            Number of cleaned up entries
        """
        # This is mainly for Redis cleanup
        # In-memory storage will be cleaned up automatically by Python garbage collector
        cleaned_count = 0
        
        if self.redis_client:
            try:
                # Get all task progress keys
                pattern = "task_progress:*"
                keys = self.redis_client.keys(pattern)
                
                # Check each key for expiration
                cutoff_time = datetime.utcnow().timestamp() - (ttl_hours * 3600)
                
                for key in keys:
                    try:
                        # Get the progress data
                        progress_json = self.redis_client.get(key)
                        if progress_json:
                            progress_data = json.loads(progress_json)
                            
                            # Check if it has a completed timestamp and is old enough
                            completed_at = progress_data.get("completed_at")
                            if completed_at:
                                completed_timestamp = datetime.fromisoformat(completed_at).timestamp()
                                if completed_timestamp < cutoff_time:
                                    self.redis_client.delete(key)
                                    cleaned_count += 1
                    except Exception:
                        continue
                        
                logger.info(f"Cleaned up {cleaned_count} expired progress tracking entries")
                
            except Exception as e:
                logger.error(f"Failed to cleanup expired progress tracking data: {e}")
        
        return cleaned_count