"""
Celery Application for GridSpoke Ecommerce SEO Optimizer
Handles asynchronous task processing for product optimization, content generation, and scheduled jobs.
"""

import os
import logging
from celery import Celery
from kombu import Queue

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Celery app for GridSpoke
celery_app = Celery('gridspoke_optimizer')

# Load configuration from celeryconfig module
celery_app.config_from_object('workers.celeryconfig')

# Configure broker and backend (Redis)
celery_app.conf.broker_url = os.environ.get(
    "CELERY_BROKER_URL", 
    "redis://redis:6379/0"
)
celery_app.conf.result_backend = os.environ.get(
    "CELERY_RESULT_BACKEND", 
    "redis://redis:6379/0"
)

# Task routing for different priority queues
celery_app.conf.task_routes = {
    'workers.tasks.product_tasks.*': {'queue': 'product_optimization'},
    'workers.tasks.content_tasks.*': {'queue': 'content_generation'},
    'workers.tasks.scheduled_tasks.*': {'queue': 'scheduled_jobs'},
    'workers.tasks.bulk_tasks.*': {'queue': 'bulk_processing'},
}

# Queue definitions with priority levels
celery_app.conf.task_queues = (
    Queue('product_optimization', routing_key='product', priority=7),
    Queue('content_generation', routing_key='content', priority=5),
    Queue('scheduled_jobs', routing_key='scheduled', priority=3),
    Queue('bulk_processing', routing_key='bulk', priority=1),
    Queue('high_priority', routing_key='urgent', priority=9),
)

# Task serialization settings
celery_app.conf.task_serializer = 'json'
celery_app.conf.accept_content = ['json']
celery_app.conf.result_serializer = 'json'

# Worker settings
celery_app.conf.worker_prefetch_multiplier = 1
celery_app.conf.worker_max_tasks_per_child = 1000
celery_app.conf.worker_disable_rate_limits = False

# Task execution settings
celery_app.conf.task_acks_late = True
celery_app.conf.task_reject_on_worker_lost = True
celery_app.conf.task_track_started = True

# Result backend settings
celery_app.conf.result_expires = 3600  # 1 hour
celery_app.conf.result_compression = 'gzip'

# Auto-discover tasks from task modules
celery_app.autodiscover_tasks([
    'workers.tasks.product_tasks',
    'workers.tasks.content_tasks', 
    'workers.tasks.scheduled_tasks',
    'workers.tasks.bulk_tasks',
])

# Health check task
@celery_app.task(bind=True, ignore_result=True)
def health_check(self):
    """Health check task for monitoring"""
    logger.info(f"Health check task executed: {self.request.id}")
    return {
        'status': 'healthy',
        'worker_id': self.request.id,
        'timestamp': str(os.environ.get('WORKER_TIMESTAMP', 'unknown'))
    }

# Debug task for development
@celery_app.task(bind=True, ignore_result=True)
def debug_task(self):
    """Debug task for development and testing"""
    logger.info(f"Request: {self.request!r}")
    return {
        'task_id': self.request.id,
        'hostname': self.request.hostname,
        'args': self.request.args,
        'kwargs': self.request.kwargs
    }

if __name__ == '__main__':
    celery_app.start()
