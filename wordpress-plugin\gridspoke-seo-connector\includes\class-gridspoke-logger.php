<?php
/**
 * Logger for GridSpoke SEO Connector
 * 
 * Handles logging of plugin activities, errors, and debug information.
 */

if (!defined('ABSPATH')) {
    exit;
}

class GridSpoke_Logger {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Log levels
     */
    const LOG_LEVEL_DEBUG = 'debug';
    const LOG_LEVEL_INFO = 'info';
    const LOG_LEVEL_WARNING = 'warning';
    const LOG_LEVEL_ERROR = 'error';
    
    /**
     * Log level priorities
     */
    private static $log_priorities = array(
        self::LOG_LEVEL_DEBUG => 0,
        self::LOG_LEVEL_INFO => 1,
        self::LOG_LEVEL_WARNING => 2,
        self::LOG_LEVEL_ERROR => 3
    );
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Schedule cleanup of old logs
        if (!wp_next_scheduled('gridspoke_cleanup_logs')) {
            wp_schedule_event(time(), 'daily', 'gridspoke_cleanup_logs');
        }
        
        add_action('gridspoke_cleanup_logs', array($this, 'cleanup_old_logs'));
    }
    
    /**
     * Log a message
     */
    public static function log($message, $level = self::LOG_LEVEL_INFO, $context = array()) {
        $instance = self::get_instance();
        $instance->write_log($message, $level, $context);
    }
    
    /**
     * Log debug message
     */
    public static function debug($message, $context = array()) {
        self::log($message, self::LOG_LEVEL_DEBUG, $context);
    }
    
    /**
     * Log info message
     */
    public static function info($message, $context = array()) {
        self::log($message, self::LOG_LEVEL_INFO, $context);
    }
    
    /**
     * Log warning message
     */
    public static function warning($message, $context = array()) {
        self::log($message, self::LOG_LEVEL_WARNING, $context);
    }
    
    /**
     * Log error message
     */
    public static function error($message, $context = array()) {
        self::log($message, self::LOG_LEVEL_ERROR, $context);
    }
    
    /**
     * Write log entry
     */
    private function write_log($message, $level, $context) {
        $settings = GridSpoke_SEO_Connector::get_settings();
        $configured_level = $settings['log_level'] ?? self::LOG_LEVEL_INFO;
        
        // Check if this log level should be recorded
        if (!$this->should_log($level, $configured_level)) {
            return;
        }
        
        // Prepare log entry
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'level' => $level,
            'message' => $message,
            'context' => !empty($context) ? wp_json_encode($context) : null,
            'user_id' => get_current_user_id(),
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'server_name' => $_SERVER['SERVER_NAME'] ?? '',
            'php_version' => phpversion(),
            'wordpress_version' => get_bloginfo('version'),
            'plugin_version' => GRIDSPOKE_SEO_VERSION
        );
        
        // Add additional context if available
        $additional_context = $this->get_additional_context();
        if (!empty($additional_context)) {
            $log_entry['additional_context'] = wp_json_encode($additional_context);
        }
        
        // Write to database
        $this->write_to_database($log_entry);
        
        // Also write to WordPress debug log if WP_DEBUG_LOG is enabled
        if (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
            $formatted_message = sprintf(
                '[GridSpoke SEO] [%s] %s %s',
                strtoupper($level),
                $message,
                !empty($context) ? wp_json_encode($context) : ''
            );
            error_log($formatted_message);
        }
        
        // Send critical errors to admin email if notifications are enabled
        if ($level === self::LOG_LEVEL_ERROR && !empty($settings['admin_notifications'])) {
            $this->send_error_notification($message, $context);
        }
    }
    
    /**
     * Check if log level should be recorded
     */
    private function should_log($level, $configured_level) {
        $level_priority = self::$log_priorities[$level] ?? 0;
        $configured_priority = self::$log_priorities[$configured_level] ?? 1;
        
        return $level_priority >= $configured_priority;
    }
    
    /**
     * Write log entry to database
     */
    private function write_to_database($log_entry) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        
        // Create table if it doesn't exist
        $this->maybe_create_logs_table();
        
        $wpdb->insert($table_name, $log_entry);
    }
    
    /**
     * Create logs table if it doesn't exist
     */
    private function maybe_create_logs_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        
        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name) {
            return;
        }
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            level varchar(20) NOT NULL,
            message text NOT NULL,
            context longtext,
            user_id bigint(20),
            ip_address varchar(45),
            user_agent text,
            request_uri text,
            PRIMARY KEY (id),
            KEY level_index (level),
            KEY timestamp_index (timestamp),
            KEY user_index (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Get additional contextual information
     */
    private function get_additional_context() {
        $context = array();
        
        // Get current screen information if in admin
        if (is_admin() && function_exists('get_current_screen')) {
            $screen = get_current_screen();
            if ($screen) {
                $context['screen_id'] = $screen->id;
                $context['screen_base'] = $screen->base;
                $context['screen_post_type'] = $screen->post_type;
            }
        }
        
        // Get plugin settings summary (without sensitive data)
        $settings = GridSpoke_SEO_Connector::get_settings();
        if (!empty($settings)) {
            $context['api_endpoint'] = !empty($settings['api_endpoint']) ? $this->mask_sensitive_data($settings['api_endpoint']) : '';
            $context['api_key_set'] = !empty($settings['api_key']);
            $context['auto_sync'] = !empty($settings['auto_sync']);
            $context['log_level'] = $settings['log_level'] ?? '';
        }
        
        // Get system information
        $context['memory_usage'] = round(memory_get_usage() / 1024 / 1024, 2) . 'MB';
        $context['memory_peak_usage'] = round(memory_get_peak_usage() / 1024 / 1024, 2) . 'MB';
        $context['execution_time'] = round(microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'], 4) . 's';
        
        // Get active plugins count
        $context['active_plugins_count'] = count(get_option('active_plugins', array()));
        
        // Check for WooCommerce
        $context['woocommerce_active'] = class_exists('WooCommerce');
        
        // Check for SureCart
        $context['surecart_active'] = defined('SURECART_PLUGIN_FILE');
        
        return $context;
    }
    
    /**
     * Mask sensitive data in strings
     */
    private function mask_sensitive_data($data) {
        // Mask API keys (64 character hex strings)
        $data = preg_replace('/[0-9a-f]{64}/i', '********', $data);
        
        // Mask URLs that might contain credentials
        $data = preg_replace('/(https?:\/\/)[^\/\s]+:[^\/\s]+@/', '$1****:****@', $data);
        
        return $data;
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                
                $ip = trim($ip);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Send error notification to admin
     */
    private function send_error_notification($message, $context) {
        $admin_email = get_option('admin_email');
        
        if (empty($admin_email)) {
            return;
        }
        
        $site_name = get_bloginfo('name');
        $subject = sprintf(__('[%s] GridSpoke SEO Connector Error', 'gridspoke-seo'), $site_name);
        
        $email_message = sprintf(
            __('An error occurred in the GridSpoke SEO Connector plugin on %s.

Error Details:
%s

Context:
%s

Timestamp: %s
URL: %s

Please check the plugin logs for more details.', 'gridspoke-seo'),
            $site_name,
            $message,
            !empty($context) ? wp_json_encode($context, JSON_PRETTY_PRINT) : 'None',
            current_time('mysql'),
            home_url($_SERVER['REQUEST_URI'] ?? '')
        );
        
        wp_mail($admin_email, $subject, $email_message);
    }
    
    /**
     * Get logs from database
     */
    public function get_logs($args = array()) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        
        $defaults = array(
            'level' => '',
            'limit' => 100,
            'offset' => 0,
            'start_date' => '',
            'end_date' => '',
            'user_id' => '',
            'search' => ''
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $where_conditions = array('1=1');
        $where_values = array();
        
        // Filter by level
        if (!empty($args['level'])) {
            $where_conditions[] = 'level = %s';
            $where_values[] = $args['level'];
        }
        
        // Filter by date range
        if (!empty($args['start_date'])) {
            $where_conditions[] = 'timestamp >= %s';
            $where_values[] = $args['start_date'];
        }
        
        if (!empty($args['end_date'])) {
            $where_conditions[] = 'timestamp <= %s';
            $where_values[] = $args['end_date'];
        }
        
        // Filter by user
        if (!empty($args['user_id'])) {
            $where_conditions[] = 'user_id = %d';
            $where_values[] = intval($args['user_id']);
        }
        
        // Search in message
        if (!empty($args['search'])) {
            $where_conditions[] = 'message LIKE %s';
            $where_values[] = '%' . $wpdb->esc_like($args['search']) . '%';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "SELECT * FROM $table_name WHERE $where_clause ORDER BY timestamp DESC LIMIT %d OFFSET %d";
        $where_values[] = intval($args['limit']);
        $where_values[] = intval($args['offset']);
        
        if (!empty($where_values)) {
            $sql = $wpdb->prepare($sql, $where_values);
        }
        
        return $wpdb->get_results($sql, ARRAY_A);
    }
    
    /**
     * Get log statistics
     */
    public function get_log_stats($days = 7) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        
        $start_date = date('Y-m-d', strtotime("-$days days"));
        
        $stats = array();
        
        // Total logs
        $stats['total'] = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE timestamp >= %s",
                $start_date
            )
        );
        
        // Logs by level
        $levels = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT level, COUNT(*) as count FROM $table_name WHERE timestamp >= %s GROUP BY level",
                $start_date
            ),
            ARRAY_A
        );
        
        $stats['by_level'] = array();
        foreach ($levels as $level_data) {
            $stats['by_level'][$level_data['level']] = intval($level_data['count']);
        }
        
        // Recent errors
        $stats['recent_errors'] = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT message, timestamp FROM $table_name WHERE level = 'error' AND timestamp >= %s ORDER BY timestamp DESC LIMIT 5",
                $start_date
            ),
            ARRAY_A
        );
        
        return $stats;
    }
    
    /**
     * Clear all logs
     */
    public function clear_logs($level = '') {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        
        if (empty($level)) {
            // Clear all logs
            $deleted = $wpdb->query("TRUNCATE TABLE $table_name");
        } else {
            // Clear logs of specific level
            $deleted = $wpdb->delete($table_name, array('level' => $level));
        }
        
        self::info(sprintf('Cleared %d log entries%s', $deleted, $level ? " (level: $level)" : ''));
        
        return $deleted;
    }
    
    /**
     * Cleanup old logs
     */
    public function cleanup_old_logs() {
        $settings = GridSpoke_SEO_Connector::get_settings();
        $cleanup_days = $settings['cleanup_logs_days'] ?? 30;
        
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        
        $deleted = $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM $table_name WHERE timestamp < DATE_SUB(NOW(), INTERVAL %d DAY)",
                $cleanup_days
            )
        );
        
        if ($deleted > 0) {
            self::info(sprintf('Cleaned up %d old log entries (older than %d days)', $deleted, $cleanup_days));
        }
        
        return $deleted;
    }
    
    /**
     * Export logs to CSV
     */
    public function export_logs_csv($args = array()) {
        $logs = $this->get_logs(array_merge($args, array('limit' => 5000)));
        
        if (empty($logs)) {
            return false;
        }
        
        $filename = 'gridspoke-logs-' . date('Y-m-d') . '.csv';
        $filepath = wp_upload_dir()['path'] . '/' . $filename;
        
        $file = fopen($filepath, 'w');
        
        if ($file === false) {
            return false;
        }
        
        // Write header
        fputcsv($file, array(
            'Timestamp',
            'Level',
            'Message',
            'Context',
            'User ID',
            'IP Address',
            'User Agent',
            'Request URI'
        ));
        
        // Write data
        foreach ($logs as $log) {
            fputcsv($file, array(
                $log['timestamp'],
                $log['level'],
                $log['message'],
                $log['context'],
                $log['user_id'],
                $log['ip_address'],
                $log['user_agent'],
                $log['request_uri']
            ));
        }
        
        fclose($file);
        
        return array(
            'filename' => $filename,
            'filepath' => $filepath,
            'url' => wp_upload_dir()['url'] . '/' . $filename,
            'count' => count($logs)
        );
    }
    
    /**
     * Log API request/response for debugging
     */
    public static function log_api_call($method, $endpoint, $request_data = null, $response_data = null, $status_code = null) {
        $context = array(
            'method' => $method,
            'endpoint' => $endpoint,
            'status_code' => $status_code
        );
        
        if ($request_data !== null) {
            $context['request'] = $request_data;
        }
        
        if ($response_data !== null) {
            $context['response'] = $response_data;
        }
        
        $message = sprintf('API Call: %s %s', $method, $endpoint);
        
        if ($status_code && $status_code >= 400) {
            self::error($message, $context);
        } else {
            self::debug($message, $context);
        }
    }
    
    /**
     * Log optimization activity
     */
    public static function log_optimization($product_id, $action, $status, $details = array()) {
        $message = sprintf(
            'Product %s %s: %s',
            $product_id,
            $action,
            $status
        );
        
        $context = array_merge(array(
            'product_id' => $product_id,
            'action' => $action,
            'status' => $status
        ), $details);
        
        if ($status === 'failed' || $status === 'error') {
            self::error($message, $context);
        } else {
            self::info($message, $context);
        }
    }
}
