"""Initial database migration

Revision ID: 001_initial_schema
Revises: 
Create Date: 2025-01-20 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '001_initial_schema'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Create users table
    op.create_table(
        'users',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('password_hash', sa.String(), nullable=False),
        sa.Column('first_name', sa.String(), nullable=True),
        sa.Column('last_name', sa.String(), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=True),
        sa.Column('is_superuser', sa.Bo<PERSON>(), nullable=True),
        sa.Column('plan', sa.String(), nullable=True),
        sa.Column('api_key', sa.String(), nullable=True),
        sa.Column('subscription_id', sa.String(), nullable=True),
        sa.Column('subscription_status', sa.String(), nullable=True),
        sa.Column('trial_ends_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email'),
        sa.UniqueConstraint('api_key')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    
    # Create stores table
    op.create_table(
        'stores',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('url', sa.String(), nullable=False),
        sa.Column('platform', sa.String(), nullable=False),
        sa.Column('api_key', sa.String(), nullable=True),
        sa.Column('api_secret', sa.String(), nullable=True),
        sa.Column('webhook_secret', sa.String(), nullable=True),
        sa.Column('auto_sync', sa.Boolean(), nullable=True),
        sa.Column('sync_frequency', sa.String(), nullable=True),
        sa.Column('optimization_schedule', sa.String(), nullable=True),
        sa.Column('last_sync', sa.DateTime(), nullable=True),
        sa.Column('sync_status', sa.String(), nullable=True),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('settings', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stores_user_id'), 'stores', ['user_id'], unique=False)
    
    # Create products table
    op.create_table(
        'products',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('short_description', sa.String(), nullable=True),
        sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('sale_price', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('sku', sa.String(), nullable=True),
        sa.Column('category', sa.String(), nullable=True),
        sa.Column('subcategory', sa.String(), nullable=True),
        sa.Column('brand', sa.String(), nullable=True),
        sa.Column('tags', postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column('images', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('attributes', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('seo_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('store_id', sa.String(), nullable=False),
        sa.Column('external_id', sa.String(), nullable=True),
        sa.Column('slug', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('stock_quantity', sa.Integer(), nullable=True),
        sa.Column('weight', sa.Numeric(precision=8, scale=3), nullable=True),
        sa.Column('dimensions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('optimization_status', sa.String(), nullable=True),
        sa.Column('seo_score', sa.Float(), nullable=True),
        sa.Column('last_optimized', sa.DateTime(), nullable=True),
        sa.Column('optimization_count', sa.Integer(), nullable=True),
        sa.Column('canonical_url', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_products_store_id'), 'products', ['store_id'], unique=False)
    op.create_index(op.f('ix_products_sku'), 'products', ['sku'], unique=False)
    op.create_index(op.f('ix_products_category'), 'products', ['category'], unique=False)
    op.create_index(op.f('ix_products_status'), 'products', ['status'], unique=False)
    op.create_index(op.f('ix_products_optimization_status'), 'products', ['optimization_status'], unique=False)
    
    # Create optimization_jobs table
    op.create_table(
        'optimization_jobs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('store_id', sa.String(), nullable=False),
        sa.Column('product_ids', postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column('optimization_type', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('priority', sa.String(), nullable=True),
        sa.Column('total_items', sa.Integer(), nullable=True),
        sa.Column('processed_items', sa.Integer(), nullable=True),
        sa.Column('failed_items', sa.Integer(), nullable=True),
        sa.Column('skipped_items', sa.Integer(), nullable=True),
        sa.Column('progress', sa.Float(), nullable=True),
        sa.Column('celery_task_id', sa.String(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('estimated_completion', sa.DateTime(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('settings', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('results_summary', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_optimization_jobs_user_id'), 'optimization_jobs', ['user_id'], unique=False)
    op.create_index(op.f('ix_optimization_jobs_store_id'), 'optimization_jobs', ['store_id'], unique=False)
    op.create_index(op.f('ix_optimization_jobs_status'), 'optimization_jobs', ['status'], unique=False)
    op.create_index(op.f('ix_optimization_jobs_celery_task_id'), 'optimization_jobs', ['celery_task_id'], unique=False)
    
    # Create generated_content table
    op.create_table(
        'generated_content',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('product_id', sa.String(), nullable=False),
        sa.Column('job_id', sa.String(), nullable=True),
        sa.Column('content_type', sa.String(), nullable=False),
        sa.Column('original_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('generated_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('seo_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('ai_model', sa.String(), nullable=True),
        sa.Column('ai_metrics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('tokens_used', sa.Integer(), nullable=True),
        sa.Column('cost', sa.Numeric(precision=8, scale=4), nullable=True),
        sa.Column('quality_score', sa.Float(), nullable=True),
        sa.Column('applied', sa.Boolean(), nullable=True),
        sa.Column('applied_at', sa.DateTime(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=True),
        sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['job_id'], ['optimization_jobs.id'], ),
        sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generated_content_product_id'), 'generated_content', ['product_id'], unique=False)
    op.create_index(op.f('ix_generated_content_job_id'), 'generated_content', ['job_id'], unique=False)
    op.create_index(op.f('ix_generated_content_content_type'), 'generated_content', ['content_type'], unique=False)
    op.create_index(op.f('ix_generated_content_ai_model'), 'generated_content', ['ai_model'], unique=False)
    
    # Create additional indexes for performance
    op.create_index('ix_products_search', 'products', ['name', 'description'], postgresql_using='gin', postgresql_ops={'name': 'gin_trgm_ops', 'description': 'gin_trgm_ops'})
    op.create_index('ix_products_seo_score', 'products', ['seo_score'])
    op.create_index('ix_optimization_jobs_created_at', 'optimization_jobs', ['created_at'])
    op.create_index('ix_generated_content_created_at', 'generated_content', ['created_at'])
    
    # Create WordPress sync logs table
    op.create_table(
        'wordpress_sync_logs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('store_id', sa.String(), nullable=False),
        sa.Column('sync_type', sa.String(), nullable=False),
        sa.Column('status', sa.String(), nullable=False),
        sa.Column('products_synced', sa.Integer(), nullable=True),
        sa.Column('products_updated', sa.Integer(), nullable=True),
        sa.Column('products_failed', sa.Integer(), nullable=True),
        sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('sync_duration', sa.Float(), nullable=True),
        sa.Column('webhook_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_wordpress_sync_logs_store_id'), 'wordpress_sync_logs', ['store_id'], unique=False)
    op.create_index(op.f('ix_wordpress_sync_logs_status'), 'wordpress_sync_logs', ['status'], unique=False)
    op.create_index(op.f('ix_wordpress_sync_logs_created_at'), 'wordpress_sync_logs', ['created_at'], unique=False)
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('wordpress_sync_logs')
    op.drop_table('generated_content')
    op.drop_table('optimization_jobs')
    op.drop_table('products')
    op.drop_table('stores')
    op.drop_table('users')
    # ### end Alembic commands ###
