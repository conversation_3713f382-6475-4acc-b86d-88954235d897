<?php
/**
 * Integration tests for complete GridSpoke SEO workflow
 */

class Test_GridSpoke_Integration extends GridSpoke_Test_Case {
    
    /**
     * Test complete workflow: authentication -> sync -> optimize -> webhook
     */
    public function test_complete_workflow() {
        // Skip if WooCommerce not available
        if (!class_exists('WooCommerce')) {
            $this->markTestSkipped('WooCommerce is not available');
        }
        
        // Step 1: Set up plugin settings
        $this->update_plugin_settings(array(
            'api_endpoint' => 'http://localhost:8000/api/v1',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'auto_sync' => true,
            'webhook_secret' => 'test_webhook_secret_123'
        ));
        
        // Step 2: Mock API responses
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_store_creation();
        $this->api_mock->mock_sync_success(1, 0);
        $this->api_mock->mock_optimization_success();
        
        // Step 3: Authenticate
        $api_client = GridSpoke_API_Client::get_instance();
        $auth_result = $api_client->authenticate('<EMAIL>', 'password123');
        
        $this->assertTrue($auth_result['success']);
        $this->assertNotEmpty(get_transient('gridspoke_access_token'));
        
        // Step 4: Create and sync product
        $product_id = $this->create_test_woo_product(array(
            'name' => 'Integration Test Product',
            'description' => 'Original product description',
            'sku' => 'INTEGRATION-TEST-001',
            'price' => '49.99'
        ));
        
        $woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
        $sync_result = $woo_integration->sync_single_product($product_id, true);
        
        $this->assertNotFalse($sync_result);
        $this->assertTrue($sync_result['success']);
        
        // Verify sync request was made
        $sync_request = $this->api_mock->find_request('products/bulk/sync', 'POST');
        $this->assertNotNull($sync_request);
        $this->assertCount(1, $sync_request['data']);
        
        // Step 5: Request optimization
        update_post_meta($product_id, '_gridspoke_product_uuid', 'test_uuid_123');
        
        $optimization_result = $api_client->request_optimization(array($product_id), array(
            'optimization_types' => array('title', 'description', 'meta_description')
        ));
        
        $this->assertNotFalse($optimization_result);
        $this->assertNotEmpty($optimization_result['job_id']);
        
        // Verify optimization request was made
        $opt_request = $this->api_mock->find_request('products/test_uuid_123/optimize', 'POST');
        $this->assertNotNull($opt_request);
        
        // Step 6: Simulate webhook completion
        $webhook_handler = GridSpoke_Webhook_Handler::get_instance();
        $webhook_data = array(
            'event' => 'optimization.completed',
            'job_id' => $optimization_result['job_id'],
            'product_id' => 'test_uuid_123',
            'external_id' => (string) $product_id,
            'status' => 'completed',
            'optimized_content' => array(
                'title' => 'SEO Optimized Integration Test Product - Enhanced for Search',
                'description' => 'This is an SEO-optimized product description with relevant keywords and compelling copy designed to improve search rankings and conversion rates.',
                'meta_description' => 'SEO-optimized meta description for better search visibility and click-through rates.'
            ),
            'optimization_types' => array('title', 'description', 'meta_description'),
            'ai_model' => 'claude-3-sonnet',
            'processing_time' => 42.5,
            'timestamp' => date('c')
        );
        
        $webhook_result = $webhook_handler->process_webhook($webhook_data);
        $this->assertTrue($webhook_result);
        
        // Step 7: Verify product was updated
        $updated_product = wc_get_product($product_id);
        $this->assertEquals('SEO Optimized Integration Test Product - Enhanced for Search', $updated_product->get_name());
        $this->assertEquals('This is an SEO-optimized product description with relevant keywords and compelling copy designed to improve search rankings and conversion rates.', $updated_product->get_description());
        
        // Verify meta description was updated
        $meta_desc = get_post_meta($product_id, '_yoast_wpseo_metadesc', true);
        $this->assertEquals('SEO-optimized meta description for better search visibility and click-through rates.', $meta_desc);
        
        // Step 8: Verify optimization record was created
        global $wpdb;
        $table_name = $wpdb->prefix . 'gridspoke_optimizations';
        $record = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE product_id = %s", $product_id),
            ARRAY_A
        );
        
        $this->assertNotNull($record);
        $this->assertEquals('completed', $record['status']);
        $this->assertEquals('woocommerce', $record['product_type']);
        
        $fields_optimized = json_decode($record['fields_optimized'], true);
        $this->assertContains('title', $fields_optimized);
        $this->assertContains('description', $fields_optimized);
        $this->assertContains('meta_description', $fields_optimized);
        
        // Step 9: Verify logs were created
        $this->assertLogEntryExists('info', '/authentication successful/i');
        $this->assertLogEntryExists('info', '/successfully synced/i');
        $this->assertLogEntryExists('info', '/optimization requested/i');
        $this->assertLogEntryExists('info', '/optimization completed/i');
    }
    
    /**
     * Test error handling in complete workflow
     */
    public function test_workflow_error_handling() {
        // Step 1: Test authentication failure
        $this->api_mock->mock_auth_failure();
        
        $api_client = GridSpoke_API_Client::get_instance();
        $auth_result = $api_client->authenticate('<EMAIL>', 'wrong_password');
        
        $this->assertFalse($auth_result['success']);
        $this->assertEquals('Invalid credentials', $auth_result['message']);
        
        // Step 2: Test sync without authentication
        $product_id = $this->create_test_woo_product();
        $woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
        $sync_result = $woo_integration->sync_single_product($product_id, true);
        
        $this->assertFalse($sync_result);
        
        // Step 3: Test optimization without product UUID
        $optimization_result = $api_client->request_optimization(array($product_id));
        $this->assertFalse($optimization_result);
        
        // Verify error logs were created
        $this->assertLogEntryExists('error', '/not authenticated/i');
    }
    
    /**
     * Test bulk operations workflow
     */
    public function test_bulk_operations_workflow() {
        if (!class_exists('WooCommerce')) {
            $this->markTestSkipped('WooCommerce is not available');
        }
        
        // Set up authentication
        $this->update_plugin_settings(array(
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_store_creation();
        $this->api_mock->mock_sync_success(5, 0);
        
        // Create multiple test products
        $product_ids = array();
        for ($i = 1; $i <= 5; $i++) {
            $product_ids[] = $this->create_test_woo_product(array(
                'name' => "Bulk Test Product $i",
                'sku' => "BULK-$i"
            ));
        }
        
        // Authenticate
        $api_client = GridSpoke_API_Client::get_instance();
        $auth_result = $api_client->authenticate('<EMAIL>', 'password123');
        $this->assertTrue($auth_result['success']);
        
        // Bulk sync
        $woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
        $sync_result = $woo_integration->sync_products($product_ids);
        
        $this->assertNotFalse($sync_result);
        $this->assertEquals(5, $sync_result['created']);
        
        // Mock bulk optimization
        $this->api_mock->mock_response('products/bulk/optimize', array(
            'status_code' => 200,
            'body' => json_encode(array(
                'job_id' => 'bulk_job_123',
                'message' => 'Bulk optimization queued for 5 products',
                'estimated_completion' => null
            ))
        ));
        
        // Store UUIDs for products
        foreach ($product_ids as $i => $product_id) {
            update_post_meta($product_id, '_gridspoke_product_uuid', "uuid_$i");
        }
        
        // Request bulk optimization
        $optimization_result = $api_client->request_optimization($product_ids, array(
            'optimization_types' => array('title', 'description')
        ));
        
        $this->assertNotFalse($optimization_result);
        $this->assertEquals('bulk_job_123', $optimization_result['job_id']);
        
        // Verify bulk optimization request was made
        $bulk_request = $this->api_mock->find_request('products/bulk/optimize', 'POST');
        $this->assertNotNull($bulk_request);
    }
    
    /**
     * Test webhook signature verification in real scenario
     */
    public function test_webhook_signature_verification_real() {
        $secret = 'real_webhook_secret_' . uniqid();
        $this->update_plugin_settings(array('webhook_secret' => $secret));
        
        $webhook_data = $this->test_data->get_sample_webhook_data();
        $payload = json_encode($webhook_data);
        $signature = hash_hmac('sha256', $payload, $secret);
        
        // Create test product
        $product_id = $this->create_test_woo_product();
        $webhook_data['external_id'] = (string) $product_id;
        update_post_meta($product_id, '_gridspoke_product_uuid', $webhook_data['product_id']);
        
        // Simulate real REST API request
        $request = new WP_REST_Request('POST', '/gridspoke-seo/v1/webhook');
        $request->set_body(json_encode($webhook_data));
        $request->set_header('X-GridSpoke-Signature', $signature);
        $request->set_header('Content-Type', 'application/json');
        
        $webhook_handler = GridSpoke_Webhook_Handler::get_instance();
        $response = $webhook_handler->handle_webhook_request($request);
        
        $this->assertInstanceOf('WP_REST_Response', $response);
        $this->assertEquals(200, $response->get_status());
        
        $response_data = $response->get_data();
        $this->assertTrue($response_data['success']);
        $this->assertEquals('Webhook processed successfully', $response_data['message']);
    }
    
    /**
     * Test plugin activation and deactivation
     */
    public function test_plugin_activation_deactivation() {
        // Test activation
        GridSpoke_SEO_Connector::activate();
        
        // Check that database tables were created
        global $wpdb;
        
        $logs_table = $wpdb->prefix . 'gridspoke_logs';
        $optimizations_table = $wpdb->prefix . 'gridspoke_optimizations';
        
        $this->assertEquals($logs_table, $wpdb->get_var("SHOW TABLES LIKE '$logs_table'"));
        $this->assertEquals($optimizations_table, $wpdb->get_var("SHOW TABLES LIKE '$optimizations_table'"));
        
        // Check that default settings were created
        $settings = get_option('gridspoke_seo_settings');
        $this->assertNotEmpty($settings);
        $this->assertArrayHasKey('api_endpoint', $settings);
        
        // Check that webhook secret was generated
        $this->assertNotEmpty($settings['webhook_secret']);
        
        // Test deactivation
        GridSpoke_SEO_Connector::deactivate();
        
        // Check that cleanup was performed (if configured)
        // Tables should still exist but transients should be cleared
        $this->assertEmpty(get_transient('gridspoke_access_token'));
    }
    
    /**
     * Test rate limiting functionality
     */
    public function test_rate_limiting() {
        // Set up rate limiting
        $this->update_plugin_settings(array(
            'rate_limit_delay' => 2, // 2 seconds between requests
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_store_creation();
        $this->api_mock->mock_sync_success(1, 0);
        
        $api_client = GridSpoke_API_Client::get_instance();
        $auth_result = $api_client->authenticate('<EMAIL>', 'password123');
        $this->assertTrue($auth_result['success']);
        
        // Create test products
        $product_ids = array();
        for ($i = 1; $i <= 3; $i++) {
            $product_ids[] = $this->create_test_woo_product(array('name' => "Rate Limit Test $i"));
        }
        
        // Sync products (should respect rate limiting)
        $start_time = microtime(true);
        
        $woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
        $result = $woo_integration->sync_products($product_ids);
        
        $end_time = microtime(true);
        $duration = $end_time - $start_time;
        
        $this->assertNotFalse($result);
        
        // Should have taken at least some time due to rate limiting
        // (In real scenario, but in tests we're mocking so this might not apply)
        $this->assertGreaterThan(0, $duration);
    }
    
    /**
     * Test error recovery and retry logic
     */
    public function test_error_recovery() {
        // Set up authentication
        $this->update_plugin_settings(array(
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        // Mock initial auth success
        $this->api_mock->mock_auth_success();
        
        $api_client = GridSpoke_API_Client::get_instance();
        $auth_result = $api_client->authenticate('<EMAIL>', 'password123');
        $this->assertTrue($auth_result['success']);
        
        // Mock API error for first sync attempt
        $this->api_mock->mock_response('products/bulk/sync', array(
            'status_code' => 500,
            'body' => json_encode(array('detail' => 'Internal server error'))
        ));
        
        $product_id = $this->create_test_woo_product();
        $products = array($this->test_data->get_sample_woo_product_data());
        
        $result = $api_client->sync_products($products);
        $this->assertFalse($result);
        
        // Verify error was logged
        $this->assertLogEntryExists('error', '/sync failed/i');
        
        // Now mock success for retry
        $this->api_mock->mock_sync_success(1, 0);
        
        $retry_result = $api_client->sync_products($products);
        $this->assertNotFalse($retry_result);
    }
    
    /**
     * Test plugin compatibility with different WordPress configurations
     */
    public function test_plugin_compatibility() {
        // Test with multisite
        if (is_multisite()) {
            $blog_id = $this->factory->blog->create();
            switch_to_blog($blog_id);
            
            // Plugin should work on individual sites
            $settings = GridSpoke_SEO_Connector::get_settings();
            $this->assertIsArray($settings);
            
            restore_current_blog();
        }
        
        // Test with different user capabilities
        $editor_id = $this->factory->user->create(array('role' => 'editor'));
        wp_set_current_user($editor_id);
        
        // Editor should not have access to settings
        $this->assertFalse(current_user_can('manage_options'));
        
        // But should be able to edit posts
        $this->assertTrue(current_user_can('edit_posts'));
        
        // Reset to admin user
        wp_set_current_user($this->user_id);
    }
    
    /**
     * Test plugin performance with large datasets
     */
    public function test_performance_with_large_dataset() {
        if (!class_exists('WooCommerce')) {
            $this->markTestSkipped('WooCommerce is not available');
        }
        
        // Set up authentication
        $this->update_plugin_settings(array(
            'email' => '<EMAIL>',
            'password' => 'password123',
            'batch_size' => 10 // Small batch size for testing
        ));
        
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_store_creation();
        $this->api_mock->mock_sync_success(50, 0);
        
        $api_client = GridSpoke_API_Client::get_instance();
        $auth_result = $api_client->authenticate('<EMAIL>', 'password123');
        $this->assertTrue($auth_result['success']);
        
        // Create 50 test products
        $product_ids = array();
        for ($i = 1; $i <= 50; $i++) {
            $product_ids[] = $this->create_test_woo_product(array(
                'name' => "Performance Test Product $i",
                'sku' => "PERF-$i"
            ));
        }
        
        $start_time = microtime(true);
        
        $woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
        $result = $woo_integration->sync_products($product_ids);
        
        $end_time = microtime(true);
        $duration = $end_time - $start_time;
        
        $this->assertNotFalse($result);
        
        // Should complete within reasonable time (adjust as needed)
        $this->assertLessThan(30, $duration, 'Sync took too long: ' . $duration . ' seconds');
        
        // Verify all products were processed
        $this->assertEquals(50, $result['created']);
    }
}
