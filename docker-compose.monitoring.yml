# GridSpoke Monitoring Stack - Docker Compose Configuration
# Phase 8: Prometheus, Grafana, Sentry integration with GridSpoke services

version: '3.8'

services:
  # =============================================================================
  # PROMETHEUS - Metrics Collection
  # =============================================================================
  prometheus:
    image: prom/prometheus:v2.48.0
    container_name: gridspoke-prometheus
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/prometheus/rules/:/etc/prometheus/rules/:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - gridspoke-monitoring
      - gridspoke-network
    restart: unless-stopped
    labels:
      - "monitoring.service=prometheus"
      - "monitoring.component=metrics"

  # =============================================================================
  # GRAFANA - Metrics Visualization
  # =============================================================================
  grafana:
    image: grafana/grafana:10.2.2
    container_name: gridspoke-grafana
    ports:
      - "3001:3000"  # Port 3000 might conflict with frontend
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-gridspoke123}
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel
      - GF_SECURITY_SECRET_KEY=${GRAFANA_SECRET_KEY:-gridspoke-secret}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SMTP_ENABLED=true
      - GF_SMTP_HOST=${SMTP_HOST:-smtp.gmail.com:587}
      - GF_SMTP_USER=${SMTP_USER:-<EMAIL>}
      - GF_SMTP_PASSWORD=${SMTP_PASSWORD}
      - GF_SMTP_FROM_ADDRESS=${SMTP_FROM_ADDRESS:-<EMAIL>}
      - GF_SMTP_FROM_NAME=GridSpoke Monitoring
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - gridspoke-monitoring
    restart: unless-stopped
    depends_on:
      - prometheus
    labels:
      - "monitoring.service=grafana"
      - "monitoring.component=visualization"

  # =============================================================================
  # ALERTMANAGER - Alert Management
  # =============================================================================
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: gridspoke-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - alertmanager_data:/alertmanager
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - gridspoke-monitoring
    restart: unless-stopped
    labels:
      - "monitoring.service=alertmanager"
      - "monitoring.component=alerting"

  # =============================================================================
  # NODE EXPORTER - System Metrics
  # =============================================================================
  node-exporter:
    image: prom/node-exporter:v1.7.0
    container_name: gridspoke-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - gridspoke-monitoring
    restart: unless-stopped
    labels:
      - "monitoring.service=node-exporter"
      - "monitoring.component=system-metrics"

  # =============================================================================
  # POSTGRES EXPORTER - Database Metrics
  # =============================================================================
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.15.0
    container_name: gridspoke-postgres-exporter
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=postgresql://${POSTGRES_USER:-gridspoke}:${POSTGRES_PASSWORD:-gridspoke123}@postgres:5432/${POSTGRES_DB:-gridspoke_db}?sslmode=disable
    networks:
      - gridspoke-monitoring
      - gridspoke-network
    restart: unless-stopped
    depends_on:
      - postgres
    labels:
      - "monitoring.service=postgres-exporter"
      - "monitoring.component=database-metrics"

  # =============================================================================
  # REDIS EXPORTER - Cache/Queue Metrics
  # =============================================================================
  redis-exporter:
    image: oliver006/redis_exporter:v1.55.0
    container_name: gridspoke-redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_EXPORTER_INCL_SYSTEM_METRICS=true
    networks:
      - gridspoke-monitoring
      - gridspoke-network
    restart: unless-stopped
    depends_on:
      - redis
    labels:
      - "monitoring.service=redis-exporter"
      - "monitoring.component=cache-metrics"

  # =============================================================================
  # NGINX EXPORTER - Web Server Metrics (if using nginx)
  # =============================================================================
  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:0.11.0
    container_name: gridspoke-nginx-exporter
    ports:
      - "9113:9113"
    command:
      - '-nginx.scrape-uri=http://nginx:8080/nginx_status'
    networks:
      - gridspoke-monitoring
      - gridspoke-network
    restart: unless-stopped
    depends_on:
      - nginx
    labels:
      - "monitoring.service=nginx-exporter"
      - "monitoring.component=web-metrics"

  # =============================================================================
  # CADVISOR - Container Metrics
  # =============================================================================
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.2
    container_name: gridspoke-cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    networks:
      - gridspoke-monitoring
    restart: unless-stopped
    labels:
      - "monitoring.service=cadvisor"
      - "monitoring.component=container-metrics"

  # =============================================================================
  # BLACKBOX EXPORTER - External Endpoint Monitoring
  # =============================================================================
  blackbox-exporter:
    image: prom/blackbox-exporter:v0.24.0
    container_name: gridspoke-blackbox-exporter
    ports:
      - "9115:9115"
    volumes:
      - ./monitoring/blackbox/blackbox.yml:/etc/blackbox_exporter/config.yml:ro
    networks:
      - gridspoke-monitoring
    restart: unless-stopped
    labels:
      - "monitoring.service=blackbox-exporter"
      - "monitoring.component=endpoint-monitoring"

  # =============================================================================
  # JAEGER - Distributed Tracing (Optional)
  # =============================================================================
  jaeger:
    image: jaegertracing/all-in-one:1.51
    container_name: gridspoke-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - gridspoke-monitoring
    restart: unless-stopped
    labels:
      - "monitoring.service=jaeger"
      - "monitoring.component=tracing"

  # =============================================================================
  # LOKI - Log Aggregation (Optional)
  # =============================================================================
  loki:
    image: grafana/loki:2.9.2
    container_name: gridspoke-loki
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki/loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - gridspoke-monitoring
    restart: unless-stopped
    labels:
      - "monitoring.service=loki"
      - "monitoring.component=logging"

  # =============================================================================
  # PROMTAIL - Log Collection (Optional)
  # =============================================================================
  promtail:
    image: grafana/promtail:2.9.2
    container_name: gridspoke-promtail
    volumes:
      - ./monitoring/promtail/promtail-config.yml:/etc/promtail/config.yml:ro
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - gridspoke-monitoring
    depends_on:
      - loki
    restart: unless-stopped
    labels:
      - "monitoring.service=promtail"
      - "monitoring.component=log-collection"

  # =============================================================================
  # GRIDSPOKE CUSTOM METRICS EXPORTER
  # =============================================================================
  gridspoke-metrics:
    build:
      context: .
      dockerfile: Dockerfile.metrics
    container_name: gridspoke-metrics-exporter
    ports:
      - "8090:8090"  # Custom metrics endpoint
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
    volumes:
      - ./api:/app/api:ro
    networks:
      - gridspoke-monitoring
      - gridspoke-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
    labels:
      - "monitoring.service=gridspoke-metrics"
      - "monitoring.component=business-metrics"

# =============================================================================
# VOLUMES
# =============================================================================
volumes:
  prometheus_data:
    driver: local
    labels:
      - "monitoring.data=prometheus"
  
  grafana_data:
    driver: local
    labels:
      - "monitoring.data=grafana"
  
  alertmanager_data:
    driver: local
    labels:
      - "monitoring.data=alertmanager"
  
  loki_data:
    driver: local
    labels:
      - "monitoring.data=loki"

# =============================================================================
# NETWORKS
# =============================================================================
networks:
  gridspoke-monitoring:
    driver: bridge
    labels:
      - "monitoring.network=internal"
  
  gridspoke-network:
    external: true
    labels:
      - "monitoring.network=external"

# =============================================================================
# HEALTH CHECKS
# =============================================================================

# Note: Health checks are defined in the individual service configurations
# and monitored by Prometheus using the /health endpoints

# =============================================================================
# RESOURCE LIMITS (Production Recommended)
# =============================================================================

# Uncomment and adjust for production deployments:
#
# x-resource-limits: &default-resources
#   deploy:
#     resources:
#       limits:
#         memory: 512M
#         cpus: '0.5'
#       reservations:
#         memory: 256M
#         cpus: '0.25'
#
# Then add to each service:
# <<: *default-resources

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# For production:
# 1. Use Docker secrets for sensitive data
# 2. Enable TLS for all external endpoints
# 3. Implement proper firewall rules
# 4. Use read-only containers where possible
# 5. Run with non-root users

# Example secrets configuration:
# secrets:
#   grafana_admin_password:
#     external: true
#   prometheus_basic_auth:
#     external: true

# =============================================================================
# MONITORING STACK STARTUP ORDER
# =============================================================================

# Start order:
# 1. Core services (postgres, redis) - from main docker-compose.yml
# 2. Exporters (node, postgres, redis)
# 3. Prometheus
# 4. Alertmanager
# 5. Grafana
# 6. Optional services (jaeger, loki)

# Use: docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d
