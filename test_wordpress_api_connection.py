#!/usr/bin/env python3
"""
Test script to verify WordPress plugin API connection to GridSpoke backend.
This simulates what the WordPress plugin does when connecting.
"""
import requests
import json
import os
from urllib.parse import urljoin

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "test12345"

def test_health_endpoint():
    """Test basic health endpoint (no auth required)."""
    print("Testing health endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=10)
        print(f"Health check: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_authentication():
    """Test authentication endpoint like WordPress plugin."""
    print("Testing authentication...")
    try:
        auth_data = {
            "email": TEST_EMAIL,
            "password": TEST_PASSWORD
        }
        response = requests.post(
            f"{API_BASE_URL}/api/v1/auth/login",
            json=auth_data,  # Using JSON data instead of form data
            timeout=10
        )
        print(f"Auth response: {response.status_code}")
        if response.status_code == 200:
            auth_result = response.json()
            print(f"Auth successful: access_token present={bool(auth_result.get('access_token'))}")
            return auth_result.get("access_token")
        else:
            print(f"Auth failed: {response.text}")
            return None
    except Exception as e:
        print(f"Auth error: {e}")
        return None

def test_stores_endpoint(access_token):
    """Test stores endpoint like WordPress plugin."""
    print("Testing stores endpoint...")
    try:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # First, try to get existing stores
        response = requests.get(f"{API_BASE_URL}/api/v1/stores", headers=headers, timeout=10)
        print(f"Get stores: {response.status_code}")
        
        if response.status_code == 200:
            stores = response.json()
            print(f"Existing stores: {stores}")
            
            if stores:
                return stores[0]["id"]  # Return first store ID
            
            # No stores exist, create one
            print("Creating new store...")
            store_data = {
                "name": "Test WordPress Store",
                "store_url": "https://test-wordpress.com",
                "platform": "woocommerce",
                "api_credentials": {}
            }
            
            response = requests.post(
                f"{API_BASE_URL}/api/v1/stores",
                json=store_data,
                headers=headers,
                timeout=10
            )
            print(f"Create store: {response.status_code}")
            
            if response.status_code == 201:
                store = response.json()
                print(f"Store created: {store}")
                return store["id"]
            else:
                print(f"Store creation failed: {response.text}")
                return None
        else:
            print(f"Get stores failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"Stores endpoint error: {e}")
        return None

def test_dashboard_stats(access_token):
    """Test dashboard stats endpoint."""
    print("Testing dashboard stats...")
    try:
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(f"{API_BASE_URL}/api/v1/dashboard/stats", headers=headers, timeout=10)
        print(f"Dashboard stats: {response.status_code}")
        
        if response.status_code == 200:
            stats = response.json()
            print(f"Dashboard stats: {stats}")
            return True
        else:
            print(f"Dashboard stats failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"Dashboard stats error: {e}")
        return False

def main():
    """Run all WordPress plugin connection tests."""
    print("=" * 60)
    print("WordPress Plugin API Connection Test")
    print("=" * 60)
    
    # Test 1: Health check
    if not test_health_endpoint():
        print("❌ Health check failed - API may not be running")
        return
    print("✅ Health check passed")
    
    # Test 2: Authentication
    access_token = test_authentication()
    if not access_token:
        print("❌ Authentication failed - check credentials or create test user")
        return
    print("✅ Authentication passed")
    
    # Test 3: Stores endpoint
    store_id = test_stores_endpoint(access_token)
    if not store_id:
        print("❌ Stores endpoint failed")
        return
    print(f"✅ Stores endpoint passed - Store ID: {store_id}")
    
    # Test 4: Dashboard stats
    if not test_dashboard_stats(access_token):
        print("❌ Dashboard stats failed")
        return
    print("✅ Dashboard stats passed")
    
    print("\n" + "=" * 60)
    print("🎉 All WordPress plugin connection tests passed!")
    print("The WordPress plugin should be able to connect successfully.")
    print("=" * 60)

if __name__ == "__main__":
    main()
