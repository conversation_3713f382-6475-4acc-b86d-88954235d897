#!/usr/bin/env python3
"""
Script to generate a proper password hash using the same mechanism as the GridSpoke system.
"""

import sys
import os

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def generate_passlib_hash(password):
    """Generate a password hash using PassLib (same as GridSpoke system)."""
    try:
        from passlib.context import CryptContext
        
        # Use the same configuration as in core/security.py
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Hash the password
        hashed = pwd_context.hash(password)
        return hashed
        
    except Exception as e:
        print(f"Error generating hash: {e}")
        return None

def verify_passlib_hash(password, hashed_password):
    """Verify a password against a hash using Pass<PERSON>ib."""
    try:
        from passlib.context import CryptContext
        
        # Use the same configuration as in core/security.py
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Verify the password
        return pwd_context.verify(password, hashed_password)
        
    except Exception as e:
        print(f"Error verifying hash: {e}")
        return False

if __name__ == "__main__":
    password = "demo12345"
    
    print(f"Password: {password}")
    
    # Generate hash
    hashed_password = generate_passlib_hash(password)
    if not hashed_password:
        sys.exit(1)
        
    print(f"Hashed password: {hashed_password}")
    
    # Verify hash
    is_valid = verify_passlib_hash(password, hashed_password)
    if is_valid:
        print("[OK] Hash verification successful")
    else:
        print("[ERROR] Hash verification failed")
        sys.exit(1)
        
    # Print SQL command to update the user
    print("\nSQL command to update user:")
    print(f"UPDATE users SET hashed_password = '{hashed_password.replace('$', '\\$')}', is_verified = true WHERE email = '<EMAIL>';")