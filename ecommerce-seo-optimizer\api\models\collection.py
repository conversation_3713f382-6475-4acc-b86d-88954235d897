"""
Collection and CollectionProduct models for smart collections feature.
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Text, Boolean, Integer, DateTime, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from ..core.database import Base


class Collection(Base):
    """Collection model for grouping products"""
    __tablename__ = "collections"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    seo_title = Column(String(255))
    meta_description = Column(String(500))
    handle = Column(String(255), nullable=False)  # URL-friendly slug
    
    # Collection management
    is_auto_generated = Column(Boolean, default=False)
    collection_type = Column(String(50), default="manual")  # manual, automatic, smart
    rules = Column(JSON)  # Rules for automatic collections
    sort_order = Column(String(50), default="manual")  # manual, created_at, price, name
    
    # SEO and display
    image_url = Column(String(500))
    is_published = Column(Boolean, default=True)
    publish_date = Column(DateTime)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    store = relationship("Store", back_populates="collections")
    collection_products = relationship("CollectionProduct", back_populates="collection", cascade="all, delete-orphan")
    products = relationship("Product", secondary="collection_products", back_populates="collections")
    
    def __repr__(self):
        return f"<Collection(id={self.id}, name='{self.name}', store_id={self.store_id})>"


class CollectionProduct(Base):
    """Junction table for collections and products with positioning"""
    __tablename__ = "collection_products"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    collection_id = Column(UUID(as_uuid=True), ForeignKey("collections.id"), nullable=False)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=False)
    position = Column(Integer, default=0)  # Position within the collection
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    collection = relationship("Collection", back_populates="collection_products")
    product = relationship("Product", back_populates="collection_products")
    
    def __repr__(self):
        return f"<CollectionProduct(collection_id={self.collection_id}, product_id={self.product_id}, position={self.position})>"
