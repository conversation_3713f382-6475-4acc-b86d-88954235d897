# GridSpoke Database Migration Testing
"""
Testing database migrations and schema changes for GridSpoke.
Tests Alembic migrations, data integrity, and rollback functionality.
"""

import pytest
import os
import tempfile
from unittest.mock import patch, MagicMock
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from alembic.config import Config
from alembic import command
from alembic.script import ScriptDirectory


class TestDatabaseMigrations:
    """Test Alembic database migrations"""
    
    @pytest.fixture
    def temp_database(self):
        """Create temporary database for migration testing"""
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        
        database_url = f"sqlite:///{temp_db.name}"
        engine = create_engine(database_url)
        
        yield engine, database_url
        
        # Cleanup
        engine.dispose()
        os.unlink(temp_db.name)
    
    @pytest.fixture
    def alembic_config(self, temp_database):
        """Create Alembic configuration for testing"""
        engine, database_url = temp_database
        
        # Create temporary alembic.ini
        config_content = f"""
[alembic]
script_location = migrations
sqlalchemy.url = {database_url}
file_template = %%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d-%%(rev)s_%%(slug)s
"""
        
        config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False)
        config_file.write(config_content)
        config_file.close()
        
        alembic_cfg = Config(config_file.name)
        
        yield alembic_cfg, engine
        
        # Cleanup
        os.unlink(config_file.name)
    
    def test_initial_migration_creates_tables(self, alembic_config):
        """Test initial migration creates all required tables"""
        try:
            from alembic.runtime.migration import MigrationContext
            from api.models import Base
            
            alembic_cfg, engine = alembic_config
            
            # Run initial migration
            with engine.connect() as connection:
                command.upgrade(alembic_cfg, "head")
                
                # Check that all tables were created
                inspector = engine.dialect.get_inspector(engine)
                table_names = inspector.get_table_names()
                
                expected_tables = [
                    'users',
                    'stores', 
                    'products',
                    'optimization_jobs',
                    'optimization_results',
                    'ai_model_usage',
                    'webhooks',
                    'api_keys'
                ]
                
                for table in expected_tables:
                    assert table in table_names, f"Table {table} not created"
                
                # Check alembic version table
                assert 'alembic_version' in table_names
                
        except ImportError:
            pytest.skip("Alembic migrations not available")
    
    def test_migration_rollback_functionality(self, alembic_config):
        """Test migration rollback functionality"""
        try:
            alembic_cfg, engine = alembic_config
            
            # Run migrations up to head
            command.upgrade(alembic_cfg, "head")
            
            # Get current revision
            with engine.connect() as connection:
                result = connection.execute(text("SELECT version_num FROM alembic_version"))
                current_revision = result.scalar()
            
            # Rollback one migration
            command.downgrade(alembic_cfg, "-1")
            
            # Verify rollback worked
            with engine.connect() as connection:
                result = connection.execute(text("SELECT version_num FROM alembic_version"))
                new_revision = result.scalar()
                
                assert new_revision != current_revision
            
            # Can upgrade again
            command.upgrade(alembic_cfg, "head")
            
        except ImportError:
            pytest.skip("Alembic migrations not available")
    
    def test_migration_data_preservation(self, alembic_config):
        """Test that migrations preserve existing data"""
        try:
            alembic_cfg, engine = alembic_config
            
            # Run initial migration
            command.upgrade(alembic_cfg, "head")
            
            # Insert test data
            with engine.connect() as connection:
                # Insert test user
                connection.execute(text("""
                    INSERT INTO users (id, email, password_hash, created_at) 
                    VALUES ('user_123', '<EMAIL>', 'hashed_password', datetime('now'))
                """))
                
                # Insert test store
                connection.execute(text("""
                    INSERT INTO stores (id, name, url, platform, user_id, created_at)
                    VALUES ('store_456', 'Test Store', 'https://test.com', 'woocommerce', 'user_123', datetime('now'))
                """))
                
                connection.commit()
            
            # Simulate running a new migration that adds a column
            # (In real scenario, this would be an actual migration file)
            with engine.connect() as connection:
                try:
                    connection.execute(text("ALTER TABLE stores ADD COLUMN api_version VARCHAR(10) DEFAULT '1.0'"))
                    connection.commit()
                except Exception:
                    pass  # Column might already exist
            
            # Verify data is still there
            with engine.connect() as connection:
                result = connection.execute(text("SELECT * FROM users WHERE id = 'user_123'"))
                user = result.fetchone()
                assert user is not None
                assert user[1] == '<EMAIL>'  # email field
                
                result = connection.execute(text("SELECT * FROM stores WHERE id = 'store_456'"))
                store = result.fetchone()
                assert store is not None
                assert store[1] == 'Test Store'  # name field
            
        except ImportError:
            pytest.skip("Alembic migrations not available")
    
    def test_migration_version_consistency(self, alembic_config):
        """Test migration version consistency"""
        try:
            alembic_cfg, engine = alembic_config
            
            # Get script directory
            script = ScriptDirectory.from_config(alembic_cfg)
            
            # Check migration chain consistency
            revisions = list(script.walk_revisions())
            
            # Should have at least one migration
            assert len(revisions) > 0
            
            # Each revision should have proper down_revision
            for revision in revisions:
                if revision.down_revision:
                    assert revision.down_revision in [r.revision for r in revisions]
            
        except ImportError:
            pytest.skip("Alembic migrations not available")


class TestDatabaseConstraints:
    """Test database constraints and relationships"""
    
    def test_foreign_key_constraints(self, db_session):
        """Test foreign key constraints are properly enforced"""
        try:
            from api.models import User, Store, Product
            
            # Create user and store
            user = User(
                id="user_constraints_test",
                email="<EMAIL>",
                password_hash="hashed_password"
            )
            db_session.add(user)
            db_session.commit()
            
            store = Store(
                id="store_constraints_test",
                name="Constraints Test Store",
                url="https://constraints-test.com",
                platform="woocommerce",
                user_id=user.id
            )
            db_session.add(store)
            db_session.commit()
            
            # Test valid foreign key
            product = Product(
                id="product_constraints_test",
                name="Test Product",
                store_id=store.id
            )
            db_session.add(product)
            db_session.commit()  # Should succeed
            
            # Test invalid foreign key
            invalid_product = Product(
                id="invalid_product_test",
                name="Invalid Product",
                store_id="nonexistent_store"
            )
            db_session.add(invalid_product)
            
            with pytest.raises(Exception):
                db_session.commit()  # Should fail due to foreign key constraint
            
        except ImportError:
            pytest.skip("Database models not available")
    
    def test_unique_constraints(self, db_session):
        """Test unique constraints are properly enforced"""
        try:
            from api.models import User, Store
            
            # Create first user
            user1 = User(
                id="user1_unique_test",
                email="<EMAIL>",
                password_hash="password1"
            )
            db_session.add(user1)
            db_session.commit()
            
            # Try to create second user with same email
            user2 = User(
                id="user2_unique_test", 
                email="<EMAIL>",  # Duplicate email
                password_hash="password2"
            )
            db_session.add(user2)
            
            with pytest.raises(Exception):
                db_session.commit()  # Should fail due to unique constraint
            
        except ImportError:
            pytest.skip("Database models not available")
    
    def test_not_null_constraints(self, db_session):
        """Test NOT NULL constraints are properly enforced"""
        try:
            from api.models import Store
            
            # Try to create store without required field
            store = Store(
                id="store_null_test",
                # name is missing (should be required)
                url="https://test.com",
                platform="woocommerce"
            )
            db_session.add(store)
            
            with pytest.raises(Exception):
                db_session.commit()  # Should fail due to NOT NULL constraint
            
        except ImportError:
            pytest.skip("Database models not available")
    
    def test_cascade_deletes(self, db_session):
        """Test cascade delete behavior"""
        try:
            from api.models import User, Store, Product
            
            # Create user with store and products
            user = User(
                id="user_cascade_test",
                email="<EMAIL>",
                password_hash="hashed_password"
            )
            db_session.add(user)
            db_session.commit()
            
            store = Store(
                id="store_cascade_test",
                name="Cascade Test Store",
                url="https://cascade-test.com",
                platform="woocommerce",
                user_id=user.id
            )
            db_session.add(store)
            db_session.commit()
            
            product = Product(
                id="product_cascade_test",
                name="Cascade Test Product",
                store_id=store.id
            )
            db_session.add(product)
            db_session.commit()
            
            # Delete user - should cascade to stores and products
            db_session.delete(user)
            db_session.commit()
            
            # Verify cascaded deletes
            remaining_stores = db_session.query(Store).filter_by(user_id=user.id).all()
            assert len(remaining_stores) == 0
            
            remaining_products = db_session.query(Product).filter_by(store_id=store.id).all()
            assert len(remaining_products) == 0
            
        except ImportError:
            pytest.skip("Database models not available")


class TestDatabaseIndexes:
    """Test database indexes and performance"""
    
    def test_index_creation(self, alembic_config):
        """Test that indexes are created properly"""
        try:
            alembic_cfg, engine = alembic_config
            
            # Run migrations
            command.upgrade(alembic_cfg, "head")
            
            # Check for important indexes
            inspector = engine.dialect.get_inspector(engine)
            
            # Check indexes on frequently queried columns
            tables_to_check = {
                'products': ['store_id', 'created_at'],
                'optimization_jobs': ['user_id', 'status', 'created_at'],
                'stores': ['user_id'],
                'users': ['email']
            }
            
            for table_name, expected_indexed_columns in tables_to_check.items():
                try:
                    indexes = inspector.get_indexes(table_name)
                    indexed_columns = set()
                    
                    for index in indexes:
                        indexed_columns.update(index['column_names'])
                    
                    for column in expected_indexed_columns:
                        assert column in indexed_columns or f"ix_{table_name}_{column}" in [idx['name'] for idx in indexes]
                        
                except Exception:
                    # Table might not exist in test setup
                    pass
            
        except ImportError:
            pytest.skip("Database inspection not available")
    
    def test_query_performance_with_indexes(self, db_session, create_test_data):
        """Test query performance benefits from indexes"""
        try:
            from api.models import Product, Store
            import time
            
            # Create test data
            stores, products = create_test_data(num_stores=5, num_products_per_store=100)
            
            # Test query with index (store_id)
            start_time = time.time()
            
            for store in stores:
                products_query = db_session.query(Product).filter_by(store_id=store.id).all()
                
            indexed_query_time = time.time() - start_time
            
            # Query should be reasonably fast with index
            assert indexed_query_time < 2.0  # Should complete in under 2 seconds
            
        except ImportError:
            pytest.skip("Database models not available")


class TestDataIntegrity:
    """Test data integrity during migrations and operations"""
    
    def test_data_type_migrations(self, alembic_config):
        """Test migrations that change data types"""
        try:
            alembic_cfg, engine = alembic_config
            
            # Run initial migration
            command.upgrade(alembic_cfg, "head")
            
            # Insert test data with specific type
            with engine.connect() as connection:
                connection.execute(text("""
                    INSERT INTO optimization_jobs (id, status, created_at, total_items, processed_items)
                    VALUES ('job_type_test', 'completed', datetime('now'), 100, 100)
                """))
                connection.commit()
            
            # Simulate data type change (e.g., changing integer to decimal)
            # In real scenario, this would be handled by Alembic migration
            with engine.connect() as connection:
                try:
                    # Add new column with different type
                    connection.execute(text("ALTER TABLE optimization_jobs ADD COLUMN cost_decimal DECIMAL(10,2)"))
                    
                    # Migrate data from integer to decimal
                    connection.execute(text("UPDATE optimization_jobs SET cost_decimal = total_items * 0.1"))
                    
                    connection.commit()
                    
                    # Verify data integrity
                    result = connection.execute(text("SELECT cost_decimal FROM optimization_jobs WHERE id = 'job_type_test'"))
                    cost = result.scalar()
                    assert cost == 10.0  # 100 * 0.1
                    
                except Exception:
                    # Migration might not be applicable in test setup
                    pass
            
        except ImportError:
            pytest.skip("Database migration testing not available")
    
    def test_json_field_migrations(self, alembic_config):
        """Test migrations involving JSON fields"""
        try:
            alembic_cfg, engine = alembic_config
            
            # Run migrations
            command.upgrade(alembic_cfg, "head")
            
            # Test JSON field handling
            with engine.connect() as connection:
                # Insert JSON data
                json_data = '{"keywords": ["seo", "optimization"], "score": 85.5}'
                connection.execute(text("""
                    INSERT INTO optimization_results (id, product_id, optimized_data, created_at)
                    VALUES ('result_json_test', 'product_123', :json_data, datetime('now'))
                """), {"json_data": json_data})
                connection.commit()
                
                # Verify JSON data integrity
                result = connection.execute(text("""
                    SELECT optimized_data FROM optimization_results WHERE id = 'result_json_test'
                """))
                stored_data = result.scalar()
                
                import json as json_module
                parsed_data = json_module.loads(stored_data)
                assert parsed_data["score"] == 85.5
                assert "seo" in parsed_data["keywords"]
            
        except ImportError:
            pytest.skip("JSON field testing not available")
    
    def test_database_backup_and_restore(self, temp_database):
        """Test database backup and restore functionality"""
        try:
            engine, database_url = temp_database
            
            # Create test data
            with engine.connect() as connection:
                connection.execute(text("""
                    CREATE TABLE IF NOT EXISTS test_backup (
                        id TEXT PRIMARY KEY,
                        data TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                
                connection.execute(text("""
                    INSERT INTO test_backup (id, data) VALUES ('backup_test', 'important data')
                """))
                connection.commit()
            
            # Create backup (simplified - real implementation would use proper backup tools)
            backup_file = tempfile.NamedTemporaryFile(suffix='.sql', delete=False)
            backup_file.close()
            
            # In real scenario, use proper backup command
            # For SQLite, we could copy the file
            import shutil
            original_db = database_url.replace('sqlite:///', '')
            shutil.copy2(original_db, backup_file.name)
            
            # Modify original data
            with engine.connect() as connection:
                connection.execute(text("UPDATE test_backup SET data = 'modified data' WHERE id = 'backup_test'"))
                connection.commit()
            
            # Restore from backup
            shutil.copy2(backup_file.name, original_db)
            
            # Verify restore
            with engine.connect() as connection:
                result = connection.execute(text("SELECT data FROM test_backup WHERE id = 'backup_test'"))
                data = result.scalar()
                assert data == 'important data'  # Should be restored
            
            # Cleanup
            os.unlink(backup_file.name)
            
        except Exception as e:
            pytest.skip(f"Backup/restore testing not available: {e}")


class TestDatabaseSecurity:
    """Test database security measures"""
    
    def test_sql_injection_protection(self, db_session):
        """Test ORM protection against SQL injection"""
        try:
            from api.models import Product
            
            # Test parameterized query (safe)
            malicious_input = "'; DROP TABLE products; --"
            
            # ORM should handle this safely
            products = db_session.query(Product).filter(
                Product.name.like(f"%{malicious_input}%")
            ).all()
            
            # Should execute without error and not drop tables
            assert isinstance(products, list)
            
        except ImportError:
            pytest.skip("Database models not available")
    
    def test_database_connection_security(self):
        """Test database connection security settings"""
        try:
            from api.core.database import get_database_url
            
            # Test that database URL doesn't expose credentials in logs
            db_url = get_database_url()
            
            # In production, credentials should be from environment
            assert 'password=' not in db_url.lower() or 'localhost' in db_url
            
        except ImportError:
            pytest.skip("Database configuration not available")
    
    def test_sensitive_data_encryption(self, db_session):
        """Test encryption of sensitive data fields"""
        try:
            from api.models import Store
            from api.core.security import encrypt_api_key
            
            # Test encrypted field storage
            sensitive_api_key = "super_secret_api_key_123"
            
            # Should be encrypted before storage
            encrypted_key = encrypt_api_key(sensitive_api_key)
            assert encrypted_key != sensitive_api_key
            
            store = Store(
                id="store_encryption_test",
                name="Encryption Test Store", 
                url="https://encryption-test.com",
                platform="woocommerce",
                api_key=encrypted_key  # Store encrypted version
            )
            
            db_session.add(store)
            db_session.commit()
            
            # Verify encrypted storage
            stored_store = db_session.get(Store, "store_encryption_test")
            assert stored_store.api_key != sensitive_api_key
            assert stored_store.api_key == encrypted_key
            
        except ImportError:
            pytest.skip("Encryption utilities not available")


class TestMigrationPerformance:
    """Test migration performance and optimization"""
    
    @pytest.mark.slow
    def test_large_table_migration_performance(self, alembic_config):
        """Test migration performance on large tables"""
        try:
            alembic_cfg, engine = alembic_config
            
            # Run initial migration
            command.upgrade(alembic_cfg, "head")
            
            # Create large dataset
            with engine.connect() as connection:
                # Insert many records (simulate large table)
                for i in range(1000):  # Smaller number for testing
                    connection.execute(text("""
                        INSERT INTO products (id, name, store_id, created_at)
                        VALUES (:id, :name, 'store_perf_test', datetime('now'))
                    """), {"id": f"product_perf_{i}", "name": f"Performance Test Product {i}"})
                
                connection.commit()
            
            # Time a migration operation (e.g., adding index)
            import time
            start_time = time.time()
            
            with engine.connect() as connection:
                connection.execute(text("CREATE INDEX idx_products_name_perf ON products (name)"))
                connection.commit()
            
            migration_time = time.time() - start_time
            
            # Should complete in reasonable time
            assert migration_time < 10.0  # 10 seconds for 1000 records
            
        except ImportError:
            pytest.skip("Performance migration testing not available")
    
    def test_migration_rollback_performance(self, alembic_config):
        """Test rollback performance"""
        try:
            alembic_cfg, engine = alembic_config
            
            # Run migration
            command.upgrade(alembic_cfg, "head")
            
            # Time rollback operation
            import time
            start_time = time.time()
            
            command.downgrade(alembic_cfg, "-1")
            
            rollback_time = time.time() - start_time
            
            # Rollback should be fast
            assert rollback_time < 5.0
            
            # Upgrade again to verify
            command.upgrade(alembic_cfg, "head")
            
        except ImportError:
            pytest.skip("Migration rollback testing not available")


# Test Fixtures for Migration Testing
@pytest.fixture
def create_test_data():
    """Fixture to create test data for migration testing"""
    def _create_data(num_stores=2, num_products_per_store=10):
        try:
            from api.models import User, Store, Product
            from api.core.database import get_db
            
            db = next(get_db())
            
            users = []
            stores = []
            products = []
            
            # Create users
            for i in range(num_stores):
                user = User(
                    id=f"migration_user_{i}",
                    email=f"migration_user_{i}@example.com",
                    password_hash="hashed_password"
                )
                db.add(user)
                users.append(user)
            
            db.commit()
            
            # Create stores
            for i, user in enumerate(users):
                store = Store(
                    id=f"migration_store_{i}",
                    name=f"Migration Test Store {i}",
                    url=f"https://migration-test-{i}.com",
                    platform="woocommerce",
                    user_id=user.id
                )
                db.add(store)
                stores.append(store)
            
            db.commit()
            
            # Create products
            for store in stores:
                for j in range(num_products_per_store):
                    product = Product(
                        id=f"migration_product_{store.id}_{j}",
                        name=f"Migration Test Product {j}",
                        store_id=store.id
                    )
                    db.add(product)
                    products.append(product)
            
            db.commit()
            
            return stores, products
            
        except ImportError:
            pytest.skip("Test data creation not available")
    
    return _create_data
