<?php
/**
 * Base test case for GridSpoke SEO Connector tests
 */

class GridSpoke_Test_Case extends WP_UnitTestCase {
    
    /**
     * API mock instance
     */
    protected $api_mock;
    
    /**
     * Test data helper
     */
    protected $test_data;
    
    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();
        
        // Initialize test helpers
        $this->api_mock = new GridSpoke_API_Mock();
        $this->test_data = new GridSpoke_Test_Data();
        
        // Reset plugin settings
        $this->reset_plugin_settings();
        
        // Clear any existing logs
        $this->clear_test_logs();
        
        // Set up test user
        $this->setup_test_user();
    }
    
    /**
     * Tear down test environment
     */
    public function tearDown(): void {
        // Clean up test data
        $this->cleanup_test_data();
        
        // Reset API mock
        $this->api_mock->reset();
        
        parent::tearDown();
    }
    
    /**
     * Reset plugin settings to defaults
     */
    protected function reset_plugin_settings() {
        delete_option('gridspoke_seo_settings');
        delete_transient('gridspoke_access_token');
        delete_option('gridspoke_refresh_token');
        delete_option('gridspoke_store_id');
        delete_option('gridspoke_user_info');
    }
    
    /**
     * Clear test logs
     */
    protected function clear_test_logs() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        $wpdb->query("TRUNCATE TABLE $table_name");
    }
    
    /**
     * Set up test user with admin capabilities
     */
    protected function setup_test_user() {
        $this->user_id = $this->factory->user->create(array(
            'role' => 'administrator',
            'user_email' => '<EMAIL>',
            'user_login' => 'gridspoke_test_admin'
        ));
        
        wp_set_current_user($this->user_id);
    }
    
    /**
     * Clean up test data
     */
    protected function cleanup_test_data() {
        // Remove test products
        $test_products = get_posts(array(
            'post_type' => 'product',
            'meta_key' => '_gridspoke_test_product',
            'meta_value' => '1',
            'posts_per_page' => -1
        ));
        
        foreach ($test_products as $product) {
            wp_delete_post($product->ID, true);
        }
        
        // Clean up test user
        if (!empty($this->user_id)) {
            wp_delete_user($this->user_id);
        }
    }
    
    /**
     * Create test WooCommerce product
     */
    protected function create_test_woo_product($args = array()) {
        if (!class_exists('WC_Product_Simple')) {
            $this->markTestSkipped('WooCommerce not available');
        }
        
        $defaults = array(
            'name' => 'Test Product',
            'description' => 'Test product description',
            'short_description' => 'Test short description',
            'sku' => 'TEST-SKU-' . uniqid(),
            'price' => '19.99',
            'status' => 'publish'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $product = new WC_Product_Simple();
        $product->set_name($args['name']);
        $product->set_description($args['description']);
        $product->set_short_description($args['short_description']);
        $product->set_sku($args['sku']);
        $product->set_price($args['price']);
        $product->set_status($args['status']);
        
        $product_id = $product->save();
        
        // Mark as test product
        update_post_meta($product_id, '_gridspoke_test_product', '1');
        
        return $product_id;
    }
    
    /**
     * Assert API request was made
     */
    protected function assertApiRequestMade($method, $endpoint, $data = null) {
        $requests = $this->api_mock->get_requests();
        
        foreach ($requests as $request) {
            if ($request['method'] === $method && $request['endpoint'] === $endpoint) {
                if ($data === null || $request['data'] === $data) {
                    return true;
                }
            }
        }
        
        $this->fail("Expected API request not found: $method $endpoint");
    }
    
    /**
     * Assert log entry exists
     */
    protected function assertLogEntryExists($level, $message_pattern = null) {
        $logger = GridSpoke_Logger::get_instance();
        $logs = $logger->get_logs(array('level' => $level));
        
        if (empty($logs)) {
            $this->fail("No log entries found for level: $level");
        }
        
        if ($message_pattern !== null) {
            foreach ($logs as $log) {
                if (preg_match($message_pattern, $log['message'])) {
                    return true;
                }
            }
            $this->fail("No log entry found matching pattern: $message_pattern");
        }
        
        return true;
    }
    
    /**
     * Mock successful API response
     */
    protected function mock_api_success($endpoint, $response_data) {
        $this->api_mock->mock_response($endpoint, array(
            'status_code' => 200,
            'body' => json_encode($response_data)
        ));
    }
    
    /**
     * Mock API error response
     */
    protected function mock_api_error($endpoint, $status_code = 400, $error_message = 'API Error') {
        $this->api_mock->mock_response($endpoint, array(
            'status_code' => $status_code,
            'body' => json_encode(array('detail' => $error_message))
        ));
    }
    
    /**
     * Get plugin settings
     */
    protected function get_plugin_settings() {
        return GridSpoke_SEO_Connector::get_settings();
    }
    
    /**
     * Update plugin settings
     */
    protected function update_plugin_settings($settings) {
        $current_settings = $this->get_plugin_settings();
        $new_settings = wp_parse_args($settings, $current_settings);
        update_option('gridspoke_seo_settings', $new_settings);
    }
}
