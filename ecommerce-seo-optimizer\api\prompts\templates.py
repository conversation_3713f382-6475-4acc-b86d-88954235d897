"""
Prompt template management system for GridSpoke AI agents.

This module contains all prompt templates used for SEO optimization and content generation.
Templates are organized by functionality and use Jinja2-style formatting for flexibility.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class PromptTemplate(BaseModel):
    """Individual prompt template with metadata."""
    
    name: str
    template: str
    description: str
    required_fields: List[str]
    optional_fields: List[str] = Field(default_factory=list)
    version: str = "1.0"
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    def format(self, **kwargs) -> str:
        """Format the template with provided parameters."""
        # Validate required fields
        missing_fields = [field for field in self.required_fields if field not in kwargs]
        if missing_fields:
            raise ValueError(f"Missing required fields: {missing_fields}")
        
        # Apply template formatting
        try:
            return self.template.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"Template formatting error: {e}")


class SEOPromptTemplates:
    """SEO-specific prompt templates for product optimization."""
    
    PRODUCT_TITLE_OPTIMIZATION = PromptTemplate(
        name="product_title_optimization",
        description="Generate SEO-optimized product titles",
        required_fields=["product_name", "category"],
        optional_fields=["features", "brand", "target_keywords", "price_range"],
        template="""You are an expert ecommerce SEO specialist. Generate an SEO-optimized product title for the following product.

Product Information:
- Name: {product_name}
- Category: {category}
{features_section}
{brand_section}
{keywords_section}
{price_section}

Requirements:
- Maximum 60 characters (Google's display limit)
- Include primary keyword naturally
- Create buying intent and urgency
- Be compelling and clickable
- Follow ecommerce title best practices
- Avoid keyword stuffing
- Include relevant modifiers (size, color, etc.) if provided

Generate ONLY the optimized title, no explanations or additional text."""
    )
    
    META_DESCRIPTION_OPTIMIZATION = PromptTemplate(
        name="meta_description_optimization", 
        description="Generate SEO-optimized meta descriptions",
        required_fields=["product_name", "category", "title"],
        optional_fields=["features", "benefits", "call_to_action", "brand"],
        template="""You are an expert ecommerce SEO specialist. Generate an SEO-optimized meta description for this product.

Product Information:
- Title: {title}
- Product: {product_name}
- Category: {category}
{features_section}
{benefits_section}
{brand_section}

Requirements:
- Maximum 155 characters (Google's display limit)
- Include a clear call-to-action
- Mention key benefits or features
- Create urgency or appeal
- Include relevant keywords naturally
- Be compelling and encourage clicks
- Match the search intent

Generate ONLY the meta description, no explanations or additional text."""
    )
    
    PRODUCT_DESCRIPTION_OPTIMIZATION = PromptTemplate(
        name="product_description_optimization",
        description="Generate comprehensive SEO-optimized product descriptions",
        required_fields=["product_name", "category", "features"],
        optional_fields=["specifications", "benefits", "target_audience", "brand", "use_cases"],
        template="""You are an expert ecommerce copywriter and SEO specialist. Create a compelling, SEO-optimized product description.

Product Information:
- Name: {product_name}
- Category: {category}
- Features: {features}
{specifications_section}
{benefits_section}
{target_audience_section}
{brand_section}
{use_cases_section}

Requirements:
- 300-500 words for optimal SEO length
- Include H2/H3 subheadings for structure
- Incorporate relevant keywords naturally (avoid stuffing)
- Focus on benefits, not just features
- Address customer pain points and concerns
- Include social proof elements if relevant
- Use persuasive language that converts
- Structure for readability (short paragraphs, bullet points)
- Include technical specifications in a clear format
- End with a compelling call-to-action

Generate a complete product description in HTML format with proper headings and structure."""
    )
    
    KEYWORD_GENERATION = PromptTemplate(
        name="keyword_generation",
        description="Generate relevant keywords for SEO optimization",
        required_fields=["product_name", "category"],
        optional_fields=["features", "target_audience", "competitors"],
        template="""You are an expert SEO keyword researcher. Generate a comprehensive list of keywords for this product.

Product Information:
- Product: {product_name}
- Category: {category}
{features_section}
{target_audience_section}
{competitors_section}

Generate keywords in these categories:
1. Primary Keywords (3-5): High-volume, high-intent keywords
2. Long-tail Keywords (5-8): Specific, lower competition phrases
3. Buyer Intent Keywords (3-5): Keywords indicating purchase intent
4. Local Keywords (2-3): Location-based if applicable
5. Competitor Keywords (3-5): Keywords competitors rank for

Format as JSON:
{{
    "primary_keywords": ["keyword1", "keyword2"],
    "long_tail_keywords": ["long tail phrase 1", "long tail phrase 2"],
    "buyer_intent_keywords": ["buy keyword1", "purchase keyword2"],
    "local_keywords": ["local keyword1", "local keyword2"],
    "competitor_keywords": ["competitor keyword1", "competitor keyword2"]
}}

Generate ONLY the JSON response, no additional text."""
    )


class ContentGenerationPrompts:
    """Prompt templates for blog posts, FAQs, and other content."""
    
    BLOG_POST_GENERATION = PromptTemplate(
        name="blog_post_generation",
        description="Generate SEO-optimized blog posts for product categories",
        required_fields=["topic", "target_keywords", "word_count"],
        optional_fields=["products", "target_audience", "tone", "include_products"],
        template="""You are an expert content marketer and SEO writer. Create a comprehensive blog post on the specified topic.

Blog Post Requirements:
- Topic: {topic}
- Target Keywords: {target_keywords}
- Word Count: {word_count}
{tone_section}
{audience_section}
{products_section}

Content Structure Requirements:
1. Compelling headline (H1) with primary keyword
2. Introduction with hook and overview
3. 4-6 main sections (H2) covering key aspects
4. Subsections (H3) for detailed points
5. Conclusion with call-to-action
6. Meta description (155 chars)

SEO Requirements:
- Include target keywords naturally (keyword density 1-2%)
- Use semantic keywords and variations
- Internal linking opportunities (mention where to link)
- External link suggestions to authoritative sources
- Optimize for featured snippets (include FAQ section)
- Include relevant statistics and data

Format the output as:
{{
    "title": "Blog post title",
    "meta_description": "SEO meta description",
    "content": "Full HTML content with proper headings",
    "internal_links": ["suggested internal link anchor texts"],
    "external_links": ["suggested external link opportunities"],
    "target_keywords_used": ["list of keywords used"]
}}"""
    )
    
    FAQ_GENERATION = PromptTemplate(
        name="faq_generation",
        description="Generate product-specific FAQs for voice search optimization",
        required_fields=["product_name", "category"],
        optional_fields=["common_concerns", "specifications", "features"],
        template="""You are an expert at creating customer-focused FAQs. Generate a comprehensive FAQ section for this product.

Product Information:
- Product: {product_name}
- Category: {category}
{concerns_section}
{specifications_section}
{features_section}

Generate 8-12 frequently asked questions that:
1. Address common customer concerns and objections
2. Cover technical specifications and compatibility
3. Include buying decision factors (size, warranty, shipping)
4. Optimize for voice search (natural language questions)
5. Include long-tail keyword opportunities
6. Address comparison questions vs competitors

Format as JSON:
{{
    "faqs": [
        {{
            "question": "Natural language question",
            "answer": "Comprehensive answer (50-100 words)",
            "keywords": ["relevant keywords in this Q&A"]
        }}
    ]
}}

Generate ONLY the JSON response with complete FAQ data."""
    )
    
    BUYERS_GUIDE_GENERATION = PromptTemplate(
        name="buyers_guide_generation",
        description="Generate comprehensive buyer's guides for product categories",
        required_fields=["category", "products"],
        optional_fields=["budget_ranges", "use_cases", "comparison_factors"],
        template="""You are an expert product reviewer and buyer's guide creator. Create a comprehensive buyer's guide for this product category.

Category Information:
- Category: {category}
- Products to Include: {products}
{budget_section}
{use_cases_section}
{comparison_section}

Guide Structure:
1. Introduction - Why this guide matters
2. Key factors to consider when buying
3. Product comparisons and recommendations
4. Budget considerations and value picks
5. Common mistakes to avoid
6. Final recommendations by use case

Requirements:
- 1500-2000 words for comprehensive coverage
- Include comparison tables where relevant
- Pros and cons for each recommended product
- Clear recommendations for different user types
- SEO-optimized with relevant keywords
- Include buying tips and expert advice

Format as structured HTML with proper headings and sections."""
    )


class ProductOptimizationPrompts:
    """Specialized prompts for different aspects of product optimization."""
    
    ALT_TEXT_GENERATION = PromptTemplate(
        name="alt_text_generation",
        description="Generate SEO-optimized alt text for product images",
        required_fields=["product_name", "image_description"],
        optional_fields=["category", "key_features", "context"],
        template="""Generate SEO-optimized alt text for this product image.

Product: {product_name}
Image Description: {image_description}
{category_section}
{features_section}
{context_section}

Requirements:
- Maximum 125 characters (screen reader optimal)
- Describe the image accurately
- Include product name and key identifier
- Add relevant keywords naturally
- Be helpful for visually impaired users
- Avoid "image of" or "picture of" phrases

Generate ONLY the alt text, no additional text."""
    )
    
    SCHEMA_MARKUP_GENERATION = PromptTemplate(
        name="schema_markup_generation",
        description="Generate JSON-LD schema markup for products",
        required_fields=["product_name", "category", "price", "description"],
        optional_fields=["brand", "sku", "availability", "rating", "reviews"],
        template="""Generate JSON-LD schema markup for this product to enable rich snippets.

Product Information:
- Name: {product_name}
- Category: {category}
- Price: {price}
- Description: {description}
{brand_section}
{sku_section}
{availability_section}
{rating_section}
{reviews_section}

Generate complete Product schema markup including:
- Basic product information
- Offers with price and availability
- AggregateRating if rating data provided
- Review if review data provided
- Brand information
- Image URLs (placeholder structure)

Output valid JSON-LD markup only."""
    )


class PromptManager:
    """Manager class for handling all prompt templates."""
    
    def __init__(self):
        """Initialize the prompt manager with all template categories."""
        self.seo_templates = SEOPromptTemplates()
        self.content_templates = ContentGenerationPrompts()
        self.product_templates = ProductOptimizationPrompts()
    
    def get_template(self, category: str, template_name: str) -> PromptTemplate:
        """
        Retrieve a specific template by category and name.
        
        Args:
            category: Template category (seo, content, product)
            template_name: Name of the template
            
        Returns:
            PromptTemplate instance
            
        Raises:
            ValueError: If template not found
        """
        template_map = {
            "seo": self.seo_templates,
            "content": self.content_templates, 
            "product": self.product_templates
        }
        
        if category not in template_map:
            raise ValueError(f"Unknown template category: {category}")
        
        template_obj = template_map[category]
        
        if not hasattr(template_obj, template_name.upper()):
            raise ValueError(f"Template '{template_name}' not found in category '{category}'")
        
        return getattr(template_obj, template_name.upper())
    
    def format_template(
        self, 
        category: str, 
        template_name: str, 
        **kwargs
    ) -> str:
        """
        Format a template with provided data.
        
        Args:
            category: Template category
            template_name: Template name
            **kwargs: Template variables
            
        Returns:
            Formatted prompt string
        """
        template = self.get_template(category, template_name)
        
        # Process optional sections
        formatted_kwargs = self._process_optional_sections(kwargs)
        
        return template.format(**formatted_kwargs)
    
    def _process_optional_sections(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Process optional sections for template formatting."""
        processed = kwargs.copy()
        
        # Handle common optional sections
        sections = {
            'features': 'features_section',
            'brand': 'brand_section', 
            'target_keywords': 'keywords_section',
            'price_range': 'price_section',
            'benefits': 'benefits_section',
            'specifications': 'specifications_section',
            'target_audience': 'target_audience_section',
            'use_cases': 'use_cases_section',
            'common_concerns': 'concerns_section',
            'competitors': 'competitors_section',
            'tone': 'tone_section',
            'products': 'products_section',
            'budget_ranges': 'budget_section',
            'comparison_factors': 'comparison_section',
            'category': 'category_section',
            'key_features': 'features_section',
            'context': 'context_section',
            'sku': 'sku_section',
            'availability': 'availability_section',
            'rating': 'rating_section',
            'reviews': 'reviews_section'
        }
        
        for field, section_name in sections.items():
            if field in kwargs and kwargs[field]:
                if isinstance(kwargs[field], list):
                    processed[section_name] = f"- {field.replace('_', ' ').title()}: {', '.join(kwargs[field])}\n"
                else:
                    processed[section_name] = f"- {field.replace('_', ' ').title()}: {kwargs[field]}\n"
            else:
                processed[section_name] = ""
        
        return processed
    
    def list_templates(self, category: Optional[str] = None) -> Dict[str, List[str]]:
        """
        List all available templates.
        
        Args:
            category: Optional category filter
            
        Returns:
            Dictionary of categories and their templates
        """
        all_templates = {
            "seo": [
                "product_title_optimization",
                "meta_description_optimization", 
                "product_description_optimization",
                "keyword_generation"
            ],
            "content": [
                "blog_post_generation",
                "faq_generation",
                "buyers_guide_generation"
            ],
            "product": [
                "alt_text_generation",
                "schema_markup_generation"
            ]
        }
        
        if category:
            return {category: all_templates.get(category, [])}
        
        return all_templates
