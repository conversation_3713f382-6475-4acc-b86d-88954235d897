"""
Smart Collections Celery Tasks for background processing.
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from celery import current_task
from celery.exceptions import Retry, Ignore

# Add the API directory to the Python path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '../../api'))

from workers.celery_app import celery_app
from workers.utils.progress_tracker import track_progress, update_task_status
from workers.utils.redis_client import get_redis_client
from workers.utils.error_handler import handle_task_error, retry_with_backoff

logger = logging.getLogger(__name__)


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    acks_late=True
)
def create_automatic_collections_task(self, store_id: str, custom_rules: Optional[List[Dict[str, Any]]] = None):
    """
    Background task to create automatic collections for a store.
    
    Args:
        store_id: Store identifier
        custom_rules: Optional custom collection rules
    
    Returns:
        Dict containing creation results
    """
    try:
        from api.services.collections_service import SmartCollectionsService, CollectionRule
        from api.crud.store import get_store_by_id
        from api.core.database import get_db
        
        logger.info(f"Creating automatic collections for store {store_id}")
        
        # Initialize progress tracking
        track_progress(self.request.id, 0, 4, "Initializing collection creation")
        
        # Get database session
        db = next(get_db())
        
        # Verify store exists
        store = get_store_by_id(db, store_id)
        if not store:
            raise ValueError(f"Store {store_id} not found")
        
        track_progress(self.request.id, 1, 4, "Store verified, analyzing products")
        
        # Parse custom rules if provided
        parsed_rules = None
        if custom_rules:
            parsed_rules = [CollectionRule(**rule) for rule in custom_rules]
        
        # Create collections service
        service = SmartCollectionsService()
        
        track_progress(self.request.id, 2, 4, "Creating collections")
        
        # Create automatic collections
        collections = await service.create_automatic_collections(store_id, parsed_rules)
        
        track_progress(self.request.id, 3, 4, "Finalizing collection setup")
        
        # Cache results
        redis_client = get_redis_client()
        collection_data = {
            'store_id': store_id,
            'collections': collections,
            'created_count': len(collections),
            'created_at': datetime.utcnow().isoformat(),
            'task_id': self.request.id
        }
        
        redis_client.setex(
            f"collections:automatic:{store_id}",
            86400,  # 24 hours
            json.dumps(collection_data, default=str)
        )
        
        # Complete progress tracking
        track_progress(self.request.id, 4, 4, "Automatic collections created successfully")
        
        logger.info(f"Successfully created {len(collections)} automatic collections for store {store_id}")
        
        return {
            'status': 'success',
            'store_id': store_id,
            'collections_created': len(collections),
            'collections': collections,
            'task_id': self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error creating automatic collections for store {store_id}: {str(e)}")
        handle_task_error(self, e, f"automatic_collections:{store_id}")
        raise


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 90},
    acks_late=True
)
def optimize_category_pages_task(self, store_id: str, categories: Optional[List[str]] = None):
    """
    Background task to optimize category pages for SEO.
    
    Args:
        store_id: Store identifier
        categories: Optional list of specific categories to optimize
    
    Returns:
        Dict containing optimization results
    """
    try:
        from api.services.collections_service import SmartCollectionsService
        from api.crud.store import get_store_by_id
        from api.core.database import get_db
        
        logger.info(f"Optimizing category pages for store {store_id}")
        
        # Initialize progress tracking
        track_progress(self.request.id, 0, 5, "Initializing category optimization")
        
        # Get database session
        db = next(get_db())
        
        # Verify store exists
        store = get_store_by_id(db, store_id)
        if not store:
            raise ValueError(f"Store {store_id} not found")
        
        track_progress(self.request.id, 1, 5, "Store verified, analyzing categories")
        
        # Create collections service
        service = SmartCollectionsService()
        
        track_progress(self.request.id, 2, 5, "Generating SEO content for categories")
        
        # Optimize category pages
        optimizations = await service.optimize_category_pages(store_id, categories)
        
        track_progress(self.request.id, 3, 5, "Processing optimization results")
        
        # Cache results
        redis_client = get_redis_client()
        optimization_data = {
            'store_id': store_id,
            'optimizations': [opt.dict() for opt in optimizations],
            'categories_optimized': len(optimizations),
            'created_at': datetime.utcnow().isoformat(),
            'task_id': self.request.id
        }
        
        redis_client.setex(
            f"category_optimization:{store_id}",
            86400,  # 24 hours
            json.dumps(optimization_data, default=str)
        )
        
        track_progress(self.request.id, 4, 5, "Saving optimization data")
        
        # Store optimization results in database if needed
        # This could be extended to save optimized content to database
        
        # Complete progress tracking
        track_progress(self.request.id, 5, 5, "Category optimization completed")
        
        logger.info(f"Successfully optimized {len(optimizations)} categories for store {store_id}")
        
        return {
            'status': 'success',
            'store_id': store_id,
            'categories_optimized': len(optimizations),
            'optimizations': [opt.dict() for opt in optimizations],
            'task_id': self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error optimizing categories for store {store_id}: {str(e)}")
        handle_task_error(self, e, f"category_optimization:{store_id}")
        raise


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    acks_late=True
)
def generate_internal_links_task(self, store_id: str, max_suggestions: int = 50):
    """
    Background task to generate internal linking suggestions.
    
    Args:
        store_id: Store identifier
        max_suggestions: Maximum number of suggestions to generate
    
    Returns:
        Dict containing internal linking suggestions
    """
    try:
        from api.services.collections_service import SmartCollectionsService
        from api.crud.store import get_store_by_id
        from api.core.database import get_db
        
        logger.info(f"Generating internal linking strategy for store {store_id}")
        
        # Initialize progress tracking
        track_progress(self.request.id, 0, 4, "Initializing internal linking analysis")
        
        # Get database session
        db = next(get_db())
        
        # Verify store exists
        store = get_store_by_id(db, store_id)
        if not store:
            raise ValueError(f"Store {store_id} not found")
        
        track_progress(self.request.id, 1, 4, "Store verified, analyzing products")
        
        # Create collections service
        service = SmartCollectionsService()
        
        track_progress(self.request.id, 2, 4, "Generating link suggestions")
        
        # Generate internal linking suggestions
        suggestions = await service.generate_internal_linking_strategy(store_id, max_suggestions)
        
        track_progress(self.request.id, 3, 4, "Processing link recommendations")
        
        # Cache results
        redis_client = get_redis_client()
        linking_data = {
            'store_id': store_id,
            'suggestions': [suggestion.dict() for suggestion in suggestions],
            'total_suggestions': len(suggestions),
            'created_at': datetime.utcnow().isoformat(),
            'task_id': self.request.id
        }
        
        redis_client.setex(
            f"internal_links:{store_id}",
            86400,  # 24 hours
            json.dumps(linking_data, default=str)
        )
        
        # Complete progress tracking
        track_progress(self.request.id, 4, 4, "Internal linking strategy completed")
        
        logger.info(f"Successfully generated {len(suggestions)} internal link suggestions for store {store_id}")
        
        return {
            'status': 'success',
            'store_id': store_id,
            'suggestions_generated': len(suggestions),
            'suggestions': [suggestion.dict() for suggestion in suggestions],
            'task_id': self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error generating internal links for store {store_id}: {str(e)}")
        handle_task_error(self, e, f"internal_links:{store_id}")
        raise


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 120},
    acks_late=True
)
def update_automatic_collections_task(self, store_id: str):
    """
    Background task to update existing automatic collections.
    
    Args:
        store_id: Store identifier
    
    Returns:
        Dict containing update statistics
    """
    try:
        from api.services.collections_service import SmartCollectionsService
        from api.crud.store import get_store_by_id
        from api.core.database import get_db
        
        logger.info(f"Updating automatic collections for store {store_id}")
        
        # Initialize progress tracking
        track_progress(self.request.id, 0, 3, "Initializing collection update")
        
        # Get database session
        db = next(get_db())
        
        # Verify store exists
        store = get_store_by_id(db, store_id)
        if not store:
            raise ValueError(f"Store {store_id} not found")
        
        track_progress(self.request.id, 1, 3, "Analyzing existing collections")
        
        # Create collections service
        service = SmartCollectionsService()
        
        # Update automatic collections
        stats = await service.update_collections_automatically(store_id)
        
        track_progress(self.request.id, 2, 3, "Finalizing collection updates")
        
        # Cache results
        redis_client = get_redis_client()
        update_data = {
            'store_id': store_id,
            'stats': stats,
            'updated_at': datetime.utcnow().isoformat(),
            'task_id': self.request.id
        }
        
        redis_client.setex(
            f"collection_update:{store_id}",
            3600,  # 1 hour
            json.dumps(update_data, default=str)
        )
        
        # Complete progress tracking
        track_progress(self.request.id, 3, 3, "Collection update completed")
        
        logger.info(f"Successfully updated collections for store {store_id}: {stats}")
        
        return {
            'status': 'success',
            'store_id': store_id,
            'update_stats': stats,
            'task_id': self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error updating collections for store {store_id}: {str(e)}")
        handle_task_error(self, e, f"collection_update:{store_id}")
        raise


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 300},
    acks_late=True
)
def bulk_collection_optimization_task(self, store_id: str, optimization_types: List[str]):
    """
    Background task to run multiple collection optimization types.
    
    Args:
        store_id: Store identifier
        optimization_types: List of optimization types to run
    
    Returns:
        Dict containing all optimization results
    """
    try:
        from api.services.collections_service import SmartCollectionsService
        from api.crud.store import get_store_by_id
        from api.core.database import get_db
        
        logger.info(f"Running bulk collection optimization for store {store_id}")
        
        # Initialize progress tracking
        total_steps = len(optimization_types) + 1
        track_progress(self.request.id, 0, total_steps, "Initializing bulk optimization")
        
        # Get database session
        db = next(get_db())
        
        # Verify store exists
        store = get_store_by_id(db, store_id)
        if not store:
            raise ValueError(f"Store {store_id} not found")
        
        # Create collections service
        service = SmartCollectionsService()
        results = {}
        
        step = 1
        
        # Run each optimization type
        for optimization_type in optimization_types:
            track_progress(
                self.request.id, 
                step, 
                total_steps, 
                f"Running {optimization_type} optimization"
            )
            
            if optimization_type == "automatic_collections":
                collections = await service.create_automatic_collections(store_id)
                results["automatic_collections"] = {
                    "collections_created": len(collections),
                    "collections": collections
                }
                
            elif optimization_type == "category_optimization":
                optimizations = await service.optimize_category_pages(store_id)
                results["category_optimization"] = {
                    "categories_optimized": len(optimizations),
                    "optimizations": [opt.dict() for opt in optimizations]
                }
                
            elif optimization_type == "internal_linking":
                suggestions = await service.generate_internal_linking_strategy(store_id)
                results["internal_linking"] = {
                    "suggestions_generated": len(suggestions),
                    "suggestions": [suggestion.dict() for suggestion in suggestions]
                }
                
            elif optimization_type == "collection_update":
                stats = await service.update_collections_automatically(store_id)
                results["collection_update"] = {
                    "update_stats": stats
                }
            
            step += 1
        
        # Cache comprehensive results
        redis_client = get_redis_client()
        bulk_data = {
            'store_id': store_id,
            'optimization_types': optimization_types,
            'results': results,
            'completed_at': datetime.utcnow().isoformat(),
            'task_id': self.request.id
        }
        
        redis_client.setex(
            f"bulk_optimization:{store_id}",
            86400,  # 24 hours
            json.dumps(bulk_data, default=str)
        )
        
        # Complete progress tracking
        track_progress(self.request.id, total_steps, total_steps, "Bulk optimization completed")
        
        logger.info(f"Successfully completed bulk optimization for store {store_id}")
        
        return {
            'status': 'success',
            'store_id': store_id,
            'optimization_types': optimization_types,
            'results': results,
            'task_id': self.request.id
        }
        
    except Exception as e:
        logger.error(f"Error in bulk optimization for store {store_id}: {str(e)}")
        handle_task_error(self, e, f"bulk_optimization:{store_id}")
        raise


# Scheduled task for automatic collection maintenance
@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 600}
)
def scheduled_collection_maintenance():
    """
    Scheduled task to maintain automatic collections across all stores.
    
    Runs daily to update automatic collections and keep them current.
    """
    try:
        from api.crud.store import get_all_stores
        from api.core.database import get_db
        
        logger.info("Starting scheduled collection maintenance")
        
        # Get all active stores
        db = next(get_db())
        stores = get_all_stores(db, is_active=True)
        
        maintenance_results = []
        
        for store in stores:
            try:
                # Queue collection update for each store
                result = update_automatic_collections_task.delay(str(store.id))
                maintenance_results.append({
                    "store_id": str(store.id),
                    "task_id": result.id,
                    "status": "queued"
                })
                
            except Exception as e:
                logger.error(f"Error queuing maintenance for store {store.id}: {str(e)}")
                maintenance_results.append({
                    "store_id": str(store.id),
                    "status": "error",
                    "error": str(e)
                })
        
        logger.info(f"Scheduled maintenance for {len(maintenance_results)} stores")
        
        return {
            'status': 'success',
            'stores_processed': len(maintenance_results),
            'results': maintenance_results,
            'scheduled_at': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in scheduled collection maintenance: {str(e)}")
        raise
