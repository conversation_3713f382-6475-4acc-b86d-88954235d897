# GridSpoke Reporting Service - PDF Generation and Scheduled Reports
# Phase 8: Comprehensive reporting system with PDF export and automated delivery

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, BinaryIO
import json
import tempfile
import io
from pathlib import Path
from enum import Enum
from pydantic import BaseModel, Field
import aiofiles
from weasyprint import HTML, CSS
from jinja2 import Environment, FileSystemLoader, Template
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from matplotlib.backends.backend_pdf import PdfPages
import plotly.graph_objects as go
import plotly.express as px
from plotly.offline import plot
import plotly.io as pio

from api.services.analytics import analytics_engine, AnalyticsFilter, TimeRange
from api.core.database import get_db_session
from api.core.config import settings
from api.monitoring.metrics import record_seo_improvement
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

logger = logging.getLogger(__name__)

# =============================================================================
# REPORTING MODELS
# =============================================================================

class ReportType(str, Enum):
    DAILY_SUMMARY = "daily_summary"
    WEEKLY_PERFORMANCE = "weekly_performance"  
    MONTHLY_ANALYTICS = "monthly_analytics"
    QUARTERLY_REVIEW = "quarterly_review"
    CUSTOM_REPORT = "custom_report"
    SEO_AUDIT = "seo_audit"
    COST_ANALYSIS = "cost_analysis"
    STORE_COMPARISON = "store_comparison"

class ReportFormat(str, Enum):
    PDF = "pdf"
    HTML = "html"
    JSON = "json"
    CSV = "csv"
    EXCEL = "xlsx"

class DeliveryMethod(str, Enum):
    EMAIL = "email"
    DOWNLOAD = "download"
    WEBHOOK = "webhook"
    STORAGE = "storage"

class ReportConfig(BaseModel):
    """Configuration for report generation."""
    report_type: ReportType
    title: str
    description: Optional[str] = None
    format: ReportFormat = ReportFormat.PDF
    analytics_filter: AnalyticsFilter
    include_charts: bool = True
    include_raw_data: bool = False
    branding: bool = True
    custom_sections: Optional[List[str]] = None

class ReportDelivery(BaseModel):
    """Configuration for report delivery."""
    method: DeliveryMethod
    recipients: Optional[List[str]] = None  # Email addresses
    webhook_url: Optional[str] = None
    storage_path: Optional[str] = None
    schedule: Optional[str] = None  # Cron expression

class ReportRequest(BaseModel):
    """Complete report generation request."""
    config: ReportConfig
    delivery: ReportDelivery
    generated_by: str
    priority: int = Field(default=5, ge=1, le=10)

class GeneratedReport(BaseModel):
    """Generated report metadata."""
    id: str
    config: ReportConfig
    file_path: Optional[str] = None
    download_url: Optional[str] = None
    file_size_bytes: Optional[int] = None
    generated_at: datetime
    generated_by: str
    status: str = "completed"  # pending, generating, completed, failed
    error_message: Optional[str] = None

# =============================================================================
# REPORT TEMPLATE SYSTEM
# =============================================================================

class ReportTemplateEngine:
    """Jinja2-based template engine for reports."""
    
    def __init__(self):
        # Setup Jinja2 environment
        template_dir = Path(__file__).parent.parent / "templates" / "reports"
        template_dir.mkdir(parents=True, exist_ok=True)
        
        self.env = Environment(
            loader=FileSystemLoader(str(template_dir)),
            autoescape=True
        )
        
        # Custom filters for templates
        self.env.filters['currency'] = self._currency_filter
        self.env.filters['percentage'] = self._percentage_filter
        self.env.filters['datetime_format'] = self._datetime_filter
        self.env.filters['duration'] = self._duration_filter
        
    def _currency_filter(self, value: float) -> str:
        """Format currency values."""
        return f"${value:,.4f}" if value < 1 else f"${value:,.2f}"
    
    def _percentage_filter(self, value: float) -> str:
        """Format percentage values."""
        return f"{value:.1f}%"
    
    def _datetime_filter(self, value: datetime, format: str = "%Y-%m-%d %H:%M") -> str:
        """Format datetime values."""
        return value.strftime(format)
    
    def _duration_filter(self, seconds: float) -> str:
        """Format duration in human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        else:
            return f"{seconds/3600:.1f}h"
    
    async def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render template with context data."""
        try:
            template = self.env.get_template(template_name)
            return template.render(**context)
        except Exception as e:
            logger.error(f"Template rendering error: {e}")
            raise

# =============================================================================
# CHART GENERATION SYSTEM
# =============================================================================

class ChartGenerator:
    """Generate charts and visualizations for reports."""
    
    def __init__(self):
        # Set up plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # Configure Plotly for static export
        pio.templates.default = "plotly_white"
    
    async def create_optimization_trend_chart(self, analytics_data: Dict[str, Any]) -> str:
        """Create optimization trend chart."""
        time_series = analytics_data.get("time_series", [])
        
        if not time_series:
            return self._create_no_data_chart("No optimization data available")
        
        # Extract data
        timestamps = [point["timestamp"] for point in time_series]
        counts = [point["optimization_count"] for point in time_series]
        success_rates = [point["success_rate"] for point in time_series]
        
        # Create plotly figure
        fig = go.Figure()
        
        # Add optimization count
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=counts,
            mode='lines+markers',
            name='Optimizations',
            yaxis='y',
            line=dict(color='#2E8B57', width=3)
        ))
        
        # Add success rate on secondary y-axis
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=success_rates,
            mode='lines+markers',
            name='Success Rate (%)',
            yaxis='y2',
            line=dict(color='#FF6B6B', width=3)
        ))
        
        # Update layout
        fig.update_layout(
            title='Optimization Trends Over Time',
            xaxis_title='Time',
            yaxis=dict(title='Number of Optimizations', side='left'),
            yaxis2=dict(title='Success Rate (%)', side='right', overlaying='y'),
            legend=dict(x=0.02, y=0.98),
            height=400,
            margin=dict(l=50, r=50, t=50, b=50)
        )
        
        # Convert to HTML
        return plot(fig, output_type='div', include_plotlyjs=False)
    
    async def create_cost_breakdown_chart(self, cost_data: Dict[str, Any]) -> str:
        """Create cost breakdown pie chart."""
        model_costs = cost_data.get("by_model_and_type", [])
        
        if not model_costs:
            return self._create_no_data_chart("No cost data available")
        
        # Aggregate by AI model
        model_totals = {}
        for item in model_costs:
            model = item["ai_model"]
            cost = item["total_cost_usd"]
            model_totals[model] = model_totals.get(model, 0) + cost
        
        # Create pie chart
        fig = go.Figure(data=[
            go.Pie(
                labels=list(model_totals.keys()),
                values=list(model_totals.values()),
                hole=0.4,
                textinfo='label+percent',
                textposition='outside'
            )
        ])
        
        fig.update_layout(
            title='AI Cost Breakdown by Model',
            height=400,
            margin=dict(l=50, r=50, t=50, b=50)
        )
        
        return plot(fig, output_type='div', include_plotlyjs=False)
    
    async def create_seo_improvement_chart(self, seo_data: Dict[str, Any]) -> str:
        """Create SEO improvement visualization."""
        store_data = seo_data.get("by_store", [])
        
        if not store_data:
            return self._create_no_data_chart("No SEO data available")
        
        # Create bar chart showing improvement rates by store
        store_ids = [item["store_id"][:8] + "..." for item in store_data]  # Truncate for display
        improvement_rates = [item["improvement_rate"] for item in store_data]
        avg_improvements = [item["avg_score_improvement"] for item in store_data]
        
        fig = go.Figure()
        
        # Add improvement rate bars
        fig.add_trace(go.Bar(
            x=store_ids,
            y=improvement_rates,
            name='Improvement Rate (%)',
            marker_color='#4CAF50',
            yaxis='y'
        ))
        
        # Add average score improvement on secondary axis
        fig.add_trace(go.Scatter(
            x=store_ids,
            y=avg_improvements,
            mode='lines+markers',
            name='Avg Score Improvement',
            yaxis='y2',
            line=dict(color='#FF9800', width=3),
            marker=dict(size=8)
        ))
        
        fig.update_layout(
            title='SEO Improvement Performance by Store',
            xaxis_title='Store ID',
            yaxis=dict(title='Improvement Rate (%)', side='left'),
            yaxis2=dict(title='Average Score Improvement', side='right', overlaying='y'),
            height=400,
            margin=dict(l=50, r=50, t=50, b=50)
        )
        
        return plot(fig, output_type='div', include_plotlyjs=False)
    
    def _create_no_data_chart(self, message: str) -> str:
        """Create a placeholder chart when no data is available."""
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        fig.update_layout(
            height=300,
            margin=dict(l=50, r=50, t=50, b=50),
            showlegend=False
        )
        return plot(fig, output_type='div', include_plotlyjs=False)

# =============================================================================
# MAIN REPORTING ENGINE
# =============================================================================

class GridSpokeReportingEngine:
    """Main reporting engine for GridSpoke."""
    
    def __init__(self):
        self.template_engine = ReportTemplateEngine()
        self.chart_generator = ChartGenerator()
        self.reports_dir = Path("reports")
        self.reports_dir.mkdir(exist_ok=True)
    
    async def generate_report(self, request: ReportRequest) -> GeneratedReport:
        """Generate a complete report based on request configuration."""
        report_id = f"report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{request.config.report_type.value}"
        
        logger.info(f"Generating report {report_id} of type {request.config.report_type}")
        
        try:
            # Gather analytics data
            analytics_data = await self._gather_analytics_data(request.config.analytics_filter)
            
            # Generate charts if requested
            charts = {}
            if request.config.include_charts:
                charts = await self._generate_charts(analytics_data, request.config.report_type)
            
            # Prepare report context
            context = await self._prepare_report_context(
                request.config, analytics_data, charts
            )
            
            # Generate report content based on format
            file_path = None
            file_size = None
            
            if request.config.format == ReportFormat.PDF:
                file_path, file_size = await self._generate_pdf_report(report_id, context, request.config)
            elif request.config.format == ReportFormat.HTML:
                file_path, file_size = await self._generate_html_report(report_id, context, request.config)
            elif request.config.format == ReportFormat.JSON:
                file_path, file_size = await self._generate_json_report(report_id, analytics_data)
            elif request.config.format == ReportFormat.CSV:
                file_path, file_size = await self._generate_csv_report(report_id, analytics_data)
            
            # Create generated report metadata
            generated_report = GeneratedReport(
                id=report_id,
                config=request.config,
                file_path=file_path,
                download_url=f"/api/v1/reports/download/{report_id}",
                file_size_bytes=file_size,
                generated_at=datetime.utcnow(),
                generated_by=request.generated_by,
                status="completed"
            )
            
            # Handle delivery
            await self._handle_delivery(generated_report, request.delivery)
            
            logger.info(f"Successfully generated report {report_id}")
            return generated_report
            
        except Exception as e:
            logger.error(f"Report generation failed for {report_id}: {e}")
            return GeneratedReport(
                id=report_id,
                config=request.config,
                generated_at=datetime.utcnow(),
                generated_by=request.generated_by,
                status="failed",
                error_message=str(e)
            )
    
    async def _gather_analytics_data(self, filters: AnalyticsFilter) -> Dict[str, Any]:
        """Gather all required analytics data."""
        logger.info("Gathering analytics data for report")
        
        # Get comprehensive analytics
        optimization_metrics, seo_metrics, cost_metrics, wp_metrics = await asyncio.gather(
            analytics_engine.get_optimization_metrics(filters),
            analytics_engine.get_seo_performance_metrics(filters),
            analytics_engine.get_cost_analytics(filters),
            analytics_engine.get_wordpress_integration_metrics(filters)
        )
        
        return {
            "optimization": optimization_metrics,
            "seo": seo_metrics,
            "cost": cost_metrics,
            "wordpress": wp_metrics,
            "filters": filters.dict()
        }
    
    async def _generate_charts(self, analytics_data: Dict[str, Any], report_type: ReportType) -> Dict[str, str]:
        """Generate charts based on report type and data."""
        charts = {}
        
        # Always include basic charts
        charts["optimization_trend"] = await self.chart_generator.create_optimization_trend_chart(
            analytics_data["optimization"]
        )
        
        charts["seo_improvement"] = await self.chart_generator.create_seo_improvement_chart(
            analytics_data["seo"]
        )
        
        # Include cost chart for relevant reports
        if report_type in [ReportType.COST_ANALYSIS, ReportType.MONTHLY_ANALYTICS, ReportType.QUARTERLY_REVIEW]:
            charts["cost_breakdown"] = await self.chart_generator.create_cost_breakdown_chart(
                analytics_data["cost"]
            )
        
        return charts
    
    async def _prepare_report_context(self, config: ReportConfig, 
                                     analytics_data: Dict[str, Any], 
                                     charts: Dict[str, str]) -> Dict[str, Any]:
        """Prepare template context for report generation."""
        
        # Calculate summary metrics
        opt_summary = analytics_data["optimization"]["summary"]
        seo_summary = analytics_data["seo"]["summary"]
        cost_summary = analytics_data["cost"]["summary"]
        
        # Determine time period description
        time_period = self._get_time_period_description(config.analytics_filter)
        
        context = {
            # Report metadata
            "report_title": config.title,
            "report_description": config.description,
            "generated_at": datetime.utcnow(),
            "time_period": time_period,
            "report_type": config.report_type.value,
            
            # Analytics data
            "analytics": analytics_data,
            "charts": charts,
            
            # Key metrics for executive summary
            "key_metrics": {
                "total_optimizations": opt_summary["total_optimizations"],
                "success_rate": opt_summary["success_rate"],
                "seo_improvement_rate": seo_summary["improvement_rate_percent"],
                "total_cost": cost_summary["total_cost_usd"],
                "cost_per_optimization": cost_summary["avg_cost_per_optimization"],
                "active_stores": opt_summary["active_stores"]
            },
            
            # Insights and recommendations
            "insights": await self._generate_insights(analytics_data),
            "recommendations": await self._generate_recommendations(analytics_data),
            
            # Branding
            "company_name": "GridSpoke",
            "company_logo": "/static/images/gridspoke-logo.png",
            "show_branding": config.branding
        }
        
        return context
    
    async def _generate_pdf_report(self, report_id: str, context: Dict[str, Any], 
                                  config: ReportConfig) -> tuple[str, int]:
        """Generate PDF report using WeasyPrint."""
        template_name = f"{config.report_type.value}.html"
        
        # Render HTML template
        html_content = await self.template_engine.render_template(template_name, context)
        
        # Create PDF
        pdf_path = self.reports_dir / f"{report_id}.pdf"
        
        # Custom CSS for PDF styling
        css_content = """
        @page {
            size: A4;
            margin: 2cm;
            @top-center {
                content: "GridSpoke Analytics Report";
                font-size: 10pt;
                color: #666;
            }
            @bottom-right {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 9pt;
                color: #666;
            }
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
        }
        
        .chart-container {
            page-break-inside: avoid;
            margin: 1em 0;
        }
        
        .metrics-table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        
        .metrics-table th, .metrics-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .metrics-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        """
        
        # Generate PDF
        html_doc = HTML(string=html_content, base_url=str(Path.cwd()))
        css_doc = CSS(string=css_content)
        
        html_doc.write_pdf(str(pdf_path), stylesheets=[css_doc])
        
        # Get file size
        file_size = pdf_path.stat().st_size
        
        return str(pdf_path), file_size
    
    async def _generate_html_report(self, report_id: str, context: Dict[str, Any], 
                                   config: ReportConfig) -> tuple[str, int]:
        """Generate HTML report."""
        template_name = f"{config.report_type.value}.html"
        
        # Render template
        html_content = await self.template_engine.render_template(template_name, context)
        
        # Save HTML file
        html_path = self.reports_dir / f"{report_id}.html"
        
        async with aiofiles.open(html_path, 'w', encoding='utf-8') as f:
            await f.write(html_content)
        
        file_size = html_path.stat().st_size
        return str(html_path), file_size
    
    async def _generate_json_report(self, report_id: str, analytics_data: Dict[str, Any]) -> tuple[str, int]:
        """Generate JSON report."""
        json_path = self.reports_dir / f"{report_id}.json"
        
        # Add metadata
        report_data = {
            "report_id": report_id,
            "generated_at": datetime.utcnow().isoformat(),
            "analytics_data": analytics_data
        }
        
        async with aiofiles.open(json_path, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(report_data, indent=2, default=str))
        
        file_size = json_path.stat().st_size
        return str(json_path), file_size
    
    async def _generate_csv_report(self, report_id: str, analytics_data: Dict[str, Any]) -> tuple[str, int]:
        """Generate CSV report with optimization data."""
        csv_path = self.reports_dir / f"{report_id}.csv"
        
        # Extract optimization time series data
        time_series = analytics_data["optimization"].get("time_series", [])
        
        if time_series:
            df = pd.DataFrame(time_series)
            await asyncio.get_event_loop().run_in_executor(
                None, df.to_csv, str(csv_path), False
            )
        else:
            # Create empty CSV with headers
            df = pd.DataFrame(columns=["timestamp", "optimization_count", "success_rate"])
            await asyncio.get_event_loop().run_in_executor(
                None, df.to_csv, str(csv_path), False
            )
        
        file_size = csv_path.stat().st_size
        return str(csv_path), file_size
    
    async def _handle_delivery(self, report: GeneratedReport, delivery: ReportDelivery):
        """Handle report delivery based on configuration."""
        if delivery.method == DeliveryMethod.EMAIL and delivery.recipients:
            await self._send_email_report(report, delivery.recipients)
        elif delivery.method == DeliveryMethod.WEBHOOK and delivery.webhook_url:
            await self._send_webhook_report(report, delivery.webhook_url)
        # Storage and download methods don't require additional action
    
    async def _send_email_report(self, report: GeneratedReport, recipients: List[str]):
        """Send report via email."""
        try:
            msg = MIMEMultipart()
            msg['From'] = settings.SMTP_USER
            msg['Subject'] = f"GridSpoke Report: {report.config.title}"
            
            # Email body
            body = f"""
            Dear GridSpoke User,
            
            Your requested {report.config.report_type.value} report has been generated successfully.
            
            Report Details:
            - Generated: {report.generated_at.strftime('%Y-%m-%d %H:%M UTC')}
            - Type: {report.config.report_type.value}
            - Format: {report.config.format.value}
            - File Size: {report.file_size_bytes / 1024:.1f} KB
            
            Please find the report attached.
            
            Best regards,
            GridSpoke Analytics Team
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Attach report file
            if report.file_path and Path(report.file_path).exists():
                with open(report.file_path, "rb") as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())
                    
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {Path(report.file_path).name}'
                )
                msg.attach(part)
            
            # Send email
            server = smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT)
            server.starttls()
            server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
            
            for recipient in recipients:
                msg['To'] = recipient
                server.sendmail(settings.SMTP_USER, recipient, msg.as_string())
                msg.replace_header('To', '')  # Clear for next recipient
            
            server.quit()
            logger.info(f"Report {report.id} emailed to {len(recipients)} recipients")
            
        except Exception as e:
            logger.error(f"Failed to email report {report.id}: {e}")
    
    async def _send_webhook_report(self, report: GeneratedReport, webhook_url: str):
        """Send report metadata via webhook."""
        import httpx
        
        try:
            payload = {
                "report_id": report.id,
                "status": report.status,
                "download_url": report.download_url,
                "generated_at": report.generated_at.isoformat(),
                "config": report.config.dict()
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(webhook_url, json=payload)
                response.raise_for_status()
            
            logger.info(f"Report {report.id} webhook sent successfully")
            
        except Exception as e:
            logger.error(f"Failed to send webhook for report {report.id}: {e}")
    
    def _get_time_period_description(self, filters: AnalyticsFilter) -> str:
        """Get human-readable time period description."""
        if filters.time_range == TimeRange.CUSTOM:
            start = filters.start_date.strftime('%Y-%m-%d') if filters.start_date else "Unknown"
            end = filters.end_date.strftime('%Y-%m-%d') if filters.end_date else "Unknown"
            return f"Custom period: {start} to {end}"
        else:
            return {
                TimeRange.LAST_24H: "Last 24 Hours",
                TimeRange.LAST_7D: "Last 7 Days", 
                TimeRange.LAST_30D: "Last 30 Days",
                TimeRange.LAST_90D: "Last 90 Days",
                TimeRange.LAST_YEAR: "Last Year"
            }.get(filters.time_range, "Unknown period")
    
    async def _generate_insights(self, analytics_data: Dict[str, Any]) -> List[str]:
        """Generate insights from analytics data."""
        insights = []
        
        opt_data = analytics_data["optimization"]["summary"]
        seo_data = analytics_data["seo"]["summary"]
        cost_data = analytics_data["cost"]["summary"]
        
        # Optimization insights
        if opt_data["success_rate"] > 95:
            insights.append(f"Excellent optimization performance with {opt_data['success_rate']:.1f}% success rate")
        elif opt_data["success_rate"] < 80:
            insights.append(f"Optimization success rate of {opt_data['success_rate']:.1f}% needs attention")
        
        # SEO insights
        if seo_data["improvement_rate_percent"] > 70:
            insights.append(f"Strong SEO results with {seo_data['improvement_rate_percent']:.1f}% of products showing improvement")
        
        # Cost insights
        if cost_data["avg_cost_per_optimization"] < 0.10:
            insights.append(f"Cost-efficient operations at ${cost_data['avg_cost_per_optimization']:.4f} per optimization")
        
        return insights
    
    async def _generate_recommendations(self, analytics_data: Dict[str, Any]) -> List[str]:
        """Generate recommendations from analytics data."""
        recommendations = []
        
        opt_data = analytics_data["optimization"]["summary"]
        cost_data = analytics_data["cost"]["summary"]
        
        # Performance recommendations
        if opt_data["avg_processing_time_minutes"] > 3:
            recommendations.append("Consider optimizing AI prompts to reduce processing time")
        
        # Cost recommendations
        if cost_data["daily_average_cost"] > 20:
            recommendations.append("Implement cost optimization strategies like caching and batching")
        
        # Capacity recommendations
        if opt_data["total_optimizations"] > 1000:
            recommendations.append("Consider scaling infrastructure for increased optimization volume")
        
        return recommendations

# Global reporting engine instance
reporting_engine = GridSpokeReportingEngine()

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

async def generate_daily_summary_report(store_id: Optional[str] = None) -> GeneratedReport:
    """Generate a daily summary report."""
    filters = AnalyticsFilter(
        time_range=TimeRange.LAST_24H,
        store_ids=[store_id] if store_id else None
    )
    
    config = ReportConfig(
        report_type=ReportType.DAILY_SUMMARY,
        title="Daily Performance Summary",
        description="24-hour optimization and performance summary",
        analytics_filter=filters
    )
    
    delivery = ReportDelivery(method=DeliveryMethod.STORAGE)
    
    request = ReportRequest(
        config=config,
        delivery=delivery,
        generated_by="system"
    )
    
    return await reporting_engine.generate_report(request)

async def generate_cost_analysis_report(days: int = 30) -> GeneratedReport:
    """Generate a cost analysis report."""
    filters = AnalyticsFilter(
        time_range=TimeRange.LAST_30D if days == 30 else TimeRange.CUSTOM,
        start_date=datetime.utcnow() - timedelta(days=days) if days != 30 else None
    )
    
    config = ReportConfig(
        report_type=ReportType.COST_ANALYSIS,
        title=f"AI Cost Analysis - Last {days} Days",
        description="Comprehensive analysis of AI API costs and efficiency",
        analytics_filter=filters,
        include_charts=True
    )
    
    delivery = ReportDelivery(method=DeliveryMethod.STORAGE)
    
    request = ReportRequest(
        config=config,
        delivery=delivery,
        generated_by="system"
    )
    
    return await reporting_engine.generate_report(request)

# Export main components
__all__ = [
    'ReportType',
    'ReportFormat',
    'ReportConfig',
    'ReportRequest',
    'GeneratedReport',
    'reporting_engine',
    'generate_daily_summary_report',
    'generate_cost_analysis_report'
]
