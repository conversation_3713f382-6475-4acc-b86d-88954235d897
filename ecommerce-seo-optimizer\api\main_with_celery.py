"""
Main FastAPI application with Celery task queue integration.
Updates the main FastAPI app to include WebSocket endpoints and task management.
"""

from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging

# Import WebSocket components
from websocket.endpoints import ws_router, startup_websocket_manager, shutdown_websocket_manager
from api.api.v1.endpoints.tasks import router as tasks_router

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.
    """
    try:
        # Startup
        logger.info("Starting GridSpoke Ecommerce SEO Optimizer...")
        
        # Initialize WebSocket manager
        await startup_websocket_manager()
        
        logger.info("Application startup complete")
        
        yield
        
    finally:
        # Shutdown
        logger.info("Shutting down GridSpoke Ecommerce SEO Optimizer...")
        
        # Cleanup WebSocket manager
        await shutdown_websocket_manager()
        
        logger.info("Application shutdown complete")

# Create FastAPI app with lifespan management
app = FastAPI(
    title="GridSpoke Ecommerce SEO Optimizer",
    description="AI-powered ecommerce SEO optimization service with real-time task processing",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(tasks_router, prefix="/api/v1")
app.include_router(ws_router, prefix="/api/v1")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "GridSpoke Ecommerce SEO Optimizer",
        "version": "1.0.0"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "GridSpoke Ecommerce SEO Optimizer API",
        "version": "1.0.0",
        "docs": "/docs",
        "websocket": "/api/v1/ws",
        "tasks": "/api/v1/tasks"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
