#!/usr/bin/env python3
"""
Test script to verify custom AI model functionality.
"""
import requests
import json
import os

# Configuration
BASE_URL = "http://localhost:8000"
API_PREFIX = "/api/v1"

# Test user credentials (using the test user created in the system)
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "demo12345"

def test_custom_ai_model():
    """Test the custom AI model functionality."""
    print("Testing custom AI model functionality...")
    
    # Step 1: Login to get authentication token
    print("1. Logging in...")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}{API_PREFIX}/auth/login",
            json=login_data
        )
        
        if response.status_code != 200:
            print(f"Login failed with status {response.status_code}")
            print(response.text)
            return False
            
        auth_data = response.json()
        token = auth_data.get("access_token")
        if not token:
            print("No access token in response")
            return False
            
        print("Login successful!")
        headers = {"Authorization": f"Bearer {token}"}
        
    except Exception as e:
        print(f"Error during login: {e}")
        return False
    
    # Step 2: Get current user settings
    print("2. Getting current user settings...")
    try:
        response = requests.get(
            f"{BASE_URL}{API_PREFIX}/users/me/settings",
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"Failed to get user settings with status {response.status_code}")
            print(response.text)
            return False
            
        settings = response.json()
        print(f"Current settings: {settings}")
        
    except Exception as e:
        print(f"Error getting user settings: {e}")
        return False
    
    # Step 3: Update user settings with custom AI model
    print("3. Updating user settings with custom AI model...")
    test_custom_model = "anthropic/claude-3.5-sonnet"  # Test custom model
    updated_settings = {
        "auto_optimize": True,
        "email_notifications": True,
        "scheduled_optimization": True,
        "ai_model": "custom",
        "custom_ai_model": test_custom_model,
        "optimization_frequency": "weekly",
        "openrouter_api_key": "sk-or-test1234567890abcdefg"
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}{API_PREFIX}/users/me/settings",
            headers=headers,
            json=updated_settings
        )
        
        if response.status_code != 200:
            print(f"Failed to update user settings with status {response.status_code}")
            print(response.text)
            return False
            
        updated_settings_response = response.json()
        print(f"Updated settings: {updated_settings_response}")
        
        # Verify the custom model was saved
        if (updated_settings_response.get("ai_model") == "custom" and 
            updated_settings_response.get("custom_ai_model") == test_custom_model):
            print("Custom AI model saved successfully!")
        else:
            print("Warning: Custom AI model may not have been saved correctly")
            
    except Exception as e:
        print(f"Error updating user settings: {e}")
        return False
    
    # Step 4: Verify settings were saved by retrieving them again
    print("4. Verifying settings were saved...")
    try:
        response = requests.get(
            f"{BASE_URL}{API_PREFIX}/users/me/settings",
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"Failed to get user settings with status {response.status_code}")
            print(response.text)
            return False
            
        settings = response.json()
        print(f"Retrieved settings: {settings}")
        
        if (settings.get("ai_model") == "custom" and 
            settings.get("custom_ai_model") == test_custom_model):
            print("Custom AI model verified successfully!")
            return True
        else:
            print("Custom AI model verification failed!")
            return False
            
    except Exception as e:
        print(f"Error verifying user settings: {e}")
        return False

def test_invalid_custom_model():
    """Test that invalid custom models are rejected."""
    print("\nTesting invalid custom model rejection...")
    
    # Step 1: Login to get authentication token
    print("1. Logging in...")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}{API_PREFIX}/auth/login",
            json=login_data
        )
        
        if response.status_code != 200:
            print(f"Login failed with status {response.status_code}")
            print(response.text)
            return False
            
        auth_data = response.json()
        token = auth_data.get("access_token")
        if not token:
            print("No access token in response")
            return False
            
        print("Login successful!")
        headers = {"Authorization": f"Bearer {token}"}
        
    except Exception as e:
        print(f"Error during login: {e}")
        return False
    
    # Step 2: Try to update user settings with invalid custom AI model
    print("2. Attempting to update with invalid custom AI model...")
    invalid_models = [
        "../etc/passwd",  # Path traversal attempt
        "http://malicious.com/model",  # URL attempt
        "model; rm -rf /",  # Command injection attempt
        "",  # Empty string
        "a" * 150,  # Too long
        "invalid_model"  # Wrong format
    ]
    
    for invalid_model in invalid_models:
        updated_settings = {
            "ai_model": "custom",
            "custom_ai_model": invalid_model,
            "optimization_frequency": "weekly"
        }
        
        try:
            response = requests.put(
                f"{BASE_URL}{API_PREFIX}/users/me/settings",
                headers=headers,
                json=updated_settings
            )
            
            # Should fail with 400 Bad Request
            if response.status_code == 400:
                print(f"Correctly rejected invalid model: {invalid_model}")
            else:
                print(f"ERROR: Invalid model was accepted: {invalid_model}")
                print(f"Status: {response.status_code}, Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"Error testing invalid model {invalid_model}: {e}")
            return False
    
    print("All invalid models were correctly rejected!")
    return True

if __name__ == "__main__":
    print("Starting custom AI model functionality test...")
    success1 = test_custom_ai_model()
    success2 = test_invalid_custom_model()
    
    if success1 and success2:
        print("\nAll tests passed! Custom AI model functionality is working correctly.")
    else:
        print("\nSome tests failed. Please check the implementation.")