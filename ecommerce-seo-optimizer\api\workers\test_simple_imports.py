"""
Simple test to verify task module imports without full application dependencies.
"""
import sys
import os

# Add the workers directory to the Python path
workers_dir = os.path.join(os.path.dirname(__file__))
sys.path.insert(0, workers_dir)

def test_simple_imports():
    """Test importing task modules without full application dependencies."""
    try:
        # Test product tasks import
        import tasks.product_tasks
        print("Product tasks imported successfully")
        
        # Test content tasks import
        import tasks.content_tasks
        print("Content tasks imported successfully")
        
        # Test scheduled tasks import
        import tasks.scheduled_tasks
        print("Scheduled tasks imported successfully")
        
        # Test validation tasks import
        import tasks.validation_tasks
        print("Validation tasks imported successfully")
        
        # Test utils imports
        import utils.progress_tracker
        print("Progress tracker imported successfully")
        
        import utils.redis_client
        print("Redis client imported successfully")
        
        print("\nAll imports successful!")
        return True
        
    except ImportError as e:
        print(f"Import error: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_imports()
    sys.exit(0 if success else 1)