# GridSpoke Documentation Testing Guide
"""
Comprehensive guide for running and maintaining the GridSpoke testing suite.
This documentation covers all testing components and best practices.
"""

## Testing Architecture Overview

The GridSpoke testing suite follows the testing pyramid pattern:
- **70% Unit Tests**: Fast, isolated tests for individual components
- **25% Integration Tests**: Tests for component interactions and workflows
- **5% E2E/Load Tests**: Full system tests and performance validation

## Test Directory Structure

```
tests/
├── fixtures/                  # Shared test data and fixtures
│   └── __init__.py            # Common fixtures and test data
├── utils/                     # Test utilities and helpers
│   └── test_helpers.py        # Helper functions and mock utilities
├── unit/                      # Unit tests (70% of test suite)
│   ├── test_api.py           # FastAPI endpoint tests
│   ├── test_agents.py        # Mirascope AI agent tests
│   ├── test_models.py        # Database model tests
│   ├── test_services.py      # Business logic service tests
│   └── test_utils.py         # Utility function tests
├── integration/               # Integration tests (25% of test suite)
│   ├── test_workflows.py     # End-to-end workflow tests
│   ├── test_ai_integration.py # AI service integration tests
│   ├── test_celery_tasks.py  # Celery task integration tests
│   └── test_external_apis.py # External API integration tests
├── load/                      # Load and performance tests
│   ├── locustfile.py         # Locust load testing configuration
│   ├── test_performance.py   # Performance benchmarks
│   └── stress_tests.py       # Stress testing scenarios
├── websocket/                 # WebSocket testing
│   ├── test_websocket.py     # WebSocket connection tests
│   └── test_realtime.py      # Real-time feature tests
├── security/                  # Security testing
│   ├── test_security.py      # Security vulnerability tests
│   ├── test_auth.py          # Authentication/authorization tests
│   └── test_validation.py    # Input validation tests
├── wordpress/                 # WordPress plugin tests
│   ├── test_wordpress_plugin.py # Plugin functionality tests
│   ├── test_woocommerce.py     # WooCommerce integration tests
│   └── test_webhooks.py        # Webhook handling tests
├── database/                  # Database testing
│   ├── test_migrations.py    # Database migration tests
│   ├── test_models.py        # Database model tests
│   └── test_queries.py       # Query performance tests
├── frontend/                  # Frontend testing
│   ├── test_frontend.py      # Jest configuration and tests
│   ├── e2e/                  # End-to-end browser tests
│   └── components/           # Component-specific tests
└── conftest.py               # Global pytest configuration
```

## Running Tests

### Prerequisites

1. **Install Dependencies**:
   ```bash
   # Python dependencies
   pip install -r requirements-test.txt
   
   # Frontend dependencies  
   cd frontend && npm install
   ```

2. **Start Test Services**:
   ```bash
   # Start test database and Redis
   docker-compose -f docker-compose.test.yml up -d
   ```

3. **Set Environment Variables**:
   ```bash
   export TESTING=true
   export DATABASE_URL=postgresql://test_user:test_pass@localhost:5433/test_db
   export REDIS_URL=redis://localhost:6380/15
   ```

### Running Different Test Types

#### All Tests
```bash
# Run complete test suite
pytest

# Run with coverage report
pytest --cov=api --cov-report=html --cov-report=term
```

#### Unit Tests Only
```bash
# Fast unit tests (recommended for development)
pytest tests/unit/ -v

# Specific unit test file
pytest tests/unit/test_api.py -v

# Specific test function
pytest tests/unit/test_api.py::test_create_product -v
```

#### Integration Tests
```bash
# Integration tests (requires services)
pytest tests/integration/ -v

# With real external services
pytest tests/integration/ --real-services
```

#### Load Testing
```bash
# Start application
docker-compose up -d

# Run Locust load tests
locust -f tests/load/locustfile.py --host=http://localhost:8000

# Automated load test
pytest tests/load/test_performance.py
```

#### Security Tests
```bash
# Security vulnerability scanning
pytest tests/security/ -v

# Include slow security tests
pytest tests/security/ -v --runslow
```

#### WebSocket Tests
```bash
# WebSocket functionality tests
pytest tests/websocket/ -v

# Real-time feature tests
pytest tests/websocket/test_realtime.py -v
```

#### Database Tests
```bash
# Database migration tests
pytest tests/database/test_migrations.py -v

# Database model tests
pytest tests/database/test_models.py -v
```

#### Frontend Tests
```bash
# JavaScript unit tests
cd frontend && npm test

# E2E browser tests
cd frontend && npm run test:e2e

# Component tests
cd frontend && npm run test:components
```

#### WordPress Plugin Tests
```bash
# WordPress plugin tests (requires WordPress test environment)
pytest tests/wordpress/ -v

# WooCommerce integration tests
pytest tests/wordpress/test_woocommerce.py -v
```

### Test Markers and Filtering

#### Available Test Markers
- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.slow` - Slow-running tests
- `@pytest.mark.api` - API endpoint tests
- `@pytest.mark.websocket` - WebSocket tests
- `@pytest.mark.security` - Security tests
- `@pytest.mark.load` - Load/performance tests
- `@pytest.mark.e2e` - End-to-end tests

#### Running Specific Test Types
```bash
# Only fast tests (exclude slow)
pytest -m "not slow"

# Only unit tests
pytest -m unit

# Only API tests
pytest -m api

# Integration and security tests
pytest -m "integration or security"

# Exclude load tests
pytest -m "not load"
```

## Mock Configuration

### AI Service Mocking
The test suite includes comprehensive mocking for AI services:

```python
# OpenRouter API responses are mocked by default
# To test with real AI services:
pytest --real-ai-services

# Mock responses include realistic data:
mock_response = {
    "choices": [{"message": {"content": "optimized content"}}],
    "usage": {"total_tokens": 650},
    "model": "anthropic/claude-3-opus"
}
```

### External Service Mocking
- **WordPress API**: Mocked by default, use `--real-wordpress` for real API
- **Payment Services**: Always mocked in tests
- **Email Services**: Mocked by default, use `--real-email` for real sending

### Database Mocking
- **Unit Tests**: Use in-memory SQLite database
- **Integration Tests**: Use dedicated PostgreSQL test database
- **Load Tests**: Use production-like database setup

## CI/CD Pipeline

### GitHub Actions Workflow

The CI/CD pipeline runs automatically on:
- **Pull Requests**: Full test suite
- **Push to main**: Full test suite + deployment
- **Scheduled**: Nightly full test suite + security scans

#### Pipeline Stages

1. **Linting** (2-3 minutes):
   - Python: Black, isort, Flake8, MyPy
   - JavaScript: ESLint, Prettier
   - YAML: yamllint

2. **Unit Tests** (5-8 minutes):
   - Python unit tests with coverage
   - JavaScript unit tests
   - Coverage reporting to Codecov

3. **Integration Tests** (10-15 minutes):
   - Database integration tests
   - API integration tests
   - Celery task tests

4. **Security Tests** (3-5 minutes):
   - Dependency vulnerability scanning
   - Static security analysis
   - OWASP security checks

5. **WebSocket Tests** (2-3 minutes):
   - Connection lifecycle tests
   - Real-time feature tests

6. **Database Tests** (5-7 minutes):
   - Migration tests
   - Performance tests
   - Data integrity tests

7. **Frontend Tests** (8-12 minutes):
   - Jest unit tests
   - Component tests
   - E2E browser tests with Playwright

8. **WordPress Plugin Tests** (6-10 minutes):
   - Plugin functionality tests
   - WooCommerce integration tests
   - Webhook tests

9. **Load Tests** (15-20 minutes):
   - Performance benchmarks
   - Stress testing
   - Scalability tests

10. **E2E Tests** (10-15 minutes):
    - Full user journey tests
    - Cross-browser testing

11. **Build & Deploy** (5-8 minutes):
    - Docker image building
    - Staging deployment
    - Smoke tests

### Pipeline Configuration

```yaml
# Environment variables in GitHub Secrets:
DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
REDIS_URL: ${{ secrets.TEST_REDIS_URL }}
OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
DOCKER_HUB_USERNAME: ${{ secrets.DOCKER_HUB_USERNAME }}
DOCKER_HUB_TOKEN: ${{ secrets.DOCKER_HUB_TOKEN }}
STAGING_SSH_KEY: ${{ secrets.STAGING_SSH_KEY }}
SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

## Performance Benchmarks

### Target Performance Metrics

#### API Response Times
- **Authentication**: < 100ms (95th percentile)
- **Product listing**: < 200ms (95th percentile)
- **Product optimization**: < 5s (95th percentile)
- **Bulk operations**: < 30s for 100 products

#### Database Query Performance
- **Simple queries**: < 10ms
- **Complex joins**: < 50ms
- **Bulk inserts**: < 1s for 1000 records
- **Search queries**: < 100ms

#### WebSocket Performance
- **Connection establishment**: < 500ms
- **Message delivery**: < 50ms
- **Concurrent connections**: Support 1000+ connections

#### AI Service Performance
- **OpenRouter API calls**: < 10s (95th percentile)
- **Content optimization**: < 15s per product
- **Batch processing**: 100 products in < 10 minutes

### Running Performance Tests

```bash
# Quick performance check
pytest tests/load/test_performance.py -v

# Full load testing with Locust
locust -f tests/load/locustfile.py --host=http://localhost:8000 \
       --users=100 --spawn-rate=10 --run-time=300s

# Database performance tests
pytest tests/database/test_queries.py -v --benchmark

# API performance tests
pytest tests/unit/test_api.py -v --benchmark
```

## Test Data Management

### Fixtures and Test Data

#### Database Fixtures
- **Minimal**: Basic user, store, product for unit tests
- **Complete**: Full dataset for integration tests
- **Performance**: Large datasets for load testing

#### File Fixtures
- **CSV Import**: Sample product CSV files
- **JSON Export**: Sample export data
- **Images**: Test product images in multiple formats

#### Mock Data
- **AI Responses**: Realistic AI-generated content
- **API Responses**: External service responses
- **WebSocket Messages**: Real-time update messages

### Test Data Cleanup

```python
# Automatic cleanup after each test
@pytest.fixture(autouse=True)
def cleanup_test_data():
    yield
    # Cleanup code runs automatically
```

## Debugging Tests

### Common Issues and Solutions

#### Database Connection Issues
```bash
# Check database service
docker-compose -f docker-compose.test.yml ps

# Reset test database
docker-compose -f docker-compose.test.yml down -v
docker-compose -f docker-compose.test.yml up -d
```

#### AI Service Timeouts
```python
# Increase timeout for slow AI responses
@pytest.mark.timeout(60)
def test_slow_ai_optimization():
    # Test code here
```

#### WebSocket Connection Problems
```python
# Debug WebSocket connections
pytest tests/websocket/ -v -s --log-cli-level=DEBUG
```

#### Memory Issues in Load Tests
```bash
# Run with limited concurrency
pytest tests/load/ --max-workers=4
```

### Test Debugging Commands

```bash
# Run with detailed output
pytest -vvv -s

# Debug specific test
pytest tests/unit/test_api.py::test_create_product -vvv -s --pdb

# Show all print statements
pytest -s

# Show warnings
pytest -W default

# Stop on first failure
pytest -x

# Show local variables on failure
pytest --tb=long

# Profile slow tests
pytest --durations=10
```

## Code Coverage

### Coverage Requirements
- **Overall Coverage**: Minimum 90%
- **Unit Tests**: Minimum 95%
- **Integration Tests**: Minimum 85%
- **Critical Paths**: 100% coverage required

### Generating Coverage Reports

```bash
# HTML coverage report
pytest --cov=api --cov-report=html
open htmlcov/index.html

# Terminal coverage report
pytest --cov=api --cov-report=term-missing

# XML coverage for CI
pytest --cov=api --cov-report=xml

# Coverage with specific threshold
pytest --cov=api --cov-fail-under=90
```

### Coverage Configuration

```ini
# setup.cfg or pyproject.toml
[coverage:run]
source = api
omit = 
    */tests/*
    */venv/*
    */migrations/*
    */conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
```

## Security Testing

### Security Test Categories

#### Authentication & Authorization
- JWT token validation
- API key security
- Role-based access control
- Session management

#### Input Validation
- SQL injection prevention
- XSS protection
- CSRF protection
- File upload security

#### Data Protection
- Encryption at rest
- Encryption in transit
- PII data handling
- API key storage

#### Infrastructure Security
- Container security
- Network security
- Environment variable protection
- Secrets management

### Running Security Tests

```bash
# Full security test suite
pytest tests/security/ -v

# Specific security categories
pytest tests/security/test_auth.py -v
pytest tests/security/test_validation.py -v

# Security scanning with external tools
bandit -r api/
safety check
```

## Maintenance Guidelines

### Regular Maintenance Tasks

#### Weekly
- Review test execution times
- Update test data fixtures
- Check coverage reports
- Review failed tests in CI

#### Monthly
- Update test dependencies
- Review and update mock responses
- Performance benchmark comparison
- Security vulnerability scanning

#### Quarterly
- Full test suite review
- Load testing with increased traffic
- Documentation updates
- Test infrastructure optimization

### Adding New Tests

#### For New Features
1. Write unit tests first (TDD approach)
2. Add integration tests for workflows
3. Include security tests for new endpoints
4. Add performance tests for critical paths

#### Test Naming Conventions
```python
# Unit tests
def test_create_product_with_valid_data():
def test_create_product_with_invalid_data():
def test_create_product_unauthorized():

# Integration tests  
def test_product_optimization_workflow():
def test_bulk_product_processing():

# Performance tests
def test_api_response_time_under_load():
def test_database_query_performance():
```

#### Test Organization
- Group related tests in classes
- Use descriptive test names
- Include docstrings for complex tests
- Follow AAA pattern (Arrange, Act, Assert)

## Troubleshooting

### Common Test Failures

#### "Database connection failed"
- Check PostgreSQL service is running
- Verify DATABASE_URL environment variable
- Ensure test database exists and is accessible

#### "Redis connection refused"
- Check Redis service is running
- Verify REDIS_URL environment variable
- Ensure Redis is configured for testing

#### "AI service timeout"
- Check internet connection
- Verify OPENROUTER_API_KEY is valid
- Use mocked responses for offline testing

#### "WebSocket connection failed"
- Check application is running
- Verify WebSocket endpoint is accessible
- Check for port conflicts

### Getting Help

1. **Check CI Logs**: Review GitHub Actions logs for detailed error information
2. **Local Debugging**: Run tests locally with verbose output
3. **Test Coverage**: Use coverage reports to identify untested code
4. **Performance Profiling**: Use profiling tools for slow tests

## Best Practices

### Writing Effective Tests

1. **Test Behavior, Not Implementation**: Focus on what the code does, not how
2. **Use Descriptive Names**: Test names should explain the scenario
3. **Keep Tests Independent**: Each test should be able to run in isolation
4. **Use Appropriate Mocking**: Mock external dependencies, not internal logic
5. **Test Edge Cases**: Include boundary conditions and error scenarios

### Test Performance

1. **Fast Unit Tests**: Unit tests should complete in milliseconds
2. **Reasonable Integration Tests**: Integration tests under 10 seconds
3. **Efficient Load Tests**: Optimize load tests for CI/CD pipeline
4. **Parallel Execution**: Use pytest-xdist for parallel test execution

### Continuous Improvement

1. **Regular Review**: Periodically review and refactor tests
2. **Monitor Metrics**: Track test execution time and success rates
3. **Update Dependencies**: Keep testing frameworks and tools updated
4. **Team Training**: Ensure team understands testing practices

This comprehensive testing guide ensures the GridSpoke testing suite is maintainable, reliable, and effective at catching issues before they reach production.
