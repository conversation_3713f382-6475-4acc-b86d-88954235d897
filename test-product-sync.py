#!/usr/bin/env python3
"""
Test script to verify product syncing functionality between WordPress and GridSpoke API
"""

import requests
import json
import uuid

# API endpoint
api_endpoint = 'http://localhost:8000/'

# Test user credentials
email = '<EMAIL>'
password = 'test12345'

def make_request(method, endpoint, data=None, access_token=None):
    """
    Make HTTP request to GridSpoke API
    """
    url = api_endpoint + endpoint.lstrip('/')
    
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'GridSpoke-Test-Script/1.0'
    }
    
    # Add authentication header if token is provided
    if access_token:
        headers['Authorization'] = 'Bearer ' + access_token
    
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers, timeout=30)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=data, timeout=30)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
            
        return {
            'status_code': response.status_code,
            'body': response.json() if response.content else None
        }
    except requests.exceptions.RequestException as e:
        return {
            'status_code': 0,
            'body': {'error': str(e)}
        }

def authenticate(email, password):
    """
    Authenticate with email/password and get JWT tokens
    """
    payload = {
        'email': email,
        'password': password
    }
    
    response = make_request('POST', 'api/v1/auth/login', payload)
    
    if response['status_code'] == 200 and 'access_token' in response['body']:
        return {
            'success': True,
            'access_token': response['body']['access_token'],
            'refresh_token': response['body']['refresh_token'],
            'user': response['body']['user']
        }
    else:
        return {
            'success': False,
            'message': response['body'].get('detail', 'Authentication failed') if response['body'] else 'Authentication failed'
        }

def create_store(access_token, store_name="Test Store", platform="woocommerce"):
    """
    Create a store for testing
    """
    payload = {
        'name': store_name,
        'store_url': 'http://test-store.local',
        'platform': platform,
        'api_credentials': {}
    }
    
    response = make_request('POST', 'api/v1/stores', payload, access_token)
    
    if response['status_code'] == 201 and 'id' in response['body']:
        return {
            'success': True,
            'store_id': response['body']['id']
        }
    else:
        return {
            'success': False,
            'message': response['body'].get('detail', 'Store creation failed') if response['body'] else 'Store creation failed'
        }

def sync_products(access_token, store_id, products):
    """
    Sync products to the GridSpoke API
    """
    # Format products for the API
    formatted_products = []
    for product in products:
        formatted_products.append({
            'store_id': store_id,
            'external_id': str(product['id']),
            'name': product['name'],
            'sku': product.get('sku', ''),
            'description': product.get('description', ''),
            'price': product.get('price'),
            'categories': product.get('categories', []),
            'tags': product.get('tags', []),
            'images': product.get('images', []),
            'original_data': product
        })
    
    response = make_request('POST', 'api/v1/products/bulk/sync', formatted_products, access_token)
    
    if response['status_code'] in [200, 201]:
        return {
            'success': True,
            'result': response['body']
        }
    else:
        return {
            'success': False,
            'message': response['body'].get('detail', f'Product sync failed with status {response["status_code"]}') if response['body'] else f'Product sync failed with status {response["status_code"]}'
        }

if __name__ == "__main__":
    print("[INFO] Testing product syncing functionality...")
    
    # Authenticate
    print("[INFO] Authenticating...")
    auth_result = authenticate(email, password)
    if not auth_result['success']:
        print(f"[ERROR] Authentication failed: {auth_result['message']}")
        exit(1)
    
    access_token = auth_result['access_token']
    print("[OK] Authentication successful")
    
    # Create a test store
    print("[INFO] Creating test store...")
    store_result = create_store(access_token)
    if not store_result['success']:
        print(f"[ERROR] Store creation failed: {store_result['message']}")
        exit(1)
    
    store_id = store_result['store_id']
    print(f"[OK] Store created with ID: {store_id}")
    
    # Create test products
    test_products = [
        {
            'id': 1,
            'name': 'Test Product 1',
            'sku': 'TP001',
            'description': 'This is a test product for syncing',
            'price': 19.99,
            'categories': ['Test Category'],
            'tags': ['test', 'product'],
            'images': ['http://example.com/image1.jpg']
        },
        {
            'id': 2,
            'name': 'Test Product 2',
            'sku': 'TP002',
            'description': 'This is another test product for syncing',
            'price': 29.99,
            'categories': ['Test Category', 'Another Category'],
            'tags': ['test', 'product', 'second'],
            'images': ['http://example.com/image2.jpg', 'http://example.com/image3.jpg']
        }
    ]
    
    # Sync products
    print("[INFO] Syncing test products...")
    sync_result = sync_products(access_token, store_id, test_products)
    if not sync_result['success']:
        print(f"[ERROR] Product sync failed: {sync_result['message']}")
        exit(1)
    
    result = sync_result['result']
    print(f"[OK] Product sync completed")
    print(f"  - Created: {result.get('created', 0)} products")
    print(f"  - Updated: {result.get('updated', 0)} products")
    print(f"  - Errors: {len(result.get('errors', []))}")
    
    print("\n[SUCCESS] Product syncing functionality verified!")
    print("The WordPress plugin should be able to sync products with the GridSpoke API.")
