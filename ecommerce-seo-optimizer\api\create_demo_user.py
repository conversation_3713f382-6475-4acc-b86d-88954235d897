import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from core.database import get_db_session
from crud.user import user_crud
from schemas.user import UserCreate

async def create_demo_user():
    async for db in get_db_session():
        user_in = UserCreate(email="<EMAIL>", password="demo12345")
        user = await user_crud.create(db, obj_in=user_in)
        if user:
            print(f"User {user.email} created successfully.")
        else:
            print("Failed to create user.")
        break  # Only need one iteration

if __name__ == "__main__":
    asyncio.run(create_demo_user())
