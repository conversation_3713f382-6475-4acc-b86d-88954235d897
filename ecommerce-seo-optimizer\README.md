# GridSpoke - AI-Powered Ecommerce SEO Optimizer

An intelligent SEO optimization service for ecommerce websites, powered by AI agents and designed for bulk product optimization.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- OpenRouter API key (for AI processing)

### Setup Instructions

1. **<PERSON>lone and navigate to the project:**
   ```bash
   cd ecommerce-seo-optimizer
   ```

2. **Create environment file:**
   ```bash
   cp .env.example .env
   ```

3. **Configure your environment variables in `.env`:**
   ```env
   POSTGRES_PASSWORD=your_secure_password
   SECRET_KEY=your_32_character_secret_key
   OPENROUTER_API_KEY=your_openrouter_api_key
   ```

4. **Start all services:**
   ```bash
   docker-compose up -d
   ```

5. **Check service health:**
   ```bash
   docker-compose ps
   docker-compose logs -f api
   ```

6. **Access the application:**
   - API Documentation: http://localhost:8000/docs
   - Frontend Dashboard: http://localhost
   - Database: localhost:5432
   - Redis: localhost:6379

## 🏗️ Architecture Overview

### Services
- **API** (FastAPI): Core backend with AI agent integration
- **Database** (PostgreSQL + pgvector): Data storage with vector search
- **Redis**: Message broker and caching
- **Celery Worker**: Async task processing
- **Celery Beat**: Scheduled job management
- **Nginx**: Reverse proxy and static file serving
- **Frontend**: React dashboard (development ready)

### Key Features
- **AI-Powered Optimization**: Uses Mirascope + OpenRouter for multi-LLM support
- **Vector Search**: PostgreSQL with pgvector for semantic product matching
- **Bulk Processing**: Celery-based async processing for thousands of products
- **Real-time Updates**: WebSocket support for live progress tracking
- **WordPress Integration**: Plugin-ready API for WooCommerce/SureCart

## 🔧 Development Commands

### Docker Management
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f [service_name]

# Stop all services
docker-compose down

# Rebuild services
docker-compose build --no-cache

# Clean restart
docker-compose down --volumes && docker-compose up -d
```

### Database Operations
```bash
# Access PostgreSQL
docker-compose exec db psql -U gridspoke_user -d gridspoke_db

# Run migrations (when implemented)
docker-compose exec api alembic upgrade head

# Create migration (when implemented)
docker-compose exec api alembic revision --autogenerate -m "description"
```

### Celery Management
```bash
# View worker status
docker-compose exec celery-worker celery -A workers.celery_app inspect active

# Monitor tasks
docker-compose exec celery-worker celery -A workers.celery_app events

# Restart workers
docker-compose restart celery-worker celery-beat
```

## 📁 Project Structure

```
ecommerce-seo-optimizer/
├── api/                    # FastAPI backend
│   ├── Dockerfile
│   ├── requirements.txt
│   ├── app/               # (to be created in Phase 2)
│   └── workers/           # (to be created in Phase 2)
├── frontend/              # React dashboard
│   ├── Dockerfile
│   └── src/               # (to be created in Phase 3)
├── nginx/                 # Reverse proxy
│   ├── Dockerfile
│   └── nginx.conf
├── scripts/               # Database initialization
│   └── init-db.sql
├── docker-compose.yml     # Service orchestration
├── .env.example          # Environment template
└── README.md
```

## 🌟 Next Steps

### Phase 2: Backend API Foundation
- FastAPI application structure
- SQLAlchemy models with pgvector
- JWT authentication
- Database migrations with Alembic

### Phase 3: AI Integration
- Mirascope agent implementation
- OpenRouter API integration
- Product optimization workflows
- Streaming response handling

### Phase 4: Task Processing
- Celery task definitions
- Progress tracking system
- Bulk optimization jobs
- WebSocket real-time updates

### Phase 5: Frontend Dashboard
- React application setup
- Authentication flow
- Product management interface
- Job monitoring dashboard

## 🔐 Security Features

- **Container Security**: Non-root users in all containers
- **Network Isolation**: Custom Docker network
- **Rate Limiting**: Nginx-based API rate limiting
- **Environment Secrets**: Secure environment variable management
- **Health Checks**: Comprehensive service health monitoring

## 📊 Monitoring & Observability

### Health Check Endpoints
- API Health: http://localhost:8000/health
- Database: Built-in PostgreSQL health check
- Redis: Built-in Redis ping check

### Logs
```bash
# View all logs
docker-compose logs -f

# Service-specific logs
docker-compose logs -f api
docker-compose logs -f celery-worker
docker-compose logs -f nginx
```

## 🚨 Troubleshooting

### Common Issues

1. **Services not starting:**
   ```bash
   docker-compose down --volumes
   docker-compose build --no-cache
   docker-compose up -d
   ```

2. **Database connection issues:**
   - Check PostgreSQL is healthy: `docker-compose ps`
   - Verify environment variables in `.env`
   - Check database logs: `docker-compose logs db`

3. **Permission issues:**
   - Ensure `.env` file has correct values
   - Check Docker daemon is running
   - Verify port availability (80, 8000, 5432, 6379)

### Port Conflicts
If you have port conflicts, modify `docker-compose.yml`:
```yaml
ports:
  - "8080:80"    # Change 80 to 8080
  - "8001:80"    # Change 8000 to 8001
```

## 📝 Environment Variables

Required variables in `.env`:
- `POSTGRES_PASSWORD`: Database password
- `SECRET_KEY`: Application secret key (32+ characters)
- `OPENROUTER_API_KEY`: AI provider API key

Optional variables:
- `DEBUG`: Enable debug mode (development)
- `LOG_LEVEL`: Logging level (INFO, DEBUG, WARNING)
- `ENVIRONMENT`: Runtime environment (development, production)

## 🤝 Contributing

1. Follow the phase-based development approach
2. Use Docker for all development
3. Run tests before committing
4. Follow conventional commit messages
5. Update documentation for new features

## 📄 License

MIT License - see LICENSE file for details

---

**Note**: This is Phase 1 of the implementation. The API and frontend applications will be developed in subsequent phases following the project roadmap.
