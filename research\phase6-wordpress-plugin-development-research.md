# Phase 6: WordPress Plugin Development Research - GridSpoke Ecommerce SEO Optimizer

## Research Overview
This research focuses on WordPress plugin development specifically for creating a connector plugin that integrates WooCommerce and SureCart stores with the GridSpoke Ecommerce SEO Optimizer backend service.

## Core Concepts and Requirements

### Project Context
- **Main Service**: GridSpoke Ecommerce SEO Optimizer - AI-powered backend for bulk product optimization
- **Plugin Purpose**: Lightweight connector to sync product data and receive AI-optimized content
- **Integration Target**: WooCommerce and SureCart stores
- **Architecture**: Zero local processing - all AI computation on GridSpoke backend

## WordPress Plugin Foundation

### 1. Plugin Structure and Basics

#### Plugin Header Requirements
```php
<?php
/**
 * Plugin Name: GridSpoke SEO Optimizer Connector
 * Plugin URI: https://gridspoke.com/wordpress-plugin
 * Description: Connect your WooCommerce/SureCart store to GridSpoke's AI-powered SEO optimization service.
 * Version: 1.0.0
 * Author: GridSpoke
 * Author URI: https://gridspoke.com
 * License: GPL v2 or later
 * Text Domain: gridspoke-seo
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 4.0
 * WC tested up to: 8.5
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
```

#### Directory Structure
```
wordpress-plugin/
├── gridspoke-seo-connector/
│   ├── gridspoke-seo-connector.php          # Main plugin file
│   ├── includes/
│   │   ├── class-gridspoke-api-client.php   # API communication
│   │   ├── class-woocommerce-integration.php # WooCommerce hooks
│   │   ├── class-surecart-integration.php   # SureCart integration
│   │   ├── class-webhook-handler.php        # Receive optimized content
│   │   ├── class-settings.php               # Plugin settings
│   │   └── class-logger.php                 # Error logging
│   ├── admin/
│   │   ├── views/
│   │   │   ├── settings-page.php            # Admin settings UI
│   │   │   └── dashboard-widget.php         # Dashboard widget
│   │   ├── assets/
│   │   │   ├── css/admin.css                # Admin styles
│   │   │   └── js/admin.js                  # Admin JavaScript
│   │   └── class-admin.php                  # Admin functionality
│   ├── languages/                           # Translation files
│   └── readme.txt                           # WordPress.org readme
```

### 2. WordPress Plugin APIs and Hooks

#### Activation and Deactivation Hooks
```php
// Activation hook - setup plugin data
register_activation_hook(__FILE__, 'gridspoke_seo_activate');
function gridspoke_seo_activate() {
    // Create database tables if needed
    // Set default options
    // Schedule cron jobs
    flush_rewrite_rules();
}

// Deactivation hook - cleanup
register_deactivation_hook(__FILE__, 'gridspoke_seo_deactivate');
function gridspoke_seo_deactivate() {
    // Clear scheduled cron jobs
    wp_clear_scheduled_hook('gridspoke_sync_products');
}

// Uninstall hook - complete cleanup
register_uninstall_hook(__FILE__, 'gridspoke_seo_uninstall');
function gridspoke_seo_uninstall() {
    // Delete all plugin data
    delete_option('gridspoke_seo_settings');
    // Drop custom tables if any
}
```

#### WordPress HTTP API for External Service Communication
```php
class GridSpoke_API_Client {
    private $api_url = 'https://api.gridspoke.com/v1';
    private $api_key;
    
    public function __construct($api_key) {
        $this->api_key = $api_key;
    }
    
    /**
     * Send products to GridSpoke for optimization
     */
    public function send_products_for_optimization($products) {
        $response = wp_remote_post($this->api_url . '/products/optimize', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->api_key,
            ],
            'body' => json_encode(['products' => $products]),
            'timeout' => 30,
        ]);
        
        if (is_wp_error($response)) {
            error_log('GridSpoke API Error: ' . $response->get_error_message());
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $code = wp_remote_retrieve_response_code($response);
        
        if ($code !== 200) {
            error_log('GridSpoke API returned status: ' . $code);
            return false;
        }
        
        return json_decode($body, true);
    }
    
    /**
     * Get optimization job status
     */
    public function get_job_status($job_id) {
        $response = wp_remote_get($this->api_url . '/jobs/' . $job_id, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->api_key,
            ],
            'timeout' => 15,
        ]);
        
        return $this->process_response($response);
    }
    
    private function process_response($response) {
        if (is_wp_error($response)) {
            return ['error' => $response->get_error_message()];
        }
        
        $code = wp_remote_retrieve_response_code($response);
        $body = json_decode(wp_remote_retrieve_body($response), true);
        
        return [
            'status_code' => $code,
            'data' => $body,
            'success' => $code >= 200 && $code < 300
        ];
    }
}
```

#### WordPress Cron for Scheduled Tasks
```php
// Schedule automatic sync
if (!wp_next_scheduled('gridspoke_sync_products')) {
    wp_schedule_event(time(), 'daily', 'gridspoke_sync_products');
}

// Cron hook handler
add_action('gridspoke_sync_products', 'gridspoke_handle_scheduled_sync');
function gridspoke_handle_scheduled_sync() {
    $settings = get_option('gridspoke_seo_settings');
    
    if (!$settings['auto_sync_enabled']) {
        return;
    }
    
    // Get modified products since last sync
    $last_sync = get_option('gridspoke_last_sync', 0);
    $products = get_modified_products_since($last_sync);
    
    if (!empty($products)) {
        $api_client = new GridSpoke_API_Client($settings['api_key']);
        $result = $api_client->send_products_for_optimization($products);
        
        if ($result) {
            update_option('gridspoke_last_sync', time());
            gridspoke_log('Scheduled sync completed: ' . count($products) . ' products sent');
        }
    }
}
```

## WooCommerce Integration

### 1. WooCommerce Hooks and Filters

#### Product Data Extraction
```php
class GridSpoke_WooCommerce_Integration {
    
    public function __construct() {
        // Hook into WooCommerce actions
        add_action('save_post_product', [$this, 'on_product_save'], 10, 2);
        add_action('woocommerce_product_object_updated_props', [$this, 'on_product_updated'], 10, 2);
        
        // Add bulk actions
        add_filter('bulk_actions-edit-product', [$this, 'add_bulk_actions']);
        add_filter('handle_bulk_actions-edit-product', [$this, 'handle_bulk_actions'], 10, 3);
        
        // Add meta boxes
        add_action('add_meta_boxes', [$this, 'add_product_meta_box']);
        
        // AJAX handlers
        add_action('wp_ajax_gridspoke_optimize_product', [$this, 'ajax_optimize_product']);
    }
    
    /**
     * Extract product data for GridSpoke optimization
     */
    public function extract_product_data($product_id) {
        $product = wc_get_product($product_id);
        
        if (!$product) {
            return false;
        }
        
        return [
            'id' => $product_id,
            'name' => $product->get_name(),
            'description' => $product->get_description(),
            'short_description' => $product->get_short_description(),
            'sku' => $product->get_sku(),
            'price' => $product->get_price(),
            'categories' => wp_get_post_terms($product_id, 'product_cat', ['fields' => 'names']),
            'tags' => wp_get_post_terms($product_id, 'product_tag', ['fields' => 'names']),
            'images' => $this->get_product_images($product),
            'attributes' => $this->get_product_attributes($product),
            'meta_title' => get_post_meta($product_id, '_yoast_wpseo_title', true),
            'meta_description' => get_post_meta($product_id, '_yoast_wpseo_metadesc', true),
            'status' => $product->get_status(),
            'type' => $product->get_type(),
            'last_modified' => $product->get_date_modified()->getTimestamp(),
        ];
    }
    
    /**
     * Handle product save events
     */
    public function on_product_save($post_id, $post) {
        if ($post->post_type !== 'product') {
            return;
        }
        
        $settings = get_option('gridspoke_seo_settings', []);
        
        // Auto-optimize on save if enabled
        if (!empty($settings['auto_optimize_on_save'])) {
            $this->queue_product_for_optimization($post_id);
        }
    }
    
    /**
     * Add bulk action for SEO optimization
     */
    public function add_bulk_actions($actions) {
        $actions['gridspoke_optimize'] = 'Optimize with GridSpoke SEO';
        return $actions;
    }
    
    /**
     * Handle bulk SEO optimization
     */
    public function handle_bulk_actions($redirect_to, $action, $post_ids) {
        if ($action !== 'gridspoke_optimize') {
            return $redirect_to;
        }
        
        $settings = get_option('gridspoke_seo_settings', []);
        $api_client = new GridSpoke_API_Client($settings['api_key']);
        
        $products = [];
        foreach ($post_ids as $product_id) {
            $product_data = $this->extract_product_data($product_id);
            if ($product_data) {
                $products[] = $product_data;
            }
        }
        
        if (!empty($products)) {
            $result = $api_client->send_products_for_optimization($products);
            
            if ($result) {
                $redirect_to = add_query_arg('gridspoke_optimized', count($products), $redirect_to);
            } else {
                $redirect_to = add_query_arg('gridspoke_error', 1, $redirect_to);
            }
        }
        
        return $redirect_to;
    }
    
    private function get_product_images($product) {
        $images = [];
        
        // Main image
        $main_image_id = $product->get_image_id();
        if ($main_image_id) {
            $images[] = [
                'id' => $main_image_id,
                'url' => wp_get_attachment_image_url($main_image_id, 'full'),
                'alt' => get_post_meta($main_image_id, '_wp_attachment_image_alt', true),
                'is_main' => true,
            ];
        }
        
        // Gallery images
        $gallery_ids = $product->get_gallery_image_ids();
        foreach ($gallery_ids as $image_id) {
            $images[] = [
                'id' => $image_id,
                'url' => wp_get_attachment_image_url($image_id, 'full'),
                'alt' => get_post_meta($image_id, '_wp_attachment_image_alt', true),
                'is_main' => false,
            ];
        }
        
        return $images;
    }
}
```

### 2. Product Meta Box for Individual Optimization
```php
public function add_product_meta_box() {
    add_meta_box(
        'gridspoke-seo-optimization',
        'GridSpoke SEO Optimization',
        [$this, 'render_product_meta_box'],
        'product',
        'side',
        'default'
    );
}

public function render_product_meta_box($post) {
    wp_nonce_field('gridspoke_product_meta', 'gridspoke_nonce');
    
    $optimization_status = get_post_meta($post->ID, '_gridspoke_optimization_status', true);
    $last_optimized = get_post_meta($post->ID, '_gridspoke_last_optimized', true);
    $job_id = get_post_meta($post->ID, '_gridspoke_job_id', true);
    
    ?>
    <div id="gridspoke-optimization-panel">
        <p><strong>Optimization Status:</strong> <?php echo esc_html($optimization_status ?: 'Not optimized'); ?></p>
        
        <?php if ($last_optimized): ?>
            <p><strong>Last Optimized:</strong> <?php echo date('Y-m-d H:i:s', $last_optimized); ?></p>
        <?php endif; ?>
        
        <p>
            <button type="button" id="gridspoke-optimize-btn" class="button button-primary" 
                    data-product-id="<?php echo $post->ID; ?>"
                    <?php echo $job_id ? 'disabled' : ''; ?>>
                <?php echo $job_id ? 'Optimization in Progress...' : 'Optimize with GridSpoke'; ?>
            </button>
        </p>
        
        <?php if ($job_id): ?>
            <p><button type="button" id="gridspoke-check-status" class="button" 
                       data-job-id="<?php echo esc_attr($job_id); ?>">Check Status</button></p>
        <?php endif; ?>
        
        <div id="gridspoke-status-message"></div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        $('#gridspoke-optimize-btn').on('click', function() {
            var productId = $(this).data('product-id');
            var button = $(this);
            var statusDiv = $('#gridspoke-status-message');
            
            button.prop('disabled', true).text('Starting optimization...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'gridspoke_optimize_product',
                    product_id: productId,
                    nonce: $('#gridspoke_nonce').val()
                },
                success: function(response) {
                    if (response.success) {
                        statusDiv.html('<div class="notice notice-success"><p>Optimization started! Job ID: ' + response.data.job_id + '</p></div>');
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        statusDiv.html('<div class="notice notice-error"><p>Error: ' + response.data.message + '</p></div>');
                        button.prop('disabled', false).text('Optimize with GridSpoke');
                    }
                },
                error: function() {
                    statusDiv.html('<div class="notice notice-error"><p>Network error occurred</p></div>');
                    button.prop('disabled', false).text('Optimize with GridSpoke');
                }
            });
        });
    });
    </script>
    <?php
}
```

### 3. AJAX Handlers
```php
public function ajax_optimize_product() {
    check_ajax_referer('gridspoke_product_meta', 'nonce');
    
    if (!current_user_can('edit_products')) {
        wp_die('Insufficient permissions');
    }
    
    $product_id = intval($_POST['product_id']);
    $product_data = $this->extract_product_data($product_id);
    
    if (!$product_data) {
        wp_send_json_error(['message' => 'Invalid product']);
    }
    
    $settings = get_option('gridspoke_seo_settings', []);
    $api_client = new GridSpoke_API_Client($settings['api_key']);
    
    $result = $api_client->send_products_for_optimization([$product_data]);
    
    if ($result && !empty($result['job_id'])) {
        update_post_meta($product_id, '_gridspoke_job_id', $result['job_id']);
        update_post_meta($product_id, '_gridspoke_optimization_status', 'processing');
        
        wp_send_json_success([
            'job_id' => $result['job_id'],
            'message' => 'Optimization job started successfully'
        ]);
    } else {
        wp_send_json_error(['message' => 'Failed to start optimization job']);
    }
}
```

## SureCart Integration

### 1. SureCart Plugin Detection and Integration
```php
class GridSpoke_SureCart_Integration {
    
    public function __construct() {
        // Check if SureCart is active
        if (!$this->is_surecart_active()) {
            return;
        }
        
        add_action('surecart/product_updated', [$this, 'on_product_updated'], 10, 1);
        add_action('surecart/product_created', [$this, 'on_product_created'], 10, 1);
        
        // Add SureCart admin integration
        add_action('surecart/admin_product_edit', [$this, 'add_optimization_section'], 10, 1);
    }
    
    private function is_surecart_active() {
        return class_exists('SureCart') || function_exists('surecart');
    }
    
    /**
     * Extract SureCart product data
     */
    public function extract_surecart_product_data($product_id) {
        // Note: This would need to be adapted based on SureCart's actual API
        // The structure below is illustrative
        
        $product = surecart_get_product($product_id);
        
        if (!$product) {
            return false;
        }
        
        return [
            'id' => $product_id,
            'platform' => 'surecart',
            'name' => $product->name,
            'description' => $product->description,
            'price' => $product->price,
            'images' => $this->get_surecart_product_images($product),
            'status' => $product->status,
            'last_modified' => $product->updated_at,
        ];
    }
    
    public function on_product_updated($product_id) {
        $settings = get_option('gridspoke_seo_settings', []);
        
        if (!empty($settings['auto_optimize_surecart'])) {
            $this->queue_surecart_product_for_optimization($product_id);
        }
    }
    
    private function queue_surecart_product_for_optimization($product_id) {
        $product_data = $this->extract_surecart_product_data($product_id);
        
        if (!$product_data) {
            return false;
        }
        
        $settings = get_option('gridspoke_seo_settings', []);
        $api_client = new GridSpoke_API_Client($settings['api_key']);
        
        return $api_client->send_products_for_optimization([$product_data]);
    }
}
```

## Webhook Handler for Receiving Optimized Content

### 1. Webhook Endpoint Setup
```php
class GridSpoke_Webhook_Handler {
    
    public function __construct() {
        add_action('init', [$this, 'add_webhook_endpoint']);
        add_action('template_redirect', [$this, 'handle_webhook_request']);
    }
    
    public function add_webhook_endpoint() {
        add_rewrite_rule(
            '^gridspoke-webhook/?$',
            'index.php?gridspoke_webhook=1',
            'top'
        );
    }
    
    public function handle_webhook_request() {
        if (!get_query_var('gridspoke_webhook')) {
            return;
        }
        
        // Verify request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            status_header(405);
            exit('Method not allowed');
        }
        
        // Get raw POST data
        $payload = file_get_contents('php://input');
        
        // Verify webhook signature
        if (!$this->verify_webhook_signature($payload)) {
            status_header(401);
            exit('Unauthorized');
        }
        
        $data = json_decode($payload, true);
        
        if (!$data) {
            status_header(400);
            exit('Invalid JSON');
        }
        
        try {
            $this->process_optimization_result($data);
            status_header(200);
            echo json_encode(['status' => 'success']);
        } catch (Exception $e) {
            error_log('GridSpoke webhook error: ' . $e->getMessage());
            status_header(500);
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        }
        
        exit;
    }
    
    private function verify_webhook_signature($payload) {
        $settings = get_option('gridspoke_seo_settings', []);
        $secret = $settings['webhook_secret'] ?? '';
        
        if (empty($secret)) {
            return false;
        }
        
        $signature = $_SERVER['HTTP_X_GRIDSPOKE_SIGNATURE'] ?? '';
        $expected = 'sha256=' . hash_hmac('sha256', $payload, $secret);
        
        return hash_equals($expected, $signature);
    }
    
    /**
     * Process optimization results from GridSpoke
     */
    private function process_optimization_result($data) {
        $job_id = $data['job_id'];
        $status = $data['status'];
        $products = $data['products'] ?? [];
        
        foreach ($products as $product_data) {
            $product_id = $product_data['id'];
            $platform = $product_data['platform'] ?? 'woocommerce';
            
            if ($platform === 'woocommerce') {
                $this->update_woocommerce_product($product_id, $product_data);
            } elseif ($platform === 'surecart') {
                $this->update_surecart_product($product_id, $product_data);
            }
        }
        
        // Update job status
        $this->update_job_status($job_id, $status);
        
        // Send admin notification
        $this->send_optimization_notification($data);
    }
    
    private function update_woocommerce_product($product_id, $optimized_data) {
        $product = wc_get_product($product_id);
        
        if (!$product) {
            throw new Exception("Product $product_id not found");
        }
        
        // Update SEO title and meta description
        if (!empty($optimized_data['seo_title'])) {
            update_post_meta($product_id, '_yoast_wpseo_title', $optimized_data['seo_title']);
        }
        
        if (!empty($optimized_data['meta_description'])) {
            update_post_meta($product_id, '_yoast_wpseo_metadesc', $optimized_data['meta_description']);
        }
        
        // Update product title and description if included
        if (!empty($optimized_data['optimized_title'])) {
            wp_update_post([
                'ID' => $product_id,
                'post_title' => $optimized_data['optimized_title'],
            ]);
        }
        
        if (!empty($optimized_data['optimized_description'])) {
            wp_update_post([
                'ID' => $product_id,
                'post_content' => $optimized_data['optimized_description'],
            ]);
        }
        
        if (!empty($optimized_data['optimized_short_description'])) {
            wp_update_post([
                'ID' => $product_id,
                'post_excerpt' => $optimized_data['optimized_short_description'],
            ]);
        }
        
        // Update image alt texts
        if (!empty($optimized_data['image_alt_texts'])) {
            foreach ($optimized_data['image_alt_texts'] as $image_id => $alt_text) {
                update_post_meta($image_id, '_wp_attachment_image_alt', $alt_text);
            }
        }
        
        // Update optimization metadata
        update_post_meta($product_id, '_gridspoke_optimization_status', 'completed');
        update_post_meta($product_id, '_gridspoke_last_optimized', time());
        update_post_meta($product_id, '_gridspoke_job_id', '');
        
        // Store optimization history
        $history = get_post_meta($product_id, '_gridspoke_optimization_history', true) ?: [];
        $history[] = [
            'timestamp' => time(),
            'changes' => array_keys($optimized_data),
            'job_id' => $optimized_data['job_id'] ?? '',
        ];
        update_post_meta($product_id, '_gridspoke_optimization_history', $history);
    }
}
```

## Admin Interface and Settings

### 1. Settings Page
```php
class GridSpoke_Admin {
    
    public function __construct() {
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
        add_action('wp_dashboard_setup', [$this, 'add_dashboard_widget']);
        
        // Admin notices
        add_action('admin_notices', [$this, 'show_admin_notices']);
    }
    
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            'GridSpoke SEO Settings',
            'GridSpoke SEO',
            'manage_woocommerce',
            'gridspoke-seo-settings',
            [$this, 'render_settings_page']
        );
    }
    
    public function register_settings() {
        register_setting('gridspoke_seo_settings', 'gridspoke_seo_settings', [
            'sanitize_callback' => [$this, 'sanitize_settings']
        ]);
        
        add_settings_section(
            'gridspoke_api_section',
            'API Configuration',
            [$this, 'render_api_section'],
            'gridspoke_seo_settings'
        );
        
        add_settings_field(
            'api_key',
            'API Key',
            [$this, 'render_api_key_field'],
            'gridspoke_seo_settings',
            'gridspoke_api_section'
        );
        
        add_settings_field(
            'api_endpoint',
            'API Endpoint',
            [$this, 'render_api_endpoint_field'],
            'gridspoke_seo_settings',
            'gridspoke_api_section'
        );
        
        // Automation settings
        add_settings_section(
            'gridspoke_automation_section',
            'Automation Settings',
            [$this, 'render_automation_section'],
            'gridspoke_seo_settings'
        );
        
        add_settings_field(
            'auto_optimize_on_save',
            'Auto-optimize on product save',
            [$this, 'render_checkbox_field'],
            'gridspoke_seo_settings',
            'gridspoke_automation_section',
            ['field_name' => 'auto_optimize_on_save']
        );
    }
    
    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1>GridSpoke SEO Optimizer Settings</h1>
            
            <div class="gridspoke-admin-content">
                <div class="gridspoke-main-settings">
                    <form method="post" action="options.php">
                        <?php
                        settings_fields('gridspoke_seo_settings');
                        do_settings_sections('gridspoke_seo_settings');
                        submit_button();
                        ?>
                    </form>
                </div>
                
                <div class="gridspoke-sidebar">
                    <div class="gridspoke-widget">
                        <h3>Connection Status</h3>
                        <div id="gridspoke-connection-status">
                            <button type="button" id="test-connection" class="button">Test Connection</button>
                            <div id="connection-result"></div>
                        </div>
                    </div>
                    
                    <div class="gridspoke-widget">
                        <h3>Quick Actions</h3>
                        <p><a href="<?php echo admin_url('edit.php?post_type=product&gridspoke_bulk=1'); ?>" class="button">Bulk Optimize Products</a></p>
                        <p><a href="<?php echo admin_url('admin.php?page=gridspoke-seo-logs'); ?>" class="button">View Logs</a></p>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .gridspoke-admin-content {
            display: flex;
            gap: 20px;
        }
        .gridspoke-main-settings {
            flex: 2;
        }
        .gridspoke-sidebar {
            flex: 1;
        }
        .gridspoke-widget {
            background: #fff;
            border: 1px solid #ccd0d4;
            padding: 15px;
            margin-bottom: 15px;
        }
        .gridspoke-widget h3 {
            margin-top: 0;
        }
        </style>
        <?php
    }
    
    public function render_api_key_field() {
        $settings = get_option('gridspoke_seo_settings', []);
        $api_key = $settings['api_key'] ?? '';
        ?>
        <input type="password" name="gridspoke_seo_settings[api_key]" 
               value="<?php echo esc_attr($api_key); ?>" 
               class="regular-text" />
        <p class="description">Your GridSpoke API key. Get this from your GridSpoke dashboard.</p>
        <?php
    }
    
    public function add_dashboard_widget() {
        wp_add_dashboard_widget(
            'gridspoke_seo_dashboard',
            'GridSpoke SEO Optimization',
            [$this, 'render_dashboard_widget']
        );
    }
    
    public function render_dashboard_widget() {
        $recent_jobs = $this->get_recent_optimization_jobs();
        $stats = $this->get_optimization_stats();
        ?>
        <div class="gridspoke-dashboard-widget">
            <div class="gridspoke-stats">
                <div class="stat-item">
                    <span class="stat-number"><?php echo $stats['total_optimized']; ?></span>
                    <span class="stat-label">Products Optimized</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo $stats['pending_jobs']; ?></span>
                    <span class="stat-label">Jobs in Progress</span>
                </div>
            </div>
            
            <?php if (!empty($recent_jobs)): ?>
                <h4>Recent Optimization Jobs</h4>
                <ul class="gridspoke-recent-jobs">
                    <?php foreach ($recent_jobs as $job): ?>
                        <li>
                            <strong><?php echo esc_html($job['title']); ?></strong>
                            <span class="job-status status-<?php echo esc_attr($job['status']); ?>">
                                <?php echo esc_html(ucfirst($job['status'])); ?>
                            </span>
                            <br>
                            <small><?php echo esc_html($job['date']); ?></small>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
            
            <p>
                <a href="<?php echo admin_url('admin.php?page=gridspoke-seo-settings'); ?>" class="button">Settings</a>
                <a href="<?php echo admin_url('edit.php?post_type=product'); ?>" class="button">Manage Products</a>
            </p>
        </div>
        
        <style>
        .gridspoke-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            flex: 1;
        }
        .stat-number {
            display: block;
            font-size: 24px;
            font-weight: bold;
            color: #0073aa;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        .gridspoke-recent-jobs {
            max-height: 200px;
            overflow-y: auto;
        }
        .gridspoke-recent-jobs li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .job-status {
            float: right;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        .status-completed { background: #d4edda; color: #155724; }
        .status-processing { background: #d1ecf1; color: #0c5460; }
        .status-failed { background: #f8d7da; color: #721c24; }
        </style>
        <?php
    }
}
```

### 2. AJAX and JavaScript Integration
```javascript
// admin/assets/js/admin.js
jQuery(document).ready(function($) {
    // Test API connection
    $('#test-connection').on('click', function() {
        var button = $(this);
        var resultDiv = $('#connection-result');
        
        button.prop('disabled', true).text('Testing...');
        resultDiv.html('<div class="spinner is-active"></div>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'gridspoke_test_connection',
                nonce: gridspoke_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    resultDiv.html('<div class="notice notice-success inline"><p>✓ Connection successful!</p></div>');
                } else {
                    resultDiv.html('<div class="notice notice-error inline"><p>✗ ' + response.data.message + '</p></div>');
                }
            },
            error: function() {
                resultDiv.html('<div class="notice notice-error inline"><p>✗ Network error</p></div>');
            },
            complete: function() {
                button.prop('disabled', false).text('Test Connection');
            }
        });
    });
    
    // Auto-refresh job statuses
    function refreshJobStatuses() {
        $('.gridspoke-job-status[data-job-id]').each(function() {
            var element = $(this);
            var jobId = element.data('job-id');
            
            if (element.hasClass('status-processing')) {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'gridspoke_check_job_status',
                        job_id: jobId,
                        nonce: gridspoke_admin.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data.status !== 'processing') {
                            location.reload(); // Refresh to show updated status
                        }
                    }
                });
            }
        });
    }
    
    // Refresh job statuses every 30 seconds
    setInterval(refreshJobStatuses, 30000);
});
```

## Error Handling and Logging

### 1. Custom Logger Class
```php
class GridSpoke_Logger {
    
    private $log_file;
    
    public function __construct() {
        $upload_dir = wp_upload_dir();
        $this->log_file = $upload_dir['basedir'] . '/gridspoke-seo-logs.txt';
    }
    
    public function log($message, $level = 'info') {
        $timestamp = current_time('Y-m-d H:i:s');
        $log_entry = "[$timestamp] [$level] $message" . PHP_EOL;
        
        error_log($log_entry, 3, $this->log_file);
        
        // Also log to WordPress debug if enabled
        if (WP_DEBUG_LOG) {
            error_log("GridSpoke SEO: $message");
        }
    }
    
    public function get_recent_logs($lines = 100) {
        if (!file_exists($this->log_file)) {
            return [];
        }
        
        $file = file($this->log_file);
        return array_slice($file, -$lines);
    }
    
    public function clear_logs() {
        if (file_exists($this->log_file)) {
            unlink($this->log_file);
        }
    }
}

// Global logging function
function gridspoke_log($message, $level = 'info') {
    static $logger = null;
    
    if ($logger === null) {
        $logger = new GridSpoke_Logger();
    }
    
    $logger->log($message, $level);
}
```

## Security Considerations

### 1. Input Validation and Sanitization
```php
public function sanitize_settings($settings) {
    $clean_settings = [];
    
    // Sanitize API key
    if (isset($settings['api_key'])) {
        $clean_settings['api_key'] = sanitize_text_field($settings['api_key']);
    }
    
    // Validate API endpoint URL
    if (isset($settings['api_endpoint'])) {
        $url = esc_url_raw($settings['api_endpoint']);
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            $clean_settings['api_endpoint'] = $url;
        }
    }
    
    // Sanitize boolean options
    $boolean_fields = ['auto_optimize_on_save', 'auto_sync_enabled', 'enable_logging'];
    foreach ($boolean_fields as $field) {
        $clean_settings[$field] = !empty($settings[$field]);
    }
    
    return $clean_settings;
}
```

### 2. Capability Checks
```php
public function check_permissions() {
    // Only users who can manage WooCommerce should access GridSpoke features
    if (!current_user_can('manage_woocommerce')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }
}

// Use in all admin functions
public function handle_bulk_optimization() {
    $this->check_permissions();
    // ... rest of function
}
```

### 3. Nonce Verification
```php
// Always verify nonces for forms and AJAX requests
public function handle_settings_save() {
    if (!wp_verify_nonce($_POST['_wpnonce'], 'gridspoke_settings')) {
        wp_die('Security check failed');
    }
    // ... process settings
}
```

## Testing and Development

### 1. Plugin Development Environment
```php
// Add debug mode for development
class GridSpoke_Debug {
    
    public static function is_debug_mode() {
        return defined('GRIDSPOKE_DEBUG') && GRIDSPOKE_DEBUG;
    }
    
    public static function log_api_request($url, $data, $response) {
        if (!self::is_debug_mode()) {
            return;
        }
        
        $log_data = [
            'timestamp' => current_time('c'),
            'url' => $url,
            'request_data' => $data,
            'response' => $response,
        ];
        
        error_log('GridSpoke API Debug: ' . json_encode($log_data));
    }
}

// Enable debug mode in wp-config.php during development:
// define('GRIDSPOKE_DEBUG', true);
```

### 2. Mock API Responses for Testing
```php
class GridSpoke_Mock_API {
    
    public static function send_products_for_optimization($products) {
        if (!GridSpoke_Debug::is_debug_mode()) {
            return false;
        }
        
        // Return mock response for testing
        return [
            'job_id' => 'mock_job_' . time(),
            'status' => 'processing',
            'estimated_completion' => time() + 300, // 5 minutes
        ];
    }
}
```

## Key Implementation Patterns

### 1. Plugin Activation and Update Handling
```php
class GridSpoke_Plugin_Lifecycle {
    
    public static function activate() {
        // Create database tables if needed
        self::create_tables();
        
        // Set default options
        $default_settings = [
            'api_endpoint' => 'https://api.gridspoke.com/v1',
            'auto_optimize_on_save' => false,
            'auto_sync_enabled' => false,
            'webhook_secret' => wp_generate_password(32, false),
        ];
        
        add_option('gridspoke_seo_settings', $default_settings);
        add_option('gridspoke_seo_version', GRIDSPOKE_VERSION);
        
        // Schedule cron jobs
        if (!wp_next_scheduled('gridspoke_sync_products')) {
            wp_schedule_event(time(), 'daily', 'gridspoke_sync_products');
        }
        
        // Flush rewrite rules for webhook endpoint
        flush_rewrite_rules();
    }
    
    public static function deactivate() {
        // Clear scheduled hooks
        wp_clear_scheduled_hook('gridspoke_sync_products');
    }
    
    public static function uninstall() {
        // Remove all plugin data
        delete_option('gridspoke_seo_settings');
        delete_option('gridspoke_seo_version');
        
        // Remove product meta
        delete_metadata('post', 0, '_gridspoke_optimization_status', '', true);
        delete_metadata('post', 0, '_gridspoke_last_optimized', '', true);
        delete_metadata('post', 0, '_gridspoke_job_id', '', true);
        
        // Drop custom tables if any
        global $wpdb;
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}gridspoke_optimization_jobs");
    }
}
```

## Summary and Best Practices

### Key WordPress Plugin Development Principles for GridSpoke Integration:

1. **Zero Local Processing**: All AI optimization happens on GridSpoke backend
2. **Secure API Communication**: Use WordPress HTTP API with proper authentication
3. **Webhook Integration**: Handle optimized content via secure webhook endpoint
4. **Multi-Platform Support**: Support both WooCommerce and SureCart
5. **User-Friendly Interface**: Integrate seamlessly into WordPress admin
6. **Bulk Operations**: Handle thousands of products efficiently
7. **Real-time Updates**: Use AJAX and WebSockets for progress tracking
8. **Comprehensive Logging**: Track all operations for debugging
9. **Security First**: Proper sanitization, nonces, and capability checks
10. **Extensible Architecture**: Use hooks and filters for customization

This research provides the foundation for implementing a production-ready WordPress plugin that connects WooCommerce and SureCart stores to the GridSpoke Ecommerce SEO Optimizer service.
