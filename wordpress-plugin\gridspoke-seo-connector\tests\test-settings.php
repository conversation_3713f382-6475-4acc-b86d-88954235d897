<?php
/**
 * Tests for GridSpoke_Settings class
 */

class Test_GridSpoke_Settings extends GridSpoke_Test_Case {
    
    /**
     * Settings instance
     */
    private $settings;
    
    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();
        $this->settings = GridSpoke_Settings::get_instance();
    }
    
    /**
     * Test singleton instance
     */
    public function test_singleton_instance() {
        $instance1 = GridSpoke_Settings::get_instance();
        $instance2 = GridSpoke_Settings::get_instance();
        
        $this->assertSame($instance1, $instance2);
        $this->assertInstanceOf('GridSpoke_Settings', $instance1);
    }
    
    /**
     * Test default settings
     */
    public function test_default_settings() {
        $settings = $this->settings->get_settings();
        
        // Check that default values are set
        $this->assertEquals('http://localhost:8000/api/v1/', $settings['api_endpoint']);
        $this->assertEquals('', $settings['email']);
        $this->assertEquals('', $settings['password']);
        $this->assertFalse($settings['auto_sync']);
        $this->assertEquals('daily', $settings['sync_interval']);
        $this->assertEquals('info', $settings['log_level']);
        $this->assertTrue($settings['admin_notifications']);
        $this->assertContains('title', $settings['optimization_fields']);
        $this->assertContains('description', $settings['optimization_fields']);
        $this->assertContains('meta_description', $settings['optimization_fields']);
    }
    
    /**
     * Test settings validation and sanitization
     */
    public function test_settings_sanitization() {
        $input = array(
            'api_endpoint' => 'https://api.gridspoke.com/v1',
            'email' => '  <EMAIL>  ',
            'password' => 'password123!@#',
            'auto_sync' => '1',
            'sync_interval' => 'hourly',
            'log_level' => 'debug',
            'admin_notifications' => '0',
            'optimization_fields' => array('title', 'description', 'invalid_field'),
            'ai_model_preference' => 'claude-3-sonnet',
            'batch_size' => '25',
            'rate_limit_delay' => '2.5'
        );
        
        $sanitized = $this->settings->sanitize_settings($input);
        
        // Test URL sanitization
        $this->assertEquals('https://api.gridspoke.com/v1/', $sanitized['api_endpoint']);
        
        // Test email sanitization
        $this->assertEquals('<EMAIL>', $sanitized['email']);
        
        // Test password preservation
        $this->assertEquals('password123!@#', $sanitized['password']);
        
        // Test boolean conversion
        $this->assertTrue($sanitized['auto_sync']);
        $this->assertFalse($sanitized['admin_notifications']);
        
        // Test array sanitization
        $this->assertContains('title', $sanitized['optimization_fields']);
        $this->assertContains('description', $sanitized['optimization_fields']);
        $this->assertNotContains('invalid_field', $sanitized['optimization_fields']);
        
        // Test numeric sanitization
        $this->assertEquals(25, $sanitized['batch_size']);
        $this->assertEquals(2.5, $sanitized['rate_limit_delay']);
    }
    
    /**
     * Test settings validation errors
     */
    public function test_settings_validation() {
        $input = array(
            'api_endpoint' => 'invalid-url',
            'email' => 'invalid-email',
            'batch_size' => '-5',
            'rate_limit_delay' => '-1',
            'cleanup_logs_days' => '0'
        );
        
        $sanitized = $this->settings->sanitize_settings($input);
        
        // Check that invalid values were replaced with defaults
        $this->assertEquals('http://localhost:8000/api/v1/', $sanitized['api_endpoint']);
        $this->assertEquals('', $sanitized['email']);
        $this->assertEquals(1, $sanitized['batch_size']); // Should be at least 1
        $this->assertEquals(0, $sanitized['rate_limit_delay']); // Should be at least 0
        $this->assertEquals(1, $sanitized['cleanup_logs_days']); // Should be at least 1
    }
    
    /**
     * Test settings update and retrieval
     */
    public function test_settings_update_and_retrieval() {
        $new_settings = array(
            'email' => '<EMAIL>',
            'auto_sync' => true,
            'log_level' => 'debug'
        );
        
        $this->settings->update_settings($new_settings);
        $retrieved_settings = $this->settings->get_settings();
        
        $this->assertEquals('<EMAIL>', $retrieved_settings['email']);
        $this->assertTrue($retrieved_settings['auto_sync']);
        $this->assertEquals('debug', $retrieved_settings['log_level']);
        
        // Check that other settings retained their defaults
        $this->assertEquals('http://localhost:8000/api/v1/', $retrieved_settings['api_endpoint']);
    }
    
    /**
     * Test settings reset
     */
    public function test_settings_reset() {
        // Update some settings
        $this->settings->update_settings(array(
            'email' => '<EMAIL>',
            'auto_sync' => true,
            'log_level' => 'error'
        ));
        
        // Reset settings
        $this->settings->reset_settings();
        $settings = $this->settings->get_settings();
        
        // Check that settings are back to defaults
        $this->assertEquals('', $settings['email']);
        $this->assertFalse($settings['auto_sync']);
        $this->assertEquals('info', $settings['log_level']);
    }
    
    /**
     * Test settings page registration
     */
    public function test_settings_page_registration() {
        global $wp_settings_sections, $wp_settings_fields;
        
        // Trigger settings registration
        do_action('admin_init');
        
        // Check that settings sections were registered
        $this->assertArrayHasKey('gridspoke_seo_settings', $wp_settings_sections);
        
        $sections = $wp_settings_sections['gridspoke_seo_settings'];
        $this->assertArrayHasKey('gridspoke_api_settings', $sections);
        $this->assertArrayHasKey('gridspoke_sync_settings', $sections);
        $this->assertArrayHasKey('gridspoke_optimization_settings', $sections);
        
        // Check that settings fields were registered
        $this->assertArrayHasKey('gridspoke_seo_settings', $wp_settings_fields);
        
        $fields = $wp_settings_fields['gridspoke_seo_settings'];
        $this->assertArrayHasKey('gridspoke_api_settings', $fields);
        
        $api_fields = $fields['gridspoke_api_settings'];
        $this->assertArrayHasKey('api_endpoint', $api_fields);
        $this->assertArrayHasKey('email', $api_fields);
        $this->assertArrayHasKey('password', $api_fields);
    }
    
    /**
     * Test webhook secret generation
     */
    public function test_webhook_secret_generation() {
        // Initially no secret
        $settings = $this->settings->get_settings();
        $this->assertEmpty($settings['webhook_secret']);
        
        // Generate secret
        $secret = $this->settings->generate_webhook_secret();
        
        $this->assertNotEmpty($secret);
        $this->assertEquals(64, strlen($secret));
        
        // Verify it was saved
        $updated_settings = $this->settings->get_settings();
        $this->assertEquals($secret, $updated_settings['webhook_secret']);
        
        // Generate again should return same secret
        $secret2 = $this->settings->generate_webhook_secret();
        $this->assertEquals($secret, $secret2);
    }
    
    /**
     * Test settings export
     */
    public function test_settings_export() {
        // Set up some settings
        $test_settings = array(
            'email' => '<EMAIL>',
            'auto_sync' => true,
            'optimization_fields' => array('title', 'description')
        );
        
        $this->settings->update_settings($test_settings);
        
        $exported = $this->settings->export_settings();
        
        $this->assertIsArray($exported);
        $this->assertEquals('<EMAIL>', $exported['email']);
        $this->assertTrue($exported['auto_sync']);
        $this->assertContains('title', $exported['optimization_fields']);
        
        // Password should not be exported
        $this->assertArrayNotHasKey('password', $exported);
    }
    
    /**
     * Test settings import
     */
    public function test_settings_import() {
        $import_data = array(
            'email' => '<EMAIL>',
            'auto_sync' => true,
            'log_level' => 'debug',
            'optimization_fields' => array('title', 'meta_description')
        );
        
        $result = $this->settings->import_settings($import_data);
        
        $this->assertTrue($result);
        
        $settings = $this->settings->get_settings();
        $this->assertEquals('<EMAIL>', $settings['email']);
        $this->assertTrue($settings['auto_sync']);
        $this->assertEquals('debug', $settings['log_level']);
        $this->assertContains('title', $settings['optimization_fields']);
        $this->assertContains('meta_description', $settings['optimization_fields']);
    }
    
    /**
     * Test invalid settings import
     */
    public function test_invalid_settings_import() {
        $invalid_data = 'not an array';
        
        $result = $this->settings->import_settings($invalid_data);
        
        $this->assertFalse($result);
        
        // Settings should remain unchanged
        $settings = $this->settings->get_settings();
        $this->assertEquals('', $settings['email']); // Should still be default
    }
}
