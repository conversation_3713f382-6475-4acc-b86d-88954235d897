<?php
/**
 * Tests for GridSpoke_Admin class
 */

class Test_GridSpoke_Admin extends GridSpoke_Test_Case {
    
    /**
     * Admin instance
     */
    private $admin;
    
    /**
     * Set up test environment
     */
    public function setUp(): void {
        parent::setUp();
        $this->admin = GridSpoke_Admin::get_instance();
    }
    
    /**
     * Test singleton instance
     */
    public function test_singleton_instance() {
        $instance1 = GridSpoke_Admin::get_instance();
        $instance2 = GridSpoke_Admin::get_instance();
        
        $this->assertSame($instance1, $instance2);
        $this->assertInstanceOf('GridSpoke_Admin', $instance1);
    }
    
    /**
     * Test admin menu registration
     */
    public function test_admin_menu_registration() {
        global $menu, $submenu;
        
        // Trigger menu registration
        do_action('admin_menu');
        
        // Check main menu item exists
        $found_main_menu = false;
        foreach ($menu as $menu_item) {
            if ($menu_item[2] === 'gridspoke-seo') {
                $found_main_menu = true;
                $this->assertEquals('GridSpoke SEO', $menu_item[0]);
                break;
            }
        }
        
        $this->assertTrue($found_main_menu, 'Main menu item not found');
        
        // Check submenu items
        $this->assertArrayHasKey('gridspoke-seo', $submenu);
        $gridspoke_submenu = $submenu['gridspoke-seo'];
        
        $expected_pages = array('gridspoke-seo', 'gridspoke-seo-settings', 'gridspoke-seo-logs', 'gridspoke-seo-tools');
        $actual_pages = array_column($gridspoke_submenu, 4);
        
        foreach ($expected_pages as $page) {
            $this->assertContains($page, $actual_pages, "Submenu page $page not found");
        }
    }
    
    /**
     * Test AJAX test connection
     */
    public function test_ajax_test_connection() {
        // Set up nonce
        $_POST['nonce'] = wp_create_nonce('gridspoke_admin_nonce');
        
        // Mock successful connection
        $this->api_mock->mock_response('health', array(
            'status_code' => 200,
            'body' => json_encode(array('status' => 'healthy'))
        ));
        
        // Capture output
        ob_start();
        
        try {
            $this->admin->ajax_test_connection();
        } catch (WPAjaxDieContinueException $e) {
            // Expected for wp_send_json_success
        }
        
        $output = ob_get_clean();
        $response = json_decode($output, true);
        
        $this->assertTrue($response['success']);
        $this->assertStringContainsString('connection successful', strtolower($response['data']['message']));
    }
    
    /**
     * Test AJAX authentication
     */
    public function test_ajax_authentication() {
        // Set up nonce and POST data
        $_POST['nonce'] = wp_create_nonce('gridspoke_admin_nonce');
        $_POST['email'] = '<EMAIL>';
        $_POST['password'] = 'password123';
        
        // Mock successful authentication
        $this->api_mock->mock_auth_success();
        
        // Capture output
        ob_start();
        
        try {
            $this->admin->ajax_test_authentication();
        } catch (WPAjaxDieContinueException $e) {
            // Expected for wp_send_json_success
        }
        
        $output = ob_get_clean();
        $response = json_decode($output, true);
        
        $this->assertTrue($response['success']);
        $this->assertEquals('Authentication successful', $response['data']['message']);
        $this->assertNotEmpty($response['data']['user']);
    }
    
    /**
     * Test AJAX authentication with missing credentials
     */
    public function test_ajax_authentication_missing_credentials() {
        // Set up nonce but no credentials
        $_POST['nonce'] = wp_create_nonce('gridspoke_admin_nonce');
        $_POST['email'] = '';
        $_POST['password'] = '';
        
        // Capture output
        ob_start();
        
        try {
            $this->admin->ajax_test_authentication();
        } catch (WPAjaxDieContinueException $e) {
            // Expected for wp_send_json_error
        }
        
        $output = ob_get_clean();
        $response = json_decode($output, true);
        
        $this->assertFalse($response['success']);
        $this->assertEquals('Email and password are required', $response['data']['message']);
    }
    
    /**
     * Test AJAX sync all products
     */
    public function test_ajax_sync_all_products() {
        // Set up authentication and mocks
        $this->update_plugin_settings(array(
            'email' => '<EMAIL>',
            'password' => 'password123'
        ));
        
        $this->api_mock->mock_auth_success();
        $this->api_mock->mock_store_creation();
        $this->api_mock->mock_sync_success(3, 0);
        
        // Create test products
        for ($i = 1; $i <= 3; $i++) {
            $this->create_test_woo_product(array('name' => "Test Product $i"));
        }
        
        // Set up nonce
        $_POST['nonce'] = wp_create_nonce('gridspoke_admin_nonce');
        
        // Capture output
        ob_start();
        
        try {
            $this->admin->ajax_sync_all_products();
        } catch (WPAjaxDieContinueException $e) {
            // Expected for wp_send_json_success
        }
        
        $output = ob_get_clean();
        $response = json_decode($output, true);
        
        $this->assertTrue($response['success']);
        $this->assertStringContainsString('synced', strtolower($response['data']['message']));
    }
    
    /**
     * Test AJAX get optimization stats
     */
    public function test_ajax_get_optimization_stats() {
        // Set up test stats
        update_option('gridspoke_completed_optimizations', 15);
        update_option('gridspoke_pending_optimizations', 3);
        update_option('gridspoke_failed_optimizations', 2);
        update_option('gridspoke_total_optimizations', 20);
        
        // Set up nonce
        $_POST['nonce'] = wp_create_nonce('gridspoke_admin_nonce');
        
        // Capture output
        ob_start();
        
        try {
            $this->admin->ajax_get_optimization_stats();
        } catch (WPAjaxDieContinueException $e) {
            // Expected for wp_send_json_success
        }
        
        $output = ob_get_clean();
        $response = json_decode($output, true);
        
        $this->assertTrue($response['success']);
        $this->assertEquals(15, $response['data']['completed_optimizations']);
        $this->assertEquals(3, $response['data']['pending_optimizations']);
        $this->assertEquals(2, $response['data']['failed_optimizations']);
        $this->assertEquals(20, $response['data']['total_optimizations']);
    }
    
    /**
     * Test AJAX clear logs
     */
    public function test_ajax_clear_logs() {
        // Create test logs
        $this->test_data->create_test_logs(10);
        
        // Set up nonce
        $_POST['nonce'] = wp_create_nonce('gridspoke_admin_nonce');
        $_POST['level'] = 'error';
        
        // Capture output
        ob_start();
        
        try {
            $this->admin->ajax_clear_logs();
        } catch (WPAjaxDieContinueException $e) {
            // Expected for wp_send_json_success
        }
        
        $output = ob_get_clean();
        $response = json_decode($output, true);
        
        $this->assertTrue($response['success']);
        $this->assertStringContainsString('error logs cleared', strtolower($response['data']['message']));
    }
    
    /**
     * Test admin notices display
     */
    public function test_admin_notices() {
        // Test success notice
        GridSpoke_Admin::add_admin_message('Test success message', 'success');
        
        ob_start();
        GridSpoke_Admin::display_admin_message();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('notice-success', $output);
        $this->assertStringContainsString('Test success message', $output);
        
        // Test error notice
        GridSpoke_Admin::add_admin_message('Test error message', 'error');
        
        ob_start();
        GridSpoke_Admin::display_admin_message();
        $output = ob_get_clean();
        
        $this->assertStringContainsString('notice-error', $output);
        $this->assertStringContainsString('Test error message', $output);
    }
    
    /**
     * Test dashboard widget registration
     */
    public function test_dashboard_widget() {
        global $wp_meta_boxes;
        
        // Trigger dashboard setup
        do_action('wp_dashboard_setup');
        
        // Check that widget was registered
        $this->assertArrayHasKey('gridspoke_seo_widget', $wp_meta_boxes['dashboard']['normal']['core']);
        
        $widget = $wp_meta_boxes['dashboard']['normal']['core']['gridspoke_seo_widget'];
        $this->assertEquals('GridSpoke SEO Optimization Status', $widget['title']);
    }
    
    /**
     * Test admin asset enqueuing
     */
    public function test_admin_asset_enqueuing() {
        // Simulate GridSpoke admin page
        set_current_screen('toplevel_page_gridspoke-seo');
        
        // Trigger asset enqueuing
        do_action('admin_enqueue_scripts', 'toplevel_page_gridspoke-seo');
        
        // Check that scripts were enqueued
        $this->assertTrue(wp_script_is('gridspoke-admin', 'enqueued'));
        $this->assertTrue(wp_style_is('gridspoke-admin', 'enqueued'));
        $this->assertTrue(wp_script_is('chartjs', 'enqueued'));
        
        // Check localized script data
        $localized_data = wp_scripts()->get_data('gridspoke-admin', 'data');
        $this->assertStringContainsString('gridspoke_admin', $localized_data);
    }
}
