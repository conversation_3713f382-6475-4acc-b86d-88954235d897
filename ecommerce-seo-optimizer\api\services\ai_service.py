"""
AI Service orchestrator for GridSpoke ecommerce SEO optimization.

This service provides a high-level interface for coordinating AI agents,
managing streaming responses, tracking token usage, and handling batch operations.
"""

import asyncio
import json
import time
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator, Union
from datetime import datetime
from contextlib import asynccontextmanager

import redis
from pydantic import BaseModel, Field

from ..agents import BaseAgent, ProductOptimizer, ContentGenerator
from ..agents.base import AgentConfig, OptimizationResult
from ..core.config import settings


logger = logging.getLogger(__name__)


class AIJobRequest(BaseModel):
    """Request structure for AI processing jobs."""
    
    job_id: str
    job_type: str  # product_optimization, content_generation, batch_optimization
    input_data: Dict[str, Any]
    priority: str = "normal"  # low, normal, high, urgent
    complexity: str = "standard"  # economy, standard, premium
    options: Dict[str, Any] = Field(default_factory=dict)
    store_id: Optional[str] = None
    user_id: Optional[str] = None
    user_settings: Optional[Dict[str, Any]] = None


class AIJobStatus(BaseModel):
    """Job status tracking structure."""
    
    job_id: str
    status: str  # pending, processing, completed, failed, cancelled
    progress: float = 0.0
    current_task: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_tokens_used: int = 0
    estimated_cost: float = 0.0
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class StreamingUpdate(BaseModel):
    """Structure for streaming updates."""
    
    job_id: str
    update_type: str  # progress, content_chunk, completion, error
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    data: Dict[str, Any]


class TokenUsageTracker(BaseModel):
    """Track token usage and costs."""
    
    total_tokens: int = 0
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_requests: int = 0
    estimated_cost: float = 0.0
    model_usage: Dict[str, int] = Field(default_factory=dict)
    
    def add_usage(self, tokens: int, model: str, cost_per_token: float = 0.00001):
        """Add token usage data."""
        self.total_tokens += tokens
        self.completion_tokens += tokens  # Simplified - in reality would split prompt/completion
        self.total_requests += 1
        self.estimated_cost += tokens * cost_per_token
        
        if model in self.model_usage:
            self.model_usage[model] += tokens
        else:
            self.model_usage[model] = tokens


class AIService:
    """
    Main AI service orchestrator for GridSpoke.
    
    Provides high-level interface for:
    - Product optimization workflows
    - Content generation pipelines
    - Batch processing operations
    - Real-time streaming responses
    - Token usage tracking and cost management
    - Job queue management and prioritization
    """
    
    def __init__(self, config: Optional[AgentConfig] = None, user_id: Optional[str] = None):
        """Initialize the AI service."""
        # If user_id is provided, try to get user's API key from settings
        if user_id and config:
            # We'll update the config with user's API key when needed
            # since we don't have access to the database here
            pass
            
        self.config = config or AgentConfig()
        self.user_id = user_id
        
        # Initialize agents
        self.product_optimizer = ProductOptimizer(self.config)
        self.content_generator = ContentGenerator(self.config)
        
        # Initialize Redis for job tracking and streaming
        try:
            self.redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
            self.redis_client.ping()
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}")
            self.redis_client = None
        
        # Job tracking
        self.active_jobs: Dict[str, AIJobStatus] = {}
        self.usage_tracker = TokenUsageTracker()
        
        logger.info("AIService initialized with ProductOptimizer and ContentGenerator")
    
    async def _get_user_agent_config(self, user_id: Optional[str] = None) -> AgentConfig:
        """
        Get agent configuration for a specific user.
        
        Args:
            user_id: User ID to get configuration for
            
        Returns:
            AgentConfig with user-specific settings
        """
        # If no user ID provided, use default config
        if not user_id:
            return self.config
            
        # Try to get user's settings from database
        try:
            from ..models.user import User
            from sqlalchemy.ext.asyncio import AsyncSession
            from ..core.database import get_db_session
            
            # Note: In a real implementation, we would pass the database session
            # For now, we'll return the default config
            # This is a simplified implementation for demonstration
            return self.config
        except Exception as e:
            logger.warning(f"Failed to get user settings: {e}")
            return self.config
    
    async def submit_job(self, job_request: AIJobRequest) -> AIJobStatus:
        """
        Submit a new AI processing job.
        
        Args:
            job_request: Job request with all parameters
            
        Returns:
            AIJobStatus object for tracking
        """
        job_status = AIJobStatus(
            job_id=job_request.job_id,
            status="pending",
            started_at=datetime.utcnow()
        )
        
        # Store job status
        self.active_jobs[job_request.job_id] = job_status
        
        if self.redis_client:
            await self._store_job_status(job_status)
        
        logger.info(f"Job {job_request.job_id} submitted: {job_request.job_type}")
        
        # Process job asynchronously
        asyncio.create_task(self._process_job(job_request))
        
        return job_status
    
    async def _process_job(self, job_request: AIJobRequest) -> None:
        """Process a job asynchronously."""
        job_status = self.active_jobs[job_request.job_id]
        
        try:
            job_status.status = "processing"
            job_status.current_task = f"Starting {job_request.job_type}"
            
            if self.redis_client:
                await self._store_job_status(job_status)
                await self._publish_update(StreamingUpdate(
                    job_id=job_request.job_id,
                    update_type="progress",
                    data={"status": "processing", "progress": 0.0}
                ))
            
            # Route to appropriate processor
            if job_request.job_type == "product_optimization":
                result = await self._process_product_optimization(job_request)
            elif job_request.job_type == "content_generation":
                result = await self._process_content_generation(job_request)
            elif job_request.job_type == "batch_optimization":
                result = await self._process_batch_optimization(job_request)
            else:
                raise ValueError(f"Unknown job type: {job_request.job_type}")
            
            # Update job status with result
            job_status.status = "completed"
            job_status.completed_at = datetime.utcnow()
            job_status.result = result.model_dump() if hasattr(result, 'model_dump') else result
            job_status.progress = 100.0
            
            if result and hasattr(result, 'tokens_used') and result.tokens_used:
                job_status.total_tokens_used = result.tokens_used
                self.usage_tracker.add_usage(result.tokens_used, result.model_used or "unknown")
            
            logger.info(f"Job {job_request.job_id} completed successfully")
            
        except Exception as e:
            job_status.status = "failed"
            job_status.error_message = str(e)
            job_status.completed_at = datetime.utcnow()
            
            logger.error(f"Job {job_request.job_id} failed: {e}")
        
        finally:
            if self.redis_client:
                await self._store_job_status(job_status)
                await self._publish_update(StreamingUpdate(
                    job_id=job_request.job_id,
                    update_type="completion" if job_status.status == "completed" else "error",
                    data={"status": job_status.status, "result": job_status.result}
                ))
    
    async def _process_product_optimization(self, job_request: AIJobRequest) -> OptimizationResult:
        """Process product optimization job."""
        # Create a copy of the base config
        agent_config = AgentConfig(**self.config.model_dump())
        
        # If user settings are provided and contain an API key, use it
        if job_request.user_settings and 'openrouter_api_key' in job_request.user_settings:
            agent_config.user_api_key = job_request.user_settings['openrouter_api_key']
            
        # If user settings are provided and contain a custom model, use it
        if job_request.user_settings and 'custom_ai_model' in job_request.user_settings:
            agent_config.custom_model = job_request.user_settings['custom_ai_model']
        
        # Create a new optimizer with user-specific config if needed
        if agent_config.user_api_key or agent_config.custom_model:
            from ..agents import ProductOptimizer
            optimizer = ProductOptimizer(agent_config)
            return await optimizer.process(
                job_request.input_data,
                optimization_type=job_request.options.get('optimization_type', 'complete'),
                complexity=job_request.complexity
            )
        else:
            # Use the default optimizer
            return await self.product_optimizer.process(
                job_request.input_data,
                optimization_type=job_request.options.get('optimization_type', 'complete'),
                complexity=job_request.complexity
            )
    
    async def _process_content_generation(self, job_request: AIJobRequest) -> OptimizationResult:
        """Process content generation job."""
        # Create a copy of the base config
        agent_config = AgentConfig(**self.config.model_dump())
        
        # If user settings are provided and contain an API key, use it
        if job_request.user_settings and 'openrouter_api_key' in job_request.user_settings:
            agent_config.user_api_key = job_request.user_settings['openrouter_api_key']
            
        # If user settings are provided and contain a custom model, use it
        if job_request.user_settings and 'custom_ai_model' in job_request.user_settings:
            agent_config.custom_model = job_request.user_settings['custom_ai_model']
        
        # Create a new generator with user-specific config if needed
        if agent_config.user_api_key or agent_config.custom_model:
            from ..agents import ContentGenerator
            generator = ContentGenerator(agent_config)
            return await generator.process(
                job_request.input_data,
                complexity=job_request.complexity
            )
        else:
            # Use the default generator
            return await self.content_generator.process(
                job_request.input_data,
                complexity=job_request.complexity
            )
    
    async def _process_batch_optimization(self, job_request: AIJobRequest) -> List[OptimizationResult]:
        """Process batch optimization job."""
        products = job_request.input_data.get('products', [])
        optimization_type = job_request.options.get('optimization_type', 'complete')
        
        # Create a copy of the base config
        agent_config = AgentConfig(**self.config.model_dump())
        
        # If user settings are provided and contain an API key, use it
        if job_request.user_settings and 'openrouter_api_key' in job_request.user_settings:
            agent_config.user_api_key = job_request.user_settings['openrouter_api_key']
            
        # If user settings are provided and contain a custom model, use it
        if job_request.user_settings and 'custom_ai_model' in job_request.user_settings:
            agent_config.custom_model = job_request.user_settings['custom_ai_model']
        
        # Create a new optimizer with user-specific config if needed
        optimizer = self.product_optimizer
        if agent_config.user_api_key or agent_config.custom_model:
            from ..agents import ProductOptimizer
            optimizer = ProductOptimizer(agent_config)
        
        # Stream progress updates during batch processing
        results = []
        total_products = len(products)
        
        for i, product_data in enumerate(products):
            try:
                result = await optimizer.process(
                    product_data,
                    optimization_type=optimization_type,
                    complexity=job_request.complexity
                )
                results.append(result)
                
                # Send progress update
                progress = ((i + 1) / total_products) * 100
                if self.redis_client:
                    await self._publish_update(StreamingUpdate(
                        job_id=job_request.job_id,
                        update_type="progress",
                        data={
                            "progress": progress,
                            "current_product": i + 1,
                            "total_products": total_products,
                            "last_completed": product_data.get('name', f'Product {i+1}')
                        }
                    ))
                
                # Small delay to avoid overwhelming the API
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Failed to optimize product {i}: {e}")
                results.append(OptimizationResult(
                    success=False,
                    error_message=str(e)
                ))
        
        return results
    
    async def get_job_status(self, job_id: str) -> Optional[AIJobStatus]:
        """Get current status of a job."""
        if job_id in self.active_jobs:
            return self.active_jobs[job_id]
        
        # Try to load from Redis if not in memory
        if self.redis_client:
            try:
                job_data = self.redis_client.get(f"job_status:{job_id}")
                if job_data:
                    return AIJobStatus(**json.loads(job_data))
            except Exception as e:
                logger.warning(f"Failed to load job status from Redis: {e}")
        
        return None
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a running job."""
        if job_id in self.active_jobs:
            job_status = self.active_jobs[job_id]
            if job_status.status in ["pending", "processing"]:
                job_status.status = "cancelled"
                job_status.completed_at = datetime.utcnow()
                
                if self.redis_client:
                    await self._store_job_status(job_status)
                    await self._publish_update(StreamingUpdate(
                        job_id=job_id,
                        update_type="completion",
                        data={"status": "cancelled"}
                    ))
                
                logger.info(f"Job {job_id} cancelled")
                return True
        
        return False
    
    async def stream_job_updates(self, job_id: str) -> AsyncGenerator[StreamingUpdate, None]:
        """
        Stream real-time updates for a job.
        
        Args:
            job_id: Job ID to stream updates for
            
        Yields:
            StreamingUpdate objects with job progress
        """
        if not self.redis_client:
            raise RuntimeError("Redis connection required for streaming")
        
        # Subscribe to job updates
        pubsub = self.redis_client.pubsub()
        channel = f"job_updates:{job_id}"
        pubsub.subscribe(channel)
        
        try:
            # Send initial status
            job_status = await self.get_job_status(job_id)
            if job_status:
                yield StreamingUpdate(
                    job_id=job_id,
                    update_type="status",
                    data=job_status.model_dump()
                )
            
            # Stream updates
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    try:
                        update_data = json.loads(message['data'])
                        yield StreamingUpdate(**update_data)
                        
                        # Break if job is completed
                        if update_data.get('update_type') in ['completion', 'error']:
                            break
                    except json.JSONDecodeError:
                        continue
                        
        finally:
            pubsub.unsubscribe(channel)
            pubsub.close()
    
    async def _store_job_status(self, job_status: AIJobStatus) -> None:
        """Store job status in Redis."""
        if self.redis_client:
            try:
                key = f"job_status:{job_status.job_id}"
                self.redis_client.setex(
                    key,
                    86400,  # 24 hours TTL
                    json.dumps(job_status.model_dump(), default=str)
                )
            except Exception as e:
                logger.warning(f"Failed to store job status: {e}")
    
    async def _publish_update(self, update: StreamingUpdate) -> None:
        """Publish streaming update to Redis."""
        if self.redis_client:
            try:
                channel = f"job_updates:{update.job_id}"
                self.redis_client.publish(
                    channel,
                    json.dumps(update.model_dump(), default=str)
                )
            except Exception as e:
                logger.warning(f"Failed to publish update: {e}")
    
    async def optimize_product(
        self,
        product_data: Dict[str, Any],
        optimization_type: str = "complete",
        complexity: str = "standard",
        stream: bool = False,
        user_id: Optional[str] = None,
        user_settings: Optional[Dict[str, Any]] = None
    ) -> Union[OptimizationResult, AsyncGenerator[StreamingUpdate, None]]:
        """
        Optimize a single product with optional streaming.
        
        Args:
            product_data: Product information
            optimization_type: Type of optimization
            complexity: AI model complexity level
            stream: Whether to return streaming updates
            user_id: User ID for user-specific settings
            user_settings: User settings including API key
            
        Returns:
            OptimizationResult or streaming generator
        """
        job_id = f"product_{int(time.time() * 1000)}"
        
        job_request = AIJobRequest(
            job_id=job_id,
            job_type="product_optimization",
            input_data=product_data,
            complexity=complexity,
            options={"optimization_type": optimization_type},
            user_id=user_id,
            user_settings=user_settings
        )
        
        if stream:
            # Submit job and return stream
            await self.submit_job(job_request)
            return self.stream_job_updates(job_id)
        else:
            # Process synchronously
            return await self._process_product_optimization(job_request)
    
    async def generate_content(
        self,
        content_request: Dict[str, Any],
        complexity: str = "standard",
        stream: bool = False,
        user_id: Optional[str] = None,
        user_settings: Optional[Dict[str, Any]] = None
    ) -> Union[OptimizationResult, AsyncGenerator[StreamingUpdate, None]]:
        """
        Generate content with optional streaming.
        
        Args:
            content_request: Content generation parameters
            complexity: AI model complexity level
            stream: Whether to return streaming updates
            user_id: User ID for user-specific settings
            user_settings: User settings including API key
            
        Returns:
            OptimizationResult or streaming generator
        """
        job_id = f"content_{int(time.time() * 1000)}"
        
        job_request = AIJobRequest(
            job_id=job_id,
            job_type="content_generation",
            input_data=content_request,
            complexity=complexity,
            user_id=user_id,
            user_settings=user_settings
        )
        
        if stream:
            await self.submit_job(job_request)
            return self.stream_job_updates(job_id)
        else:
            return await self._process_content_generation(job_request)
    
    async def optimize_products_batch(
        self,
        products: List[Dict[str, Any]],
        optimization_type: str = "complete",
        complexity: str = "standard",
        priority: str = "normal",
        user_id: Optional[str] = None,
        user_settings: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Start batch optimization and return job ID for tracking.
        
        Args:
            products: List of product data
            optimization_type: Type of optimization
            complexity: AI model complexity level
            priority: Job priority
            user_id: User ID for user-specific settings
            user_settings: User settings including API key
            
        Returns:
            Job ID for tracking progress
        """
        job_id = f"batch_{int(time.time() * 1000)}"
        
        job_request = AIJobRequest(
            job_id=job_id,
            job_type="batch_optimization",
            input_data={"products": products},
            complexity=complexity,
            priority=priority,
            options={"optimization_type": optimization_type},
            user_id=user_id,
            user_settings=user_settings
        )
        
        await self.submit_job(job_request)
        return job_id
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current token usage and cost statistics."""
        return {
            "total_tokens": self.usage_tracker.total_tokens,
            "total_requests": self.usage_tracker.total_requests,
            "estimated_cost": self.usage_tracker.estimated_cost,
            "model_usage": self.usage_tracker.model_usage,
            "average_tokens_per_request": (
                self.usage_tracker.total_tokens / max(1, self.usage_tracker.total_requests)
            ),
            "cost_per_request": (
                self.usage_tracker.estimated_cost / max(1, self.usage_tracker.total_requests)
            )
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the AI service."""
        health_status = {
            "service": "AIService",
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "redis_connected": self.redis_client is not None,
            "active_jobs": len(self.active_jobs),
            "usage_stats": self.get_usage_stats()
        }
        
        # Check agent health
        try:
            product_agent_health = await self.product_optimizer.health_check()
            content_agent_health = await self.content_generator.health_check()
            
            health_status["agents"] = {
                "product_optimizer": product_agent_health,
                "content_generator": content_agent_health
            }
            
            # Check if any agents are unhealthy
            if (product_agent_health.get("status") != "healthy" or 
                content_agent_health.get("status") != "healthy"):
                health_status["status"] = "degraded"
                
        except Exception as e:
            health_status["status"] = "degraded"
            health_status["agent_error"] = str(e)
        
        # Test Redis connection
        if self.redis_client:
            try:
                self.redis_client.ping()
                health_status["redis_connected"] = True
            except Exception:
                health_status["redis_connected"] = False
                health_status["status"] = "degraded"
        
        return health_status
    
    async def cleanup_completed_jobs(self, max_age_hours: int = 24) -> int:
        """
        Clean up completed jobs older than specified age.
        
        Args:
            max_age_hours: Maximum age in hours for completed jobs
            
        Returns:
            Number of jobs cleaned up
        """
        cutoff_time = datetime.utcnow().timestamp() - (max_age_hours * 3600)
        cleaned_count = 0
        
        jobs_to_remove = []
        for job_id, job_status in self.active_jobs.items():
            if (job_status.status in ["completed", "failed", "cancelled"] and
                job_status.completed_at and 
                job_status.completed_at.timestamp() < cutoff_time):
                jobs_to_remove.append(job_id)
        
        for job_id in jobs_to_remove:
            del self.active_jobs[job_id]
            cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} completed jobs")
        return cleaned_count
