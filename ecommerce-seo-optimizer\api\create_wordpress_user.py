#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a test user with a known password in the GridSpoke database.
"""

import sys
import os

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_user():
    """Create a test user with a known password."""
    try:
        # Import required modules
        from passlib.context import CryptContext
        import uuid
        from datetime import datetime
        
        # Create password context
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Hash the password
        password = "wordpress123"
        hashed_password = pwd_context.hash(password)
        
        print(f"Password: {password}")
        print(f"Hashed password: {hashed_password}")
        
        # Generate a UUID for the user
        user_id = str(uuid.uuid4())
        print(f"User ID: {user_id}")
        
        # Current timestamp
        now = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
        print(f"Created at: {now}")
        
        # SQL command to insert the user
        sql_command = f"""
INSERT INTO users (id, email, hashed_password, full_name, is_active, is_superuser, is_verified, created_at) 
VALUES (
    '{user_id}',
    '<EMAIL>',
    '{hashed_password}',
    'WordPress Plugin User',
    true,
    false,
    true,
    '{now}'
);
"""
        print("\nSQL Command to create user:")
        print(sql_command)
        
        return True
        
    except Exception as e:
        print(f"Error creating test user: {e}")
        return False

if __name__ == "__main__":
    success = create_test_user()
    sys.exit(0 if success else 1)